{"name": "workforce-management-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@apollo/client": "^3.13.8", "@heroicons/react": "^2.2.0", "@types/apollo-upload-client": "^18.0.0", "@types/leaflet": "^1.9.20", "apollo-upload-client": "^18.0.1", "graphql": "^16.11.0", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "lucide-react": "^0.510.0", "netlify-cli": "^22.2.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "react-leaflet-draw": "^0.20.6", "react-router-dom": "^7.6.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3"}, "devDependencies": {"@getmocha/vite-plugins": "latest", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.28.0", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.5.3", "vite": "^6.2.1"}}