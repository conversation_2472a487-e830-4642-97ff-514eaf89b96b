# Site Creation Schema v2025.1

we need to redesign the Regulatory step as this need to meet the requirements on the @d:\atomio\workforce-dev/frontendv1\docs\dev\siteschema.md . bascially how it works is the user has already acquired all the necessary docs and approvals needed for a site and at this point all they do is fill it out to make sure that this docs can be easily accessed and assessed, so they will fill out the info and then upload the doc, for upload feature check how the @d:\atomio\workforce-dev/frontendv1\src\components\inspections/ handle uploads

## Overview
This schema defines the complete data structure for site creation in the workforce management system. It follows best practices for construction site management and integrates with modern mapping and location services.

## Design Principles
1. **Simplicity First**: Only essential data for site creation and operation
2. **Performance Optimized**: Mandatory fields as direct columns, optional data in JSON
3. **Single Table Architecture**: All site data in one table for optimal performance
4. **Geographic Focus**: Proper GeoJSON support for site boundary definition
5. **OSM Integration**: Leverages OpenStreetMap for location discovery and validation
6. **Workflow-Aware**: Tracks creation progress and validation state
7. **Query Efficient**: Direct field access for common queries, no JSON parsing overhead

## Key Features
- **Magical Site Creation**: Minimal user input, maximum auto-calculation
- **High Performance**: Mandatory fields as direct columns for fast queries
- **Single Table Design**: All site data in one table for optimal performance
- **User-Drawn Boundaries**: Polygon drawing for precise site boundaries (essential for remote sites)
- **Location Search**: OSM-powered location discovery and address resolution
- **Simple Area Names**: Just area names - no complex metadata required
- **Direct Field Access**: Known stakeholders and approvals as direct columns
- **Flexible JSON**: Optional data stored in JSON for extensibility
- **Workflow State**: Creation process state management and validation

## Usage Notes
- All coordinates use WGS84 (EPSG:4326) coordinate system
- Site boundaries stored as PostGIS geometry for spatial operations
- Mandatory stakeholders (client, PM, contractor, architect) as direct columns
- Mandatory approvals (building permit, NEMA, NCA, fire safety) as direct columns
- Optional stakeholders and approvals stored in JSON for flexibility
- Areas defined as simple JSON array of names
- Direct field queries for optimal performance on common operations
- Creation workflow tracks user progress through setup steps

---

{
  "BasicInfo": {
    "siteName": "",
    "siteCode": "",                   /* Auto-generated: TYPE-NAME-YY-XXX */
    "description": "",
    "projectType": "",                /* Single selection from predefined list "Residential","Commercial","Industrial","Infrastructure","Technology","Healthcare","Fit-Out","Hospitality","Institution","MixedUse" */
    "constructionType": "",           /* Single selection: New | Renovation | Extension | Demolition | Maintenace */
    "plannedStartDate": "",
    "plannedEndDate": "",
    "estimatedDuration": 0,           /* Auto-calculated from dates */
    "estimatedBudget": 0,
    "currency": "KES",
    "clientName": "",
    "projectManager": "",
    "status": "Draft",                /* Draft | Active | Completed | Cancelled */
    "createdBy": "",
    "createdAt": "",
    "lastModified": ""
  },

  "Location": {
    "searchQuery": "",                /* OSM search query used to find location */
    "displayName": "",                /* Human-readable location from OSM */
    "addressStreet": "",              /* Flattened for direct column access */
    "addressCity": "",
    "addressCounty": "",
    "addressPostalCode": "",
    "addressCountry": "Kenya",
    "latitude": 0,                    /* Center point of site */
    "longitude": 0,
    "accuracy": "",                   /* GPS accuracy if available */
    "osmPlaceId": "",                 /* OSM place ID for reference */
    "osmType": "",                    /* way | node | relation */
    "osmId": ""
  },
  "SiteBoundary": {
    "geometry": {
      "type": "Polygon",
      "coordinates": [[[0, 0]]]         /* User-drawn polygon coordinates - essential for remote sites */
    },
    "drawingComplete": false,
    "lastModified": ""
  },

  "SiteAreas": {
    "areaNames": [                      /* Simple list - just names, nothing else */
      "Main Building",
      "Parking Area",
      "Storage Zone"
    ]
  },
  "Stakeholders": {
    /* Mandatory stakeholders as direct fields for performance */
    "clientName": "",                 /* Already in BasicInfo - reference here */
    "clientEmail": "",
    "clientPhone": "",

    "projectManagerName": "",         /* Already in BasicInfo - reference here */
    "projectManagerEmail": "",
    "projectManagerPhone": "",

    "mainContractorName": "",
    "mainContractorEmail": "",
    "mainContractorPhone": "",
    "mainContractorCompany": "",

    "architectName": "",
    "architectEmail": "",
    "architectPhone": "",
    "architectCompany": "",

    /* Optional stakeholders in JSON for flexibility */
    "additionalStakeholders": [
      {
        "role": "",                   /* Sub-Contractor | Specialist | Consultant | etc. */
        "name": "",
        "company": "",
        "email": "",
        "phone": "",
        "specialization": ""
      }
    ]
  },

  "RegulatoryCompliance": {
    /* Mandatory approvals as direct fields for fast queries */
    "buildingPermitRequired": true,
    "buildingPermitStatus": "Not Started",    /* Not Started | In Progress | Approved | Rejected */
    "buildingPermitNumber": "",
    "buildingPermitAuthority": "",
    "buildingPermitDate": "",
    "buildingPermitExpiry": "",

    "nemaEiaRequired": false,
    "nemaEiaStatus": "Not Started",
    "nemaEiaNumber": "",
    "nemaEiaDate": "",

    "ncaRegistrationRequired": true,
    "ncaRegistrationStatus": "Not Started",
    "ncaRegistrationNumber": "",
    "ncaRegistrationDate": "",

    "fireSafetyRequired": true,
    "fireSafetyStatus": "Not Started",
    "fireSafetyNumber": "",
    "fireSafetyDate": "",

    "overallComplianceStatus": "Incomplete",  /* Incomplete | In Progress | Complete */

    /* Optional approvals in JSON for flexibility */
    "additionalApprovals": [
      {
        "type": "",                   /* Special Permit | Local Approval | etc. */
        "authority": "",
        "status": "Not Started",
        "referenceNumber": "",
        "date": "",
        "notes": ""
      }
    ]
  },

  "CreationWorkflow": {
    "currentStep": 1,                   /* 1: Basic Info | 2: Site Boundary | 3: Areas | 4: Stakeholders | 5: Regulatory */
    "completedSteps": [],               /* JSON array: [1, 2, 3] */
    "sessionId": "",
    "startedAt": "",
    "lastSavedAt": "",
    "isComplete": false,
    "validationErrors": []              /* JSON array of validation errors */
  },

  "Metadata": {
    "version": "1.0",
    "schemaVersion": "2025.1",
    "createdBy": "",
    "createdAt": "",
    "lastModifiedBy": "",
    "lastModifiedAt": "",
    "tags": [],
    "notes": ""
  }
}

---

## Implementation Guidelines

### Site Creation Process Flow (Magical & Minimal)
1. **Basic Info**: Core project details, auto-generate site code
2. **Site Boundary**: OSM search → polygon drawing → auto-calculate everything
3. **Areas**: Simple area name list (just names, nothing else)
4. **Stakeholders**: Mandatory contacts as direct fields, optional in JSON
5. **Regulatory**: Mandatory approvals as direct fields, optional in JSON

### Site Boundary Definition Process (User-Drawn for Remote Sites)
1. **Location Search**: Use OSM Nominatim API for location discovery
2. **Map Display**: Show location on interactive map (Leaflet/MapBox)
3. **Polygon Drawing**: User draws site boundary polygon (essential for remote sites)
4. **Auto-Calculate**: System calculates area, perimeter, center point, bounding box
5. **Validation**: Ensure valid polygon geometry and reasonable size

### Performance Optimization Strategy
1. **Direct Columns**: Mandatory fields (client, PM, building permit, etc.) as direct columns
2. **Fast Queries**: No JSON parsing for common queries (find by client, compliance status)
3. **Efficient Indexes**: Standard B-tree indexes on frequently queried fields
4. **JSON Flexibility**: Optional data in JSON for extensibility without schema changes

### OSM Integration Details
- **Search API**: Nominatim for location search and geocoding
- **Place Data**: Store OSM place ID for future reference
- **Bounding Box**: Use OSM bounding box for initial map view
- **Address Resolution**: Extract structured address from OSM data
- **Validation**: Cross-reference user input with OSM data

### Data Validation Rules (Minimal & Magical)
- Site name: Required, 3-100 characters
- Site code: Auto-generated, unique across system
- Site boundary: Valid polygon with minimum 3 points and reasonable area (100+ sq meters)
- Client: Name and email required (direct columns)
- Project Manager: Name and email required (direct columns)
- Location: Valid coordinates within supported geographic bounds
- Dates: Start date before end date, both in future
- Areas: At least one area name defined (JSON array)
- Regulatory: Building permit status required (direct column)

### Storage Considerations
- **Single Table Design**: All site data in one table for optimal performance
- **Direct Columns**: Mandatory fields as individual columns for fast queries
- **PostGIS Geometry**: Site boundary as GEOMETRY(POLYGON, 4326) column
- **JSON Arrays**: Simple arrays for area names, completed steps, validation errors
- **JSON Objects**: Complex optional data (additional stakeholders, approvals)
- **Efficient Indexes**: B-tree indexes on frequently queried direct columns
- **Spatial Indexes**: GiST index on geometry column for spatial queries
- **Soft Deletes**: Audit trail with deleted_at timestamp
- **Schema Evolution**: Add new direct columns or extend JSON without breaking changes

### Single Table SQL Structure
```sql
CREATE TABLE sites (
    -- Primary Key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    -- Basic Info (Direct Columns)
    site_name VARCHAR(100) NOT NULL,
    site_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    project_type VARCHAR(50) NOT NULL,
    construction_type VARCHAR(50) NOT NULL,
    planned_start_date DATE NOT NULL,
    planned_end_date DATE NOT NULL,
    estimated_duration INTEGER,
    estimated_budget DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'KES',
    status VARCHAR(20) DEFAULT 'Draft',

    -- Location (Direct Columns)
    search_query VARCHAR(500),
    display_name VARCHAR(500),
    address_street VARCHAR(200),
    address_city VARCHAR(100),
    address_county VARCHAR(100),
    address_postal_code VARCHAR(20),
    address_country VARCHAR(100) DEFAULT 'Kenya',
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    accuracy VARCHAR(50),
    osm_place_id VARCHAR(100),
    osm_type VARCHAR(20),
    osm_id VARCHAR(100),

    -- Site Boundary
    site_boundary_geom GEOMETRY(POLYGON, 4326),
    drawing_complete BOOLEAN DEFAULT false,

    -- Site Areas (JSON Array)
    area_names JSON,  -- ["Main Building", "Parking Area"]

    -- Mandatory Stakeholders (Direct Columns)
    client_name VARCHAR(100) NOT NULL,
    client_email VARCHAR(100),
    client_phone VARCHAR(20),
    project_manager_name VARCHAR(100) NOT NULL,
    project_manager_email VARCHAR(100),
    project_manager_phone VARCHAR(20),
    main_contractor_name VARCHAR(100),
    main_contractor_email VARCHAR(100),
    main_contractor_phone VARCHAR(20),
    main_contractor_company VARCHAR(100),
    architect_name VARCHAR(100),
    architect_email VARCHAR(100),
    architect_phone VARCHAR(20),
    architect_company VARCHAR(100),
    additional_stakeholders JSON,  -- Optional stakeholders

    -- Mandatory Regulatory (Direct Columns)
    building_permit_required BOOLEAN DEFAULT true,
    building_permit_status VARCHAR(20) DEFAULT 'Not Started',
    building_permit_number VARCHAR(50),
    building_permit_authority VARCHAR(100),
    building_permit_date DATE,
    building_permit_expiry DATE,
    nema_eia_required BOOLEAN DEFAULT false,
    nema_eia_status VARCHAR(20) DEFAULT 'Not Started',
    nema_eia_number VARCHAR(50),
    nema_eia_date DATE,
    nca_registration_required BOOLEAN DEFAULT true,
    nca_registration_status VARCHAR(20) DEFAULT 'Not Started',
    nca_registration_number VARCHAR(50),
    nca_registration_date DATE,
    fire_safety_required BOOLEAN DEFAULT true,
    fire_safety_status VARCHAR(20) DEFAULT 'Not Started',
    fire_safety_number VARCHAR(50),
    fire_safety_date DATE,
    overall_compliance_status VARCHAR(20) DEFAULT 'Incomplete',
    additional_approvals JSON,  -- Optional approvals

    -- Workflow (Mixed)
    current_step INTEGER DEFAULT 1,
    completed_steps JSON,  -- [1, 2, 3]
    session_id VARCHAR(100),
    started_at TIMESTAMP,
    last_saved_at TIMESTAMP,
    is_complete BOOLEAN DEFAULT false,
    validation_errors JSON,  -- Validation error array

    -- Metadata
    version VARCHAR(10) DEFAULT '1.0',
    schema_version VARCHAR(10) DEFAULT '2025.1',
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(100),
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tags JSON,
    notes TEXT
);

-- Performance Indexes
CREATE INDEX idx_sites_client_name ON sites(client_name);
CREATE INDEX idx_sites_pm_email ON sites(project_manager_email);
CREATE INDEX idx_sites_building_permit_status ON sites(building_permit_status);
CREATE INDEX idx_sites_compliance_status ON sites(overall_compliance_status);
CREATE INDEX idx_sites_location ON sites(address_city, address_county);
CREATE INDEX idx_sites_status ON sites(status);
CREATE INDEX idx_sites_project_type ON sites(project_type);
CREATE INDEX idx_sites_geom ON sites USING GIST(site_boundary_geom);
```

### Query Performance Examples
```sql
-- Fast queries using direct columns (no JSON parsing)
SELECT * FROM sites WHERE client_name = 'John Doe';
SELECT * FROM sites WHERE building_permit_status = 'Approved';
SELECT * FROM sites WHERE project_manager_email = '<EMAIL>';
SELECT * FROM sites WHERE overall_compliance_status = 'Complete';

-- Spatial queries
SELECT * FROM sites WHERE ST_Contains(site_boundary_geom, ST_Point(36.8219, -1.2921));
```

### Security & Privacy
- Validate all coordinate inputs to prevent injection
- Sanitize OSM search queries
- Encrypt sensitive stakeholder information
- Audit log all location and area modifications
- Implement role-based access to site data
