# Worker Management API Specification

## API Endpoints Overview

### 1. Worker Management Endpoints

#### Create Worker
```http
POST /api/workers
Content-Type: application/json

{
  "employee_number": "EMP001",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "hire_date": "2024-01-15",
  "trades": [
    {
      "trade_id": "uuid-carpenter",
      "is_primary": true,
      "certification_level": "intermediate"
    }
  ]
}
```

#### Get Worker with Compliance Status
```http
GET /api/workers/{worker_id}/compliance

Response:
{
  "worker": {
    "id": "uuid",
    "employee_number": "EMP001",
    "name": "<PERSON>",
    "trades": ["Carpenter"],
    "status": "active"
  },
  "training_compliance": {
    "overall_status": "compliant", // compliant, pending, non_compliant
    "required_trainings": [
      {
        "training_module": "HIRA",
        "status": "valid",
        "completion_date": "2024-01-20",
        "expiry_date": "2025-01-20",
        "days_until_expiry": 45
      },
      {
        "training_module": "Occupational First Aid",
        "status": "expired",
        "completion_date": "2023-01-15",
        "expiry_date": "2024-01-15",
        "days_overdue": 30
      }
    ]
  },
  "medical_compliance": {
    "overall_status": "compliant",
    "medical_records": [
      {
        "type": "fitness_certificate",
        "status": "valid",
        "expiry_date": "2024-12-31"
      }
    ]
  },
  "site_eligibility": {
    "eligible": false,
    "blocking_issues": ["Occupational First Aid expired"]
  }
}
```

### 2. Training Management Endpoints

#### Bulk Update Training Records
```http
POST /api/training/bulk-update
Content-Type: application/json

{
  "training_session": {
    "training_module_id": "uuid-hira",
    "completion_date": "2024-02-15",
    "training_provider": "Safety Training Inc",
    "certificate_batch": "BATCH-2024-001"
  },
  "participants": [
    {
      "worker_id": "uuid-worker-1",
      "certificate_number": "CERT-001",
      "certificate_file_url": "https://storage/cert-001.pdf"
    },
    {
      "worker_id": "uuid-worker-2",
      "certificate_number": "CERT-002",
      "certificate_file_url": "https://storage/cert-002.pdf"
    }
  ]
}
```

#### Get Training Renewal Schedule
```http
GET /api/training/renewal-schedule?days_ahead=90

Response:
{
  "upcoming_renewals": [
    {
      "worker": {
        "id": "uuid",
        "name": "John Doe",
        "employee_number": "EMP001"
      },
      "training_module": "Occupational First Aid",
      "current_expiry": "2024-03-15",
      "days_until_expiry": 30,
      "priority": "high",
      "sites_affected": ["Site A", "Site B"]
    }
  ],
  "overdue_certifications": [
    {
      "worker": {
        "id": "uuid",
        "name": "Jane Smith",
        "employee_number": "EMP002"
      },
      "training_module": "Work at Height",
      "expiry_date": "2024-01-01",
      "days_overdue": 45,
      "sites_affected": ["Site C"]
    }
  ]
}
```

### 3. Site Assignment Endpoints

#### Check Site Assignment Eligibility
```http
POST /api/sites/{site_id}/check-eligibility
Content-Type: application/json

{
  "worker_ids": ["uuid-1", "uuid-2", "uuid-3"]
}

Response:
{
  "eligibility_results": [
    {
      "worker_id": "uuid-1",
      "worker_name": "John Doe",
      "eligible": true,
      "compliance_status": "compliant"
    },
    {
      "worker_id": "uuid-2",
      "worker_name": "Jane Smith",
      "eligible": false,
      "blocking_issues": [
        {
          "type": "training",
          "module": "Occupational First Aid",
          "status": "expired",
          "expiry_date": "2024-01-15"
        }
      ]
    }
  ]
}
```

#### Assign Workers to Site
```http
POST /api/sites/{site_id}/assignments
Content-Type: application/json

{
  "assignments": [
    {
      "worker_id": "uuid-1",
      "assigned_date": "2024-02-20",
      "role": "Lead Carpenter"
    }
  ],
  "assigned_by": "uuid-manager"
}
```

### 4. Hikvision Device Integration Endpoints

#### Enroll Worker to Site Devices
```http
POST /api/sites/{site_id}/hikvision/enroll-worker
Content-Type: application/json

{
  "worker_id": "uuid-worker",
  "access_level": 1
}

Response:
{
  "enrollment_results": [
    {
      "device_id": "HIK-001",
      "device_name": "Main Gate",
      "status": "success",
      "device_user_id": "12345"
    },
    {
      "device_id": "HIK-002", 
      "device_name": "Building A Entrance",
      "status": "failed",
      "error": "Face template upload failed"
    }
  ]
}
```

#### Hikvision Event Webhook
```http
POST /api/hikvision/events
Content-Type: application/json

{
  "deviceId": "HIK-001",
  "eventType": "ACCESS_GRANTED",
  "timestamp": "2024-02-20T08:00:00Z",
  "userId": "12345",
  "eventId": "EVT-789",
  "confidence": 0.95,
  "additionalData": {
    "doorId": "1",
    "cardNo": "",
    "faceImage": "base64_image_data"
  }
}

Response:
{
  "status": "received",
  "processed": true
}
```

### 5. Site-Level Attendance Module Endpoints

#### Get Daily Attendance Report
```http
GET /api/sites/{site_id}/attendance?date=2024-02-20

Response:
{
  "site": {
    "id": "uuid",
    "name": "Construction Site A"
  },
  "date": "2024-02-20",
  "summary": {
    "total_assigned": 25,
    "present": 23,
    "absent": 2,
    "late_arrivals": 3,
    "early_departures": 1,
    "total_hours": 207.5,
    "overtime_hours": 15.5
  },
  "attendance_records": [
    {
      "worker": {
        "id": "uuid",
        "name": "John Doe",
        "employee_number": "EMP001",
        "primary_trade": "Carpenter"
      },
      "check_in_time": "08:00:00",
      "check_out_time": "17:00:00",
      "total_hours": 9.0,
      "status": "present",
      "overtime": 1.0,
      "device_used": "Main Gate",
      "face_recognition_confidence": 0.95
    }
  ],
  "device_status": [
    {
      "device_name": "Main Gate",
      "status": "active",
      "last_event": "2024-02-20T16:45:00Z"
    }
  ]
}
```

#### Real-time Attendance WebSocket
```http
WebSocket: ws://api/sites/{site_id}/attendance/live

Message Format:
{
  "type": "attendance_event",
  "data": {
    "worker_name": "John Doe",
    "employee_number": "EMP001",
    "event_type": "check_in",
    "timestamp": "2024-02-20T08:00:00Z",
    "device_location": "Main Gate",
    "confidence": 0.95
  }
}
```

#### Manual Attendance Entry
```http
POST /api/sites/{site_id}/attendance/manual-entry
Content-Type: application/json

{
  "worker_id": "uuid-worker",
  "attendance_date": "2024-02-20",
  "check_in_time": "08:00:00",
  "check_out_time": "17:00:00",
  "reason": "Device malfunction",
  "entered_by": "uuid-site-admin"
}

Response:
{
  "attendance_id": "uuid",
  "status": "created",
  "total_hours": 9.0,
  "requires_approval": true
}
```

#### Device Management
```http
GET /api/sites/{site_id}/devices

Response:
{
  "devices": [
    {
      "id": "uuid",
      "device_name": "Main Gate",
      "device_ip": "*************",
      "status": "active",
      "last_sync": "2024-02-20T07:30:00Z",
      "enrolled_users": 25,
      "location_description": "Main entrance gate"
    }
  ]
}

POST /api/sites/{site_id}/devices/{device_id}/sync-workers
Sync all site workers to specific device

GET /api/sites/{site_id}/devices/health-check
Check connectivity and status of all site devices
```

## Data Flow Examples

### 1. New Worker Onboarding Flow

```mermaid
sequenceDiagram
    participant HR as HR Manager
    participant API as API Server
    participant DB as Database
    participant TM as Training Manager

    HR->>API: POST /api/workers (Create worker)
    API->>DB: Insert worker record
    API->>DB: Insert worker_trades mapping
    API-->>HR: Worker created with ID

    HR->>API: GET /api/workers/{id}/required-trainings
    API->>DB: Query trade training requirements
    API-->>HR: List of required trainings

    TM->>API: POST /api/training/records (Upload certificates)
    API->>DB: Insert training records
    API->>DB: Calculate expiry dates
    API-->>TM: Training records updated

    HR->>API: GET /api/workers/{id}/compliance
    API->>DB: Check training and medical compliance
    API-->>HR: Compliance status report
```

### 2. Site Assignment Flow

```mermaid
sequenceDiagram
    participant SM as Site Manager
    participant API as API Server
    participant DB as Database
    participant Worker as Worker

    SM->>API: POST /api/sites/{id}/check-eligibility
    API->>DB: Query worker compliance status
    API-->>SM: Eligibility results

    SM->>API: POST /api/sites/{id}/assignments
    API->>DB: Insert site assignments
    API->>DB: Update worker status
    API-->>SM: Assignment confirmation

    API->>Worker: Send assignment notification
    Worker-->>API: Acknowledge assignment
```

### 3. Daily Attendance Flow

```mermaid
sequenceDiagram
    participant Worker as Worker
    participant Mobile as Mobile App
    participant API as API Server
    participant DB as Database

    Worker->>Mobile: Scan QR code / Biometric
    Mobile->>API: POST /api/attendance/check-in
    API->>DB: Verify worker site assignment
    API->>DB: Check compliance status
    API->>DB: Insert attendance record
    API-->>Mobile: Check-in confirmation

    Note over Worker,DB: Work period...

    Worker->>Mobile: Check-out request
    Mobile->>API: POST /api/attendance/check-out
    API->>DB: Update attendance record
    API->>DB: Calculate total hours
    API-->>Mobile: Check-out confirmation with hours
```

## Error Handling

### Common Error Responses

#### Worker Not Eligible for Site
```json
{
  "error": "WORKER_NOT_ELIGIBLE",
  "message": "Worker cannot be assigned to site due to compliance issues",
  "details": {
    "worker_id": "uuid",
    "blocking_issues": [
      {
        "type": "training_expired",
        "training_module": "Occupational First Aid",
        "expiry_date": "2024-01-15"
      }
    ]
  }
}
```

#### Training Record Conflict
```json
{
  "error": "TRAINING_RECORD_CONFLICT",
  "message": "Cannot update training record - newer record exists",
  "details": {
    "existing_completion_date": "2024-02-01",
    "attempted_completion_date": "2024-01-15"
  }
}
```

## Integration Considerations

### 1. External Training Providers
- API webhooks for automatic certificate updates
- Standardized certificate formats (PDF with metadata)
- Bulk import capabilities for training batches

### 2. Payroll System Integration
- Daily/weekly attendance data export
- Overtime calculation rules
- Leave and absence tracking

### 3. Access Control Systems
- Real-time worker eligibility checks
- Site access credential management
- Emergency override capabilities

### 4. Mobile Applications
- Offline capability for remote sites
- Biometric authentication support
- GPS location verification
- Photo capture for attendance verification

This API specification provides the foundation for implementing a comprehensive worker management system that handles all aspects of worker lifecycle, training compliance, site assignments, and attendance tracking.