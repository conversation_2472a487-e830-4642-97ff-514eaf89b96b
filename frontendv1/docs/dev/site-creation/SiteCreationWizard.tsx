import React, { useState, useCallback } from 'react';
import { StepSidebar } from './StepSidebar';
import { BasicInfoStep } from './steps/BasicInfoStep';
import { BoundaryStep } from './steps/BoundaryStep';
import { AreasStep } from './steps/AreasStep';
import { StakeholdersStep } from './steps/StakeholdersStep';
import { RegulatoryStep } from './steps/RegulatoryStep';
import { Button } from '@/components/ui/button';
import { SiteCreationState, StepData } from './types';
import { Building, MapPin, Grid3X3, Users, Shield } from 'lucide-react';

const initialState: SiteCreationState = {
  currentStep: 1,
  completedSteps: [],
  basicInfo: {
    siteName: '',
    projectType: '',
    constructionType: '',
    plannedStartDate: '',
    plannedEndDate: '',
    estimatedBudget: 0,
    currency: 'KES',
    clientName: '',
    projectManagerName: '',
    description: ''
  },
  boundary: {
    searchQuery: '',
    displayName: '',
    addressStreet: '',
    addressCity: '',
    addressCounty: '',
    latitude: 0,
    longitude: 0,
    siteBoundaryGeom: null,
    drawingComplete: false
  },
  areas: {
    areaNames: []
  },
  stakeholders: {
    clientEmail: '',
    clientPhone: '',
    projectManagerEmail: '',
    projectManagerPhone: '',
    mainContractorName: '',
    mainContractorEmail: '',
    mainContractorCompany: '',
    architectName: '',
    architectEmail: '',
    architectCompany: '',
    additionalStakeholders: []
  },
  regulatory: {
    buildingPermitRequired: true,
    buildingPermitStatus: 'Not Started',
    nemaEiaRequired: false,
    nemaEiaStatus: 'Not Started',
    ncaRegistrationRequired: true,
    ncaRegistrationStatus: 'Not Started',
    fireSafetyRequired: true,
    fireSafetyStatus: 'Not Started',
    additionalApprovals: []
  },
  validationErrors: {},
  isComplete: false,
  lastSavedAt: null
};

export const SiteCreationWizard: React.FC = () => {
  const [state, setState] = useState<SiteCreationState>(initialState);

  const steps: StepData[] = [
    {
      id: 1,
      title: 'Basic Info',
      description: 'Project details & timeline',
      icon: 'Building',
      isCompleted: state.completedSteps.includes(1),
      isActive: state.currentStep === 1,
      hasErrors: Object.keys(state.validationErrors).some(key => key.startsWith('basicInfo'))
    },
    {
      id: 2,
      title: 'Site Boundary',
      description: 'Location & boundary drawing',
      icon: 'MapPin',
      isCompleted: state.completedSteps.includes(2),
      isActive: state.currentStep === 2,
      hasErrors: Object.keys(state.validationErrors).some(key => key.startsWith('boundary'))
    },
    {
      id: 3,
      title: 'Areas',
      description: 'Define site areas',
      icon: 'Grid3X3',
      isCompleted: state.completedSteps.includes(3),
      isActive: state.currentStep === 3,
      hasErrors: Object.keys(state.validationErrors).some(key => key.startsWith('areas'))
    },
    {
      id: 4,
      title: 'Stakeholders',
      description: 'Key contacts & roles',
      icon: 'Users',
      isCompleted: state.completedSteps.includes(4),
      isActive: state.currentStep === 4,
      hasErrors: Object.keys(state.validationErrors).some(key => key.startsWith('stakeholders'))
    },
    {
      id: 5,
      title: 'Regulatory',
      description: 'Required approvals',
      icon: 'Shield',
      isCompleted: state.completedSteps.includes(5),
      isActive: state.currentStep === 5,
      hasErrors: Object.keys(state.validationErrors).some(key => key.startsWith('regulatory'))
    }
  ];

  const validateStep = useCallback((stepNumber: number): boolean => {
    const errors: Record<string, string[]> = {};

    switch (stepNumber) {
      case 1:
        if (!state.basicInfo.siteName.trim()) {
          errors['basicInfo.siteName'] = ['Site name is required'];
        }
        if (!state.basicInfo.projectType) {
          errors['basicInfo.projectType'] = ['Project type is required'];
        }
        if (!state.basicInfo.plannedStartDate) {
          errors['basicInfo.plannedStartDate'] = ['Start date is required'];
        }
        if (!state.basicInfo.plannedEndDate) {
          errors['basicInfo.plannedEndDate'] = ['End date is required'];
        }
        if (!state.basicInfo.clientName.trim()) {
          errors['basicInfo.clientName'] = ['Client name is required'];
        }
        if (!state.basicInfo.projectManagerName.trim()) {
          errors['basicInfo.projectManagerName'] = ['Project manager name is required'];
        }
        break;

      case 2:
        if (!state.boundary.drawingComplete) {
          errors['boundary.polygon'] = ['Site boundary must be drawn'];
        }
        break;

      case 3:
        if (state.areas.areaNames.length === 0) {
          errors['areas.names'] = ['At least one area is required'];
        }
        break;

      case 4:
        if (!state.stakeholders.clientEmail.trim()) {
          errors['stakeholders.clientEmail'] = ['Client email is required'];
        }
        if (!state.stakeholders.projectManagerEmail.trim()) {
          errors['stakeholders.projectManagerEmail'] = ['Project manager email is required'];
        }
        break;

      case 5:
        // Regulatory validation logic here
        break;
    }

    setState(prev => ({ ...prev, validationErrors: { ...prev.validationErrors, ...errors } }));
    return Object.keys(errors).length === 0;
  }, [state]);

  const handleNextStep = useCallback(() => {
    if (validateStep(state.currentStep)) {
      const newCompletedSteps = [...state.completedSteps];
      if (!newCompletedSteps.includes(state.currentStep)) {
        newCompletedSteps.push(state.currentStep);
      }

      setState(prev => ({
        ...prev,
        currentStep: Math.min(prev.currentStep + 1, 5),
        completedSteps: newCompletedSteps,
        lastSavedAt: new Date()
      }));
    }
  }, [state.currentStep, validateStep]);

  const handlePreviousStep = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentStep: Math.max(prev.currentStep - 1, 1)
    }));
  }, []);

  const handleStepClick = useCallback((stepNumber: number) => {
    // Only allow navigation to completed steps or the next step
    if (state.completedSteps.includes(stepNumber) || stepNumber === state.currentStep + 1) {
      setState(prev => ({ ...prev, currentStep: stepNumber }));
    }
  }, [state.completedSteps, state.currentStep]);

  const updateBasicInfo = useCallback((data: Partial<typeof state.basicInfo>) => {
    setState(prev => ({
      ...prev,
      basicInfo: { ...prev.basicInfo, ...data }
    }));
  }, []);

  const updateBoundary = useCallback((data: Partial<typeof state.boundary>) => {
    setState(prev => ({
      ...prev,
      boundary: { ...prev.boundary, ...data }
    }));
  }, []);

  const updateAreas = useCallback((data: Partial<typeof state.areas>) => {
    setState(prev => ({
      ...prev,
      areas: { ...prev.areas, ...data }
    }));
  }, []);

  const updateStakeholders = useCallback((data: Partial<typeof state.stakeholders>) => {
    setState(prev => ({
      ...prev,
      stakeholders: { ...prev.stakeholders, ...data }
    }));
  }, []);

  const updateRegulatory = useCallback((data: Partial<typeof state.regulatory>) => {
    setState(prev => ({
      ...prev,
      regulatory: { ...prev.regulatory, ...data }
    }));
  }, []);

  const renderCurrentStep = () => {
    switch (state.currentStep) {
      case 1:
        return (
          <BasicInfoStep
            data={state.basicInfo}
            onUpdate={updateBasicInfo}
            errors={state.validationErrors}
          />
        );
      case 2:
        return (
          <BoundaryStep
            data={state.boundary}
            onUpdate={updateBoundary}
            errors={state.validationErrors}
          />
        );
      case 3:
        return (
          <AreasStep
            data={state.areas}
            onUpdate={updateAreas}
            errors={state.validationErrors}
          />
        );
      case 4:
        return (
          <StakeholdersStep
            data={state.stakeholders}
            onUpdate={updateStakeholders}
            errors={state.validationErrors}
          />
        );
      case 5:
        return (
          <RegulatoryStep
            data={state.regulatory}
            onUpdate={updateRegulatory}
            errors={state.validationErrors}
          />
        );
      default:
        return null;
    }
  };

  const progressPercentage = (state.completedSteps.length / 5) * 100;

  return (
    <div className="site-creation-wizard">
      <StepSidebar
        steps={steps}
        currentStep={state.currentStep}
        onStepClick={handleStepClick}
        progressPercentage={progressPercentage}
      />

      <main className="main-content">
        <div className="form-container">
          <div className="step-header mb-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-2xl font-semibold text-foreground">
                Step {state.currentStep}/5
              </h1>
              <div className="text-sm text-muted-foreground">
                {state.lastSavedAt && (
                  <span>Last saved: {state.lastSavedAt.toLocaleTimeString()}</span>
                )}
              </div>
            </div>
            <h2 className="text-3xl font-bold text-foreground mb-2">
              {steps[state.currentStep - 1]?.title}
            </h2>
            <p className="text-lg text-muted-foreground">
              {steps[state.currentStep - 1]?.description}
            </p>
          </div>

          <div className="step-content mb-12">
            {renderCurrentStep()}
          </div>

          <div className="step-navigation">
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={handlePreviousStep}
                disabled={state.currentStep === 1}
                className="px-6 py-2"
              >
                Previous
              </Button>

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => console.log('Save draft')}
                  className="px-6 py-2"
                >
                  Save Draft
                </Button>

                <Button
                  onClick={handleNextStep}
                  disabled={state.currentStep === 5 && !state.isComplete}
                  className="px-6 py-2"
                >
                  {state.currentStep === 5 ? 'Complete' : 'Next'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};