import React from 'react';
import { cn } from '@/lib/utils';
import { StepData } from './types';
import { Check, AlertTriangle, Building, MapPin, Grid3X3, Users, Shield } from 'lucide-react';

interface StepSidebarProps {
  steps: StepData[];
  currentStep: number;
  onStepClick: (stepNumber: number) => void;
  progressPercentage: number;
}

const getStepIcon = (iconName: string, isCompleted: boolean, hasErrors: boolean) => {
  if (isCompleted) {
    return <Check className="w-4 h-4" />;
  }
  
  if (hasErrors) {
    return <AlertTriangle className="w-4 h-4" />;
  }

  switch (iconName) {
    case 'Building':
      return <Building className="w-4 h-4" />;
    case 'MapPin':
      return <MapPin className="w-4 h-4" />;
    case 'Grid3X3':
      return <Grid3X3 className="w-4 h-4" />;
    case 'Users':
      return <Users className="w-4 h-4" />;
    case 'Shield':
      return <Shield className="w-4 h-4" />;
    default:
      return null;
  }
};

export const StepSidebar: React.FC<StepSidebarProps> = ({
  steps,
  currentStep,
  onStepClick,
  progressPercentage
}) => {
  return (
    <div className="step-sidebar">
      <div className="step-list">
        {/* Progress line background */}
        <div
          className="step-connector-line"
          style={{ '--progress-percentage': `${progressPercentage}%` } as React.CSSProperties}
        />

        {steps.map((step, index) => {
          const isClickable = step.isCompleted || step.id === currentStep || step.id === currentStep + 1;
          
          return (
            <div
              key={step.id}
              className={cn(
                "step-item",
                {
                  'cursor-pointer hover:bg-accent/5 rounded-lg p-2 -m-2 transition-colors': isClickable,
                  'cursor-not-allowed': !isClickable
                }
              )}
              onClick={() => isClickable && onStepClick(step.id)}
            >
              <div className="step-content">
                <h3 className={cn(
                  "step-title",
                  {
                    'text-foreground': step.isActive || step.isCompleted,
                    'text-muted-foreground': !step.isActive && !step.isCompleted,
                    'text-destructive': step.hasErrors
                  }
                )}>
                  {step.title}
                </h3>
                <p className={cn(
                  "step-description",
                  {
                    'text-muted-foreground': !step.hasErrors,
                    'text-destructive/80': step.hasErrors
                  }
                )}>
                  {step.description}
                  {step.hasErrors && step.errorCount && (
                    <span className="ml-1 text-destructive">
                      ({step.errorCount} error{step.errorCount > 1 ? 's' : ''})
                    </span>
                  )}
                </p>
              </div>

              <div className={cn(
                "step-circle",
                {
                  'completed': step.isCompleted,
                  'current': step.isActive && !step.isCompleted,
                  'pending': !step.isActive && !step.isCompleted && !step.hasErrors,
                  'error': step.hasErrors
                }
              )}>
                {step.isCompleted ? (
                  <Check className="w-4 h-4" />
                ) : step.hasErrors ? (
                  <AlertTriangle className="w-4 h-4" />
                ) : (
                  <span className="text-sm font-semibold">{step.id}</span>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};