# Area Polygon Management: Complete Technical Documentation

This document provides comprehensive technical documentation for how the Foresta application handles area polygons, covering both search-based and drawing-based approaches for defining conservation areas.

## Overview

The Foresta application allows users to define conservation areas in two ways:
1. **Search-based**: Using OpenStreetMap (OSM) data to find existing geographical boundaries
2. **Drawing-based**: Manually drawing custom polygon boundaries on an interactive map

This documentation covers both approaches and their complete workflows, providing the foundation for understanding and implementing hybrid area definition methods.

## Technology Stack

### Frontend Dependencies
- **Leaflet** (v1.9.4): Core mapping library for interactive maps
- **React-Leaflet** (v4.2.1): React bindings for Leaflet
- **React-Leaflet-Draw** (v0.20.6): Drawing tools integration for Leaflet
- **Leaflet-Draw**: Provides polygon drawing capabilities

### Backend Dependencies
- **SQLAlchemy**: ORM for database operations
- **Flask**: Web framework for API endpoints
- **JSON**: For storing GeoJSON polygon data

### Map Tile Providers
- **OpenStreetMap**: Street view tiles (`https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png`)
- **Esri World Imagery**: Satellite view tiles
- **OpenTopoMap**: Terrain view tiles

## 1. Search-Based Area Definition

### 1.1 Overview of Search-Based Method

The search-based approach allows users to find and select existing geographical locations from OpenStreetMap (OSM) data. This method leverages OSM's comprehensive database of administrative boundaries, natural features, and named locations to automatically define area boundaries.

### 1.2 User Interface Components

The search functionality is implemented in the `AddAreaDialog` component with a tabbed interface:

```javascript
// Tab structure for area definition methods
<Tabs value={locationTab} onChange={handleTabChange}>
  <Tab
    icon={<SearchIcon />}
    label="Search Location"
    iconPosition="start"
  />
  <Tab
    icon={<DrawIcon />}
    label="Draw Area"
    iconPosition="start"
  />
</Tabs>
```

### 1.3 Location Search Process

#### 1.3.1 Search Input Interface

Users interact with a search interface that includes:

```javascript
<TextField
  fullWidth
  placeholder="Search for a city, park, forest, or other location"
  variant="outlined"
  value={searchQuery}
  onChange={(e) => setSearchQuery(e.target.value)}
  onKeyPress={handleKeyPress}
  InputProps={{
    startAdornment: (
      <InputAdornment position="start">
        <SearchIcon color="action" />
      </InputAdornment>
    ),
    endAdornment: searchQuery && (
      <InputAdornment position="end">
        <IconButton onClick={() => setSearchQuery('')}>
          <CloseIcon fontSize="small" />
        </IconButton>
      </InputAdornment>
    )
  }}
/>
```

#### 1.3.2 Search Execution

The search process involves multiple API layers:

```javascript
const handleSearch = async () => {
  if (!searchQuery.trim()) return;

  setIsSearching(true);
  setSearchError(null);

  try {
    // Call location service which interfaces with OSM
    const results = await locationService.searchLocations(searchQuery);

    if (results && results.length > 0) {
      setSearchResults(results);
    } else {
      setSearchError('No locations found. Please try a different search term.');
    }
  } catch (error) {
    setSearchError('Error searching for locations. Please try again.');
  } finally {
    setIsSearching(false);
  }
};
```

### 1.4 OpenStreetMap Integration

#### 1.4.1 Nominatim Search API

The backend uses OSM's Nominatim API for location search:

```python
@staticmethod
def search_locations(query, country_code=None, limit=5):
    """Search for locations using OSM Nominatim API."""
    try:
        # Build the Nominatim API URL
        url = "https://nominatim.openstreetmap.org/search"

        # Prepare parameters
        params = {
            "q": query,
            "format": "json",
            "limit": limit,
            "addressdetails": 1
        }

        # Add country code filter if provided
        if country_code:
            params["countrycodes"] = country_code

        # Add headers to identify the application
        headers = {
            "User-Agent": "Foresta-App/1.0"
        }

        # Make the request
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()

        # Process and format results
        results = response.json()
        locations = []
        for result in results:
            location = {
                "osm_id": result.get("osm_id"),
                "osm_type": result.get("osm_type"),
                "display_name": result.get("display_name"),
                "name": result.get("name", result.get("display_name").split(",")[0].strip()),
                "lat": float(result.get("lat")),
                "lon": float(result.get("lon")),
                "type": result.get("type"),
                "address": result.get("address", {})
            }
            locations.append(location)

        return locations

    except requests.RequestException as e:
        logger.error(f"Error searching OSM locations: {str(e)}")
        return []
```

#### 1.4.2 Search Result Structure

Each search result contains:

```json
{
  "osm_id": 123456789,
  "osm_type": "way",
  "display_name": "Yellowstone National Park, Wyoming, United States",
  "name": "Yellowstone National Park",
  "lat": 44.427963,
  "lon": -110.588455,
  "type": "national_park",
  "address": {
    "national_park": "Yellowstone National Park",
    "state": "Wyoming",
    "country": "United States",
    "country_code": "us"
  }
}
```

### 1.5 Location Selection and Validation

#### 1.5.1 Results Display

Search results are displayed in an interactive list:

```javascript
<List disablePadding>
  {searchResults.map((location, index) => (
    <ListItem
      button
      selected={selectedLocation && selectedLocation.osm_id === location.osm_id}
      onClick={() => handleSelectLocation(location)}
    >
      <LocationIcon />
      <ListItemText
        primary={location.name || location.display_name.split(',')[0]}
        secondary={location.display_name}
      />
    </ListItem>
  ))}
</List>
```

#### 1.5.2 Location Selection Process

When a user selects a location:

```javascript
const handleSelectLocation = (location) => {
  setSelectedLocation(location);
  // Auto-populate the area name from the selected location
  setValue('name', location.name || location.display_name.split(',')[0]);
};
```

### 1.6 Boundary Retrieval Process

#### 1.6.1 Overpass API Integration

For selected locations, the system retrieves detailed boundary data using OSM's Overpass API:

```python
@staticmethod
def get_boundary(osm_id, osm_type):
    """Get boundary coordinates for a location using OSM Overpass API."""
    try:
        # Multiple Overpass API endpoints for redundancy
        overpass_endpoints = [
            "https://overpass-api.de/api/interpreter",
            "https://overpass.kumi.systems/api/interpreter",
            "https://overpass.openstreetmap.ru/api/interpreter"
        ]

        # Build query based on OSM type
        if osm_type == "way":
            # For ways: get the way and all its nodes
            query = f"[out:json][timeout:90];way({osm_id});out body;>;out skel qt;"
        elif osm_type == "relation":
            # For relations: complex query to handle multipolygons
            query = f"[out:json][timeout:90];relation({osm_id});out body;>>;out skel qt;relation({osm_id});way(r);out geom qt;"
        elif osm_type == "node":
            # For nodes: create a small circular area around the point
            query = f"[out:json];node({osm_id});out;"

        # Try each endpoint until one works
        for endpoint in overpass_endpoints:
            try:
                response = requests.post(endpoint, data={"data": query}, timeout=30)
                if response.status_code == 200:
                    break
            except requests.RequestException:
                continue

        # Process the response based on OSM type
        result = response.json()

        if osm_type == "way":
            return OSMService._process_way_response(result, osm_id, osm_type)
        elif osm_type == "relation":
            return OSMService._process_relation_response(result, osm_id, osm_type)
        elif osm_type == "node":
            return OSMService._process_node_response(result, osm_id, osm_type)

    except Exception as e:
        logger.error(f"Error getting OSM boundary: {str(e)}")
        return None
```

#### 1.6.2 GeoJSON Generation from OSM Data

The system converts OSM boundary data to GeoJSON format:

```python
# For simple ways (closed polygons)
def _process_way_response(result, osm_id, osm_type):
    """Process Overpass API response for ways."""
    # Extract nodes and coordinates
    nodes = {node["id"]: node for node in result["elements"] if node["type"] == "node"}
    ways = [element for element in result["elements"] if element["type"] == "way"]

    if not ways:
        return None

    way = ways[0]
    coordinates = []

    # Build coordinate array from node references
    for node_id in way["nodes"]:
        if node_id in nodes:
            node = nodes[node_id]
            coordinates.append([node["lon"], node["lat"]])

    # Close the polygon if not already closed
    if coordinates and coordinates[0] != coordinates[-1]:
        coordinates.append(coordinates[0])

    # Create GeoJSON
    geojson = {
        "type": "Feature",
        "properties": {
            "osm_id": osm_id,
            "osm_type": osm_type,
            "name": way.get("tags", {}).get("name", "Unknown")
        },
        "geometry": {
            "type": "Polygon",
            "coordinates": [coordinates]
        }
    }

    return geojson
```

### 1.7 Area Creation from Search Results

#### 1.7.1 Data Structure for OSM Areas

When creating an area from search results:

```javascript
// Frontend data preparation
if (locationTab === 0) {
  // Create area data from OSM search
  areaData = {
    name: data.name,
    osm_id: selectedLocation.osm_id,
    osm_type: selectedLocation.osm_type,
    type: data.type || 'forest',
    description: data.description || '',
    lat: selectedLocation.lat, // Central latitude from OSM
    lon: selectedLocation.lon  // Central longitude from OSM
  };
}
```

#### 1.7.2 Backend Processing for OSM Areas

The backend handles OSM area creation:

```python
@areas_bp.route('', methods=['POST'])
@jwt_required()
def create_area():
    # Check if this is a drawn area or OSM search area
    is_drawn = data.get('is_drawn', False)

    if not is_drawn:
        # For OSM search areas, validate required fields
        if not all(k in data for k in ('name', 'osm_id', 'osm_type')):
            return jsonify({'error': 'Missing required fields for OSM area'}), 400

        # Get boundary from OSM using Overpass API
        geojson = OSMService.get_boundary(data['osm_id'], data['osm_type'])

        if not geojson:
            return jsonify({'error': 'Could not get boundary from OpenStreetMap'}), 400

    # Calculate area size
    size = OSMService.calculate_area_size(geojson)

    # Create new area record
    area = Area(
        name=data['name'],
        user_id=int(user_id),
        boundaries=geojson,           # Store retrieved GeoJSON
        size=size,
        center_lat=data.get('lat'),   # From OSM search result
        center_lon=data.get('lon'),   # From OSM search result
        type=data.get('type', 'forest'),
        description=data.get('description', '')
    )
```

## 2. Drawing-Based Polygon Creation

### 2.1 Overview of Drawing-Based Method

The drawing-based approach allows users to manually define custom polygon boundaries on an interactive map. This method provides precise control over area boundaries and is ideal for defining areas that don't correspond to existing OSM boundaries.

### 2.2 User Interface Components

The polygon drawing functionality is implemented in the `DrawMap` component (`frontend/src/components/areas/DrawMap.jsx`):

```javascript
// Key imports for drawing functionality
import { MapContainer, TileLayer, FeatureGroup } from 'react-leaflet';
import { EditControl } from 'react-leaflet-draw';
import L from 'leaflet';
import 'leaflet-draw/dist/leaflet.draw.css';
```

### 2.3 Drawing Tool Configuration

The drawing tools are configured with specific options:

```javascript
<EditControl
  position="topright"
  onCreated={handleCreated}
  draw={{
    rectangle: false,      // Disabled
    circle: false,         // Disabled
    circlemarker: false,   // Disabled
    marker: false,         // Disabled
    polyline: false,       // Disabled
    polygon: {             // Only polygon drawing enabled
      allowIntersection: false,
      drawError: {
        color: '#e1e100',
        message: '<strong>Error:</strong> Polygon edges cannot cross!'
      },
      shapeOptions: {
        color: '#FF8C00',        // Orange border
        weight: 2,               // Border thickness
        opacity: 0.9,            // Border opacity
        fillColor: '#FFD580',    // Light orange fill
        fillOpacity: 0.15        // Fill transparency
      }
    }
  }}
/>
```

### 2.4 User Drawing Process

1. **Tool Activation**: User clicks the polygon icon (⬡) in the top-right corner of the map
2. **Point Placement**: User clicks on the map to place polygon vertices
3. **Shape Completion**: User closes the polygon by clicking on the first point
4. **Validation**: System validates that polygon edges don't intersect
5. **Confirmation**: Polygon is created and ready for processing

## 3. Coordinate Extraction & Processing (Drawing-Based)

### 3.1 GeoJSON Conversion

When a polygon is completed, the `handleCreated` function processes the drawn shape:

```javascript
const handleCreated = (e) => {
  const { layerType, layer } = e;
  
  if (layerType === 'polygon') {
    // Convert Leaflet layer to GeoJSON
    const drawnGeoJSON = layer.toGeoJSON();
    
    // Format to match application structure
    const formattedGeoJSON = {
      type: 'Feature',
      properties: {
        name: 'Custom Area'
      },
      geometry: {
        type: 'Polygon',
        coordinates: drawnGeoJSON.geometry.coordinates
      }
    };
  }
};
```

### 3.2 Center Point Calculation

The system calculates the polygon's center point for map positioning:

```javascript
// Extract coordinates from the first ring (outer boundary)
const coordinates = drawnGeoJSON.geometry.coordinates[0];
const lats = coordinates.map(coord => coord[1]);  // Latitude values
const lngs = coordinates.map(coord => coord[0]);  // Longitude values

// Calculate centroid using simple averaging
const centerLat = lats.reduce((sum, lat) => sum + lat, 0) / lats.length;
const centerLng = lngs.reduce((sum, lng) => sum + lng, 0) / lngs.length;
```

### 3.3 Data Structure

The processed polygon data is structured as:

```javascript
{
  geojson: {
    type: 'Feature',
    properties: { name: 'Custom Area' },
    geometry: {
      type: 'Polygon',
      coordinates: [[[lng1, lat1], [lng2, lat2], ...]]  // [longitude, latitude] format
    }
  },
  center: {
    lat: centerLat,
    lng: centerLng
  }
}
```

## 4. Database Storage (Both Methods)

### 4.1 Area Model Schema

Polygons are stored in the `areas` table using the following model (`backend/models/area.py`):

```python
class Area(db.Model):
    __tablename__ = 'areas'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    boundaries = db.Column(db.JSON, nullable=False)  # GeoJSON polygon
    size = db.Column(db.Float)                       # Area in hectares
    type = db.Column(db.String(50))
    center_lat = db.Column(db.Float, nullable=True)  # Central latitude
    center_lon = db.Column(db.Float, nullable=True)  # Central longitude
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    description = db.Column(db.Text)
```

### 4.2 Storage Process

The API endpoint (`backend/routes/areas.py`) handles polygon storage:

```python
@areas_bp.route('', methods=['POST'])
@jwt_required()
def create_area():
    # Check if this is a drawn area
    is_drawn = data.get('is_drawn', False)
    
    if is_drawn:
        # Use provided GeoJSON directly
        geojson = data['boundaries']
        
        # Calculate area size
        size = OSMService.calculate_area_size(geojson)
        
        # Create new area record
        area = Area(
            name=data['name'],
            user_id=int(user_id),
            boundaries=geojson,           # Store complete GeoJSON
            size=size,
            center_lat=data.get('lat'),   # From calculated center
            center_lon=data.get('lon'),   # From calculated center
            type=data.get('type', 'forest'),
            description=data.get('description', '')
        )
```

### 4.3 GeoJSON Format in Database

The `boundaries` field stores the complete GeoJSON Feature:

```json
{
  "type": "Feature",
  "properties": {
    "name": "Custom Area"
  },
  "geometry": {
    "type": "Polygon",
    "coordinates": [
      [
        [longitude1, latitude1],
        [longitude2, latitude2],
        [longitude3, latitude3],
        [longitude1, latitude1]  // Closed polygon
      ]
    ]
  }
}
```

## 5. Polygon Retrieval & Display (Both Methods)

### 5.1 Data Retrieval

Areas are retrieved via the GET endpoint:

```python
@areas_bp.route('', methods=['GET'])
@jwt_required()
def get_areas():
    # Get areas for authenticated user
    areas = Area.query.filter_by(user_id=int(user_id)).all()
    
    # Convert to dictionaries (includes boundaries)
    areas_data = [area.to_dict() for area in areas]
    
    return jsonify({'areas': areas_data})
```

### 5.2 Frontend Display Components

Multiple components handle polygon display:

#### 5.2.1 Area Detail Map (`AreaMap.jsx`)
- Displays individual area with detailed polygon rendering
- Includes hover effects and styling
- Supports multiple map types (street, satellite, terrain)

#### 5.2.2 Dashboard Map (`DashboardMapView.jsx`)
- Shows all user areas on a single map
- Renders polygon boundaries with area-specific styling
- Includes click navigation to area details

#### 5.2.3 Landing Page Map (`InteractiveMap.jsx`)
- Public view of areas with basic polygon display
- Simplified styling and interaction

### 5.3 GeoJSON Processing for Display

Before rendering, GeoJSON data is processed to ensure compatibility:

```javascript
const processGeoJSON = (geojson) => {
  if (!geojson) return null;
  
  // Create deep copy to avoid modifying original
  const processed = JSON.parse(JSON.stringify(geojson));
  
  // Validate structure
  if (!processed.geometry || !processed.geometry.coordinates) {
    console.warn('GeoJSON missing geometry or coordinates:', processed);
  }
  
  return processed;
};
```

### 5.4 Polygon Styling

Consistent styling is applied across all map components:

```javascript
const geoJSONStyle = {
  color: '#FF8C00',        // Orange border
  weight: 2,               // Border thickness
  opacity: 0.9,            // Border opacity
  fillColor: '#FFD580',    // Light orange fill
  fillOpacity: 0.15        // Fill transparency
};

// Hover effects
const hoverStyle = {
  weight: 3,
  color: '#FFA500',        // Brighter orange
  opacity: 1,
  fillOpacity: 0.25        // More visible fill
};
```

### 5.5 Interactive Features

Polygons support various interactive features:

- **Hover Effects**: Visual feedback when mouse hovers over polygon
- **Click Navigation**: Clicking polygon navigates to area details
- **Popup Information**: Display area name, type, and size
- **Map Bounds Adjustment**: Automatic zoom to fit all polygons

## 6. Integration with External Services

### 6.1 Google Earth Engine Integration

Stored polygons are used for satellite data analysis:

```python
def geojson_to_ee_geometry(geojson):
    """Convert GeoJSON to Google Earth Engine geometry."""
    geometry_type = geojson['geometry']['type']
    coordinates = geojson['geometry']['coordinates']
    
    if geometry_type == 'Polygon':
        return ee.Geometry.Polygon(coordinates)
    elif geometry_type == 'MultiPolygon':
        return ee.Geometry.MultiPolygon(coordinates)
```

### 6.2 Area Calculation

Polygon area is calculated using the OSM service:

```python
def calculate_area_size(geojson):
    """Calculate area size in hectares from GeoJSON."""
    # Implementation uses geospatial calculations
    # Returns area in hectares
```

## 7. Error Handling & Validation

### 7.1 Frontend Validation
- Prevents self-intersecting polygons
- Validates minimum number of points (3)
- Handles drawing errors gracefully

### 7.2 Backend Validation
- Validates GeoJSON structure
- Ensures required fields are present
- Handles database constraint violations

### 7.3 Common Error Scenarios
- Invalid GeoJSON format
- Self-intersecting polygons
- Insufficient coordinate points
- Database storage failures
- Network connectivity issues

## 8. Performance Considerations

### 8.1 Frontend Optimization
- Lazy loading of map components
- Efficient GeoJSON processing
- Minimal re-renders during drawing

### 8.2 Backend Optimization
- JSON field indexing for spatial queries
- Efficient area calculations
- Proper database connection pooling

### 8.3 Data Transfer
- Compressed GeoJSON transmission
- Minimal coordinate precision
- Efficient API response structure

## 9. Hybrid Approach Implementation

### 9.1 Combining Search and Drawing Methods

The current implementation provides a foundation for hybrid approaches that combine both search-based and drawing-based methods. Here are potential implementation strategies:

#### 9.1.1 Search-Then-Modify Workflow

```javascript
// Allow users to start with OSM search, then modify boundaries
const handleSearchThenModify = (osmLocation) => {
  // 1. Get OSM boundary
  const osmBoundary = await locationService.getBoundary(osmLocation.osm_type, osmLocation.osm_id);

  // 2. Load boundary into drawing map for modification
  setInitialPolygon(osmBoundary);
  setDrawingMode('modify');

  // 3. Allow user to edit the loaded polygon
  // 4. Save as custom drawn area with OSM reference
};
```

#### 9.1.2 Multi-Area Composition

```javascript
// Allow combining multiple OSM areas or mixing OSM + drawn areas
const handleMultiAreaComposition = () => {
  const compositeArea = {
    name: data.name,
    type: 'composite',
    components: [
      { type: 'osm', osm_id: 123, osm_type: 'way' },
      { type: 'drawn', boundaries: customPolygon },
      { type: 'osm', osm_id: 456, osm_type: 'relation' }
    ],
    boundaries: mergedGeoJSON, // Combined boundaries
    is_composite: true
  };
};
```

### 9.2 Technical Considerations for Hybrid Implementation

#### 9.2.1 Database Schema Extensions

```python
# Extended Area model for hybrid support
class Area(db.Model):
    # ... existing fields ...

    # Hybrid-specific fields
    source_type = db.Column(db.String(20))  # 'osm', 'drawn', 'hybrid', 'composite'
    osm_reference = db.Column(db.JSON)      # Original OSM data if modified
    modification_history = db.Column(db.JSON)  # Track changes from original
    component_areas = db.Column(db.JSON)    # For composite areas
```

#### 9.2.2 Frontend Component Architecture

```javascript
// Unified area creation component
const UnifiedAreaCreator = () => {
  const [creationMode, setCreationMode] = useState('search'); // 'search', 'draw', 'hybrid'
  const [baseArea, setBaseArea] = useState(null);
  const [modifications, setModifications] = useState([]);

  return (
    <Box>
      <CreationModeSelector mode={creationMode} onChange={setCreationMode} />

      {creationMode === 'search' && <SearchInterface onSelect={setBaseArea} />}
      {creationMode === 'draw' && <DrawInterface onDraw={setBaseArea} />}
      {creationMode === 'hybrid' && (
        <HybridInterface
          baseArea={baseArea}
          onModify={setModifications}
        />
      )}
    </Box>
  );
};
```

### 9.3 Advanced Features for Hybrid Approach

#### 9.3.1 Boundary Refinement Tools

- **Vertex Editing**: Allow fine-tuning of OSM boundary vertices
- **Boundary Simplification**: Reduce polygon complexity while maintaining accuracy
- **Boundary Expansion/Contraction**: Systematic boundary adjustments
- **Hole Creation**: Add exclusion areas within larger boundaries

#### 9.3.2 Validation and Quality Assurance

```javascript
// Boundary validation for hybrid areas
const validateHybridBoundary = (originalBoundary, modifiedBoundary) => {
  const validation = {
    areaChangePercentage: calculateAreaChange(originalBoundary, modifiedBoundary),
    topologyValid: validateTopology(modifiedBoundary),
    selfIntersections: checkSelfIntersections(modifiedBoundary),
    significantChanges: identifySignificantChanges(originalBoundary, modifiedBoundary)
  };

  return validation;
};
```

## 10. Conclusion

The Foresta application provides a robust, user-friendly system for creating, storing, and displaying area polygons using both search-based and drawing-based approaches. The comprehensive integration of OpenStreetMap data with custom drawing capabilities creates a flexible foundation for defining conservation areas.

### Key Strengths:

1. **Dual Approach Flexibility**: Users can choose between OSM search for existing boundaries or custom drawing for precise control
2. **Standardized Data Format**: Consistent use of GeoJSON ensures compatibility across all components
3. **Robust API Integration**: Reliable integration with OSM Nominatim and Overpass APIs
4. **Scalable Architecture**: Modular design supports easy extension and modification
5. **Comprehensive Error Handling**: Graceful handling of various failure scenarios

### Future Enhancement Opportunities:

1. **Hybrid Workflows**: Implement search-then-modify capabilities
2. **Boundary Validation**: Advanced topology and quality checking
3. **Collaborative Editing**: Multi-user boundary definition and review
4. **Offline Capabilities**: Local storage and sync for remote areas
5. **Advanced Visualization**: 3D rendering and satellite overlay integration

The system's modular design and use of standard GeoJSON format ensures compatibility with external mapping and analysis services, making it an excellent foundation for conservation area management applications.

