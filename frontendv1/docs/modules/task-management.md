# Task Management System Documentation

## Overview

The Task Management system is a comprehensive module within the Workforce platform that enables systematic planning, risk assessment, and execution of construction activities. The system operates on a hierarchical structure with company-level templates cascading down to site-specific implementations, ensuring standardization while allowing for site-specific customizations and risk assessments.
## Time of creation
A task can only be created for the coming days, you can't create a task for a past date or the current date as we need to give the HSE enough time to review the task and approve it.

## System Architecture

### Two-Tier Structure

The Task Management system operates on two distinct levels:

1. **Company Level**: General task templates that serve as the foundation
2. **Site Level**: Customized task instances with site-specific risk assessments

### Data Independence

- **No Foreign Key Relations**: Site tasks are complete copies of company templates
- **Full Auditability**: Each site maintains independent task records
- **Customization Freedom**: Sites can modify tasks without affecting other locations
- **Historical Integrity**: Task modifications don't impact historical records

## Task Hierarchy

### Structure Overview

```
📁 Company Task Templates
├── 📂 Excavation (Category)
│   ├── 📋 Remove topsoil (Subtask)
│   │   ├── ⚠️ Hazard: Underground utilities
│   │   │   └── 🛡️ Control: Call 811 before digging
│   │   ├── ⚠️ Hazard: Cave-in risk
│   │   │   └── 🛡️ Control: Proper shoring/sloping
│   │   └── ⚠️ Hazard: Equipment operation
│   │       └── 🛡️ Control: Certified operator required
│   ├── 📋 Dig trench (Subtask)
│   └── 📋 Backfill and compact (Subtask)
├── 📂 Concrete Work (Category)
├── 📂 Steel Erection (Category)
└── 📂 Electrical Installation (Category)
```

### Task Components

#### Task Category
- **Purpose**: High-level grouping of related activities
- **Examples**: Excavation, Concrete Work, Steel Erection, Electrical Installation
- **Scope**: Broad construction disciplines or work types

#### Subtasks
- **Purpose**: Specific activities within a category
- **Detail Level**: Actionable work items that can be assigned and tracked
- **Dependencies**: May have prerequisite relationships with other subtasks

#### Hazards and Control Measures
- **Risk Identification**: Specific dangers associated with each subtask
- **Control Measures**: Detailed mitigation strategies and safety procedures
- **Compliance**: Aligned with regulatory requirements and industry standards

## Task States

### Site Task States

The system tracks tasks through six distinct states:

| State | Description | Available Actions | Next States |
|-------|-------------|-------------------|-------------|
| **Opened** | Task copied from template | Request, Edit, Delete | Requested |
| **Requested** | Submitted for HSE review | Review, Cancel | Approved, Cancelled |
| **Approved** | HSE approved for execution | Start, Block, Cancel | In Progress, Cancelled |
| **In Progress** | Currently being executed | Complete, Block, Cancel | Completed, Cancelled |
| **Cancelled** | Task terminated | Archive | - |
| **Completed** | Task finished successfully | Archive | - |

### Task Request States

The approval workflow tracks requests through five states:

| State | Description | Responsible Party | Actions Available |
|-------|-------------|-------------------|-------------------|
| **Opened** | Initial request created | Site Engineer | Submit for Review |
| **Pending Approval** | Awaiting HSE Officer review | Site HSE Officer | Approve, Disapprove, Request Changes |
| **Pending Assessment** | Awaiting HSE Admin final approval | HSE Admin | Approve, Disapprove |
| **Disapproved** | Request rejected | Site Engineer | Revise and Resubmit (max 3 attempts) |
| **Assessed and Approved** | Final approval granted | System | Begin Task Execution |

## Task Workflow

### Complete Task Lifecycle

```mermaid
flowchart TD
    A[Company Task Template] --> B[Site Engineer Selects Task]
    B --> C[Create Site Task Copy]
    C --> D[Task State: Opened]
    D --> E[Engineer Submits Request]
    E --> F[Task Request State: Pending Approval]
    
    F --> G{Site HSE Officer Review}
    G -->|Approve| H[Update Hazards/Controls]
    G -->|Disapprove| I[Task Request State: Disapproved]
    
    H --> J[Attach Required Documents]
    J --> K[Submit to HSE Admin]
    K --> L[Task Request State: Pending Assessment]
    
    L --> M{HSE Admin Review}
    M -->|Approve| N[Task Request State: Assessed and Approved]
    M -->|Disapprove| O[Task Request State: Disapproved]
    
    N --> P[Task State: Approved]
    O --> Q{Retry Count < 3?}
    Q -->|Yes| R[Allow Resubmission]
    Q -->|No| S[Discard Task]
    
    P --> T[Task Available for Execution]
    T --> U[Task State: In Progress]
    U --> V[Task State: Completed]
    
    I --> Q
    R --> E
```

### Task Request Process

```mermaid
sequenceDiagram
    participant SE as Site Engineer
    participant System as Workforce System
    participant HSE as Site HSE Officer
    participant Admin as HSE Admin
    participant TB as Toolbox System
    
    SE->>System: Select Task from Template
    System->>System: Create Site Task Copy
    SE->>System: Fill Work Description
    SE->>System: Submit Task Request
    
    System->>HSE: Notification - Task Pending Review
    HSE->>System: Review Task Details
    HSE->>System: Update Hazards/Controls
    HSE->>System: Attach Required Documents
    HSE->>System: Submit to HSE Admin
    
    System->>Admin: Notification - Final Approval Needed
    Admin->>System: Review Complete Task Package
    
    alt Approve
        Admin->>System: Approve Task
        System->>SE: Notification - Task Approved
        System->>TB: Add to Available Tasks
    else Disapprove
        Admin->>System: Disapprove with Comments
        System->>SE: Notification - Task Disapproved
        alt Retry Available
            SE->>System: Revise and Resubmit
        else Max Retries Reached
            System->>System: Discard Task
        end
    end
```

## User Roles and Responsibilities

### Site Engineer/Agent
- **Primary Actions**:
  - Browse and select tasks from company templates
  - Create task requests with work descriptions
  - Respond to HSE feedback and revisions
  - Monitor task approval status
- **Access Level**: Read-only access to company templates, full access to own requests
- **Limitations**: Cannot modify hazards/controls, cannot approve tasks

### Site HSE Officer
- **Primary Actions**:
  - Review and validate task requests
  - Modify hazards and control measures based on site conditions
  - Attach required documentation and certificates
  - Block tasks when site conditions become unsafe
  - Submit approved tasks to HSE Admin
- **Authority**: Can modify task safety elements, approve initial safety assessment
- **Responsibilities**: Ensure site-specific risks are properly assessed and controlled

### HSE Admin
- **Primary Actions**:
  - Final approval authority for all task requests
  - Review complete task packages including documentation
  - Approve or disapprove tasks with detailed comments
  - Monitor task approval metrics and trends
- **Authority**: Ultimate decision-making power for task execution authorization
- **Oversight**: Company-wide task management and safety standards

### System Administrator
- **Primary Actions**:
  - Manage company-level task templates
  - Configure task categories and standard hazards
  - Set up RAMS document parsing (future feature)
  - Monitor system performance and usage
- **Access Level**: Full system configuration and template management
- **Responsibilities**: Maintain standardized task templates and system integrity

## Task Documentation Management

### VSCode-Inspired Interface for Tasks

Given the document-heavy nature of tasks, the interface follows the same folder-based approach as permits:

#### Left Panel - Task Explorer
```
📁 Active Tasks
├── 📂 EXC-2024-001 (Excavation - Trench Installation)
│   ├── 📋 Task Details
│   ├── 📄 Equipment Certificate - Excavator-XC200
│   ├── 📄 Operator Competency - John Smith
│   ├── 📄 RAMS-EXC-001.pdf
│   ├── 🔗 Linked Permit: SP-2024-045
│   └── 📊 Risk Assessment Matrix
├── 📂 CON-2024-002 (Concrete - Foundation Pour)
│   ├── 📋 Task Details
│   ├── 📄 Concrete Mix Design
│   ├── 📄 Pump Operator Certificate
│   ├── 🔗 Linked Permit: GP-2024-001
│   └── 📊 Quality Control Plan
└── 📂 STL-2024-003 (Steel Erection - Beam Installation)

📁 Pending Approval
├── 📂 ELE-2024-004 (Electrical - Panel Installation)
├── 📂 PLM-2024-005 (Plumbing - Rough-in)
└── 📂 FIN-2024-006 (Finishing - Drywall)

📁 Completed Tasks
├── 📂 EXC-2024-001 (Completed)
└── 📂 CON-2024-001 (Completed)

📁 Company Templates
├── 📂 Excavation Templates
├── 📂 Concrete Templates
├── 📂 Steel Erection Templates
└── 📂 Electrical Templates
```

#### Right Panel - Tabbed Task Views
- **Task Details Tab**: Complete task information, hazards, and controls
- **Documents Tab**: All attached certificates, RAMS, and supporting documents
- **Risk Assessment Tab**: Interactive risk matrix and control measures
- **Progress Tab**: Task execution timeline and milestones
- **Audit Trail Tab**: Complete history of approvals, modifications, and comments
- **Linked Items Tab**: Associated permits, equipment, and personnel

### Document Management Features

#### Automatic Document Linking
- **Equipment Certificates**: Auto-link based on equipment assignments
- **Operator Competencies**: Connect to worker qualification records
- **RAMS Documents**: Associate relevant risk assessment method statements
- **Permit References**: Link to required work permits

#### Document Validation
- **Expiry Checking**: Automatic validation of certificate expiration dates
- **Completeness Verification**: Ensure all required documents are attached
- **Version Control**: Track document updates and revisions
- **Approval Status**: Visual indicators for document approval status

## Integration Points

### Existing Workforce Modules

#### Worker Management Integration
- **Competency Verification**: Validate worker qualifications against task requirements
- **Automatic Assignment**: Suggest qualified workers based on task needs
- **Training Alerts**: Identify additional training needed for task execution
- **Availability Checking**: Ensure assigned workers are available and on-site

#### Equipment Management Integration
- **Equipment Requirements**: Link required equipment to specific tasks
- **Availability Verification**: Check equipment availability and maintenance status
- **Certificate Validation**: Ensure equipment inspection certificates are current
- **Usage Tracking**: Monitor equipment utilization across tasks

#### Safety Module Integration
- **Toolbox Talk Population**: Auto-populate safety briefings with task-specific hazards
- **Incident Association**: Link incidents to specific tasks for trend analysis
- **Risk Assessment Updates**: Update task risks based on incident data
- **Safety Performance**: Track task-related safety metrics

#### Permit Management Integration
- **Permit Requirements**: Identify permits needed for specific tasks
- **Automatic Linking**: Associate tasks with relevant work permits
- **Cross-Validation**: Ensure permit coverage aligns with task scope
- **Expiry Coordination**: Coordinate task timing with permit validity

### External System Integration

#### RAMS Document Parsing (Future Feature)
- **Automated Extraction**: Parse standard RAMS documents to populate task templates
- **Hazard Identification**: Automatically identify and categorize risks
- **Control Measure Mapping**: Extract and standardize control measures
- **Template Generation**: Create task templates from parsed RAMS data

#### Document Management Systems
- **Central Repository**: Connect to corporate document management systems
- **Version Synchronization**: Maintain current versions of all documents
- **Access Control**: Respect existing document permissions and security
- **Search Integration**: Enable document search across connected systems

## Technical Implementation

### Data Structure

```typescript
interface TaskTemplate {
  id: string;
  companyId: string;
  category: string;
  name: string;
  description: string;
  hazards: Hazard[];
  controlMeasures: ControlMeasure[];
  requiredDocuments: DocumentRequirement[];
  estimatedDuration: number;
  skillRequirements: SkillRequirement[];
  equipmentRequirements: EquipmentRequirement[];
}

interface SiteTask {
  id: string;
  siteId: string;
  templateId: string; // Reference only, not foreign key
  status: TaskStatus;
  category: string;
  name: string;
  description: string;
  workDescription: string; // Site-specific description
  hazards: Hazard[]; // Can be modified from template
  controlMeasures: ControlMeasure[]; // Can be modified from template
  attachedDocuments: Document[];
  linkedPermits: string[];
  assignedWorkers: WorkerAssignment[];
  assignedEquipment: EquipmentAssignment[];
  createdBy: string;
  createdAt: Date;
  approvedBy?: string;
  approvedAt?: Date;
  completedAt?: Date;
  blockedReason?: string;
  blockedBy?: string;
  blockedAt?: Date;
}

interface TaskRequest {
  id: string;
  taskId: string;
  siteId: string;
  status: TaskRequestStatus;
  requestedBy: string;
  reviewedBy?: string;
  approvedBy?: string;
  comments: Comment[];
  retryCount: number;
  maxRetries: number; // Default: 3
  createdAt: Date;
  reviewedAt?: Date;
  approvedAt?: Date;
}

interface Hazard {
  id: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  likelihood: number; // 1-5 scale
  severity: number; // 1-5 scale
  riskScore: number; // likelihood × severity
  controlMeasures: string[]; // Array of control measure IDs
}

interface ControlMeasure {
  id: string;
  description: string;
  type: 'elimination' | 'substitution' | 'engineering' | 'administrative' | 'ppe';
  effectiveness: number; // 1-5 scale
  implementationCost: 'low' | 'medium' | 'high';
  trainingRequired: boolean;
  equipmentRequired: string[];
}

interface DocumentRequirement {
  type: string;
  description: string;
  mandatory: boolean;
  validityPeriod?: number; // days
  renewalRequired: boolean;
}
```

### State Management

#### Task State Transitions
```typescript
const taskStateTransitions = {
  opened: ['requested', 'cancelled'],
  requested: ['approved', 'cancelled', 'opened'], // Can return to opened for revisions
  approved: ['in_progress', 'cancelled', 'blocked'],
  in_progress: ['completed', 'cancelled', 'blocked'],
  blocked: ['approved', 'cancelled'], // Can return to approved when unblocked
  cancelled: [], // Terminal state
  completed: [] // Terminal state
};

const taskRequestStateTransitions = {
  opened: ['pending_approval'],
  pending_approval: ['pending_assessment', 'disapproved'],
  pending_assessment: ['assessed_and_approved', 'disapproved'],
  disapproved: ['opened'], // Can retry up to maxRetries
  assessed_and_approved: [] // Terminal state
};
```

#### Business Rules Engine
- **Retry Limit Enforcement**: Automatically discard tasks after 3 failed attempts
- **Document Validation**: Ensure all mandatory documents are attached and valid
- **Competency Verification**: Validate worker qualifications before task approval
- **Equipment Availability**: Check equipment availability during task scheduling
- **Permit Dependencies**: Ensure required permits are active before task execution

### Mobile Considerations

#### Field Task Management
- **Offline Task Access**: Cache approved tasks for offline viewing
- **Photo Documentation**: Capture progress photos and attach to tasks
- **Voice Notes**: Record audio notes for task updates
- **QR Code Scanning**: Quick task identification and status updates
- **GPS Tracking**: Location verification for task execution

#### Mobile Interface Adaptations
- **Simplified Task Browser**: Mobile-optimized task selection interface
- **Touch-Friendly Forms**: Large buttons and simplified input methods
- **Offline Synchronization**: Queue actions for sync when connectivity returns
- **Push Notifications**: Real-time alerts for task approvals and assignments

## Benefits and ROI

### Safety Benefits
- **Standardized Risk Assessment**: Consistent hazard identification across all sites
- **Proactive Risk Management**: Address risks before task execution begins
- **Document Completeness**: Ensure all safety documentation is current and accessible
- **Audit Trail**: Complete history of safety decisions and approvals
- **Incident Prevention**: Reduce accidents through systematic risk control

### Operational Benefits
- **Process Standardization**: Consistent task management across all company sites
- **Improved Efficiency**: 50% reduction in task planning time through templates
- **Better Resource Utilization**: Optimize worker and equipment assignments
- **Quality Assurance**: Systematic approach ensures consistent work quality
- **Regulatory Compliance**: Maintain complete documentation for inspections

### Financial Benefits
- **Reduced Rework**: Proper planning reduces costly mistakes and rework
- **Insurance Benefits**: Demonstrate systematic risk management to insurers
- **Productivity Gains**: 30% improvement in task completion times
- **Documentation Savings**: 70% reduction in manual paperwork
- **Scalability**: Handle increased project volume without proportional overhead

### User Experience Benefits
- **Familiar Interface**: VSCode-inspired design reduces learning curve
- **Centralized Information**: All task-related information in one location
- **Mobile Access**: Field-friendly interface for on-site task management
- **Real-time Updates**: Live status updates prevent information delays
- **Integration Benefits**: Seamless connection with other Workforce modules

## Implementation Roadmap

### Phase 1: Core Task Management (Weeks 1-6)
- Company template management system
- Basic site task creation and copying
- Simple approval workflow
- Document attachment capabilities

### Phase 2: Advanced Workflow (Weeks 7-12)
- Complete state management system
- Multi-level approval process
- Retry mechanism implementation
- Integration with worker/equipment modules

### Phase 3: Enhanced Interface (Weeks 13-18)
- VSCode-inspired interface development
- Advanced document management
- Risk assessment tools
- Mobile interface optimization

### Phase 4: Advanced Features (Weeks 19-24)
- RAMS document parsing capability
- Advanced analytics and reporting
- Predictive risk assessment
- AI-powered task optimization

### Phase 5: Integration & Analytics (Weeks 25-30)
- Complete integration with all Workforce modules
- Advanced reporting and dashboard features
- Performance analytics and optimization
- Training and rollout preparation

This comprehensive task management system ensures systematic planning and execution of construction activities while maintaining the highest safety standards and operational efficiency.