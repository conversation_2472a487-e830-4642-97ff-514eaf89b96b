# Permits Management System Documentation

## Overview

The Permits Management system is a critical component of the Workforce platform, designed to handle authorization and control of work activities on construction sites. The system manages two distinct permit types with different validity periods and risk levels, ensuring proper authorization workflows and compliance tracking.

## Permit Types

### 1. General Permit
- **Purpose**: Covers standard work activities for the week
- **Validity**: 7 days from issue date
- **Generation**: Typically created at the start of each week
- **Quantity**: Multiple general permits can be active simultaneously within the same week
- **Usage**: Serves as the foundation permit that special permits must link to

### 2. Special Permit
- **Purpose**: Authorizes high-risk work activities
- **Validity**: 8 hours from issue time
- **Risk Categories**:
  - Hot works (welding, cutting, grinding)
  - Confined space entry
  - Excavation work
  - Working at height
  - Electrical work
- **Dependencies**: Must be linked to an active general permit
- **Linking**: Can also be linked to other permits when required

## Permit States

The system tracks permits through five distinct states:

| State | Description | Actions Available |
|-------|-------------|-------------------|
| **Raised/Open** | Initial state when permit is submitted | Approve, Reject, Cancel |
| **Approved** | Permit has been authorized for use | Use, Cancel, Close |
| **Rejected** | Permit denied with comments | Resubmit (new permit) |
| **Cancelled** | Permit withdrawn before completion | Archive |
| **Closed** | Work completed and permit returned | Archive |

## Permit Workflow

```mermaid
flowchart TD
    A[Site HSE Officer Creates Permit] --> B{Assign Required Personnel}
    B --> C[Competent Person Assigned]
    B --> D[Authorizing Person(s) Assigned]
    C --> E[Submit to HSE Admin/System Admin]
    D --> E
    
    E --> F{Review Process}
    F -->|Approved| G[Status: Approved]
    F -->|Rejected| H[Status: Rejected + Comments]
    F -->|Cancelled| I[Status: Cancelled + Comments]
    
    G --> J[Permit Available for Use]
    H --> K[End - Resubmit if needed]
    I --> K
    
    J --> L[Work Execution]
    L --> M[Work Completed]
    M --> N[Return Permit]
    N --> O[Workers Add Signatures]
    O --> P{All Signatures Complete?}
    P -->|No| Q[Pending Signatures]
    P -->|Yes| R[Status: Closed]
    Q --> O
    R --> S[Archive Permit]
```

## User Roles and Responsibilities

### Site HSE Officer
- **Primary Actions**:
  - Create and raise permits
  - Assign competent persons
  - Assign authorizing persons
  - Submit permits for approval
- **Required Fields**:
  - Competent person assignment
  - Authorizing person(s) assignment
  - Risk assessment details
  - Work scope and duration

### HSE Admin/System Admin
- **Primary Actions**:
  - Review submitted permits
  - Approve or reject permits
  - Add review comments
  - Manage approval workflows
- **Authority**:
  - Final approval authority
  - Becomes authorizing person upon approval
  - Can assign additional authorizing persons

### Competent Person
- **Responsibilities**:
  - Oversee work execution
  - Ensure compliance with permit conditions
  - Monitor safety requirements
  - Sign off on work completion

### Authorizing Person(s)
- **Responsibilities**:
  - Authorize work commencement
  - Verify safety conditions
  - Multiple persons can be assigned
  - Final authority for work execution

### Permitted Workers
- **Requirements**:
  - Add name and signature upon work completion
  - Timestamp automatically captured by system
  - All workers must sign before permit closure

## Permit Lifecycle Management

### Creation Phase
```mermaid
sequenceDiagram
    participant HSE as Site HSE Officer
    participant System as Workforce System
    participant Admin as HSE Admin
    
    HSE->>System: Create New Permit
    System->>HSE: Display Permit Form
    HSE->>System: Fill Required Fields
    HSE->>System: Assign Competent Person
    HSE->>System: Assign Authorizing Person(s)
    HSE->>System: Submit for Approval
    System->>Admin: Notification - Permit Pending Review
```

### Approval Phase
```mermaid
sequenceDiagram
    participant Admin as HSE Admin
    participant System as Workforce System
    participant HSE as Site HSE Officer
    participant Workers as Site Workers
    
    Admin->>System: Review Permit
    alt Approve
        Admin->>System: Approve with Comments
        System->>HSE: Notification - Permit Approved
        System->>Workers: Permit Available for Use
    else Reject
        Admin->>System: Reject with Comments
        System->>HSE: Notification - Permit Rejected
    else Cancel
        Admin->>System: Cancel with Comments
        System->>HSE: Notification - Permit Cancelled
    end
```

### Closure Phase
```mermaid
sequenceDiagram
    participant Workers as Permitted Workers
    participant System as Workforce System
    participant Competent as Competent Person
    participant HSE as Site HSE Officer
    
    Workers->>System: Return Completed Permit
    loop For Each Worker
        Workers->>System: Add Name & Signature
        System->>System: Auto-timestamp Entry
    end
    System->>Competent: Verify All Signatures
    Competent->>System: Confirm Completion
    System->>HSE: Permit Closed Notification
    System->>System: Archive Permit
```

## Proposed Permit Interface Design

### VSCode-Inspired Interface

The proposed interface follows a familiar IDE-style layout that enhances productivity and organization:

#### Left Panel - Permit Explorer
```
📁 Active Permits
├── 📄 GP-2024-001 (General - Expires: 5 days)
│   ├── 🔗 SP-2024-045 (Hot Work - Expires: 6h)
│   ├── 🔗 SP-2024-046 (Height Work - Expires: 2h)
│   └── 📋 RAMS-HW-001.pdf
├── 📄 GP-2024-002 (General - Expires: 3 days)
│   ├── 🔗 SP-2024-047 (Confined Space - Active)
│   └── 📋 RAMS-CS-002.pdf
└── 📄 SP-2024-048 (Excavation - Pending Approval)

📁 Pending Approval
├── 📄 SP-2024-049 (Hot Work)
└── 📄 GP-2024-003 (General)

📁 Recently Closed
├── 📄 SP-2024-044 (Completed)
└── 📄 SP-2024-043 (Completed)
```

#### Right Panel - Tabbed Permit Views
- **Multiple Tabs**: Open multiple permits simultaneously
- **Permit Form View**: Complete permit details in read-only or edit mode
- **Document Viewer**: Integrated RAMS and attachment viewing
- **Signature Panel**: Worker signature collection interface
- **History Tab**: Permit lifecycle and audit trail

### Interface Features

#### Left Panel Functionality
- **Folder Structure**: Hierarchical organization of permits
- **Visual Indicators**: 
  - Color coding for permit status (green=active, yellow=expiring, red=expired)
  - Icons for permit types and documents
  - Expiry countdown timers
- **Quick Actions**: Right-click context menus for common actions
- **Search & Filter**: Find permits by type, status, date, or personnel

#### Right Panel Functionality
- **Tab Management**: 
  - Open multiple permits in separate tabs
  - Close, pin, and reorder tabs
  - Save tab sessions for later
- **Split View**: View multiple documents side-by-side
- **Zoom & Print**: Document viewing controls
- **Real-time Updates**: Live status updates without refresh

## Technical Implementation Considerations

### Data Structure
```typescript
interface Permit {
  id: string;
  type: 'general' | 'special';
  status: 'raised' | 'approved' | 'rejected' | 'cancelled' | 'closed';
  validityPeriod: number; // hours
  createdAt: Date;
  expiresAt: Date;
  linkedPermits: string[]; // IDs of linked permits
  
  // Personnel
  competentPerson: PersonReference;
  authorizingPersons: PersonReference[];
  permittedWorkers: WorkerSignature[];
  
  // Workflow
  createdBy: PersonReference;
  reviewedBy: PersonReference;
  comments: Comment[];
  
  // Documents
  attachments: DocumentReference[];
  ramsDocuments: DocumentReference[];
}

interface WorkerSignature {
  workerId: string;
  name: string;
  signature: string; // base64 or signature data
  timestamp: Date;
  ipAddress?: string;
  device?: string;
}

interface Comment {
  id: string;
  userId: string;
  content: string;
  timestamp: Date;
  type: 'approval' | 'rejection' | 'general';
}
```

### State Management
- Real-time permit status updates using WebSocket connections
- Automatic expiry notifications with configurable lead times
- Signature collection workflow with validation
- Document synchronization and version control
- Offline capability for mobile field operations

### Mobile Considerations
- **Offline Access**: Cache active permits for field workers without connectivity
- **Signature Capture**: Touch-based signature collection with biometric options
- **Push Notifications**: Real-time alerts for approvals, expirations, and assignments
- **Simplified Interface**: Mobile-optimized permit viewing and basic actions
- **Camera Integration**: Photo capture for work completion evidence

## Integration Points

### Existing Workforce Modules
- **Worker Management**: Personnel assignment and verification
  - Validate competent person qualifications
  - Check worker certifications and training status
  - Automatic assignment based on skills and availability
- **Training System**: Competency validation
  - Verify required training completion before permit approval
  - Link specific training requirements to permit types
  - Alert for expired certifications
- **Safety Module**: Incident linking and risk assessment
  - Associate incidents with active permits
  - Risk assessment integration for permit approval
  - Safety performance metrics affecting permit privileges
- **Time Management**: Work duration tracking
  - Log actual work hours against permitted time
  - Overtime alerts for permit extensions
  - Productivity metrics for permit efficiency
- **Mobile Engineer App**: Field permit management
  - Mobile permit creation and submission
  - Field supervisor approval capabilities
  - Real-time permit status updates

### External Systems
- **Document Management**: RAMS and procedure documents
  - Automatic RAMS attachment based on work type
  - Version control for safety procedures
  - Digital document signing capabilities
- **Audit Systems**: Compliance reporting
  - Regulatory compliance reports
  - Permit utilization analytics
  - Audit trail generation
- **Notification Services**: Email/SMS alerts
  - Multi-channel notification delivery
  - Escalation procedures for overdue approvals
  - Customizable notification preferences
- **Digital Signature**: Legal signature capture
  - Biometric signature validation
  - Legal compliance for digital signatures
  - Signature verification and authentication

## Benefits and ROI

### Operational Benefits
- **Streamlined Approval Process**: 60% reduction in approval time through automated workflows
- **Improved Compliance**: 95% permit compliance rate with automated validation
- **Enhanced Safety**: Real-time permit status visibility prevents unauthorized work
- **Reduced Paperwork**: 80% reduction in manual forms and physical documentation
- **Resource Optimization**: Better allocation of competent persons across multiple permits

### Safety Benefits
- **Risk Mitigation**: Proper authorization for high-risk work with linked risk assessments
- **Audit Trail**: Complete permit lifecycle documentation for investigations
- **Expiry Management**: Automatic alerts prevent permit overruns and unsafe work continuation
- **Personnel Tracking**: Clear accountability for all work activities and responsibilities
- **Competency Assurance**: Validation of worker qualifications before permit approval

### User Experience Benefits
- **Familiar Interface**: VSCode-inspired design reduces learning curve for technical users
- **Multi-tasking**: Tab-based interface improves productivity for permit reviewers
- **Quick Access**: Hierarchical organization speeds permit location and management
- **Real-time Updates**: Live status prevents outdated information and conflicts
- **Mobile Optimization**: Field-friendly interface for on-site permit management

### Financial Benefits
- **Cost Reduction**: Eliminate physical permit books and printing costs
- **Time Savings**: Reduce administrative overhead by 70%
- **Compliance Savings**: Avoid regulatory fines through improved compliance
- **Insurance Benefits**: Potential premium reductions due to improved safety processes
- **Scalability**: Handle increased permit volume without proportional staff increase

## Implementation Roadmap

### Phase 1: Core Permit Management (Weeks 1-4)
- Basic permit creation and approval workflows
- User role implementation
- Simple status tracking
- Mobile-responsive design

### Phase 2: Advanced Features (Weeks 5-8)
- VSCode-inspired interface development
- Document attachment system
- Digital signature collection
- Real-time notifications

### Phase 3: Integration & Analytics (Weeks 9-12)
- Integration with existing Workforce modules
- Advanced reporting and analytics
- Mobile app enhancements
- Performance optimization

### Phase 4: Advanced Capabilities (Weeks 13-16)
- Offline functionality
- Advanced workflow customization
- AI-powered risk assessment
- Comprehensive audit features

This comprehensive permit management system ensures safe work authorization while providing an intuitive, efficient interface for all stakeholders involved in the permit lifecycle.