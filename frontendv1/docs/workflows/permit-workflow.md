# Permit Workflow Documentation

## Overview

The Permit system manages the authorization of high-risk work activities by creating formal permits that reference approved task risk assessments. Permits serve as the final authorization layer before work begins, ensuring that all safety requirements, worker competencies, and environmental conditions are verified and documented.

## Core Principles

1. **Task-Centric Foundation**: Permits reference approved tasks for hazards, control measures, and requirements
2. **Streamlined Authorization**: Permits auto-generate when conditions are met, eliminating redundant approvals
3. **Worker Assignment**: Links specific workers to permitted activities with competency verification
4. **Real-Time Conditions**: Validates current conditions against approved parameters
5. **Audit and Compliance**: Maintains complete documentation for regulatory compliance
6. **Operational Efficiency**: Minimizes delays while maintaining safety standards

## Rationale for Streamlined Approach

### Why No Secondary Approval is Needed

**The Admin HSE has already approved:**
- All hazards and their risk assessments
- All control measures and their effectiveness
- Required worker competencies and certifications
- Weather protocols and environmental conditions
- Equipment requirements and safety procedures

**The system automatically validates:**
- Task was approved by Admin HSE (within last 24 hours)
- All assigned workers meet competency requirements
- Current conditions are within approved parameters
- Required equipment is available and functional

### Operational Benefits

1. **Eliminates Bottlenecks**: Admin HSE manages ~10 sites, preventing permit approval delays
2. **Maintains Safety Standards**: All safety requirements are pre-approved and system-validated
3. **Enables Timely Execution**: Work can begin within 30 minutes of toolbox completion
4. **Reduces Administrative Burden**: Admin HSE focuses on exceptions and oversight
5. **Preserves Audit Trail**: Complete documentation maintained for compliance

### Admin HSE Role Shift

**From**: Reactive permit approver
**To**: Proactive risk manager and oversight provider

- **Dashboard Monitoring**: Real-time visibility of all active permits across sites
- **Exception Management**: Intervenes only when conditions change or issues arise
- **Post-Work Sign-off**: Provides formal acknowledgment for record-keeping
- **Continuous Improvement**: Reviews permit data for process enhancement

## Permit Workflow Overview

```mermaid
graph TD
    A[Approved Task Available] --> B[Site HSE Selects Permit Type]
    B --> C[System Auto-Populates from Task]
    C --> D[Add Workers from Attendance]
    D --> E[System Verifies Worker Competencies]
    E --> F{All Workers Qualified?}
    F -->|Yes| G[Permit Auto-Generated & Active]
    F -->|No| H[Show Competency Gaps]
    H --> I[Select Different Workers or Provide Training]
    I --> E
    G --> J[Notify Admin HSE - Dashboard Update]
    J --> K[Work Execution with Monitoring]
    K --> L[Admin HSE Signs Off for Records]
    L --> M[Permit Closure]
    
    style A fill:#e8f5e8
    style G fill:#e3f2fd
    style H fill:#fff3e0
    style M fill:#f3e5f5
```

## Detailed Workflow Steps

### Phase 1: Permit Initiation
**Time: Day of Work Execution**
**Actor: Site HSE**

#### Step 1: Select Approved Task
- Site HSE accesses permit system
- Views list of approved tasks requiring permits
- Selects specific task that needs permit authorization
- System displays task details including required permit types

```mermaid
sequenceDiagram
    participant HSE as Site HSE
    participant PS as Permit System
    participant DB as Task Database
    participant AS as Attendance System
    
    HSE->>PS: Access permit system
    PS->>DB: Query approved tasks needing permits
    DB->>PS: Return tasks with permit requirements
    PS->>HSE: Display available tasks
    HSE->>PS: Select specific task
    PS->>DB: Retrieve full task details
    DB->>PS: Return task with hazards/controls
```

#### Step 2: System Auto-Populates from Task
When a task is selected, the system automatically populates:
- **Task Information**: Name, description, location, category
- **Hazards**: All identified hazards from approved task (read-only)
- **Control Measures**: All approved control measures (read-only)
- **Weather Protocols**: Applicable weather-specific controls
- **Required PPE**: Personal protective equipment requirements
- **Required Competencies**: Worker certification and training needs
- **Permit Type**: Specific permit type required (hot work, confined space, etc.)

```typescript
interface PermitAutoPopulation {
  // From Approved Task (Read-Only)
  taskId: string;
  taskName: string;
  taskDescription: string;
  location: string;
  hazards: Hazard[];
  controlMeasures: ControlMeasure[];
  weatherProtocols: WeatherHazardProtocol[];
  requiredPPE: string[];
  requiredCompetencies: string[];
  permitType: PermitType;
  
  // Current Context
  currentDate: Date;
  currentWeatherCondition: string;
  applicableWeatherProtocols: WeatherHazardProtocol[];
}
```

### Phase 2: Worker Assignment
**Actor: Site HSE**

#### Step 3: Add Workers from Today's Attendance
- System displays workers present for the day (from attendance system)
- Site HSE selects workers who will perform the permitted work
- System shows each worker's competencies and certifications
- Only workers with required competencies can be assigned

```mermaid
flowchart TD
    A[View Today's Attendance] --> B[Select Workers for Task]
    B --> C[Check Worker Competencies]
    C --> D{Competencies Match Requirements?}
    D -->|Yes| E[Add Worker to Permit]
    D -->|No| F[Show Competency Gap]
    F --> G[Select Different Worker or Provide Training]
    G --> C
    E --> H[Assign Worker Role]
    H --> I{More Workers Needed?}
    I -->|Yes| B
    I -->|No| J[Worker Assignment Complete]
    
    style D fill:#fff3e0
    style F fill:#ffebee
    style J fill:#e8f5e8
```

#### Step 4: Verify Worker Competencies
For each assigned worker, the system verifies:
- **Required Certifications**: Valid and current certificates
- **Training Requirements**: Completed mandatory training
- **Medical Fitness**: Current medical clearances
- **Experience Level**: Appropriate experience for assigned role
- **Previous Incidents**: Any relevant safety history

```typescript
interface WorkerAssignment {
  workerId: string;
  workerName: string;
  role: 'permit-holder' | 'entrant' | 'attendant' | 'fire-watch' | 'operator';
  
  // Competency Verification
  requiredCertifications: {
    certificationType: string;
    required: boolean;
    hasValid: boolean;
    expiryDate?: Date;
  }[];
  
  requiredTraining: {
    trainingType: string;
    required: boolean;
    completed: boolean;
    completionDate?: Date;
  }[];
  
  medicalClearance: {
    required: boolean;
    valid: boolean;
    expiryDate?: Date;
  };
  
  competencyStatus: 'qualified' | 'needs-training' | 'not-qualified';
}
```

### Phase 3: Automated Permit Generation
**Actor: System + Site HSE**

#### Step 5: System Validation and Auto-Generation
Once workers are assigned, the system automatically:
- **Validates Task Approval**: Confirms task was approved by Admin HSE
- **Verifies Worker Competencies**: Ensures all assigned workers meet requirements
- **Checks Environmental Conditions**: Validates current conditions are within approved parameters
- **Confirms Equipment Availability**: Verifies required safety equipment is available
- **Generates Active Permit**: Creates permit with status: `active`

```mermaid
flowchart TD
    A[Workers Assigned] --> B[System Validates Task Approval]
    B --> C{Task Approved by Admin HSE?}
    C -->|No| D[Error - Invalid Task]
    C -->|Yes| E[Verify All Worker Competencies]
    E --> F{All Workers Qualified?}
    F -->|No| G[Show Competency Gaps]
    F -->|Yes| H[Check Current Conditions]
    H --> I{Conditions Within Approved Parameters?}
    I -->|No| J[Warning - Conditions Changed]
    I -->|Yes| K[Generate Active Permit]
    
    G --> L[Select Different Workers]
    J --> M[Apply Weather Protocols or Stop]
    L --> E
    M --> N{Can Work Proceed Safely?}
    N -->|Yes| K
    N -->|No| O[Require Task Re-Assessment]
    
    style K fill:#e8f5e8
    style D fill:#ffebee
    style O fill:#ffebee
```

**Automated Validation Criteria:**
- **Task Validity**: Approved task exists and is current (already approved by Admin HSE)
- **Worker Qualifications**: System-verified competencies match task requirements
- **Environmental Conditions**: Current conditions within pre-approved weather protocols
- **Equipment Availability**: Required safety equipment confirmed available
- **Time Validity**: Permit generated within valid timeframe (same day as approval)

### Phase 4: Permit Execution and Admin Notification
**Time: During Authorized Work Period**
**Actor: Permit Holder + Assigned Workers + Admin HSE**

#### Step 6: Permit Auto-Generated - Work Authorized
When system validation passes:
- Permit status is automatically set to: `active`
- Work authorization is granted immediately
- All assigned workers are notified
- Permit conditions and restrictions are communicated
- Monitoring and compliance requirements activated

#### Step 7: Admin HSE Dashboard Notification
Simultaneously:
- Admin HSE dashboard is updated with new active permit
- Notification shows: task reference, workers assigned, permit type, start time
- Admin HSE can view permit details for oversight
- **No approval action required** - permit is already active
- Admin HSE focuses on monitoring and exception management

#### Step 8: Work Execution with Monitoring
During permitted work:

```mermaid
graph LR
    A[Work Begins] --> B[Continuous Monitoring]
    B --> C{Conditions Within Permit Parameters?}
    C -->|Yes| D[Continue Work]
    C -->|No| E[Stop Work Immediately]
    E --> F[Assess Changed Conditions]
    F --> G{Can Work Resume Safely?}
    G -->|Yes| H[Resume with Additional Controls]
    G -->|No| I[Suspend Permit]
    H --> D
    I --> J[Return to Admin HSE for Review]
    D --> B
    
    style E fill:#ffebee
    style I fill:#ffebee
    style J fill:#fff3e0
```

**Monitoring Requirements:**
- **Environmental Conditions**: Weather, air quality, noise levels
- **Worker Compliance**: PPE usage, procedure adherence
- **Equipment Status**: Safety equipment functionality
- **Emergency Readiness**: Emergency response capability
- **Communication**: Regular check-ins with permit authority

### Phase 5: Permit Closure and Admin Sign-Off
**Actor: Permit Holder + Site HSE + Admin HSE**

#### Step 9: Work Completion and Permit Closure
At work completion:
- Permit holder confirms work is complete
- Site area is secured and cleaned
- Equipment is properly stored
- Any incidents or near-misses are documented
- Permit is formally closed with completion report

#### Step 10: Admin HSE Sign-Off for Records
After permit closure:
- Admin HSE receives notification of completed permit
- Reviews completion report and any incidents
- **Provides digital signature for record-keeping purposes**
- Signs off on permit for regulatory compliance
- **No approval required** - just formal acknowledgment for audit trail

```typescript
interface PermitClosure {
  permitId: string;
  closedBy: string;
  closedAt: Date;
  
  workCompletion: {
    tasksCompleted: string[];
    workQuality: 'satisfactory' | 'needs-rework' | 'excellent';
    timeSpent: number;
    materialsUsed: string[];
  };
  
  safetyPerformance: {
    incidentsReported: number;
    nearMissesReported: number;
    safetyObservations: string[];
    complianceRating: number; // 1-5 scale
  };
  
  environmentalImpact: {
    wasteGenerated: string[];
    spillsOrReleases: boolean;
    environmentalControls: string[];
  };
  
  lessonsLearned: string[];
  recommendationsForFuture: string[];
}
```

## Permit Types and Specific Requirements

### Hot Work Permits
- **Fire watch requirements**: Trained fire watch personnel
- **Fire prevention**: Fire extinguishers, water supply
- **Area preparation**: Combustible material removal
- **Atmospheric monitoring**: Gas detection if required

### Confined Space Permits
- **Atmospheric testing**: Oxygen, toxic gases, flammable gases
- **Ventilation**: Continuous or intermittent ventilation
- **Communication**: Reliable communication with attendant
- **Rescue procedures**: Emergency rescue plan and equipment

### Working at Height Permits
- **Fall protection**: Harnesses, lanyards, anchor points
- **Weather conditions**: Wind speed, precipitation limits
- **Equipment inspection**: Scaffolds, ladders, platforms
- **Rescue plan**: High-angle rescue procedures

### Electrical Work Permits
- **Isolation verification**: Lockout/tagout procedures
- **Testing**: Voltage testing, earth continuity
- **Weather restrictions**: Dry conditions required
- **Emergency response**: Electrical emergency procedures

## Integration Points

### With Task Management System
- **References approved tasks** for all safety information
- **Inherits hazards and controls** from task risk assessments
- **Updates task status** when permits are issued
- **Maintains audit trail** linking permits to tasks

### With Toolbox System
- **Coordinates with daily briefings** for permitted work
- **Shares worker assignments** between systems
- **Aligns safety messaging** across all communications
- **Supports integrated safety management**

### With Attendance System
- **Accesses daily attendance** for worker selection
- **Verifies worker availability** for permitted work
- **Updates worker assignments** with permit responsibilities
- **Maintains competency records** for compliance

## Data Model Structure

```typescript
interface WorkPermit {
  // Basic Information
  id: string;
  permitNumber: string;
  permitType: PermitType;
  taskId: string; // Reference to approved task
  
  // Status and Workflow
  status: 'active' | 'suspended' | 'closed' | 'cancelled';
  
  // Task Reference (Read-Only)
  taskHazards: Hazard[];
  taskControlMeasures: ControlMeasure[];
  taskWeatherProtocols: WeatherHazardProtocol[];
  
  // Worker Assignments
  assignedWorkers: WorkerAssignment[];
  permitHolder: string;
  
  // Conditions and Requirements
  validFrom: Date;
  validUntil: Date;
  currentConditions: EnvironmentalConditions;
  additionalRequirements: string[];
  
  // Generation and Sign-off Chain
  generatedBy: string; // Site HSE who created the permit
  taskApprovedBy: string; // Admin HSE who approved the original task
  signedOffBy?: string; // Admin HSE sign-off for records (post-completion)
  
  // Monitoring and Compliance
  monitoringRequirements: MonitoringRequirement[];
  complianceChecks: ComplianceCheck[];
  
  // Closure
  closureReport?: PermitClosure;
}
```

## Compliance and Audit Features

### Documentation Requirements
- **Complete permit history** from request to closure
- **Worker competency verification** records
- **Environmental condition** monitoring logs
- **Compliance check** results and corrective actions
- **Incident and near-miss** reporting integration

### Regulatory Compliance
- **Industry standard alignment** (OSHA, local regulations)
- **Audit trail maintenance** for regulatory inspections
- **Competency management** for worker qualifications
- **Emergency response** documentation and testing

## System Benefits

1. **Risk Management**: Formal authorization process for high-risk work
2. **Competency Assurance**: Verified worker qualifications for specific tasks
3. **Regulatory Compliance**: Complete documentation for audit requirements
4. **Real-Time Safety**: Continuous monitoring and response capabilities
5. **Data Integration**: Seamless connection with task and attendance systems
6. **Continuous Improvement**: Lessons learned feedback to task management

## Conclusion

The Permit workflow serves as the final authorization layer in the task-centric safety management system. By referencing approved task risk assessments and integrating with attendance and toolbox systems, permits ensure that:

- **All safety information** flows from approved task assessments
- **Worker competencies** are verified before work authorization
- **Current conditions** are validated against approved parameters
- **Regulatory requirements** are met through proper documentation
- **Continuous monitoring** maintains safety throughout work execution

The permit system completes the integrated safety management approach, providing formal work authorization while maintaining the task-centric architecture and single source of truth for safety information.