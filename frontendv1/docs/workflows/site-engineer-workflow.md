# Site Engineer Workflow Documentation

## Overview

The Site Engineer is a key operational role responsible for identifying, planning, and requesting work activities on construction sites. Site Engineers serve as the bridge between project planning and execution, working closely with the HSE team to ensure all work is properly documented and approved before execution.

## Core Responsibilities

1. **Work Identification**: Identify tasks that need to be performed based on project schedules and site conditions
2. **Task Planning**: Plan work sequences, timing, and resource requirements
3. **Task Lifecycle Monitoring**: Track tasks through all stages from submission to completion
4. **Documentation Review**: Review HSE-added safety documentation and permit details
5. **Communication**: Coordinate with HSE team on task requirements and changes

## System Access and Capabilities

### Primary Functions
- **Task Creation and Submission** (Bulk mode)
- **Complete Task Lifecycle Visibility**
- **HSE Documentation Review** (hazards, control measures, requirements)
- **Permit Status and Details Viewing**
- **Overtime Request Submission**
- **Weather Information Access**

### Restricted Functions
- No access to reporting dashboards
- No team management capabilities
- No direct task approval authority
- No permit generation or approval
- No access to other engineers' submissions
- No safety observation reporting (handled through separate channels)

## Detailed Workflow Documentation

### 1. Task Creation and Bulk Submission

#### Purpose
Site Engineers create multiple related tasks in a single session and submit them as a batch to the Site HSE for review and safety documentation.

#### Workflow Steps

```mermaid
graph TD
    A[Site Engineer Identifies Work] --> B[Access Task Creation Interface]
    B --> C[Select Task Category]
    C --> D[Choose Task Template]
    D --> E[Fill Task Details]
    E --> F[Add to Task Chain]
    F --> G{Add Another Task?}
    G -->|Yes| C
    G -->|No| H[Review Task Chain]
    H --> I[Submit Bulk Tasks to Site HSE]
    I --> J[Receive Confirmation]
    J --> K[Monitor Task Status]
    
    style A fill:#e1f5fe
    style I fill:#e8f5e8
    style K fill:#f3e5f5
```

#### Task Creation Process

**Step 1: Work Identification**
- Site Engineer walks the site and identifies work that needs to be done
- Considers project schedules, dependencies, and site conditions
- Notes locations, urgency, and basic requirements

**Step 2: Bulk Task Creation Interface**
- Access mobile-optimized task creation interface
- View current weather conditions and site status
- Begin task creation workflow

**Step 3: Task Chain Building**
```typescript
interface TaskChain {
  id: string;
  createdBy: string;
  createdAt: Date;
  tasks: TaskSubmission[];
  status: 'draft' | 'submitted' | 'under-review';
  submissionNotes?: string;
}

interface TaskSubmission {
  category: TaskCategory;
  templateId: string;
  workDescription: string;
  location: string;
  plannedDate: Date;
  plannedTime: string;
  estimatedDuration: number;
  urgency: 'normal' | 'urgent';
  dependencies?: string[];
}
```

**Step 4: Individual Task Creation**
For each task in the chain:
- **Select Category**: Choose from predefined categories (excavation, electrical, construction, etc.)
- **Choose Template**: Select specific task template with pre-defined parameters
- **Describe Work**: Provide detailed description of what needs to be done
- **Set Location**: Specify exact location on site
- **Plan Timing**: Set planned date and start time
- **Add to Chain**: Add task to the submission batch

**Step 5: Chain Review and Submission**
- Review all tasks in the chain
- Add overall submission notes if needed
- Submit entire chain to Site HSE
- Receive confirmation and tracking information

#### Task Categories Available to Site Engineers

```yaml
Available Categories:
  - Excavation:
      - Standard excavation
      - Deep excavation
      - Utility excavation
      - Foundation excavation
  
  - Electrical Installation:
      - Panel installation
      - Conduit installation
      - Lighting installation
      - Power outlet installation
  
  - Construction:
      - Concrete pour
      - Steel erection
      - Formwork installation
      - Masonry work
  
  - Maintenance:
      - Equipment maintenance
      - Plumbing repair
      - HVAC maintenance
  
  - Safety:
      - Safety inspection
      - Equipment safety check
      - Site safety audit
```

### 2. Complete Task Lifecycle Visibility

#### Purpose
Site Engineers have full visibility into their submitted tasks throughout the entire lifecycle, from initial submission through completion, including all HSE-added documentation and permit details.

#### Task Lifecycle Stages

```mermaid
stateDiagram-v2
    [*] --> submitted: Engineer submits task chain
    submitted --> under_review: Site HSE begins review
    under_review --> needs_clarification: HSE requests more info
    under_review --> approved: HSE completes documentation
    needs_clarification --> under_review: Engineer provides clarification
    approved --> permit_pending: Next day - permit creation
    permit_pending --> permit_active: Permit issued and active
    permit_active --> in_progress: Work begins
    in_progress --> completed: Work finished
    completed --> [*]
    
    note right of approved: Engineer can view:\n- Added hazards\n- Control measures\n- Required PPE\n- Permit types needed
    note right of permit_active: Engineer can view:\n- Full permit details\n- Assigned workers\n- Safety requirements
```

#### Detailed Stage Information Available

**Stage 1: Submitted**
- Task chain received by Site HSE
- Original submission details
- Estimated review timeline

**Stage 2: Under Review**
- Site HSE is adding safety documentation
- Progress indicators showing completion percentage
- Estimated completion time

**Stage 3: Needs Clarification**
- HSE feedback and specific questions
- Required additional information
- Ability to respond with clarifications

**Stage 4: Approved - HSE Documentation Complete**
Engineer can now view all HSE-added information:
- **Hazards Identified**: Complete list of hazards added by HSE
- **Control Measures**: Safety controls and protective measures
- **Required PPE**: Personal protective equipment needed
- **Required Training**: Worker competency requirements
- **Permit Types**: Types of permits that will be needed
- **Weather Protocols**: Weather-specific safety measures

**Stage 5: Permit Pending**
- Task approved and ready for permit generation
- Permit creation in progress
- Expected permit issuance time

**Stage 6: Permit Active**
Engineer can view complete permit details:
- **Permit Information**: Permit number, type, validity period
- **Assigned Workers**: Who is authorized to perform the work
- **Permit Holder**: Primary responsible person
- **Safety Requirements**: All safety measures from the permit
- **Emergency Contacts**: Key contact information

**Stage 7: In Progress**
- Work actively being performed
- Real-time progress updates
- Any issues or delays reported

**Stage 8: Completed**
- Work finished and permit closed
- Final completion details
- Any lessons learned or feedback

#### Enhanced Monitoring Capabilities

```typescript
interface TaskLifecycleView {
  taskId: string;
  originalSubmission: TaskSubmission;
  currentStage: TaskStage;
  
  // HSE Documentation (visible after approval)
  hseDocumentation?: {
    hazards: Hazard[];
    controlMeasures: ControlMeasure[];
    requiredPPE: string[];
    requiredTraining: string[];
    permitTypes: string[];
    weatherProtocols: WeatherProtocol[];
    addedBy: string;
    addedAt: Date;
  };
  
  // Permit Information (visible when permit is active)
  permitDetails?: {
    permitNumber: string;
    permitType: string;
    status: 'active' | 'suspended' | 'closed';
    validFrom: Date;
    validUntil: Date;
    permitHolder: string;
    assignedWorkers: WorkerInfo[];
    emergencyContacts: ContactInfo[];
  };
  
  // Progress Tracking
  progressUpdates: ProgressUpdate[];
  estimatedCompletion?: Date;
  actualCompletion?: Date;
}
```

#### Information Display Features
- **Timeline View**: Visual timeline showing task progression
- **Documentation Comparison**: Before/after view of task details
- **Safety Information**: Detailed view of all HSE-added safety measures
- **Permit Dashboard**: Complete permit information in easy-to-read format
- **Progress Tracking**: Real-time updates on work progress

### 3. HSE Documentation and Permit Integration

#### Purpose
Site Engineers have integrated access to view both the HSE-added safety documentation and the resulting permit details for their tasks, providing complete visibility into how their original task submission evolved through the safety review process.

#### Integrated Documentation View

```typescript
interface IntegratedTaskView {
  // Original Submission
  originalTask: TaskSubmission;
  
  // HSE Enhancements (visible after approval)
  hseEnhancements: {
    hazardsAdded: Hazard[];
    controlMeasuresAdded: ControlMeasure[];
    requiredPPE: string[];
    requiredCertifications: string[];
    permitTypesRequired: string[];
    weatherProtocols: WeatherProtocol[];
    reviewNotes?: string;
  };
  
  // Permit Details (visible when permit is active)
  permitInformation: {
    permitNumber: string;
    permitType: string;
    status: 'pending' | 'active' | 'suspended' | 'closed';
    issuedAt: Date;
    validUntil: Date;
    permitHolder: string;
    assignedWorkers: WorkerAssignment[];
    safetyRequirements: string[];
    emergencyProcedures: string[];
    contactInformation: ContactInfo[];
  };
}
```

#### Documentation Evolution Tracking

```mermaid
graph TD
    A[Original Task Submission] --> B[HSE Safety Review]
    B --> C[HSE Adds Safety Documentation]
    C --> D[Task Approved with Full Documentation]
    D --> E[Permit Generated from Task Data]
    E --> F[Engineer Views Complete Information]
    
    C --> G[Hazards Identified]
    C --> H[Control Measures Added]
    C --> I[PPE Requirements Set]
    C --> J[Permit Types Determined]
    
    E --> K[Workers Assigned]
    E --> L[Permit Holder Designated]
    E --> M[Emergency Contacts Added]
    E --> N[Validity Period Set]
    
    style A fill:#e1f5fe
    style D fill:#e8f5e8
    style F fill:#f3e5f5
```

#### What Engineers Can View at Each Stage

**After HSE Approval:**
- **Hazards Added**: Complete list of hazards identified by HSE for their task
- **Control Measures**: Specific safety controls and protective measures
- **PPE Requirements**: Required personal protective equipment
- **Training Requirements**: Worker competency and certification needs
- **Permit Types**: Types of permits that will be generated
- **Weather Protocols**: Weather-specific safety procedures
- **Review Comments**: Any notes or feedback from HSE review

**After Permit Issuance:**
- **Permit Details**: Number, type, validity period, and status
- **Authorized Personnel**: Workers assigned to perform the work
- **Permit Holder**: Primary responsible person for the permit
- **Safety Briefing**: Key safety points and requirements
- **Emergency Information**: Emergency contacts and procedures
- **Work Restrictions**: Any limitations or special conditions
- **Monitoring Requirements**: Safety monitoring and check-in procedures

### 4. Overtime Request Submission

#### Purpose
Site Engineers can request overtime authorization for approved tasks when additional time is needed beyond the original estimate.

#### Overtime Request Workflow

```mermaid
graph TD
    A[Identify Need for Overtime] --> B[Access Overtime Request Interface]
    B --> C[Select Approved Task]
    C --> D[Specify Additional Hours Needed]
    D --> E[Provide Justification]
    E --> F[Submit Overtime Request]
    F --> G[Site HSE Reviews Request]
    G --> H{Approved?}
    H -->|Yes| I[Overtime Authorized]
    H -->|No| J[Request Denied with Reason]
    I --> K[Work Can Continue]
    J --> L[Engineer Notified]
    
    style A fill:#fff3e0
    style F fill:#e3f2fd
    style I fill:#e8f5e8
    style J fill:#ffebee
```

#### Overtime Request Process

**Step 1: Task Selection**
- View list of approved tasks assigned to the engineer
- Select specific task requiring overtime
- System displays current task status and original time allocation

**Step 2: Overtime Details**
```typescript
interface OvertimeRequest {
  taskId: string;
  requestedBy: string;
  additionalHours: number;
  justification: string;
  urgency: 'normal' | 'urgent';
  impactIfNotApproved: string;
  requestedDate: Date;
  status: 'pending' | 'approved' | 'denied';
}
```

**Step 3: Justification Requirements**
- **Reason for Overtime**: Detailed explanation of why additional time is needed
- **Impact Assessment**: What happens if overtime is not approved
- **Resource Requirements**: Additional workers, equipment, or materials needed
- **Safety Considerations**: Any safety implications of extended work

**Step 4: Submission and Tracking**
- Submit request to Site HSE for approval
- Receive confirmation and tracking number
- Monitor request status through dashboard
- Receive notification of approval/denial decision

#### Common Overtime Scenarios
- **Weather Delays**: Work took longer due to weather conditions
- **Complexity**: Task more complex than initially estimated
- **Dependencies**: Waiting for other work to complete
- **Equipment Issues**: Equipment problems causing delays
- **Quality Requirements**: Additional time needed for quality standards

### 5. Weather Information Access

#### Purpose
Site Engineers need current and forecasted weather information to plan work activities and understand potential impacts on their submitted tasks.

#### Weather Dashboard Features

```typescript
interface WeatherInfo {
  current: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    precipitation: number;
    visibility: number;
    conditions: string;
  };
  
  forecast: {
    date: Date;
    highTemp: number;
    lowTemp: number;
    precipitationChance: number;
    windSpeed: number;
    conditions: string;
    workImpact: 'none' | 'minor' | 'moderate' | 'severe';
  }[];
  
  alerts: WeatherAlert[];
}
```

#### Weather Impact Indicators
- **Green (No Impact)**: Normal work conditions
- **Yellow (Minor Impact)**: Some work types may be affected
- **Orange (Moderate Impact)**: Outdoor work may be limited
- **Red (Severe Impact)**: Most outdoor work should be postponed

#### Integration with Task Planning
- Weather conditions displayed during task creation
- Automatic alerts for tasks scheduled during poor weather
- Suggestions for indoor alternatives when weather is unsuitable
- Historical weather data for better planning



## System Interface Design

### Mobile-First Dashboard

#### Main Navigation
- **Create Tasks**: Primary action button for bulk task creation
- **My Tasks**: Complete lifecycle visibility and status tracking
- **Task Details**: View HSE-added documentation and permit information
- **Weather**: Current conditions and forecast
- **Overtime**: Request additional time for approved tasks
- **Profile**: Basic settings and preferences

#### Key Information Display
- **Today's Weather**: Current conditions affecting work
- **Task Summary**: Quick overview of submitted/approved tasks
- **Urgent Notifications**: Important updates requiring attention
- **Quick Actions**: Fast access to common functions

### Notification System

#### Notification Types
- **Task Status Updates**: Changes in task approval status and lifecycle progression
- **HSE Documentation Added**: When safety documentation is completed
- **Permit Issued**: When permits are generated and become active
- **Weather Alerts**: Severe weather affecting planned work
- **Overtime Decisions**: Approval/denial of overtime requests
- **System Updates**: Important system changes or maintenance

## Integration Points

### With Task Management System
- **Task Submission**: Creates tasks in 'requested' status
- **Status Updates**: Receives notifications of task progress
- **Task Details**: Views approved task information

### With Permit System
- **Permit Viewing**: Read-only access to permits for their tasks
- **Worker Information**: See who is assigned to their work
- **Safety Information**: View hazards and controls added by HSE

### With Weather System
- **Real-time Data**: Current weather conditions
- **Forecasting**: Multi-day weather predictions
- **Impact Assessment**: Weather impact on planned work



## Success Metrics

### Efficiency Metrics
- **Task Creation Time**: Average time to create and submit task chain
- **Submission Frequency**: Number of tasks submitted per engineer per day
- **Approval Rate**: Percentage of tasks approved without clarification requests

### Quality Metrics
- **Clarification Requests**: Frequency of HSE requests for additional information
- **Task Accuracy**: How well submitted tasks match actual work performed
- **Safety Compliance**: Adherence to safety requirements in task descriptions

### Engagement Metrics
- **System Usage**: Daily active users and session duration
- **Feature Adoption**: Usage of lifecycle tracking, documentation viewing, overtime requests
- **User Satisfaction**: Feedback on system usability and effectiveness

## Dummy Data Examples for Development

### Example 1: Task Chain Submission

```typescript
// Example of a task chain being created by Site Engineer
const exampleTaskChain: TaskChain = {
  id: "chain-2024-001",
  createdBy: "engineer-john-smith",
  createdAt: new Date("2024-01-15T14:30:00Z"),
  status: "submitted",
  submissionNotes: "Electrical work for office block. All work scheduled for tomorrow morning.",
  tasks: [
    {
      category: "electrical-installation",
      templateId: "elec-001",
      workDescription: "Install main electrical panel for office block. Connect main feed from transformer and install distribution breakers for office circuits.",
      location: "Office Block - First Floor",
      plannedDate: new Date("2024-01-16T08:00:00Z"),
      plannedTime: "08:00",
      estimatedDuration: 6,
      urgency: "normal",
      dependencies: []
    },
    {
      category: "electrical-installation", 
      templateId: "elec-002",
      workDescription: "Install conduit runs from main panel to office areas. Route conduits through ceiling space and install junction boxes.",
      location: "Office Block - First Floor",
      plannedDate: new Date("2024-01-16T10:00:00Z"),
      plannedTime: "10:00",
      estimatedDuration: 4,
      urgency: "normal",
      dependencies: ["chain-2024-001-task-1"]
    },
    {
      category: "electrical-installation",
      templateId: "elec-004", 
      workDescription: "Install power outlets in office areas. Install GFCI outlets in break room and storage areas.",
      location: "Office Block - First Floor",
      plannedDate: new Date("2024-01-16T14:00:00Z"),
      plannedTime: "14:00",
      estimatedDuration: 3,
      urgency: "normal",
      dependencies: ["chain-2024-001-task-2"]
    }
  ]
};
```

### Example 2: Task Lifecycle Progression

```typescript
// Example showing how a task evolves through the lifecycle
const taskLifecycleExample: TaskLifecycleView = {
  taskId: "task-2024-001",
  originalSubmission: {
    category: "electrical-installation",
    templateId: "elec-001",
    workDescription: "Install main electrical panel for office block. Connect main feed from transformer and install distribution breakers for office circuits.",
    location: "Office Block - First Floor",
    plannedDate: new Date("2024-01-16T08:00:00Z"),
    plannedTime: "08:00",
    estimatedDuration: 6,
    urgency: "normal"
  },
  currentStage: "permit_active",
  
  // HSE Documentation added after approval
  hseDocumentation: {
    hazards: [
      {
        id: "haz-001",
        description: "Electrical shock from live conductors",
        riskLevel: "high",
        likelihood: 3,
        severity: 5,
        riskScore: 15
      },
      {
        id: "haz-002", 
        description: "Arc flash during panel energization",
        riskLevel: "critical",
        likelihood: 2,
        severity: 5,
        riskScore: 10
      },
      {
        id: "haz-003",
        description: "Falls from ladder during overhead work",
        riskLevel: "medium",
        likelihood: 2,
        severity: 4,
        riskScore: 8
      }
    ],
    controlMeasures: [
      {
        id: "ctrl-001",
        description: "Lockout/Tagout (LOTO) procedures must be followed",
        type: "administrative",
        effectiveness: 5,
        implementationCost: "low",
        trainingRequired: true,
        equipmentRequired: ["LOTO locks", "LOTO tags", "Voltage tester"]
      },
      {
        id: "ctrl-002",
        description: "Arc flash rated PPE (Category 2 minimum)",
        type: "ppe", 
        effectiveness: 4,
        implementationCost: "medium",
        trainingRequired: true,
        equipmentRequired: ["Arc flash suit", "Face shield", "Insulated gloves"]
      },
      {
        id: "ctrl-003",
        description: "Use proper ladder with 3:1 ratio, maintain 3-point contact",
        type: "engineering",
        effectiveness: 4,
        implementationCost: "low", 
        trainingRequired: false,
        equipmentRequired: ["Extension ladder", "Ladder stabilizer"]
      }
    ],
    requiredPPE: [
      "Arc flash rated coveralls (Category 2)",
      "Arc flash face shield",
      "Insulated electrical gloves (Class 0)",
      "Safety glasses",
      "Hard hat",
      "Safety boots"
    ],
    requiredTraining: [
      "Electrical safety training",
      "LOTO procedures",
      "Arc flash awareness",
      "Ladder safety"
    ],
    permitTypes: ["electrical-work"],
    weatherProtocols: [
      {
        weatherCondition: "clear",
        riskLevel: "low",
        controlMeasures: [],
        stopWorkCriteria: [],
        alternativeActions: []
      },
      {
        weatherCondition: "light-rain",
        riskLevel: "high", 
        controlMeasures: [
          {
            id: "weather-001",
            description: "Ensure all electrical work areas are completely dry",
            type: "administrative"
          }
        ],
        stopWorkCriteria: ["Any moisture in electrical room", "Humidity >80%"],
        alternativeActions: ["Move to indoor work only", "Use dehumidifiers"]
      },
      {
        weatherCondition: "heavy-rain",
        riskLevel: "critical",
        controlMeasures: [],
        stopWorkCriteria: ["Any precipitation"],
        alternativeActions: ["Stop all electrical work", "Reschedule for dry conditions"]
      }
    ],
    addedBy: "hse-sarah-johnson",
    addedAt: new Date("2024-01-15T16:45:00Z")
  },
  
  // Permit Details when permit becomes active
  permitDetails: {
    permitNumber: "EWP-2024-001",
    permitType: "electrical-work",
    status: "active",
    validFrom: new Date("2024-01-16T07:00:00Z"),
    validUntil: new Date("2024-01-16T18:00:00Z"),
    permitHolder: "electrician-mike-wilson",
    assignedWorkers: [
      {
        workerId: "worker-001",
        workerName: "Mike Wilson",
        role: "permit-holder",
        hasRequiredTraining: true,
        hasRequiredCertifications: true,
        estimatedHours: 6
      },
      {
        workerId: "worker-002", 
        workerName: "David Chen",
        role: "worker",
        hasRequiredTraining: true,
        hasRequiredCertifications: true,
        estimatedHours: 6
      }
    ],
    emergencyContacts: [
      {
        name: "Site HSE Officer",
        phone: "+254-700-123-456",
        role: "emergency-coordinator"
      },
      {
        name: "Electrical Supervisor", 
        phone: "+254-700-123-457",
        role: "technical-support"
      }
    ]
  },
  
  progressUpdates: [
    {
      timestamp: new Date("2024-01-15T14:30:00Z"),
      status: "submitted",
      description: "Task chain submitted by Site Engineer",
      updatedBy: "engineer-john-smith"
    },
    {
      timestamp: new Date("2024-01-15T15:00:00Z"),
      status: "under_review", 
      description: "HSE review started",
      updatedBy: "hse-sarah-johnson"
    },
    {
      timestamp: new Date("2024-01-15T16:45:00Z"),
      status: "approved",
      description: "HSE documentation complete - task approved",
      updatedBy: "hse-sarah-johnson"
    },
    {
      timestamp: new Date("2024-01-16T07:00:00Z"),
      status: "permit_active",
      description: "Electrical work permit issued and active",
      updatedBy: "system"
    }
  ],
  estimatedCompletion: new Date("2024-01-16T14:00:00Z")
};
```

### Example 3: Overtime Request

```typescript
// Example overtime request for a task
const overtimeRequestExample: OvertimeRequest = {
  taskId: "task-2024-001",
  requestedBy: "engineer-john-smith",
  additionalHours: 2,
  justification: "Panel installation is taking longer than expected due to complex wiring configuration. The existing conduit layout requires additional modifications to accommodate the new panel connections. Need 2 additional hours to complete the work safely without rushing.",
  urgency: "normal",
  impactIfNotApproved: "Work will remain incomplete, electrical room will be left in unsafe state with exposed conductors. Will need to reschedule for tomorrow which will delay dependent tasks (conduit installation and outlet work).",
  requestedDate: new Date("2024-01-16T12:30:00Z"),
  status: "pending"
};
```

### Example 4: Weather Information Display

```typescript
// Example weather data for task planning
const weatherExample: WeatherInfo = {
  current: {
    temperature: 24,
    humidity: 65,
    windSpeed: 12,
    precipitation: 0,
    visibility: 10,
    conditions: "Partly Cloudy"
  },
  forecast: [
    {
      date: new Date("2024-01-16T00:00:00Z"),
      highTemp: 26,
      lowTemp: 18,
      precipitationChance: 20,
      windSpeed: 15,
      conditions: "Partly Cloudy",
      workImpact: "none"
    },
    {
      date: new Date("2024-01-17T00:00:00Z"), 
      highTemp: 23,
      lowTemp: 16,
      precipitationChance: 80,
      windSpeed: 25,
      conditions: "Heavy Rain",
      workImpact: "severe"
    },
    {
      date: new Date("2024-01-18T00:00:00Z"),
      highTemp: 25,
      lowTemp: 17, 
      precipitationChance: 10,
      windSpeed: 10,
      conditions: "Clear",
      workImpact: "none"
    }
  ],
  alerts: [
    {
      id: "alert-001",
      type: "heavy-rain",
      severity: "warning",
      message: "Heavy rain expected tomorrow (Jan 17). Outdoor electrical work should be postponed.",
      validFrom: new Date("2024-01-17T06:00:00Z"),
      validUntil: new Date("2024-01-17T18:00:00Z"),
      affectedWorkTypes: ["electrical-installation", "excavation"]
    }
  ]
};
```

### Example 5: Task Dashboard View

```typescript
// Example of what Site Engineer sees on their dashboard
const engineerDashboardExample = {
  engineerId: "engineer-john-smith",
  engineerName: "John Smith",
  siteId: "site-nairobi-001",
  siteName: "Nairobi Office Building",
  
  taskSummary: {
    totalSubmitted: 15,
    underReview: 3,
    approved: 8,
    permitActive: 2,
    inProgress: 1,
    completed: 1
  },
  
  recentTasks: [
    {
      taskId: "task-2024-001",
      name: "Electrical Panel Installation",
      status: "permit_active",
      location: "Office Block - First Floor",
      plannedDate: new Date("2024-01-16T08:00:00Z"),
      permitNumber: "EWP-2024-001",
      lastUpdated: new Date("2024-01-16T07:00:00Z")
    },
    {
      taskId: "task-2024-002", 
      name: "Conduit Installation",
      status: "approved",
      location: "Office Block - First Floor",
      plannedDate: new Date("2024-01-16T10:00:00Z"),
      lastUpdated: new Date("2024-01-15T16:45:00Z")
    },
    {
      taskId: "task-2024-003",
      name: "Foundation Excavation",
      status: "under_review",
      location: "Main Building - Ground Floor", 
      plannedDate: new Date("2024-01-17T08:00:00Z"),
      lastUpdated: new Date("2024-01-15T15:30:00Z")
    }
  ],
  
  notifications: [
    {
      id: "notif-001",
      type: "permit_issued",
      message: "Electrical work permit EWP-2024-001 has been issued for your task",
      taskId: "task-2024-001",
      timestamp: new Date("2024-01-16T07:00:00Z"),
      isRead: false
    },
    {
      id: "notif-002",
      type: "weather_alert", 
      message: "Heavy rain forecast for tomorrow may affect outdoor work",
      timestamp: new Date("2024-01-15T18:00:00Z"),
      isRead: false
    }
  ],
  
  currentWeather: {
    temperature: 24,
    conditions: "Partly Cloudy",
    workImpact: "none"
  }
};
```

### Example 6: Mobile Interface Data Structure

```typescript
// Example of data structure for mobile interface
const mobileInterfaceExample = {
  // Quick Actions
  quickActions: [
    {
      id: "create-task",
      title: "Create New Task",
      icon: "plus",
      color: "blue",
      route: "/create-task"
    },
    {
      id: "view-permits",
      title: "My Permits", 
      icon: "document",
      color: "green",
      badge: 2, // Number of active permits
      route: "/permits"
    },
    {
      id: "request-overtime",
      title: "Request Overtime",
      icon: "clock",
      color: "orange", 
      route: "/overtime"
    }
  ],
  
  // Task Categories for Creation
  taskCategories: [
    {
      id: "electrical-installation",
      name: "Electrical",
      description: "Electrical installation and wiring",
      icon: "zap",
      color: "yellow",
      templates: [
        {
          id: "elec-001",
          name: "Panel Installation",
          estimatedDuration: 6,
          riskLevel: "high"
        },
        {
          id: "elec-002", 
          name: "Conduit Installation",
          estimatedDuration: 4,
          riskLevel: "medium"
        }
      ]
    },
    {
      id: "construction",
      name: "Construction", 
      description: "General construction work",
      icon: "hammer",
      color: "blue",
      templates: [
        {
          id: "const-001",
          name: "Concrete Pour",
          estimatedDuration: 8,
          riskLevel: "medium"
        }
      ]
    }
  ],
  
  // Common Locations (Simple dropdown list)
  commonLocations: [
    "Main Building - Ground Floor",
    "Main Building - First Floor", 
    "Office Block - Ground Floor",
    "Office Block - First Floor",
    "Electrical Room",
    "Generator Room",
    "Parking Area",
    "Main Entrance",
    "Site Compound"
  ]
};
```

## Implementation Notes for Developers

### Key Data Relationships
1. **TaskChain** → contains multiple **TaskSubmission** objects
2. **TaskSubmission** → evolves into **TaskLifecycleView** with HSE additions
3. **TaskLifecycleView** → includes **HSE Documentation** and **Permit Details**
4. **OvertimeRequest** → references existing approved tasks
5. **Weather** → affects task planning and execution decisions

### Mobile-First Considerations
- Touch-friendly interfaces with large tap targets
- Simple dropdown/selection interfaces for locations
- Basic form validation and error handling
- Clear visual feedback for user actions

### API Integration Points
- Task submission endpoint for bulk task chains
- Basic status polling for task updates
- Simple weather data display
- Permit viewing endpoints
- Basic notification system

## Conclusion

The Site Engineer role is crucial for effective project execution and safety management. By providing streamlined task creation and complete lifecycle visibility, the system enables Site Engineers to:

- **Efficiently Plan Work**: Bulk task creation with weather awareness
- **Track Complete Lifecycle**: Full visibility from submission through completion
- **Understand Safety Requirements**: View all HSE-added hazards, controls, and requirements
- **Monitor Permit Status**: See permit details and authorized workers
- **Manage Changes**: Overtime requests and status monitoring
- **Stay Informed**: Weather updates and comprehensive task notifications

The mobile-first design ensures Site Engineers can effectively use the system while moving around construction sites, maintaining complete visibility into their work requests and understanding how the HSE team has enhanced their submissions with proper safety documentation and permit requirements.