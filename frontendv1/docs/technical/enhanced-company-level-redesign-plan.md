# Enhanced Company-Level Architecture Redesign Plan

## Executive Summary

This document outlines a comprehensive redesign plan to enhance the company-level architecture of the workforce management application. The plan addresses five key areas: centralized worker management, equipment management, PPE management, training strategy, and document template management. The redesign ensures proper centralization while maintaining operational efficiency at the site level.

## Current State Analysis

### Strengths of Current Implementation
1. **Solid Foundation**: Good multi-tenant architecture with company/site separation
2. **Equipment Management**: Well-designed equipment ownership model with site assignment
3. **Worker Database**: Basic company-level worker database structure exists
4. **Training Framework**: Training programs and compliance tracking foundation
5. **Document System**: Basic document management and form template structure

### Identified Gaps
1. **Worker Management**: Incomplete centralization, site-level duplication issues
2. **PPE Management**: Lacks centralized procurement and assignment tracking
3. **Training Strategy**: Unclear company vs site level responsibilities
4. **Document Management**: Missing centralized repository and version control
5. **Data Synchronization**: Inconsistent data flow between company and site levels

## Redesign Requirements Analysis

### 1. Company-Level Worker Management (Master Database)

**Current State**: Partial implementation with some site-level duplication
**Target State**: Complete centralization with site assignment tracking

**Key Requirements**:
- Single source of truth for all worker data at company level
- Site assignment records (not duplicate worker records)
- Seamless worker import/creation from site interfaces
- Real-time synchronization between company and site levels

### 2. Company-Level Equipment Management

**Current State**: Well-designed architecture already exists
**Target State**: Enhance existing implementation with better tracking

**Key Requirements**:
- Maintain existing centralized ownership model
- Enhance equipment transfer workflows
- Improve compliance and maintenance tracking
- Better integration with site operations

### 3. Company-Level PPE Management

**Current State**: Basic PPE catalog, limited centralized management
**Target State**: Full centralized procurement with worker assignment tracking

**Key Requirements**:
- Centralized PPE procurement and inventory
- Individual worker PPE assignment tracking
- Site-level compliance monitoring
- Lifecycle management for PPE items

### 4. Training Management Strategy

**Current State**: Mixed company/site level implementation
**Target State**: Clear separation of responsibilities with compliance enforcement

**Key Requirements**:
- Company-level training program definition
- Site-level training execution and tracking
- Trade-based automatic requirement generation
- Compliance enforcement before site assignment

### 5. Company Document Template Management

**Current State**: Basic form templates, limited centralized repository
**Target State**: Comprehensive document management system

**Key Requirements**:
- Centralized document repository ("company Google Drive")
- Version control and approval workflows
- Category-based organization and filtering
- Site access to approved templates

## Detailed Implementation Plan

### Phase 1: Data Architecture Redesign (Weeks 1-3)

#### 1.1 Enhanced Worker Management Schema

**New/Modified Tables**:

```sql
-- Enhanced Workers Table (Company Level)
company_workers {
  id: UUID (Primary Key)
  company_id: UUID (Foreign Key)
  employee_number: String (Unique per company)
  personal_info: JSON (name, contact, emergency contacts)
  employment_info: JSON (hire_date, status, position)
  trade_assignments: JSON (primary/secondary trades)
  compliance_status: Enum (compliant, pending_training, non_compliant)
  created_at: Timestamp
  updated_at: Timestamp
  last_sync_at: Timestamp
}

-- Site Worker Assignments (Replaces site-level worker records)
site_worker_assignments {
  id: UUID (Primary Key)
  company_worker_id: UUID (Foreign Key)
  site_id: UUID (Foreign Key)
  assignment_date: Date
  assignment_status: Enum (assigned, active, suspended, completed)
  site_role: String
  assigned_by: UUID (Foreign Key to users)
  completion_date: Date (nullable)
  assignment_notes: Text
  created_at: Timestamp
  updated_at: Timestamp
}

-- Worker Training Compliance (Company Level)
worker_training_compliance {
  id: UUID (Primary Key)
  company_worker_id: UUID (Foreign Key)
  training_program_id: UUID (Foreign Key)
  compliance_status: Enum (required, in_progress, completed, expired)
  completion_date: Date (nullable)
  expiry_date: Date (nullable)
  certificate_url: String (nullable)
  last_assessment_date: Date
  next_assessment_due: Date
  blocking_assignment: Boolean
  created_at: Timestamp
  updated_at: Timestamp
}
```

#### 1.2 Enhanced PPE Management Schema

**New Tables**:

```sql
-- Company PPE Inventory (Master Level)
company_ppe_inventory {
  id: UUID (Primary Key)
  company_id: UUID (Foreign Key)
  ppe_master_id: UUID (Foreign Key)
  total_quantity: Integer
  available_quantity: Integer
  allocated_quantity: Integer
  reorder_level: Integer
  last_procurement_date: Date
  next_procurement_due: Date
  total_cost: Decimal
  created_at: Timestamp
  updated_at: Timestamp
}

-- PPE Worker Assignments (Individual Tracking)
ppe_worker_assignments {
  id: UUID (Primary Key)
  company_worker_id: UUID (Foreign Key)
  ppe_master_id: UUID (Foreign Key)
  site_id: UUID (Foreign Key)
  quantity: Integer
  assigned_date: Date
  expected_return_date: Date
  actual_return_date: Date (nullable)
  condition_at_assignment: Enum (new, good, acceptable)
  condition_at_return: Enum (good, worn, damaged, lost)
  assignment_status: Enum (assigned, active, returned, lost, damaged)
  batch_number: String
  serial_numbers: JSON (for serialized items)
  assigned_by: UUID (Foreign Key to users)
  returned_to: UUID (Foreign Key to users, nullable)
  assignment_notes: Text
  return_notes: Text
  created_at: Timestamp
  updated_at: Timestamp
}

-- Site PPE Stock (Site Level Inventory)
site_ppe_stock {
  id: UUID (Primary Key)
  site_id: UUID (Foreign Key)
  ppe_master_id: UUID (Foreign Key)
  quantity_on_hand: Integer
  quantity_available: Integer
  quantity_assigned: Integer
  last_stock_update: Date
  batch_numbers: JSON
  expiry_dates: JSON
  unit_cost: Decimal
  created_at: Timestamp
  updated_at: Timestamp
}
```

#### 1.3 Enhanced Document Management Schema

**New Tables**:

```sql
-- Company Document Repository
company_documents {
  id: UUID (Primary Key)
  company_id: UUID (Foreign Key)
  document_name: String
  document_type: Enum (template, policy, procedure, form, rams, manual)
  category_id: UUID (Foreign Key)
  description: Text
  version: String
  file_url: String
  file_size: Integer
  mime_type: String
  approval_status: Enum (draft, pending_approval, approved, archived)
  approved_by: UUID (Foreign Key to users, nullable)
  approved_at: Timestamp (nullable)
  effective_date: Date
  expiry_date: Date (nullable)
  tags: JSON
  access_level: Enum (public, internal, restricted)
  download_count: Integer
  created_by: UUID (Foreign Key to users)
  created_at: Timestamp
  updated_at: Timestamp
}

-- Document Categories (Hierarchical)
document_categories {
  id: UUID (Primary Key)
  company_id: UUID (Foreign Key)
  name: String
  description: Text
  parent_category_id: UUID (Foreign Key, nullable)
  category_path: String (for hierarchical queries)
  sort_order: Integer
  is_active: Boolean
  created_at: Timestamp
  updated_at: Timestamp
}

-- Document Access Log
document_access_log {
  id: UUID (Primary Key)
  document_id: UUID (Foreign Key)
  accessed_by: UUID (Foreign Key to users)
  site_id: UUID (Foreign Key, nullable)
  access_type: Enum (view, download, edit)
  access_timestamp: Timestamp
  ip_address: String
  user_agent: String
}
```

### Phase 2: Core System Implementation (Weeks 4-8)

#### 2.1 Centralized Worker Management System

**Implementation Components**:

1. **Company Worker Master Service**
   - Location: `frontendv1/src/services/CompanyWorkerService.ts`
   - Functions: CRUD operations, compliance checking, site assignment management

2. **Enhanced Worker Creation Flow**
   - Update: `frontendv1/src/components/workers/CreateWorkerForm.tsx`
   - Always saves to company database first
   - Automatic site assignment if created from site context
   - Real-time compliance checking

3. **Site Worker Import Interface**
   - New: `frontendv1/src/components/workers/ImportWorkerToSite.tsx`
   - Search and select from company worker database
   - Compliance verification before assignment
   - Bulk import capabilities

4. **Worker Assignment Management**
   - New: `frontendv1/src/components/workers/WorkerAssignmentManager.tsx`
   - Manage worker assignments across sites
   - Assignment history and tracking
   - Performance metrics per site

#### 2.2 Enhanced PPE Management System

**Implementation Components**:

1. **Company PPE Procurement Module**
   - New: `frontendv1/src/components/ppe/PPEProcurementManager.tsx`
   - Centralized purchasing and inventory management
   - Supplier management and cost tracking
   - Automatic reorder level monitoring

2. **PPE Worker Assignment Tracker**
   - New: `frontendv1/src/components/ppe/PPEWorkerAssignmentTracker.tsx`
   - Individual worker PPE assignment tracking
   - Lifecycle management (assignment to return)
   - Condition monitoring and replacement alerts

3. **Site PPE Distribution System**
   - New: `frontendv1/src/components/ppe/SitePPEDistribution.tsx`
   - Request PPE from company inventory
   - Track site stock levels
   - Worker assignment at site level

#### 2.3 Training Management Strategy Implementation

**Recommended Strategy**: **Hybrid Approach**
- **Company Level**: Training program definition, compliance standards, certification tracking
- **Site Level**: Training execution, attendance tracking, practical assessments

**Implementation Components**:

1. **Company Training Program Manager**
   - Enhanced: `frontendv1/src/components/training/TrainingProgramMaster.tsx`
   - Define training programs and requirements
   - Set compliance standards and renewal periods
   - Manage training materials and resources

2. **Trade-Based Training Requirements Engine**
   - New: `frontendv1/src/services/TrainingRequirementsEngine.ts`
   - Automatic training requirement generation based on worker trades
   - Compliance checking before site assignment
   - Renewal and expiry tracking

3. **Site Training Execution Module**
   - Enhanced: `frontendv1/src/components/training/SiteTrainingManager.tsx`
   - Schedule and conduct training sessions
   - Record attendance and completion
   - Upload certificates and assessment results

#### 2.4 Document Repository System

**Implementation Components**:

1. **Company Document Repository**
   - New: `frontendv1/src/components/documents/CompanyDocumentRepository.tsx`
   - Centralized document storage and management
   - Category-based organization and filtering
   - Version control and approval workflows

2. **Document Template Manager**
   - Enhanced: `frontendv1/src/components/data/FormTemplates.tsx`
   - Create and manage document templates
   - Approval workflow for template changes
   - Distribution to sites

3. **Site Document Access Interface**
   - New: `frontendv1/src/components/documents/SiteDocumentAccess.tsx`
   - Browse and download approved templates
   - Search and filter capabilities
   - Access logging and tracking

### Phase 3: Integration and Synchronization (Weeks 9-11)

#### 3.1 Data Synchronization Framework

**Implementation Components**:

1. **Real-Time Sync Service**
   - New: `frontendv1/src/services/DataSyncService.ts`
   - Real-time synchronization between company and site levels
   - Conflict resolution and data integrity checks
   - Offline capability with sync on reconnection

2. **Company-Site Data Bridge**
   - New: `frontendv1/src/services/CompanySiteDataBridge.ts`
   - Manages data flow between company and site systems
   - Ensures single source of truth maintenance
   - Handles bulk operations and migrations

#### 3.2 Enhanced Navigation and Context Management

**Implementation Components**:

1. **Enhanced Site Context Hook**
   - Update: `frontendv1/src/hooks/useSiteContext.ts`
   - Better company/site context switching
   - Data prefetching and caching
   - Permission-based feature access

2. **Company-Level Navigation Enhancement**
   - Update: `frontendv1/src/components/layout/sidebar/SidebarProvider.tsx`
   - New menu items for enhanced company features
   - Quick access to worker/equipment/PPE management
   - Cross-site visibility features

### Phase 4: User Interface and Experience (Weeks 12-14)

#### 4.1 Enhanced Company Dashboard

**Implementation Components**:

1. **Advanced Company Dashboard**
   - Enhanced: `frontendv1/src/pages/Dashboard.tsx`
   - Real-time worker assignment status across sites
   - Equipment utilization and transfer tracking
   - PPE inventory and compliance monitoring
   - Training compliance dashboard

2. **Cross-Site Analytics**
   - New: `frontendv1/src/components/analytics/CrossSiteAnalytics.tsx`
   - Worker productivity across sites
   - Equipment utilization optimization
   - PPE cost analysis and trends
   - Training effectiveness metrics

#### 4.2 Mobile Application Enhancements

**Implementation Components**:

1. **Mobile Worker Management**
   - Enhanced: `site-engineer-mobile/src/components/workers/`
   - Access to company worker database
   - Site assignment capabilities
   - Real-time compliance checking

2. **Mobile PPE Management**
   - New: `site-engineer-mobile/src/components/ppe/`
   - PPE assignment and return workflows
   - Condition assessment and reporting
   - Stock level monitoring

### Phase 5: Testing and Deployment (Weeks 15-16)

#### 5.1 Comprehensive Testing Strategy

1. **Unit Testing**: All new services and components
2. **Integration Testing**: Company-site data synchronization
3. **User Acceptance Testing**: End-to-end workflows
4. **Performance Testing**: Large-scale data operations
5. **Security Testing**: Data access and permissions

#### 5.2 Migration Strategy

1. **Data Migration**: Existing worker/equipment/PPE data to new schema
2. **Feature Rollout**: Phased deployment with feature flags
3. **User Training**: Training materials and documentation
4. **Support Plan**: Post-deployment support and monitoring

## Implementation Priorities

### High Priority (Must Have)
1. **Centralized Worker Management**: Core business requirement
2. **PPE Worker Assignment Tracking**: Safety compliance critical
3. **Training Compliance Enforcement**: Regulatory requirement
4. **Data Synchronization Framework**: System integrity essential

### Medium Priority (Should Have)
1. **Enhanced Equipment Transfer Workflows**: Operational efficiency
2. **Document Repository System**: Organizational efficiency
3. **Cross-Site Analytics**: Business intelligence
4. **Mobile Application Enhancements**: User experience

### Low Priority (Nice to Have)
1. **Advanced Reporting Features**: Business insights
2. **Predictive Analytics**: Future optimization
3. **Third-Party Integrations**: Extended functionality
4. **Advanced Workflow Automation**: Process optimization

## Risk Assessment and Mitigation

### Technical Risks
1. **Data Migration Complexity**: Mitigate with thorough testing and rollback plans
2. **Performance Impact**: Mitigate with caching and optimization strategies
3. **Integration Challenges**: Mitigate with comprehensive API testing

### Business Risks
1. **User Adoption**: Mitigate with training and change management
2. **Operational Disruption**: Mitigate with phased rollout and parallel systems
3. **Compliance Issues**: Mitigate with regulatory review and validation

## Success Metrics

### Technical Metrics
- **Data Consistency**: 99.9% synchronization accuracy
- **System Performance**: <2 second response times
- **Uptime**: 99.5% system availability

### Business Metrics
- **Worker Assignment Efficiency**: 50% reduction in assignment time
- **PPE Compliance**: 95% worker compliance rate
- **Training Completion**: 90% on-time completion rate
- **Document Access**: 80% reduction in document retrieval time

## API Endpoints Design

### Company Worker Management APIs
```http
# Company Worker CRUD
GET /api/company/workers
POST /api/company/workers
GET /api/company/workers/{workerId}
PUT /api/company/workers/{workerId}
DELETE /api/company/workers/{workerId}

# Site Assignment Management
GET /api/company/workers/{workerId}/assignments
POST /api/company/workers/{workerId}/assignments
PUT /api/company/workers/{workerId}/assignments/{assignmentId}
DELETE /api/company/workers/{workerId}/assignments/{assignmentId}

# Worker Import to Site
GET /api/sites/{siteId}/workers/available
POST /api/sites/{siteId}/workers/import
GET /api/sites/{siteId}/workers/assignments
```

### Company PPE Management APIs
```http
# Company PPE Inventory
GET /api/company/ppe/inventory
POST /api/company/ppe/procurement
GET /api/company/ppe/inventory/{ppeId}
PUT /api/company/ppe/inventory/{ppeId}

# PPE Worker Assignments
GET /api/company/ppe/assignments
POST /api/company/ppe/assignments
GET /api/company/workers/{workerId}/ppe
POST /api/company/workers/{workerId}/ppe/assign
PUT /api/company/workers/{workerId}/ppe/{assignmentId}/return

# Site PPE Distribution
POST /api/sites/{siteId}/ppe/request
GET /api/sites/{siteId}/ppe/stock
POST /api/sites/{siteId}/ppe/distribute
```

### Company Document Management APIs
```http
# Document Repository
GET /api/company/documents
POST /api/company/documents
GET /api/company/documents/{documentId}
PUT /api/company/documents/{documentId}
DELETE /api/company/documents/{documentId}

# Document Categories
GET /api/company/documents/categories
POST /api/company/documents/categories
PUT /api/company/documents/categories/{categoryId}

# Site Document Access
GET /api/sites/{siteId}/documents/available
GET /api/sites/{siteId}/documents/{documentId}/download
POST /api/sites/{siteId}/documents/{documentId}/access-log
```

### Training Management APIs
```http
# Company Training Programs
GET /api/company/training/programs
POST /api/company/training/programs
GET /api/company/training/programs/{programId}
PUT /api/company/training/programs/{programId}

# Worker Training Compliance
GET /api/company/workers/{workerId}/training/compliance
POST /api/company/workers/{workerId}/training/complete
GET /api/company/training/compliance/dashboard

# Site Training Execution
GET /api/sites/{siteId}/training/sessions
POST /api/sites/{siteId}/training/sessions
PUT /api/sites/{siteId}/training/sessions/{sessionId}/complete
```

## Data Migration Strategy

### Phase 1: Schema Migration
1. **Create New Tables**: Deploy new schema alongside existing tables
2. **Data Validation**: Ensure data integrity and relationships
3. **Index Creation**: Optimize for performance
4. **Backup Strategy**: Full backup before migration

### Phase 2: Data Migration
1. **Worker Data Migration**:
   - Extract existing worker records from site databases
   - Consolidate into company master database
   - Create site assignment records
   - Validate data consistency

2. **PPE Data Migration**:
   - Migrate PPE catalog to company level
   - Create company inventory records
   - Migrate site stock data
   - Create worker assignment records for active PPE

3. **Document Migration**:
   - Consolidate documents from various sources
   - Create category structure
   - Migrate metadata and access permissions
   - Update file references

### Phase 3: System Cutover
1. **Parallel Operation**: Run old and new systems in parallel
2. **Data Synchronization**: Keep systems in sync during transition
3. **User Testing**: Validate functionality with key users
4. **Gradual Cutover**: Phase out old system gradually

## Change Management Plan

### User Training Program
1. **Company Administrators**: Advanced training on new centralized features
2. **Site Managers**: Training on new worker import and PPE management
3. **Site Engineers**: Training on mobile app enhancements
4. **HSE Managers**: Training on document repository and compliance tracking

### Communication Strategy
1. **Executive Briefing**: High-level overview of changes and benefits
2. **Manager Training**: Detailed training for middle management
3. **User Guides**: Comprehensive documentation for end users
4. **Support Channels**: Dedicated support during transition

### Rollback Plan
1. **System Rollback**: Ability to revert to previous system if needed
2. **Data Rollback**: Restore from backup if data issues occur
3. **Feature Rollback**: Disable new features if problems arise
4. **Communication Plan**: Clear communication if rollback is needed

## Conclusion

This redesign plan provides a comprehensive roadmap for enhancing the company-level architecture while maintaining operational efficiency. The phased approach ensures minimal disruption while delivering significant improvements in centralization, compliance, and operational visibility.

The plan addresses all five key requirement areas with specific implementation details, timelines, and success metrics. The hybrid approach for training management and the enhanced data synchronization framework ensure that the system maintains both centralized control and operational flexibility.

**Key Benefits**:
- **Single Source of Truth**: Centralized worker, equipment, and PPE management
- **Improved Compliance**: Better tracking and enforcement of safety requirements
- **Operational Efficiency**: Streamlined workflows and reduced duplication
- **Better Visibility**: Cross-site analytics and reporting capabilities
- **Scalability**: Architecture supports company growth and expansion

**Next Steps**: Review and approve this plan, then proceed with Phase 1 implementation focusing on the data architecture redesign.
