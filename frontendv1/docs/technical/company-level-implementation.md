# Company Level Implementation Documentation

## Overview

This document outlines the current implementation of company-level functionality in the workforce management application. Company-level features provide centralized management, master data control, and cross-site visibility for construction companies managing multiple sites and projects.

## Architecture Overview

The application follows a **tenant-based architecture** where:
- **Company** = **Tenant** (the top-level organizational unit)
- Companies own and manage master data
- Sites are projects/locations that belong to a company
- Resources (workers, equipment, data) are owned at company level and assigned to sites

## Core Company-Level Features

### 1. Company Dashboard (`/`)

**Location**: `frontendv1/src/pages/Dashboard.tsx`

**Key Metrics Displayed**:
- Total Active Sites (8)
- Total Workforce (245)
- Company Safety Score (92%)
- Active Permits across all sites
- Equipment utilization
- Cross-site performance indicators

**Functionality**:
- Aggregated KPIs across all company sites
- Site health status monitoring (green/amber/red)
- Quick navigation to individual site dashboards
- Company-wide alerts and notifications

### 2. Master Data Management (`/data`)

**Location**: `frontendv1/src/pages/DataPage.tsx`

The Data page serves as the central hub for managing all master data that flows down to sites:

#### 2.1 Training Programs (`/data#training-programs`)
- **Component**: `frontendv1/src/components/data/TrainingPrograms.tsx`
- **Purpose**: Define training modules that apply across all sites
- **Features**:
  - Training program creation and management
  - Validity period configuration (in months)
  - Trade association mapping
  - Mandatory vs optional training designation
  - Training materials management (documents, videos, presentations)

#### 2.2 PPE Catalog (`/data#ppe-catalog`)
- **Component**: `frontendv1/src/components/data/PPECatalog.tsx`
- **Purpose**: Master catalog of Personal Protective Equipment
- **Features**:
  - PPE item definitions with specifications
  - Safety standards compliance tracking
  - Supplier management
  - Cost and reorder level management
  - Category-based organization

#### 2.3 Form Templates (`/data#form-templates`)
- **Component**: `frontendv1/src/components/data/FormTemplates.tsx`
- **Purpose**: Standardized forms used across all sites
- **Categories**:
  - Safety forms (incident reports, inspections)
  - Equipment forms (maintenance logs, checklists)
  - Progress forms (daily logs, reports)
  - Quality forms (QC inspections, compliance checks)

#### 2.4 Permit Types (`/data#permit-types`)
- **Component**: `frontendv1/src/components/data/PermitTypes.tsx`
- **Purpose**: Define work permit types and requirements
- **Features**:
  - Permit type definitions
  - Required training/certification mapping
  - Risk level classification
  - Default validity periods
  - Template configuration

#### 2.5 Incident Classifications (`/data#incident-types`)
- **Component**: `frontendv1/src/components/data/IncidentTypes.tsx`
- **Purpose**: Standardized incident categorization
- **Features**:
  - Incident type definitions
  - Severity level mapping
  - Investigation workflow templates
  - CAPA (Corrective and Preventive Actions) templates

#### 2.6 Trades & Skills (`/data#trades-skills`)
- **Component**: `frontendv1/src/components/data/TradesSkills.tsx`
- **Purpose**: Define worker trades and skill classifications
- **Features**:
  - Trade definitions with descriptions
  - Required certification mapping
  - Skill level classifications
  - Training requirement associations

### 3. Company Equipment Management

**Design Document**: `frontendv1/docs/dev/company-equipment-management-system.md`

#### 3.1 Equipment Ownership Model
- **Company-Level Ownership**: All equipment is owned by the company
- **Site Assignment**: Equipment is assigned to sites as needed
- **Transfer Capability**: Equipment can be moved between sites
- **Centralized Compliance**: Safety and compliance managed at company level

#### 3.2 Key Features
- **Equipment Registry**: Master database of all company equipment
- **Multi-Site Visibility**: Track equipment location across all sites
- **Compliance Management**: Centralized safety and regulatory compliance
- **Transfer Workflows**: Structured site-to-site equipment transfers
- **Performance Analytics**: Equipment utilization and performance metrics

### 4. Company Worker Management

**Design Document**: `frontendv1/docs/dev/worker-management-system-design.md`

#### 4.1 Worker Database Structure
- **Master Worker Database**: Company-level worker records
- **Multi-Site Assignment**: Workers can be assigned to multiple sites
- **Trade-Based Training**: Automatic training requirements based on trades
- **Compliance Tracking**: Centralized training and certification management

#### 4.2 Key Components
- **Worker Creation**: `frontendv1/src/components/workers/CreateWorkerForm.tsx`
- **Worker Updates**: `frontendv1/src/components/workers/UpdateWorkerForm.tsx`
- **Training Management**: Automated training requirement generation
- **Certification Tracking**: Expiry monitoring and renewal workflows

### 5. Company Settings (`/settings`)

**Location**: `frontendv1/src/pages/Settings.tsx`

#### 5.1 Company Profile
- **Component**: `frontendv1/src/components/settings/CompanyProfile.tsx`
- **Features**:
  - Company basic information (name, registration, tax ID)
  - Contact details and address management
  - Logo and branding configuration
  - Company description and website

#### 5.2 User Management
- **Component**: `frontendv1/src/components/settings/UserManagement.tsx`
- **Features**:
  - User account creation and management
  - Role-based access control
  - Site assignment management
  - User status tracking (active/inactive)
  - Last login monitoring

### 6. Company Reports (`/company-reports`)

**Location**: `frontendv1/src/pages/CompanyReports.tsx`

**Report Categories**:
- **Cross-Site Performance**: Comparative analysis across all sites
- **Incident Statistics**: Company-wide safety metrics
- **Training Compliance**: Training completion rates across sites
- **Equipment Utilization**: Asset performance and utilization metrics

## Navigation Structure

### Main Navigation Menu
**Location**: `frontendv1/src/components/layout/sidebar/SidebarProvider.tsx`

**Company-Level Menu Items**:
1. **Dashboard** (`/`) - Company overview with site submenu
2. **Data** (`/data`) - Master data management
3. **Reports** (`/company-reports`) - Company-wide reporting
4. **Settings** (`/settings`) - Company configuration

### Context-Aware Navigation
- **Site Context Detection**: `frontendv1/src/hooks/useSiteContext.ts`
- **Dynamic Menu Switching**: Navigation automatically switches between company and site menus
- **Breadcrumb Management**: Context-aware breadcrumb navigation

## Data Flow Architecture

### Master Data Propagation
1. **Company Level**: Master data defined and maintained
2. **Site Level**: Master data inherited and customized as needed
3. **Synchronization**: Changes at company level propagate to sites
4. **Override Capability**: Sites can override certain company defaults

### Tenant-Based Data Isolation
- **Tenant ID**: All data tagged with tenant identifier
- **Data Segregation**: Complete isolation between different companies
- **Multi-Tenancy**: Single application instance serves multiple companies

## Integration Points

### Equipment Management Integration
- **Company Equipment Registry**: Central equipment database
- **Site Assignment Tracking**: Real-time location and assignment status
- **Compliance Synchronization**: Company compliance rules enforced at sites
- **Transfer Workflows**: Structured equipment movement between sites

### Worker Management Integration
- **Company Worker Database**: Master worker records
- **Training Requirements**: Automatic generation based on trades
- **Site Assignment**: Multi-site worker assignment capability
- **Compliance Tracking**: Centralized training and certification monitoring

### Document Management Integration
- **Template Management**: Company-level form and document templates
- **Version Control**: Centralized template versioning
- **Site Distribution**: Automatic template distribution to sites
- **Compliance Tracking**: Document completion and compliance monitoring

## Technical Implementation

### State Management
- **Tenant Context**: `frontendv1/src/hooks/useTenantContext.tsx`
- **Layout Context**: `frontendv1/src/hooks/useLayoutContext.tsx`
- **Site Context**: `frontendv1/src/hooks/useSiteContext.ts`

### Routing Configuration
- **Main Router**: `frontendv1/src/App.tsx`
- **Protected Routes**: Authentication-based route protection
- **Context-Aware Routing**: Dynamic routing based on company/site context

### Component Architecture
- **Floating Cards**: Consistent UI wrapper for all pages
- **Tab Containers**: Standardized tabbed interfaces
- **Form Components**: Reusable form components with validation
- **Data Tables**: Standardized data display components

## Current Limitations and Future Considerations

### Identified Gaps
1. **Limited Multi-Site Reporting**: Cross-site analytics could be enhanced
2. **Equipment Transfer Workflows**: Manual processes need automation
3. **Real-Time Synchronization**: Some data updates require manual refresh
4. **Advanced Analytics**: Predictive analytics capabilities needed

### Planned Enhancements
1. **Enhanced Dashboard**: More sophisticated KPI visualization
2. **Advanced Reporting**: Custom report builder functionality
3. **Workflow Automation**: Automated approval and notification workflows
4. **Mobile Optimization**: Enhanced mobile experience for company management

## API Endpoints

### Company Equipment Management
```http
GET /api/company/equipment
POST /api/company/equipment
GET /api/company/equipment/{equipmentId}
PUT /api/company/equipment/{equipmentId}
DELETE /api/company/equipment/{equipmentId}
```

### Master Data Management
```http
GET /api/company/training-programs
POST /api/company/training-programs
GET /api/company/ppe-catalog
POST /api/company/ppe-catalog
GET /api/company/form-templates
POST /api/company/form-templates
```

### Company Reports
```http
GET /api/company/reports/cross-site-performance
GET /api/company/reports/incident-statistics
GET /api/company/reports/training-compliance
```

This documentation provides a comprehensive overview of the current company-level implementation. For site-level implementation details, refer to the companion document `site-level-implementation.md`.
