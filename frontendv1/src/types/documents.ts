// Document Management Types

export enum DocumentCategory {
	SAFETY = "safety",
	TRAINING = "training",
	COMPLIANCE = "compliance",
	PROJECT = "project",
	WORKER = "worker",
	EQUIPMENT = "equipment",
	ADMINISTRATIVE = "administrative",
}

export enum DocumentStatus {
	ACTIVE = "active",
	ARCHIVED = "archived",
	EXPIRED = "expired",
	DRAFT = "draft",
}

export enum ComplianceStatus {
	VALID = "valid",
	EXPIRING = "expiring",
	EXPIRED = "expired",
}

export enum EntityType {
	COMPANY = "company",
	SITE = "site",
	WORKER = "worker",
	TASK = "task",
	PERMIT = "permit",
	INCIDENT = "incident",
	TRAINING_PROGRAM = "training-program",
	WORKER_TRAINING = "worker-training",
}

export interface Document {
	createElement(arg0: string): unknown;
	body: any;
	id: string;
	name: string;
	description?: string;
	category: DocumentCategory;
	type: string;
	version: string;
	status: DocumentStatus;

	// File Information
	fileName: string;
	fileSize: number;
	mimeType: string;
	fileUrl: string;
	thumbnailUrl?: string;

	// Metadata
	tags: string[];
	customFields: Record<string, any>;

	// Compliance
	expiryDate?: string;
	renewalRequired: boolean;
	complianceStatus: ComplianceStatus;

	// Relationships
	entityType: EntityType;
	entityId: string;
	parentDocumentId?: string;

	// Audit Trail
	createdAt: string;
	createdBy: string;
	createdByName: string;
	updatedAt: string;
	updatedBy: string;
	updatedByName: string;
	accessLog?: DocumentAccess[];
}

export interface DocumentType {
	id: string;
	name: string;
	category: DocumentCategory;
	requiredFields: string[];
	expiryRequired: boolean;
	versioningEnabled: boolean;
	approvalRequired: boolean;
	allowedMimeTypes: string[];
	maxFileSize: number; // in bytes
}

export interface DocumentAccess {
	id: string;
	documentId: string;
	userId: string;
	userName: string;
	action: "view" | "download" | "edit" | "delete" | "share";
	ipAddress?: string;
	userAgent?: string;
	accessedAt: string;
}

export interface DocumentFilter {
	category?: DocumentCategory;
	status?: DocumentStatus;
	complianceStatus?: ComplianceStatus;
	entityType?: EntityType;
	entityId?: string;
	tags?: string[];
	dateFrom?: string;
	dateTo?: string;
	expiryFrom?: string;
	expiryTo?: string;
	search?: string;
	createdBy?: string;
	fileType?: string;
}

export interface DocumentUploadRequest {
	name: string;
	description?: string;
	category: DocumentCategory;
	type: string;
	entityType: EntityType;
	entityId: string;
	tags?: string[];
	expiryDate?: string;
	renewalRequired?: boolean;
	customFields?: Record<string, any>;
	file: File;
}

export interface DocumentUpdateRequest {
	name?: string;
	description?: string;
	tags?: string[];
	expiryDate?: string;
	renewalRequired?: boolean;
	customFields?: Record<string, any>;
	status?: DocumentStatus;
}

export interface DocumentVersion {
	id: string;
	documentId: string;
	version: string;
	fileName: string;
	fileSize: number;
	fileUrl: string;
	createdAt: string;
	createdBy: string;
	createdByName: string;
	changeNotes?: string;
}

export interface DocumentStats {
	totalDocuments: number;
	documentsByCategory: Record<DocumentCategory, number>;
	documentsByStatus: Record<DocumentStatus, number>;
	expiringDocuments: number;
	expiredDocuments: number;
	recentUploads: number;
	storageUsed: number; // in bytes
}

export interface ComplianceRule {
	id: string;
	documentType: string;
	category: DocumentCategory;
	expiryWarningDays: number[];
	renewalRequired: boolean;
	notificationRecipients: string[];
	escalationRules: EscalationRule[];
	isActive: boolean;
}

export interface EscalationRule {
	daysOverdue: number;
	action:
		| "notify-management"
		| "disable-task-assignment"
		| "flag-worker"
		| "send-email";
	recipients: string[];
}

export interface DocumentSearchResult {
	documents: Document[];
	totalCount: number;
	facets: {
		categories: Record<DocumentCategory, number>;
		statuses: Record<DocumentStatus, number>;
		entityTypes: Record<EntityType, number>;
		tags: Record<string, number>;
	};
}

export interface DocumentLibraryProps {
	entityType: EntityType;
	entityId: string;
	category?: DocumentCategory;
	showUpload?: boolean;
	showFilters?: boolean;
	viewMode?: "grid" | "list";
	onDocumentSelect?: (document: Document) => void;
}

export interface DocumentCardProps {
	document: Document;
	viewMode: "grid" | "list";
	onView?: (document: Document) => void;
	onEdit?: (document: Document) => void;
	onDelete?: (documentId: string) => void;
	onDownload?: (document: Document) => void;
	showActions?: boolean;
}

export interface DocumentUploadProps {
	entityType: EntityType;
	entityId: string;
	category?: DocumentCategory;
	acceptedTypes?: string[];
	maxFileSize?: number;
	onClose: () => void;
	onUploaded: (document: Document) => void;
}

export interface DocumentViewerProps {
	document: Document;
	onClose: () => void;
	onEdit?: (document: Document) => void;
	onDelete?: (documentId: string) => void;
}

// Predefined document types by category
export const DOCUMENT_TYPES: Record<DocumentCategory, DocumentType[]> = {
	[DocumentCategory.SAFETY]: [
		{
			id: "rams",
			name: "Risk Assessment Method Statement",
			category: DocumentCategory.SAFETY,
			requiredFields: ["title", "version", "effectiveDate"],
			expiryRequired: true,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 50 * 1024 * 1024, // 50MB
		},
		{
			id: "emergency-plan",
			name: "Emergency Response Plan",
			category: DocumentCategory.SAFETY,
			requiredFields: ["title", "effectiveDate"],
			expiryRequired: true,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 25 * 1024 * 1024, // 25MB
		},
		{
			id: "incident-report",
			name: "Incident Report",
			category: DocumentCategory.SAFETY,
			requiredFields: ["incidentDate", "reportedBy"],
			expiryRequired: false,
			versioningEnabled: false,
			approvalRequired: false,
			allowedMimeTypes: ["application/pdf", "image/jpeg", "image/png"],
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			id: "policy",
			name: "Safety Policy",
			category: DocumentCategory.SAFETY,
			requiredFields: ["title", "version"],
			expiryRequired: false,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 25 * 1024 * 1024, // 25MB
		},
	],
	[DocumentCategory.TRAINING]: [
		{
			id: "training-material",
			name: "Training Material",
			category: DocumentCategory.TRAINING,
			requiredFields: ["title", "trainingProgram"],
			expiryRequired: false,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"video/mp4",
				"application/vnd.ms-powerpoint",
				"application/vnd.openxmlformats-officedocument.presentationml.presentation",
			],
			maxFileSize: 100 * 1024 * 1024, // 100MB
		},
		{
			id: "safety-certificate",
			name: "Safety Certificate",
			category: DocumentCategory.TRAINING,
			requiredFields: [
				"workerName",
				"certificateNumber",
				"issueDate",
				"expiryDate",
			],
			expiryRequired: true,
			versioningEnabled: false,
			approvalRequired: false,
			allowedMimeTypes: ["application/pdf", "image/jpeg", "image/png"],
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			id: "first-aid-certificate",
			name: "First Aid Certificate",
			category: DocumentCategory.TRAINING,
			requiredFields: [
				"workerName",
				"certificateNumber",
				"issueDate",
				"expiryDate",
			],
			expiryRequired: true,
			versioningEnabled: false,
			approvalRequired: false,
			allowedMimeTypes: ["application/pdf", "image/jpeg", "image/png"],
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			id: "manual",
			name: "Training Manual",
			category: DocumentCategory.TRAINING,
			requiredFields: ["title", "version"],
			expiryRequired: false,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 50 * 1024 * 1024, // 50MB
		},
	],
	[DocumentCategory.COMPLIANCE]: [
		{
			id: "permit",
			name: "Permit",
			category: DocumentCategory.COMPLIANCE,
			requiredFields: ["permitNumber", "authority", "issueDate", "expiryDate"],
			expiryRequired: true,
			versioningEnabled: false,
			approvalRequired: false,
			allowedMimeTypes: ["application/pdf", "image/jpeg", "image/png"],
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			id: "environmental-report",
			name: "Environmental Report",
			category: DocumentCategory.COMPLIANCE,
			requiredFields: ["title", "assessmentDate"],
			expiryRequired: true,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 25 * 1024 * 1024, // 25MB
		},
		{
			id: "quality-system",
			name: "Quality Management System",
			category: DocumentCategory.COMPLIANCE,
			requiredFields: ["standard", "certificationBody"],
			expiryRequired: true,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 50 * 1024 * 1024, // 50MB
		},
	],
	[DocumentCategory.PROJECT]: [
		{
			id: "specification",
			name: "Project Specification",
			category: DocumentCategory.PROJECT,
			requiredFields: ["title", "version"],
			expiryRequired: false,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 50 * 1024 * 1024, // 50MB
		},
		{
			id: "drawing",
			name: "Technical Drawing",
			category: DocumentCategory.PROJECT,
			requiredFields: ["drawingNumber", "scale"],
			expiryRequired: false,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/acad",
				"image/jpeg",
				"image/png",
			],
			maxFileSize: 100 * 1024 * 1024, // 100MB
		},
	],
	[DocumentCategory.WORKER]: [
		{
			id: "contract",
			name: "Employment Contract",
			category: DocumentCategory.WORKER,
			requiredFields: ["workerName", "position", "startDate"],
			expiryRequired: false,
			versioningEnabled: false,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			id: "medical-certificate",
			name: "Medical Certificate",
			category: DocumentCategory.WORKER,
			requiredFields: ["workerName", "doctorName", "issueDate", "expiryDate"],
			expiryRequired: true,
			versioningEnabled: false,
			approvalRequired: false,
			allowedMimeTypes: ["application/pdf", "image/jpeg", "image/png"],
			maxFileSize: 5 * 1024 * 1024, // 5MB
		},
	],
	[DocumentCategory.EQUIPMENT]: [
		{
			id: "inspection-certificate",
			name: "Inspection Certificate",
			category: DocumentCategory.EQUIPMENT,
			requiredFields: [
				"equipmentId",
				"inspector",
				"inspectionDate",
				"expiryDate",
			],
			expiryRequired: true,
			versioningEnabled: false,
			approvalRequired: false,
			allowedMimeTypes: ["application/pdf", "image/jpeg", "image/png"],
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			id: "manual",
			name: "Equipment Manual",
			category: DocumentCategory.EQUIPMENT,
			requiredFields: ["equipmentModel", "serialNumber"],
			expiryRequired: false,
			versioningEnabled: true,
			approvalRequired: false,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 50 * 1024 * 1024, // 50MB
		},
	],
	[DocumentCategory.ADMINISTRATIVE]: [
		{
			id: "insurance-policy",
			name: "Insurance Policy",
			category: DocumentCategory.ADMINISTRATIVE,
			requiredFields: [
				"policyNumber",
				"insurer",
				"effectiveDate",
				"expiryDate",
			],
			expiryRequired: true,
			versioningEnabled: false,
			approvalRequired: false,
			allowedMimeTypes: ["application/pdf"],
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			id: "contract",
			name: "Contract",
			category: DocumentCategory.ADMINISTRATIVE,
			requiredFields: ["contractNumber", "parties", "effectiveDate"],
			expiryRequired: false,
			versioningEnabled: true,
			approvalRequired: true,
			allowedMimeTypes: [
				"application/pdf",
				"application/msword",
				"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			],
			maxFileSize: 25 * 1024 * 1024, // 25MB
		},
	],
};
