/**
 * Enhanced type definitions for the Sidebar component system
 * Provides comprehensive typing for state management, interactions, and styling
 */

import { ReactNode } from "react";

// ============================================================================
// CORE MENU TYPES
// ============================================================================

export interface BaseMenuItem {
	name: string;
	icon: ReactNode;
	path: string;
	id?: string; // Optional unique identifier
	siteData?: {
		id: string;
		name: string;
		healthStatus: "green" | "amber" | "red";
		workersOnSite: number;
		activePermits: number;
		openIncidents: number;
		projectManager: string;
		location: string;
		timeline?: string;
		currentPhase?: string;
		progressPercentage?: number;
	};
}

export interface MenuSubmenu {
	title: string;
	items: SubmenuItem[];
}

export interface MenuItem extends BaseMenuItem {
	submenu?: MenuSubmenu;
}

export interface SubmenuItem {
	name: string;
	path: string;
	action?: "add" | "view" | "manage";
	id?: string;
	siteData?: {
		id: string;
		name: string;
		healthStatus: "green" | "amber" | "red";
		workersOnSite: number;
		activePermits: number;
		openIncidents: number;
		projectManager: string;
		location: string;
		timeline?: string;
		currentPhase?: string;
		progressPercentage?: number;
	};
}

export interface SiteMenuItem extends BaseMenuItem {
	submenu?: {
		title: string;
		items: (SubmenuItem & { action?: "add" | "view" | "manage" })[];
	};
}

// ============================================================================
// SIDEBAR STATE MANAGEMENT
// ============================================================================

export interface SidebarState {
	expandedMenu: string | null;
	isHovering: boolean;
	flyoutVisible: boolean;
	keyboardNavigation: boolean;
	focusedItemId: string | null;
}

export type SidebarAction =
	| { type: "EXPAND_MENU"; payload: string }
	| { type: "COLLAPSE_MENU" }
	| { type: "SET_HOVER"; payload: boolean }
	| { type: "SET_FLYOUT_VISIBLE"; payload: boolean }
	| { type: "SET_KEYBOARD_NAV"; payload: boolean }
	| { type: "SET_FOCUSED_ITEM"; payload: string | null }
	| { type: "RESET" };

// ============================================================================
// INTERACTION TYPES
// ============================================================================

export interface HoverConfig {
	enterDelay: number;
	leaveDelay: number;
	sidebarLeaveDelay: number;
	flyoutLeaveDelay: number;
}

export interface KeyboardNavigation {
	currentIndex: number;
	items: string[];
	isActive: boolean;
}

// ============================================================================
// DESIGN TOKENS
// ============================================================================

export interface SidebarTokens {
	spacing: {
		sidebarWidth: string;
		flyoutWidth: string;
		logoHeight: string;
		itemSpacing: {
			company: string;
			site: string;
		};
		padding: {
			small: string;
			medium: string;
			large: string;
		};
	};
	colors: {
		background: {
			sidebar: string;
			flyout: string;
			flyoutItemHover: string;
			itemActive: string;
			itemHover: string;
		};
		text: {
			primary: string;
			secondary: string;
			active: string;
			hover: string;
		};
		border: {
			flyout: string;
			divider: string;
		};
		focus: {
			ring: string;
		};
	};
	animation: {
		duration: {
			fast: string;
			normal: string;
			slow: string;
		};
		easing: {
			enter: string;
			exit: string;
		};
	};
	typography: {
		fontSize: {
			small: string;
			base: string;
		};
		fontWeight: {
			normal: string;
			medium: string;
		};
	};
}

// ============================================================================
// COMPONENT PROPS
// ============================================================================

export interface SidebarContextValue {
	state: SidebarState;
	dispatch: React.Dispatch<SidebarAction>;
	menuItems: (MenuItem | SiteMenuItem)[];
	activeMenuItem: (MenuItem | SiteMenuItem) | null;
	isMenuItemActive: (item: MenuItem | SiteMenuItem) => boolean;
	tokens: SidebarTokens;
	config: HoverConfig;
}

export interface SidebarProviderProps {
	children: ReactNode;
	hoverConfig?: Partial<HoverConfig>;
}

export interface SidebarMenuItemProps {
	item: MenuItem | SiteMenuItem;
	isActive: boolean;
	isSiteLevel: boolean;
	onHover: (itemName: string) => void;
	onKeyboardSelect: (itemName: string) => void;
}

export interface SidebarFlyoutProps {
	menuItem: (MenuItem | SiteMenuItem) | null;
	visible: boolean;
	onMouseEnter: () => void;
	onMouseLeave: () => void;
}

export interface FlyoutMenuItemProps {
	item: SubmenuItem;
	isActive: boolean;
	onClick?: () => void;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type SidebarVariant = "company" | "site";

export interface AccessibilityProps {
	"aria-label"?: string;
	"aria-expanded"?: boolean;
	"aria-haspopup"?: boolean;
	"aria-current"?: "page" | "step" | "location" | "date" | "time" | boolean;
	role?: string;
	tabIndex?: number;
}

// ============================================================================
// HOOK RETURN TYPES
// ============================================================================

export interface UseSidebarHover {
	isHovering: boolean;
	handleMouseEnter: (itemName?: string) => void;
	handleMouseLeave: () => void;
	handleFlyoutMouseEnter: () => void;
	handleFlyoutMouseLeave: () => void;
}

export interface UseSidebarKeyboard {
	focusedIndex: number;
	handleKeyDown: (event: React.KeyboardEvent) => void;
	setFocusedIndex: (index: number) => void;
}

export interface UseSidebarAccessibility {
	getMenuItemProps: (
		item: MenuItem | SiteMenuItem,
		index: number,
	) => AccessibilityProps;
	getFlyoutProps: (menuItem: MenuItem | SiteMenuItem) => AccessibilityProps;
	getSubmenuItemProps: (item: SubmenuItem) => AccessibilityProps;
}
