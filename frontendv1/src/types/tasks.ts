// Task-related TypeScript interfaces

// Task Status Types - Updated to match documentation and current usage
export type TaskStatus =
	| "opened"
	| "requested"
	| "approved"
	| "in-progress"
	| "blocked"
	| "completed"
	| "cancelled"
	| "todo"
	| "permit-pending"
	| "permit-approved"
	| "rejected";

// Task Request Status Types
export type TaskRequestStatus =
	| "opened"
	| "pending-approval"
	| "pending-assessment"
	| "disapproved"
	| "assessed-and-approved";

// Task Priority Types
export type TaskPriority = "low" | "medium" | "high" | "critical";

// Risk Level Types
export type RiskLevel = "low" | "medium" | "high" | "critical";

// Task Category Types - Updated to match documentation and current usage
export type TaskCategory =
	| "excavation"
	| "concrete-work"
	| "steel-erection"
	| "electrical-installation"
	| "plumbing"
	| "hvac"
	| "safety"
	| "inspection"
	| "maintenance"
	| "other"
	| "electrical"
	| "construction";

// Control Measure Types
export type ControlMeasureType =
	| "elimination"
	| "substitution"
	| "engineering"
	| "administrative"
	| "ppe";

// Hazard Interface
export interface Hazard {
	id: string;
	description: string;
	riskLevel: RiskLevel;
	likelihood: number; // 1-5 scale
	severity: number; // 1-5 scale
	riskScore: number; // likelihood × severity
	controlMeasures: string[]; // Array of control measure IDs
}

// Control Measure Interface
export interface ControlMeasure {
	id: string;
	description: string;
	type: ControlMeasureType;
	effectiveness: number; // 1-5 scale
	implementationCost: "low" | "medium" | "high";
	trainingRequired: boolean;
	equipmentRequired: string[];
}

// Document Requirement Interface
export interface DocumentRequirement {
	type: string;
	description: string;
	mandatory: boolean;
	validityPeriod?: number; // days
	renewalRequired: boolean;
}

// Skill Requirement Interface
export interface SkillRequirement {
	id: string;
	name: string;
	level: "basic" | "intermediate" | "advanced" | "expert";
	mandatory: boolean;
}

// Equipment Requirement Interface
export interface EquipmentRequirement {
	id: string;
	name: string;
	type: string;
	mandatory: boolean;
	quantity: number;
}



// Site Task Interface - Complete copy from template with site-specific modifications
export interface SiteTask {
	id: string;
	siteId: string;
	templateId: string; // Reference only, not foreign key
	status: TaskStatus;
	category: TaskCategory;
	name: string;
	description: string;
	workDescription: string; // Site-specific description
	location: string;

	// Timing
	plannedStartDate: Date;
	plannedEndDate: Date;
	actualStartDate?: Date;
	actualEndDate?: Date;
	estimatedDuration: number;
	actualDuration?: number;

	// Safety and Risk - Can be modified from template
	hazards: Hazard[];
	controlMeasures: ControlMeasure[];
	riskLevel: RiskLevel;

	// Documents and Links
	attachedDocuments: TaskDocument[];
	linkedPermits: string[];
	requiredDocuments: DocumentRequirement[];

	// People
	createdBy: string;
	createdByName: string;
	assignedWorkers: TaskWorkerAssignment[];
	assignedEquipment: TaskEquipmentAssignment[];

	// Approval and Blocking
	approvedBy?: string;
	approvedAt?: Date;
	blockedReason?: string;
	blockedBy?: string;
	blockedAt?: Date;

	// Metadata
	createdAt: Date;
	updatedAt: Date;
	completedAt?: Date;
	progressPercentage: number;
	priority: TaskPriority;
	tags: string[];
}

// Task Request Interface
export interface TaskRequest {
	id: string;
	taskId: string;
	siteId: string;
	status: TaskRequestStatus;
	requestedBy: string;
	reviewedBy?: string;
	approvedBy?: string;
	comments: TaskComment[];
	retryCount: number;
	maxRetries: number; // Default: 3
	createdAt: Date;
	reviewedAt?: Date;
	approvedAt?: Date;
	disapprovalReason?: string;
}

// Task Comment Interface
export interface TaskComment {
	id: string;
	content: string;
	author: string;
	authorName: string;
	createdAt: Date;
	type: "general" | "approval" | "disapproval" | "revision-request";
}

// Task Worker Assignment Interface
export interface TaskWorkerAssignment {
	workerId: string;
	workerName: string;
	role: "supervisor" | "lead-worker" | "worker" | "safety-observer";
	assignedAt: Date;
	estimatedHours: number;
	actualHours?: number;
	hasRequiredTraining: boolean;
	hasRequiredCertifications: boolean;
}

// Task Equipment Assignment Interface
export interface TaskEquipmentAssignment {
	equipmentId: string;
	equipmentName: string;
	type: string;
	assignedAt: Date;
	estimatedUsageHours: number;
	actualUsageHours?: number;
	certificateValid: boolean;
	operatorRequired: boolean;
	operatorId?: string;
}

// Legacy Task Interface - keeping for backward compatibility
export interface Task {
	id: string;
	taskNumber: string;
	title: string;
	description: string;
	category: TaskCategory;
	location: string;
	siteId: string;

	// Timing
	plannedStartDate: Date;
	plannedEndDate: Date;
	actualStartDate?: Date;
	actualEndDate?: Date;
	estimatedDuration: number; // in hours
	actualDuration?: number; // in hours

	// Status and Priority
	status: TaskStatus;
	priority: TaskPriority;
	progressPercentage: number;

	// People
	createdBy: string;
	createdByName: string;
	assignedSupervisor: string;
	assignedSupervisorName: string;
	assignedWorkers: TaskWorker[];

	// Dependencies
	dependencies: TaskDependency[];
	blockedBy?: string[];

	// Permit Requirements
	requiresPermit: boolean;
	permitId?: string;
	permitStatus?:
		| "not-required"
		| "not-generated"
		| "draft"
		| "pending-approval"
		| "approved"
		| "active"
		| "expired"
		| "closed";
	permitTypes?: string[];

	// Risk and Safety
	riskLevel: "low" | "medium" | "high" | "critical";
	safetyRequirements: string[];
	requiredPPE: string[];
	requiredTrainings: string[];
	requiredCertifications: string[];

	// Documentation
	ramsDocuments: TaskDocument[];
	attachments: TaskAttachment[];

	// Quality and Compliance
	qualityChecks: QualityCheck[];
	complianceRequirements: string[];

	// Audit Trail
	createdAt: Date;
	updatedAt: Date;
	history: TaskHistoryEntry[];

	// Additional Metadata
	tags: string[];
	customFields: Record<string, any>;
}

// Worker Assignment
export interface TaskWorker {
	workerId: string;
	workerName: string;
	workerPhoto?: string;
	primaryTrade: string;
	role: "supervisor" | "lead-worker" | "worker" | "safety-observer";
	assignedAt: Date;
	acknowledgedAt?: Date;
	hasRequiredTraining: boolean;
	hasRequiredCertifications: boolean;
	estimatedHours: number;
	actualHours?: number;
}

// Task Dependencies
export interface TaskDependency {
	id: string;
	dependentTaskId: string;
	dependentTaskTitle: string;
	dependencyType:
		| "finish-to-start"
		| "start-to-start"
		| "finish-to-finish"
		| "start-to-finish";
	lagTime?: number; // in hours
	isBlocking: boolean;
}

// Task Documents
export interface TaskDocument {
	id: string;
	name: string;
	type: "rams" | "method-statement" | "drawing" | "specification" | "other" | "permit" | "certificate" | "competency" | "procedure";
	url: string;
	version: string;
	uploadedBy: string;
	uploadedAt: Date;
	isRequired: boolean;
}

// Task Attachments
export interface TaskAttachment {
	id: string;
	name: string;
	type: "image" | "document" | "video" | "other";
	url: string;
	size: number;
	uploadedBy: string;
	uploadedAt: Date;
	description?: string;
}

// Quality Checks
export interface QualityCheck {
	id: string;
	name: string;
	description: string;
	isRequired: boolean;
	isCompleted: boolean;
	completedBy?: string;
	completedAt?: Date;
	result?: "pass" | "fail" | "conditional";
	notes?: string;
	attachments: string[];
}

// Task History
export interface TaskHistoryEntry {
	id: string;
	action:
		| "created"
		| "updated"
		| "status-changed"
		| "assigned"
		| "started"
		| "completed"
		| "blocked"
		| "cancelled";
	description: string;
	performedBy: string;
	performedByName: string;
	timestamp: Date;
	oldValue?: any;
	newValue?: any;
	details?: Record<string, any>;
}

// Task Statistics
export interface TaskStats {
	totalTasks: number;
	todoTasks: number;
	permitPendingTasks: number;
	permitApprovedTasks: number;
	inProgressTasks: number;
	blockedTasks: number;
	completedTasks: number;
	cancelledTasks: number;
	overdueTasks: number;
	tasksCompletedToday: number;
	averageCompletionTime: number; // in hours
	onTimeCompletionRate: number; // percentage
	productivityScore: number; // percentage
}

// Task Filters
export interface TaskFilters {
	search: string;
	status: TaskStatus | "all";
	priority: TaskPriority | "all";
	category: TaskCategory | "all";
	assignedWorker: string;
	assignedSupervisor: string;
	dateRange: {
		start?: Date;
		end?: Date;
	};
	location: string;
	requiresPermit: boolean | "all";
	permitStatus: string;
	riskLevel: string;
	tags: string[];
}

// Kanban Column Configuration
export interface KanbanColumn {
	id: TaskStatus;
	title: string;
	color: string;
	icon: string;
	tasks: Task[];
	maxTasks?: number;
	allowDrop: boolean;
}

// Task Report Data
export interface TaskReportData {
	title: string;
	description: string;
	generatedAt: Date;
	chartData: any;
	chartType: "bar" | "line" | "pie" | "doughnut" | "gantt";
	tableData: any[];
	columns: {
		key: string;
		label: string;
		type?: "text" | "number" | "date" | "status" | "progress";
	}[];
	summary?: {
		totalTasks: number;
		completedTasks: number;
		onTimeCompletion: number;
		averageDuration: number;
	};
}

// Recent Activity
export interface RecentActivity {
	id: string;
	type:
		| "task_created"
		| "task_updated"
		| "task_completed"
		| "task_started"
		| "permit_approved"
		| "worker_assigned"
		| "quality_check_completed";
	description: string;
	taskId?: string;
	taskTitle?: string;
	performedBy: string;
	performedByName: string;
	timestamp: Date;
	metadata?: Record<string, any>;
}

// Task Template - Extended version with all required properties
export interface TaskTemplate {
	id: string;
	companyId: string;
	category: TaskCategory;
	name: string;
	description: string;
	hazards: Hazard[];
	controlMeasures: ControlMeasure[];
	requiredDocuments: DocumentRequirement[];
	estimatedDuration: number;
	skillRequirements: SkillRequirement[];
	equipmentRequirements: EquipmentRequirement[];
	requiredTrainings: string[];
	requiredCertifications: string[];
	requiredPPE: string[];
	safetyRequirements: string[];
	qualityChecks: Omit<
		QualityCheck,
		"id" | "isCompleted" | "completedBy" | "completedAt"
	>[];
	permitTypes: string[];
	riskLevel: "low" | "medium" | "high" | "critical";
	createdAt: Date;
	updatedAt: Date;
	createdBy: string;
	isActive: boolean;
}

// Task Assignment Request
export interface TaskAssignmentRequest {
	taskId: string;
	workerId: string;
	role: "supervisor" | "lead-worker" | "worker" | "safety-observer";
	estimatedHours: number;
	requestedBy: string;
	requestedAt: Date;
	status: "pending" | "approved" | "rejected";
	approvedBy?: string;
	approvedAt?: Date;
	rejectionReason?: string;
}

// Task Performance Metrics
export interface TaskPerformanceMetrics {
	taskId: string;
	plannedVsActualDuration: {
		planned: number;
		actual: number;
		variance: number;
		variancePercentage: number;
	};
	qualityScore: number;
	safetyScore: number;
	productivityScore: number;
	costVariance: {
		planned: number;
		actual: number;
		variance: number;
		variancePercentage: number;
	};
	workerEfficiency: {
		workerId: string;
		workerName: string;
		hoursWorked: number;
		productivityScore: number;
	}[];
}

// Task Notification
export interface TaskNotification {
	id: string;
	taskId: string;
	taskTitle: string;
	type:
		| "assignment"
		| "deadline"
		| "status_change"
		| "permit_required"
		| "quality_check"
		| "overdue";
	message: string;
	priority: "low" | "medium" | "high" | "critical";
	recipientId: string;
	recipientRole: string;
	isRead: boolean;
	createdAt: Date;
	scheduledFor?: Date;
	actionRequired?: boolean;
	actionUrl?: string;
}
