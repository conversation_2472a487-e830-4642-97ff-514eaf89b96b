export interface WeatherCondition {
  id: number;
  main: string; // e.g., "Clear", "Clouds", "Rain"
  description: string; // e.g., "clear sky", "few clouds"
  icon: string; // OpenWeather icon code
}

export interface WeatherData {
  siteId: string;
  siteName: string;
  location: string;
  coordinates: {
    lat: number;
    lon: number;
  };
  current: {
    temperature: number; // Celsius
    feelsLike: number;
    humidity: number; // percentage
    pressure: number; // hPa
    visibility: number; // meters
    uvIndex: number;
    windSpeed: number; // m/s
    windDirection: number; // degrees
    conditions: WeatherCondition;
    timestamp: string; // ISO string
  };
  forecast: WeatherForecast[];
  alerts?: WeatherAlert[];
  lastUpdated: string;
}

export interface WeatherForecast {
  date: string; // YYYY-MM-DD
  day: string; // e.g., "Monday"
  temperature: {
    min: number;
    max: number;
  };
  conditions: WeatherCondition;
  humidity: number;
  windSpeed: number;
  precipitationChance: number; // percentage
}

export interface WeatherAlert {
  id: string;
  type: 'warning' | 'watch' | 'advisory';
  severity: 'minor' | 'moderate' | 'severe' | 'extreme';
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  areas: string[];
}

export interface WeatherCache {
  siteId: string;
  data: WeatherData;
  cachedAt: string;
  expiresAt: string;
}

export interface WeatherServiceConfig {
  apiKey: string;
  baseUrl: string;
  cacheTimeout: number; // minutes
  retryAttempts: number;
  retryDelay: number; // milliseconds
}

export interface WeatherHookResult {
  weatherData: WeatherData[];
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  getWeatherForSite: (siteId: string) => WeatherData | undefined;
}

// Weather safety thresholds for construction sites
export interface WeatherSafetyThresholds {
  temperature: {
    min: number; // Below this, work may be unsafe
    max: number; // Above this, work may be unsafe
  };
  windSpeed: {
    max: number; // Above this, crane operations unsafe
  };
  visibility: {
    min: number; // Below this, work may be unsafe
  };
  uvIndex: {
    max: number; // Above this, additional protection needed
  };
}

export const DEFAULT_SAFETY_THRESHOLDS: WeatherSafetyThresholds = {
  temperature: { min: -10, max: 40 },
  windSpeed: { max: 15 }, // m/s
  visibility: { min: 1000 }, // meters
  uvIndex: { max: 8 }
};
