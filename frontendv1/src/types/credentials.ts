// Enhanced Credential Management Types for Worker Creation
// Supports both permanent and temporary credentials with proper expiration tracking

export type CredentialType = 'PERMANENT' | 'TEMPORARY';
export type CredentialStatus = 'VALID' | 'EXPIRING' | 'EXPIRED' | 'PENDING_VERIFICATION';

// Base credential interface
export interface BaseCredential {
  id?: string;
  name: string;
  type: CredentialType;
  description?: string;
  issuingAuthority: string;
  certificateNumber?: string;
  issueDate: string; // ISO date string
  status: CredentialStatus;
  documentUrl?: string;
  documentFile?: File;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Permanent credentials (no expiration)
export interface PermanentCredential extends BaseCredential {
  type: 'PERMANENT';
  category: 'DIPLOMA' | 'DEGREE' | 'PROFESSIONAL_LICENSE' | 'TRADE_CERTIFICATE' | 'OTHER';
  institution: string;
  fieldOfStudy?: string;
  graduationYear?: number;
  licenseNumber?: string;
  // Permanent credentials don't expire
  expiryDate?: never;
  renewalRequired?: never;
}

// Temporary credentials (with expiration)
export interface TemporaryCredential extends BaseCredential {
  type: 'TEMPORARY';
  category: 'SAFETY_TRAINING' | 'EQUIPMENT_CERTIFICATION' | 'FIRST_AID' | 'COMPLIANCE_TRAINING' | 'OTHER';
  expiryDate: string; // ISO date string - required for temporary credentials
  renewalRequired: boolean;
  renewalPeriodMonths: number;
  trainingProvider: string;
  trainingDurationHours?: number;
  competencyLevel?: 'BASIC' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  // Calculated fields
  daysUntilExpiry?: number;
  isExpiringSoon?: boolean; // Within 30 days
}

// Union type for all credentials
export type WorkerCredential = PermanentCredential | TemporaryCredential;

// Credential upload data for form handling
export interface CredentialUploadData {
  credentialType: CredentialType;
  category: string;
  name: string;
  issuingAuthority: string;
  institution?: string; // For permanent credentials
  trainingProvider?: string; // For temporary credentials
  certificateNumber?: string;
  issueDate: string;
  expiryDate?: string; // Only for temporary credentials
  renewalPeriodMonths?: number; // Only for temporary credentials
  documentFile: File;
  notes?: string;
}

// Form state for credential management
export interface CredentialFormState {
  permanentCredentials: PermanentCredential[];
  temporaryCredentials: TemporaryCredential[];
  uploadInProgress: boolean;
  validationErrors: Record<string, string>;
}

// Credential validation result
export interface CredentialValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Training requirement mapping
export interface TrainingRequirement {
  id: string;
  name: string;
  type: CredentialType;
  category: string;
  isMandatory: boolean;
  applicableToTrades: string[];
  description: string;
  renewalPeriodMonths?: number; // Only for temporary
  issuingAuthorities: string[];
}

// Worker credential summary for dashboard display
export interface WorkerCredentialSummary {
  workerId: string;
  totalCredentials: number;
  permanentCredentials: number;
  temporaryCredentials: number;
  validCredentials: number;
  expiringCredentials: number;
  expiredCredentials: number;
  nextExpiryDate?: string;
  complianceStatus: 'COMPLIANT' | 'EXPIRING' | 'NON_COMPLIANT';
  missingRequiredCredentials: string[];
}

// Enhanced worker creation input with credentials
export interface EnhancedWorkerCreationInput {
  // Basic worker information
  name: string;
  company: string;
  nationalId: string;
  gender: 'MALE' | 'FEMALE';
  phoneNumber: string;
  email?: string;
  dateOfBirth?: string;
  
  // Employment details
  employeeNumber?: string;
  hireDate?: string;
  tradeIds: number[];
  skillIds: number[];
  
  // Profile and documents
  profilePicture?: File;
  signature?: File;
  
  // Enhanced credential system
  permanentCredentials: PermanentCredential[];
  temporaryCredentials: TemporaryCredential[];
  
  // Site assignment (optional)
  assignToSite?: boolean;
  siteId?: string;
  siteRole?: string;
  hourlyRate?: number;
  overtimeRate?: number;
}

// Credential auto-naming utility types
export interface CredentialNamingContext {
  workerName: string;
  credentialType: CredentialType;
  category: string;
  trainingName?: string;
  issuingAuthority: string;
  issueDate: string;
}

export interface AutoGeneratedCredentialName {
  fileName: string;
  displayName: string;
  folderPath: string;
}
