// GeoJSON processing utilities
// Based on the drawarea.md documentation requirements

export interface GeoJSONFeature {
  type: 'Feature';
  properties: {
    [key: string]: any;
  };
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
}

export interface Coordinate {
  latitude: number;
  longitude: number;
}

export interface BoundingBox {
  north: number;
  south: number;
  east: number;
  west: number;
}

/**
 * Validate GeoJSON polygon structure
 */
export const validateGeoJSON = (geojson: any): boolean => {
  if (!geojson || geojson.type !== 'Feature') {
    return false;
  }

  if (!geojson.geometry || geojson.geometry.type !== 'Polygon') {
    return false;
  }

  if (!geojson.geometry.coordinates || !Array.isArray(geojson.geometry.coordinates)) {
    return false;
  }

  const coordinates = geojson.geometry.coordinates[0];
  if (!Array.isArray(coordinates) || coordinates.length < 4) {
    return false;
  }

  // Check if polygon is closed
  const first = coordinates[0];
  const last = coordinates[coordinates.length - 1];
  if (first[0] !== last[0] || first[1] !== last[1]) {
    return false;
  }

  return true;
};

// Define DrawnPolygon type locally to avoid circular imports
interface DrawnPolygon {
  type: 'Feature';
  properties: {
    name: string;
  };
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
}

/**
 * Process GeoJSON to ensure compatibility and fix common issues
 */
export const processGeoJSON = (geojson: any): DrawnPolygon | null => {
  if (!geojson) return null;

  // Create deep copy to avoid modifying original
  const processed = JSON.parse(JSON.stringify(geojson));

  // Validate structure
  if (!processed.geometry || !processed.geometry.coordinates) {
    console.warn('GeoJSON missing geometry or coordinates:', processed);
    return null;
  }

  // Ensure it's a Feature
  if (processed.type !== 'Feature') {
    processed.type = 'Feature';
  }

  // Ensure properties exist with required name property
  if (!processed.properties) {
    processed.properties = {};
  }

  // Ensure name property exists for DrawnPolygon compatibility
  if (!processed.properties.name) {
    processed.properties.name = 'Site Boundary';
  }

  // Ensure polygon is closed
  const coordinates = processed.geometry.coordinates[0];
  if (coordinates && coordinates.length > 0) {
    const first = coordinates[0];
    const last = coordinates[coordinates.length - 1];
    
    if (first[0] !== last[0] || first[1] !== last[1]) {
      coordinates.push([first[0], first[1]]);
    }
  }

  return processed;
};

/**
 * Calculate the center point (centroid) of a polygon
 */
export const calculatePolygonCenter = (coordinates: number[][]): Coordinate => {
  if (!coordinates || coordinates.length === 0) {
    throw new Error('Invalid coordinates for center calculation');
  }

  const lats = coordinates.map(coord => coord[1]);
  const lngs = coordinates.map(coord => coord[0]);

  const centerLat = lats.reduce((sum, lat) => sum + lat, 0) / lats.length;
  const centerLng = lngs.reduce((sum, lng) => sum + lng, 0) / lngs.length;

  return {
    latitude: centerLat,
    longitude: centerLng
  };
};

/**
 * Calculate area of a polygon in square meters using the shoelace formula
 * Adapted for spherical coordinates (Earth's surface)
 */
export const calculatePolygonArea = (coordinates: number[][]): number => {
  if (!coordinates || coordinates.length < 3) {
    return 0;
  }

  // Convert to radians and calculate area using spherical geometry
  const R = 6371000; // Earth's radius in meters
  let area = 0;

  for (let i = 0; i < coordinates.length - 1; i++) {
    const [lon1, lat1] = coordinates[i];
    const [lon2, lat2] = coordinates[i + 1];
    
    const lat1Rad = lat1 * Math.PI / 180;
    const lat2Rad = lat2 * Math.PI / 180;
    const deltaLon = (lon2 - lon1) * Math.PI / 180;
    
    area += deltaLon * (2 + Math.sin(lat1Rad) + Math.sin(lat2Rad));
  }

  area = Math.abs(area * R * R / 2);
  return area;
};

/**
 * Calculate perimeter of a polygon in meters
 */
export const calculatePolygonPerimeter = (coordinates: number[][]): number => {
  if (!coordinates || coordinates.length < 2) {
    return 0;
  }

  let perimeter = 0;
  
  for (let i = 0; i < coordinates.length - 1; i++) {
    const [lon1, lat1] = coordinates[i];
    const [lon2, lat2] = coordinates[i + 1];
    
    const distance = calculateDistance(lat1, lon1, lat2, lon2);
    perimeter += distance;
  }

  return perimeter;
};

/**
 * Calculate distance between two points using Haversine formula
 */
export const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371000; // Earth's radius in meters
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return distance;
};

/**
 * Calculate bounding box of a polygon
 */
export const calculateBoundingBox = (coordinates: number[][]): BoundingBox => {
  if (!coordinates || coordinates.length === 0) {
    throw new Error('Invalid coordinates for bounding box calculation');
  }

  let north = -90, south = 90, east = -180, west = 180;

  coordinates.forEach(([lon, lat]) => {
    if (lat > north) north = lat;
    if (lat < south) south = lat;
    if (lon > east) east = lon;
    if (lon < west) west = lon;
  });

  return { north, south, east, west };
};

/**
 * Check if a polygon is self-intersecting
 */
export const isPolygonSelfIntersecting = (coordinates: number[][]): boolean => {
  if (!coordinates || coordinates.length < 4) {
    return false;
  }

  // Simple check for self-intersection using line segment intersection
  for (let i = 0; i < coordinates.length - 1; i++) {
    for (let j = i + 2; j < coordinates.length - 1; j++) {
      // Skip adjacent segments
      if (j === i + 1 || (i === 0 && j === coordinates.length - 2)) {
        continue;
      }

      const seg1 = [coordinates[i], coordinates[i + 1]];
      const seg2 = [coordinates[j], coordinates[j + 1]];

      if (doLineSegmentsIntersect(seg1[0], seg1[1], seg2[0], seg2[1])) {
        return true;
      }
    }
  }

  return false;
};

/**
 * Check if two line segments intersect
 */
const doLineSegmentsIntersect = (p1: number[], p2: number[], p3: number[], p4: number[]): boolean => {
  const [x1, y1] = p1;
  const [x2, y2] = p2;
  const [x3, y3] = p3;
  const [x4, y4] = p4;

  const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
  
  if (Math.abs(denom) < 1e-10) {
    return false; // Lines are parallel
  }

  const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
  const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

  return t >= 0 && t <= 1 && u >= 0 && u <= 1;
};

/**
 * Format area for display (converts square meters to appropriate units)
 */
export const formatArea = (areaInSquareMeters: number): string => {
  if (areaInSquareMeters < 10000) {
    return `${Math.round(areaInSquareMeters)} m²`;
  } else {
    const hectares = areaInSquareMeters / 10000;
    if (hectares < 100) {
      return `${hectares.toFixed(2)} ha`;
    } else {
      const squareKm = hectares / 100;
      return `${squareKm.toFixed(2)} km²`;
    }
  }
};

/**
 * Format distance for display
 */
export const formatDistance = (distanceInMeters: number): string => {
  if (distanceInMeters < 1000) {
    return `${Math.round(distanceInMeters)} m`;
  } else {
    const km = distanceInMeters / 1000;
    return `${km.toFixed(2)} km`;
  }
};

/**
 * Convert coordinates from different formats to standard [longitude, latitude] format
 */
export const normalizeCoordinates = (coords: any[]): number[][] => {
  return coords.map(coord => {
    if (Array.isArray(coord) && coord.length >= 2) {
      // Assume [longitude, latitude] format
      return [coord[0], coord[1]];
    } else if (coord.lat !== undefined && coord.lng !== undefined) {
      // Convert {lat, lng} format
      return [coord.lng, coord.lat];
    } else if (coord.latitude !== undefined && coord.longitude !== undefined) {
      // Convert {latitude, longitude} format
      return [coord.longitude, coord.latitude];
    }
    throw new Error('Invalid coordinate format');
  });
};
