const title = "Excavation Permit to Work";
const description = "This permit is valid for one day ONLY. Tick as appropriate."

const excavationFormData = [
    {
        "Details": [
            {
                name: "PTW Ref.No",
                type: "text",
                required: true
            },
            {
                name: "Starting from",
                type: "datetime",
                required: true
            },
            {
                name: "Ending at",
                type: "datetime",
                required: true
            },
        ]
    },
    {
        "Other permits in use": [
            {
                name: "GWP(Indicate the number)",
                type: "text",
                required: false
            },
            {
                name: "Depth of excavation",
                type: "text",
                required: true
            }
        ]
    },
    {
        "Protection system": [
            {
                name: "Sloping",
                type: "checkbox",
                required: false
            },
            {
                name: "Benching",
                type: "checkbox",
                required: false
            },
            {
                name: "Shoring",
                type: "checkbox",
                required: false
            },
            {
                name: "Other(Specify)",
                type: "text",
                required: false
            }
        ]
    },
    {
        "Description of work": [
            {
                name: "Description of work",
                type: "text",
                required: true
            },
            {
                name: "Location",
                type: "text",
                required: true
            },
        ]
    },
    {
        "List of equipment to be engaged": {
            type: "textarea",
            required: false
        }
    },
    {
        "Hazards": [
            {
                name: "Hazards",
                type: "textarea",
                required: true
            }
        ]
    },
    {
        "Documents to be attached": [
            {
                name: "Method statement",
                type: "checkbox",
                required: false
            },
            {
                name: "Risk assessment",
                type: "checkbox",
                required: false
            },
            {
                name: "Emergency rescue plan",
                type: "checkbox",
                required: false
            },
            {
                name: "Other(Specify)",
                type: "text",
                required: false
            },

        ]
    },
    {
        'Precautions Required':{
            // This is a table with three columns "Precaution required", "Required",and "Initial(e.g V.K)" the "Precaution required" is filled by values below
            // "Required" are checkboxes" while "Initial(e.g V.K)" are text inputs
            "Precaution required values":["Shoring", "Pre excavation assessment", "Benching","Access ladders","Emergency preparedness and response", "Regular atmosphere testing","Barrication of work area", "Post signages", "Job rotation and rests","Inspection of equipment", "Supervision", "PPE(steel toe safety shoes, hard hats, clear impact resistant glasses, overalls)", "Buddy system", "Edge protection"]
        }
    },
    {
        "Inspections": {
            description: "The following areas/items have been inspected by the issuer and the receiver.",
            items: [
                {
                    name: "Danger/warning signs",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Buddy system",
                    type: "checkbox",
                    required: false
                },

                {
                    name: "Rescue",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Interface",
                    type: "checkbox",
                    required: false
                },


                {
                    name: "Safety barriers",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Lighting",
                    type: "checkbox",
                    required: false
                },

                {
                    name: "Guard rail system",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Ground stability",
                    type: "checkbox",
                    required: false
                },


                {
                    name: "Competence of the operatives",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Weather",
                    type: "checkbox",
                    required: false
                },

                {
                    name: "Access and egress",
                    type: "checkbox",
                    required: false
                },

                {
                    name: "Other(Specify)",
                    type: "text",
                    required: false
                },
            ],
            authorization: [
                {
                    name: "Name of Inspector",
                    type: "text",
                    required: true
                },
                {
                    name: "Designation",
                    type: "text",
                    required: true
                },
                {
                    name: "Date of inspection",
                    type: "date",
                    required: true
                },
                {
                    name: "Comments",
                    type: "text",
                    required: false
                }
            ]

        }
    },
    {
        "PPE": [
            {
                name: "Hard hats with chin straps",
                type: "checkbox",
                required: false
            },
            {
                name: "Clear impact resistant glasses",
                type: "checkbox",
                required: false
            },
            {
                name: "HiVis Reflective vests.",
                type: "checkbox",
                required: false
            },
            {
                name: "Respiratory protection",
                type: "checkbox",
                required: false
            },
            {
                name: "Hearing protection (Ear plugs, Ear muffs)",
                type: "checkbox",
                required: false
            },
            {
                name: "Overalls",
                type: "checkbox",
                required: false
            },
            {
                name: "Cut resistant gloves",
                type: "checkbox",
                required: false
            },
            {
                name: "Steal toe safety shoe",
                type: "checkbox",
                required: false
            },
            {
                name: "Other(Specify)",
                type: "text",
                required: false
            }
        ]
    },
    {
        "Permit Issue": {
            "description": "I have read this permit and confirm that the location where the work outlined is being carried out has been personally examined and the precautions have been checked as indicated. The permit requirements shall be communicated to all the persons involved in the work/task and I will immediatley notify the issuer of any changes to the conditions governing this permit.",
            "items": [
                {
                    "Competent Person (Permit Receiver)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorizing Person (Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]

        }
    },
    {
        "Permit Return": {
            "description": "I accept that this work has been completed satisfactorily, the personnel and the equipment have been inspected and tagged as appropriate. The work area has been properly housekept.",
            "items": [
                {
                    "Competent Person (Permit Receiver)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorizing Person (Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]

        }
    },
];

export { title, description, excavationFormData };