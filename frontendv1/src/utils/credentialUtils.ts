// Utility functions for credential management in worker creation

import { 
  WorkerCredential, 
  TemporaryCredential, 
  PermanentCredential,
  CredentialType,
  CredentialStatus,
  CredentialNamingContext,
  AutoGeneratedCredentialName,
  CredentialValidationResult
} from '../types/credentials';

/**
 * Auto-generate credential file names based on worker and training context
 */
export const generateCredentialName = (context: CredentialNamingContext): AutoGeneratedCredentialName => {
  const { workerName, credentialType, category, trainingName, issuingAuthority, issueDate } = context;
  
  // Clean worker name for file naming
  const cleanWorkerName = workerName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
  const cleanCategory = category.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
  const cleanAuthority = issuingAuthority.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
  const dateStr = new Date(issueDate).toISOString().split('T')[0]; // YYYY-MM-DD
  
  let fileName: string;
  let displayName: string;
  let folderPath: string;
  
  if (credentialType === 'PERMANENT') {
    fileName = `${cleanWorkerName}_${cleanCategory}_${cleanAuthority}_${dateStr}`;
    displayName = `${workerName} - ${category} - ${issuingAuthority}`;
    folderPath = `workers/${cleanWorkerName}/permanent_credentials`;
  } else {
    const cleanTrainingName = trainingName?.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase() || cleanCategory;
    fileName = `${cleanWorkerName}_${cleanTrainingName}_${dateStr}`;
    displayName = `${workerName} - ${trainingName || category} - ${issueDate}`;
    folderPath = `workers/${cleanWorkerName}/temporary_credentials`;
  }
  
  return {
    fileName,
    displayName,
    folderPath
  };
};

/**
 * Calculate days until expiry for temporary credentials
 */
export const calculateDaysUntilExpiry = (expiryDate: string): number => {
  const expiry = new Date(expiryDate);
  const today = new Date();
  const diffTime = expiry.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Determine credential status based on expiry date
 */
export const determineCredentialStatus = (credential: WorkerCredential): CredentialStatus => {
  if (credential.type === 'PERMANENT') {
    return 'VALID'; // Permanent credentials don't expire
  }
  
  const tempCredential = credential as TemporaryCredential;
  const daysUntilExpiry = calculateDaysUntilExpiry(tempCredential.expiryDate);
  
  if (daysUntilExpiry < 0) {
    return 'EXPIRED';
  } else if (daysUntilExpiry <= 30) {
    return 'EXPIRING';
  } else {
    return 'VALID';
  }
};

/**
 * Update credential with calculated fields
 */
export const enrichCredential = (credential: WorkerCredential): WorkerCredential => {
  const status = determineCredentialStatus(credential);
  
  if (credential.type === 'TEMPORARY') {
    const tempCredential = credential as TemporaryCredential;
    const daysUntilExpiry = calculateDaysUntilExpiry(tempCredential.expiryDate);
    
    return {
      ...tempCredential,
      status,
      daysUntilExpiry,
      isExpiringSoon: daysUntilExpiry <= 30 && daysUntilExpiry >= 0
    };
  }
  
  return {
    ...credential,
    status
  };
};

/**
 * Validate credential data
 */
export const validateCredential = (credential: Partial<WorkerCredential>): CredentialValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Basic validation
  if (!credential.name?.trim()) {
    errors.push('Credential name is required');
  }
  
  if (!credential.issuingAuthority?.trim()) {
    errors.push('Issuing authority is required');
  }
  
  if (!credential.issueDate) {
    errors.push('Issue date is required');
  } else {
    const issueDate = new Date(credential.issueDate);
    const today = new Date();
    
    if (issueDate > today) {
      errors.push('Issue date cannot be in the future');
    }
  }
  
  // Type-specific validation
  if (credential.type === 'TEMPORARY') {
    const tempCred = credential as Partial<TemporaryCredential>;
    
    if (!tempCred.expiryDate) {
      errors.push('Expiry date is required for temporary credentials');
    } else {
      const expiryDate = new Date(tempCred.expiryDate);
      const issueDate = new Date(credential.issueDate!);
      
      if (expiryDate <= issueDate) {
        errors.push('Expiry date must be after issue date');
      }
      
      const daysUntilExpiry = calculateDaysUntilExpiry(tempCred.expiryDate);
      if (daysUntilExpiry < 0) {
        warnings.push('This credential has already expired');
      } else if (daysUntilExpiry <= 30) {
        warnings.push('This credential expires within 30 days');
      }
    }
    
    if (!tempCred.trainingProvider?.trim()) {
      errors.push('Training provider is required for temporary credentials');
    }
    
    if (!tempCred.renewalPeriodMonths || tempCred.renewalPeriodMonths <= 0) {
      errors.push('Valid renewal period is required for temporary credentials');
    }
  } else if (credential.type === 'PERMANENT') {
    const permCred = credential as Partial<PermanentCredential>;
    
    if (!permCred.institution?.trim()) {
      errors.push('Institution is required for permanent credentials');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Sort credentials by priority (expiring first, then by issue date)
 */
export const sortCredentialsByPriority = (credentials: WorkerCredential[]): WorkerCredential[] => {
  return [...credentials].sort((a, b) => {
    // Expired credentials first
    if (a.status === 'EXPIRED' && b.status !== 'EXPIRED') return -1;
    if (b.status === 'EXPIRED' && a.status !== 'EXPIRED') return 1;
    
    // Expiring credentials next
    if (a.status === 'EXPIRING' && b.status !== 'EXPIRING') return -1;
    if (b.status === 'EXPIRING' && a.status !== 'EXPIRING') return 1;
    
    // For temporary credentials, sort by expiry date
    if (a.type === 'TEMPORARY' && b.type === 'TEMPORARY') {
      const aTemp = a as TemporaryCredential;
      const bTemp = b as TemporaryCredential;
      return new Date(aTemp.expiryDate).getTime() - new Date(bTemp.expiryDate).getTime();
    }
    
    // Sort by issue date (newest first)
    return new Date(b.issueDate).getTime() - new Date(a.issueDate).getTime();
  });
};

/**
 * Filter credentials by type
 */
export const filterCredentialsByType = (
  credentials: WorkerCredential[], 
  type: CredentialType
): WorkerCredential[] => {
  return credentials.filter(cred => cred.type === type);
};

/**
 * Get credential summary statistics
 */
export const getCredentialSummary = (credentials: WorkerCredential[]) => {
  const permanent = filterCredentialsByType(credentials, 'PERMANENT');
  const temporary = filterCredentialsByType(credentials, 'TEMPORARY');
  
  const valid = credentials.filter(c => c.status === 'VALID');
  const expiring = credentials.filter(c => c.status === 'EXPIRING');
  const expired = credentials.filter(c => c.status === 'EXPIRED');
  
  // Find next expiry date
  const temporaryWithExpiry = temporary as TemporaryCredential[];
  const nextExpiry = temporaryWithExpiry
    .filter(c => c.status !== 'EXPIRED')
    .sort((a, b) => new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime())[0];
  
  return {
    total: credentials.length,
    permanent: permanent.length,
    temporary: temporary.length,
    valid: valid.length,
    expiring: expiring.length,
    expired: expired.length,
    nextExpiryDate: nextExpiry?.expiryDate,
    nextExpiryDays: nextExpiry ? calculateDaysUntilExpiry(nextExpiry.expiryDate) : null
  };
};

/**
 * Check if file type is valid for credential documents
 */
export const isValidCredentialFileType = (file: File): boolean => {
  const validTypes = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp'
  ];
  
  return validTypes.includes(file.type);
};

/**
 * Check if file size is within limits
 */
export const isValidCredentialFileSize = (file: File, maxSizeMB: number = 10): boolean => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
};
