const formTitle = "Work at Height Permit";
const formDescription = "This permit is valid for ONE day only.";

const workHeightPermitData = [
    {
        "Details": [
            {
                name: "PTW Ref.No",
                type: "text",
                required: true
            },
            {
                name: "Starting from",
                type: "datetime",
                required: true
            },
            {
                name: "Ending at",
                type: "datetime",
                required: true
            },
        ]
    },
    {
        "Other permits in use": [
            {
                name: "GWP",
                type: "checkbox",
                required: false
            },
            {
                name: "HWP",
                type: "checkbox",
                required: false
            },
            {
                name: "Electrical",
                type: "checkbox",
                required: false
            },
            {
                name: "CSE",
                type: "checkbox",
                required: false
            },
        ]
    },
    {
        "Mode of access to be used": [
            {
                name: "Scaffolding",
                type: "checkbox",
                required: false
            },
            {
                name: "Ladder(for tasks not exceeding 30 minutes)",
                type: "checkbox",
                required: false
            },
            {
                name: "Aerial lifts",
                type: "checkbox",
                required: false
            },
            {
                name: "Staircase",
                type: "checkbox",
                required: false
            },
            {
                name: "Other(specify)",
                type: "text",
                required: false
            },
        ]
    },
    {
        "Description of work": [
            {
                name: "Description of work",
                type: "textarea",
                required: true
            },
            {
                name: "Location",
                type: "text",
                required: true
            },
        ]
    },
    {
        "Hazards": [
            {
                name: "Hazards",
                type: "textarea",
                required: true
            }
        ]
    },
    {
        "Documents to be attached": [
            {
                name: "Method statement",
                type: "checkbox",
                required: false
            },
            {
                name: "Risk assessment",
                type: "checkbox",
                required: false
            },
            {
                name: "Emergency rescue plan",
                type: "checkbox",
                required: false
            },
            {
                name: "SSWP",
                type: "checkbox",
                required: false
            },
            {
                name: "Other(Specify)",
                type: "text",
                required: false
            },
        ]
    },
    {
        "Precautions required": [
            {
                name: "Precautions required",
                type: "textarea",
                required: true
            }
        ]
    },
    {
        "Inspections": {
            description: "The following areas/items have been inspected by the issuer and the receiver.",
            items: [
                {
                    name: "Danger/warning signs",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Buddy system",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Scaffold tag system",
                    type: "checkbox",
                    required: false
                },

                {
                    name: "Rescue",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Man basket",
                    type: "checkbox",
                    required: false
                },


                {
                    name: "Safety barriers",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Lighting",
                    type: "checkbox",
                    required: false
                },

                {
                    name: "Ground stability",
                    type: "checkbox",
                    required: false
                },


                {
                    name: "Competence of the operatives",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Weather",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Toolbox talk",
                    type: "checkbox",
                    required: false
                },

                {
                    name: "Access and egress",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Lifeline",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "guard rail system",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Use of spirit level",
                    type: "checkbox",
                    required: false
                },

                {
                    name: "Interface",
                    type: "checkbox",
                    required: false
                },
                {
                    name: "Other(Specify)",
                    type: "text",
                    required: false
                },
            ],
            authorization: [
                {
                    name: "Name of Inspector",
                    type: "text",
                    required: true
                },
                {
                    name: "Designation",
                    type: "text",
                    required: true
                },
                {
                    name: "Date of inspection",
                    type: "date",
                    required: true
                },
                {
                    name: "Comments",
                    type: "text",
                    required: false
                }
            ]

        }
    },
    {
        "PPE": [
            {
                name: "Hard hats with chin straps",
                type: "checkbox",
                required: false
            },
            {
                name: "Clear impact resistant glasses",
                type: "checkbox",
                required: false
            },
            {
                name: "Cut resistant gloves",
                type: "checkbox",
                required: false
            },
            {
                name: "Hearing protection (Ear plugs, Ear muffs)",
                type: "checkbox",
                required: false
            },
            {
                name: "Overalls",
                type: "checkbox",
                required: false
            },
            {
                name: "Respiratory protection",
                type: "checkbox",
                required: false
            },
            {
                name: "Steel toe safety shoe",
                type: "checkbox",
                required: false
            },
            {
                name: "HiVis Reflective vests.",
                type: "checkbox",
                required: false
            },
            {
                name: "Other(Specify)",
                type: "text",
                required: false
            }
        ]
    },
    {
        "Permit Issue": {
            "description": "I have read this permit and confirm that the location where the work outlined is being carried out has been personally examined and the precautions have been checked as indicated. The permit requirements shall be communicated to all the persons involved in the work/task and I will immediatley notify the issuer of any changes to the conditions governing this permit to work.",
            "items": [
                {
                    "Competent Person (Permit Receiver)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorizing Person (Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]

        }
    },
    {
        "Permit Return": {
            "description": "I accept that this work has been completed satisfactorily, the personnel and the equipment have been inspected and tagged as appropriate. The work area has been properly housekept.",
            "items": [
                {
                    "Competent Person (Permit Receiver)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorizing Person (Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]

        }
    },
    {
        title: "Sign off",
        type: "signoff",
        description: "By signing this work at height permit, I accept to abide by the instituted control measures that will enhance safe working at height",
        items: [
            {
                name: "Safety supervisor",
                type: "text",
                required: true
            },
            {
                name: "Supervisor Signature",
                type: "signature",
                required: true
            }
        ],
        tableHeader: [
            {
                name: "Name",
                type: "text"
            },
            {
                name: "Designation",
                type: "text"
            },
            {
                name: "Signature",
                type: "signature"
            },
            {
                name: "Time",
                type: "time"
            }
        ]
    }
]

export { workHeightPermitData, formTitle, formDescription };