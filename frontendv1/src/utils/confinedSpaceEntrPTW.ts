const title = "Confined Space PTW Sign off.";

const confinedSpaceFormData = [
    {
        "Basic Information": [
            {
                name: "PTW Ref No",
                type: "text",
                required: true
            },
            {
                name: "Project Name",
                type: "text",
                required: true
            },
            {
                name: "Location",
                type: "text",
                required: true
            },
            {
                name: "Starting from",
                type: "datetime",
                required: true
            },
            {
                name: "Ending at",
                type: "datetime",
                required: true
            },
            {
                name: "Description of work",
                type: "textarea",
                required: true
            }
        ]
    },
    {
        "Training Verification": [
            {
                name: "Entrants and observer trained on Confined Space Entry and rescue within the last 2 years",
                type: "checkbox",
                required: true
            },
            {
                name: "Name of Training Organization",
                type: "text",
                required: true
            }
        ]
    },
    {
        title: "Confined space (checked at 3 levels) free from hazardous fumes",
        specialSection: true,
        items: [
            {
                "Top reading": [
                    {
                        name: "Oxygen",
                        extraDetails: "(19.5-23.5%)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "Explosive",
                        extraDetails: "(LEL<20%)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "Toxic",
                        extraDetails: "(<10ppm H2S)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "CO2",
                        extraDetails: "(<1%)",
                        type: "text",
                        required: true
                    }
                ],
                "Mid reading": [
                    {
                        name: "Oxygen",
                        extraDetails: "(19.5-23.5%)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "Explosive",
                        extraDetails: "(LEL<20%)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "Toxic",
                        extraDetails: "(<10ppm H2S)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "CO2",
                        extraDetails: "(<1%)",
                        type: "text",
                        required: true
                    }
                ],
                "Bottom reading": [
                    {
                        name: "Oxygen",
                        extraDetails: "(19.5-23.5%)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "Explosive",
                        extraDetails: "(LEL<20%)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "Toxic",
                        extraDetails: "(<10ppm H2S)",
                        type: "text",
                        required: true
                    },
                    {
                        name: "CO2",
                        extraDetails: "(<1%)",
                        type: "text",
                        required: true
                    }
                ]
            }
        ]
    },
    {
        "Hazards": [
            {
                name: "Hazards",
                type: "textarea",
                required: true
            }
        ]
    },
    {
        "Precautions required": [
            {
                name: "Precautions required",
                type: "textarea",
                required: true
            }
        ]
    },
    {
        title: "Emergency guidelines to be adhered with during entry into the confined spaces.",
        type: "guidelines",
        points: [
            "Entrant must wear personal monitor at all times. Upon hearing the alarm, evacuate immediately.",
            "In the event of the fire alarm, the observer must ensure the person in the confined space comes out immediately.",
            "Access into the confined space has to be provided and maintained in position at all times.",
            "Entrant must have the rescue set on their person or within reach at all times inside the confined space.",
            "If the watch person must leave the opening, cover must be organized or the entrant removed from the confined space until their return.",
            "In the event of an emergency the watch person must raise the alarm before attempting (non entry) rescue. Never enter the confined space."
        ],
        boldedPoint: "Please specify additional emergency arrangements:",
        additionalArrangements: {
            name: "Additional Emergency Arrangements",
            type: "textarea",
            required: false
        }
    },
    {
        "Permit Issue": {
            description: "I have read this permit and confirm that the location where the work outlined is being carried out has been personally examined and the precautions have been checked as indicated. The permit requirements shall be communicated to all the persons involved in the work/task and I will immediately notify the issuer of any changes to the conditions governing this permit.",
            items: [
                {
                    "Competent Person (Permit Receiver)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        }
                    ]
                },
                {
                    "Authorizing Person (Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        }
                    ]
                }
            ]
        }
    },
    {
        "Permit Return": {
            description: "I accept that this work has been completed satisfactorily, the personnel and the equipment have been inspected and tagged as appropriate. The work area has been properly housekept, OR this work was unsafe hence incomplete. It will require a new permit before the recommencement of the task and the work area has been left in a safe and in order.",
            items: [
                {
                    "Competent Person (Permit Receiver)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        }
                    ]
                },
                {
                    "Authorizing Person (Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        }
                    ]
                }
            ]
        }
    },
    {
        title: "Sign off",
        type: "signoff",
        description: "By signing this confined space entry permit, I accept to abide by the instituted control measures that will enhance safe working",
        items: [
            {
                name: "Task observer",
                type: "text",
                required: true
            },
            {
                name: "Observer Signature",
                type: "signature",
                required: true
            }
        ],
        tableHeader: [
            {
                name: "Name",
                type: "text"
            },
            {
                name: "Designation",
                type: "text"
            },
            {
                name: "Signature",
                type: "signature"
            },
            {
                name: "Time",
                type: "time"
            }
        ]
    }
];

export { title, confinedSpaceFormData };