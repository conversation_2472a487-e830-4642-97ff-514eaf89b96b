export const sheet_1 = [
    {
        "details": [
            {
                name: "PTW Ref No",
                type: "text",
                required: true
            },
            {
                name: "Project Name",
                type: "text",
                required: true
            },
            {
                name: "No. of employees involved",
                type: "number",
                required: true
            },
            {
                name: "Starting from",
                type: "datetime",
                required: true
            },
            {
                name: "Expected completion",
                type: "datetime",
                required: true
            },
        ]
    },
    {
        "Other Permits in use": [
            {
                name: "WAH",
                type: "checkbox",
                required: true
            },
            {
                name: "HWP",
                type: "checkbox",
                required: true
            },
            {
                name: "Excavation Permit",
                type: "checkbox",
                required: true
            },
            {
                name: "Electric Permit",
                type: "checkbox",
                required: true
            },
        ]
    },
    {
        "Description of work": [
            {
                name: "Description of work",
                type: "textarea",
                required: true
            },
            {
                name: "Location",
                type: "text",
                required: true
            },
        ]
    },
    {
        "Hazards": [
            {
                name: "Unprotected edges",
                type: "checkbox",
                required: true
            },
            {
                name: "Slips, traps and falls",
                type: "checkbox",
                required: true
            },
            {
                name: "Adverse weather",
                type: "checkbox",
                required: true
            },
            {
                name: "Manual handling",
                type: "checkbox",
                required: true
            },
            {
                name: "Dust",
                type: "checkbox",
                required: true
            },
            {
                name: "Flying objects",
                type: "checkbox",
                required: true
            },
            {
                name: "Falling objects",
                type: "checkbox",
                required: true
            },

            {
                name: "Obstructed access",
                type: "checkbox",
                required: true
            },
            {
                name: "Interface",
                type: "checkbox",
                required: true
            },
            {
                name: "Fall from height",
                type: "checkbox",
                required: true
            },
            {
                name: "Fragile roof",
                type: "checkbox",
                required: true
            },
            {
                name: "Chemicals",
                type: "checkbox",
                required: true
            },
            {
                name: "Sharp objects",
                type: "checkbox",
                required: true
            },
            {
                name: "Electricity",
                type: "checkbox",
                required: true
            },

            {
                name: "Biological",
                type: "checkbox",
                required: true
            },
            {
                name: "Noise",
                type: "checkbox",
                required: true
            },
            {
                name: "Open trenches",
                type: "checkbox",
                required: true
            },
            {
                name: "Snapping nails",
                type: "checkbox",
                required: true
            },
            {
                name: "Cave in",
                type: "checkbox",
                required: true
            },
            {
                name: "Uneven surfaces",
                type: "checkbox",
                required: true
            },
            {
                name: "Poor lighting",
                type: "checkbox",
                required: true
            },


            {
                name: "Existing services",
                type: "checkbox",
                required: true
            },
            {
                name: "Trailing cables",
                type: "checkbox",
                required: true
            },
            {
                name: "Exposed cables",
                type: "checkbox",
                required: true
            },
            {
                name: "Vibrations",
                type: "checkbox",
                required: true
            },
            {
                name: "Rotating parts",
                type: "checkbox",
                required: true
            },
            {
                name: "Awkard postures",
                type: "checkbox",
                required: true
            },
            {
                name: "Defective ladder",
                type: "checkbox",
                required: true
            },

        ]
    },
    {
        "Isolation": [
            {
                name: "Electricity",
                type: "checkbox",
                required: true
            },
            {
                name: "Water",
                type: "checkbox",
                required: true
            },
            {
                name: "Gas",
                type: "checkbox",
                required: true
            },
            {
                name: "Moving parts",
                type: "checkbox",
                required: true
            },
        ]
    },
    {
        "Documents to be attached/availed at the work area": [
            {
                name: "Method statement",
                type: "checkbox",
                required: true
            },
            {
                name: "Risk assessment",
                type: "checkbox",
                required: true
            },
            {
                name: "Trainings",
                type: "checkbox",
                required: true
            },
            {
                name: "MSDS",
                type: "checkbox",
                required: true
            },
            {
                name: "JHA",
                type: "checkbox",
                required: true
            },
        ]
    },
    {
        "Precautions required": [
            {
                name: "Institution of the guard rails(top and mid)",
                type: "checkbox",
                required: true
            },
            {
                name: "Postining of the warning signs",
                type: "checkbox",
                required: true
            },
            {
                name: "Close supervision",
                type: "checkbox",
                required: true
            },
            {
                name: "Training on manual handling",
                type: "checkbox",
                required: true
            },
            {
                name: "Clear walkways",
                type: "checkbox",
                required: true
            },
            {
                name: "Continuous housekeeping",
                type: "checkbox",
                required: true
            },
            {
                name: "Use of full body harness",
                type: "checkbox",
                required: true
            },
            {
                name: "Adhere to MSDS",
                type: "checkbox",
                required: true
            },

            {
                name: "Barrication of the work area",
                type: "checkbox",
                required: true
            },
            {
                name: "Institution of eye wash station",
                type: "checkbox",
                required: true
            },
            {
                name: "No loose clothing",
                type: "checkbox",
                required: true
            },
            {
                name: "Buddy system",
                type: "checkbox",
                required: true
            },

            {
                name: "Inspection of all equipment",
                type: "checkbox",
                required: true
            },
            {
                name: "Drop zone",
                type: "checkbox",
                required: true
            },
            {
                name: "Maintain three point of contact",
                type: "checkbox",
                required: true
            },
            {
                name: "Access to work area controlled",
                type: "checkbox",
                required: true
            },

            {
                name: "Other(Specify)",
                type: "textarea",
                required: true
            },
        ]
    },
    {
        "PPE": [
            {
                name: "Clear impact goggles/glass",
                type: "checkbox",
                required: true
            },
            {
                name: "Cut resistant gloves",
                type: "checkbox",
                required: true
            },
            {
                name: "Hard hats",
                type: "checkbox",
                required: true
            },
            {
                name: "Ear plugs",
                type: "checkbox",
                required: true
            },
            {
                name: "Full body harness with double lanyards",
                type: "checkbox",
                required: true
            },

            {
                name: "Coveralls",
                type: "checkbox",
                required: true
            },
            {
                name: "Sheppards hooks",
                type: "checkbox",
                required: true
            },
            {
                name: "Steel toe safety shoes",
                type: "checkbox",
                required: true
            },
            {
                name: "Steel toe gumboots",
                type: "checkbox",
                required: true
            },
            {
                name: "Face shield",
                type: "checkbox",
                required: true
            },

            {
                name: "Rubber/latex gloves",
                type: "checkbox",
                required: true
            },
            {
                name: "Dust suits",
                type: "checkbox",
                required: true
            },
            {
                name: "Respiratory mask",
                type: "checkbox",
                required: true
            },
            {
                name: "Ear muffs",
                type: "checkbox",
                required: true
            },

            {
                name: "Aprons",
                type: "checkbox",
                required: true
            },
            {
                name: "Welding goggles",
                type: "checkbox",
                required: true
            },
            {
                name: "HiVis reflective vests",
                type: "checkbox",
                required: true
            },
            {
                name: "Leather gloves",
                type: "checkbox",
                required: true
            },
            {
                name: "Other(Specify)",
                type: "textarea",
                required: true
            },

        ]
    },
    {
        "Permit Issue": {
            description: "I have read this permit and confirm that the location where the work outlined is being carried out has been personally examined and the precautions have been checked as indicated. The permit requirements shall be communicated to all the persons involved in the work/task and I will immediately notify the issuer of any changes to the conditions governing this permit.",
            items: [
                {
                    "Competent person(Permit Received)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "datetime",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorised person(Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]
        }
    },
    {
        "Permit Return": {
            description: "I accept that this work has been completed satisfactorily, the personnel and the equipment have been inspected and tagged as appropriate. The work area has been properly housekept, OR this work was unsafe hence incomplete. It will require a new permit before the recommencement of the task and the task and the work area has been left in a safe and in order.",
            items: [
                {
                    "Competent person(Permit Received)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "datetime",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorised person(Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]
        }
    },
]
export const sheet_2 = {
    title: "WORK AREA INSPECTION AND PERMIT RENEWAL BY THE AUTHORIZED PERSON",
    tableHeader: [
        // all are required
        {
            name: "Date",
            type: "date"
        },
        {
            name: "Time",
            type: "time"
        },
        {
            name: "Comments",
            type: "text"
        },
        {
            name: "signature",
            type: "signature"
        },
    ]
}
export const sheet_3 = {
    title: "PTW Sign off",
    description: "By signing this permit to work, I accept to abide by the instituted control measures that will enhance safe working",
    firstRow: [
        {
            name: "Date",
            type: "date"
        },
        {
            name: "Time",
            type: "time"
        },
    ],
    tableHeader: [
        // all are required
        // table should have a number row for each row
        {
            name: "Name",
            type: "text"
        },
        {
            name: "Designation",
            type: "text"
        },
        {
            name: "Signature",
            type: "signature"
        },
    ]
}