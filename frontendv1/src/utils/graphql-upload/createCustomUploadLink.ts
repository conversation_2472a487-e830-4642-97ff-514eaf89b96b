import { ApolloLink, Observable, FetchResult } from '@apollo/client';

export const createUploadLinkWithFormData = (uri: string): ApolloLink => {
  return new ApolloLink(operation => {
    return new Observable<FetchResult>(observer => {
      const { query, variables, operationName } = operation;

      const formData = new FormData();

      const { clone, files, map } = extractFiles(variables);

      formData.append('operations', JSON.stringify({
        query: query.loc?.source.body,
        variables: clone,
        operationName
      }));

      formData.append('map', JSON.stringify(map));
      Object.entries(files).forEach(([key, file]) => {
        formData.append(key, file as Blob);
      });

      fetch(uri, {
        method: 'POST',
        body: formData,
        // credentials: 'include'
        headers: {
          'GraphQL-Preflight': 'true'
        }
      })
        .then(async res => {
          const json = await res.json();
          observer.next(json);
          observer.complete();
        })
        .catch(err => observer.error(err));
    });
  });
};

function extractFiles(variables: any) {
  let i = 0;
  const map: Record<string, string[]> = {};
  const files: Record<string, File | Blob> = {};

  const recurse = (obj: any, path: string): any => {
    if (obj instanceof File || obj instanceof Blob) {
      const index = `${i++}`;
      map[index] = [path];
      files[index] = obj;
      return null;
    }

    if (Array.isArray(obj)) {
      return obj.map((v, idx) => recurse(v, `${path}.${idx}`));
    }

    if (typeof obj === 'object' && obj !== null) {
      const result: Record<string, any> = {};
      for (const key in obj) {
        result[key] = recurse(obj[key], `${path}.${key}`);
      }
      return result;
    }

    return obj;
  };

  const clone = recurse(variables, 'variables');
  return { clone, files, map };
}
