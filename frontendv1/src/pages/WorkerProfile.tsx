import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import React from 'react';
import { Pencil, Trash2 } from 'lucide-react';
// import { useQuery } from '@apollo/client';
import FloatingCard from '../components/layout/FloatingCard';
import VSCodeInterface, { VSCodeTab } from '../components/shared/VSCodeInterface';
import createWorkerExplorerItems from '../components/workers/WorkerExplorer';
import WorkerTabs from '../components/workers/WorkerTabs';
import {  SiteInfo,   TimeLog, Worker } from '../types';
import { GET_WORKER_BY_ID } from '../graphql/queries';
import { FILE_BASE_URL } from '../utils/constants';
import { mockCompanyWorkers } from '../data/workers';

// Mock data
const mockSite: SiteInfo = {
  id: "site1",
  name: "Westlands Construction Site",
  healthStatus: "green",
  workersOnSite: 42,
  activePermits: 8,
  openIncidents: 0,
  projectManager: "<PERSON>",
  location: "Waiyaki Way, Westlands, Nairobi",
  timeline: "Jan 2025 - Dec 2026",
  currentPhase: "Foundation",
  progressPercentage: 25,
  tenantId: '',
  status: 'active',
  createdAt: new Date()
};

// Mock worker will be replaced by GraphQL data

const mockTimeLogs: TimeLog[] = [
  {
    id: 'log1',
    workerId: 'w1',
    workerName: 'David Kamau',
    workerTrade: 'Carpenter',
    date: '2025-05-30',
    clockIn: '07:55',
    clockOut: '17:05',
    breakDuration: 60,
    totalHours: 8,
    overtime: 0,
    status: 'on-site',
    toolboxTalkAttended: true,
    isVerifiedByHikvision: true,
    hikvisionPersonId: 'hik_person_w1',
    terminalId: 'term1'
  },
  {
    id: 'log2',
    workerId: 'w1',
    workerName: 'David Kamau',
    workerTrade: 'Carpenter',
    date: '2025-05-29',
    clockIn: '07:50',
    clockOut: '17:10',
    breakDuration: 60,
    totalHours: 8,
    overtime: 0.25,
    status: 'on-site',
    toolboxTalkAttended: true,
    isVerifiedByHikvision: true,
    hikvisionPersonId: 'hik_person_w1',
    terminalId: 'term2'
  },
  {
    id: 'log3',
    workerId: 'w1',
    workerName: 'David Kamau',
    workerTrade: 'Carpenter',
    date: '2025-05-28',
    clockIn: '08:10',
    clockOut: '17:00',
    breakDuration: 60,
    totalHours: 7.75,
    overtime: 0,
    status: 'late',
    toolboxTalkAttended: false,
    isVerifiedByHikvision: false,
    isManuallyEdited: true,
    editReason: 'Terminal was offline - manual entry'
  },
  {
    id: 'log4',
    workerId: 'w1',
    workerName: 'David Kamau',
    workerTrade: 'Carpenter',
    date: '2025-05-27',
    clockIn: '07:45',
    clockOut: '18:15',
    breakDuration: 60,
    totalHours: 9.5,
    overtime: 1.5,
    status: 'on-site',
    toolboxTalkAttended: true,
    isVerifiedByHikvision: true,
    hikvisionPersonId: 'hik_person_w1',
    terminalId: 'term1'
  }
];

const WorkerProfile = () => {
  const navigate = useNavigate();
  const { siteId, workerId } = useParams<{ siteId: string; workerId: string }>();
  const [site, _setSite] = useState<SiteInfo>(mockSite);
  const [timeLogs, _setTimeLogs] = useState<TimeLog[]>(mockTimeLogs);
  const [worker, setWorker] = useState<Worker | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [openTabs, setOpenTabs] = useState<VSCodeTab[]>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);

  // Backend disabled: skip GraphQL, use mock data
  const workerFromMock = mockCompanyWorkers.find(w => w.id === parseInt(workerId || '1')) || null;

  // Load from mock immediately
  useEffect(() => {
    setLoading(true);
    setError(null);
    if (workerFromMock) {
      setWorker(workerFromMock);
      setLoading(false);
    } else {
      setError(new Error('Worker not found'));
      setLoading(false);
    }
  }, [workerId]);

  // Initialize VSCode-like tabs when worker is loaded
  useEffect(() => {
    if (worker) {
      const aboutTab: VSCodeTab = {
        id: 'worker-about',
        title: 'About',
        type: 'details',
        data: { type: 'about', worker },
        closable: false
      };
      setOpenTabs([aboutTab]);
      setActiveTabId('worker-about');
      setSelectedItem('worker-about');
    }
  }, [worker]);

  // Handle loading and error states
  if (loading) {
    return (
      <FloatingCard title="Loading Worker Profile..." breadcrumbs={[]}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error || !worker) {
    return (
      <FloatingCard title="Error Loading Worker" breadcrumbs={[]}>
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">
            {error ? `Failed to load worker: ${error.message}` : 'Worker not found'}
          </p>
          <button
            onClick={() => {
              const mockWorker = mockCompanyWorkers.find(w => w.id === parseInt(workerId || '1'));
              if (mockWorker) setWorker(mockWorker);
            }}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            Retry
          </button>
        </div>
      </FloatingCard>
    );
  }

  // Photo upload handlers
  const handlePhotoUpload = async (file: File): Promise<string> => {
    // TODO: Implement GraphQL mutation for photo upload
    // This will call the backend Hikvision service
    const formData = new FormData();
    formData.append('photo', file);
    formData.append('workerId', worker?.id?.toString() || '0');

    try {
      const response = await fetch('/api/workers/photo', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Upload failed');

      const result = await response.json();

      // TODO: Update worker state with new photo URL
      // setWorker(prev => ({ ...prev, photoUrl: result.photoUrl }));

      return result.photoUrl;
    } catch (error) {
      console.error('Photo upload error:', error);
      throw error;
    }
  };

  const handlePhotoDelete = async (): Promise<boolean> => {
    // TODO: Implement GraphQL mutation for photo deletion
    try {
      const response = await fetch(`/api/workers/${worker?.id || 0}/photo`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Deletion failed');

      // TODO: Update worker state to remove photo URL
      // setWorker(prev => ({ ...prev, photoUrl: undefined }));

      return true;
    } catch (error) {
      console.error('Photo deletion error:', error);
      throw error;
    }
  };

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: site.name, path: `/sites/${siteId}/dashboard` },
		{ name: "Workers", path: `/sites/${siteId}/workers` },
		{ name: worker?.name || 'Worker', path: `/sites/${siteId}/workers/${workerId}` },
	];

	// VSCode-like explorer and tabs config
	const topBarActions = (
		<div className="flex space-x-2">
          <button
				onClick={() => navigate(`/sites/${siteId}/workers/${workerId}/edit`)}
				className="px-3 py-1 text-xs bg-blue-50 border border-blue-300 text-blue-700 rounded hover:bg-blue-100 transition-colors font-medium"
				style={{ borderRadius: '5px' }}
			>
				<Pencil className="h-3.5 w-3.5 inline mr-1" /> Edit
          </button>
          <button
				onClick={() => console.log('Remove/Delete worker')}
				className="px-3 py-1 text-xs bg-red-50 border border-red-300 text-red-700 rounded hover:bg-red-100 transition-colors font-medium"
				style={{ borderRadius: '5px' }}
			>
				<Trash2 className="h-3.5 w-3.5 inline mr-1" /> Remove
              </button>
            </div>
	);

	const explorerItems = createWorkerExplorerItems(worker);
	const { renderTabContent } = WorkerTabs({ worker, timeLogs, siteId, onPhotoUpload: handlePhotoUpload, onPhotoDelete: handlePhotoDelete });

	const handleItemSelect = (itemId: string, itemType: string, itemData?: any) => {
		setSelectedItem(itemId);
		const existingTab = openTabs.find(tab => tab.id === itemId);
		if (!existingTab) {
			const title = itemId === 'worker-about' ? 'About' : itemId.replace(/-/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
			const newTab: VSCodeTab = {
				id: itemId,
				title,
				type: itemData?.type || itemType,
				data: itemData,
				closable: itemId !== 'worker-about'
			};
			setOpenTabs(prev => [...prev, newTab]);
		}
		setActiveTabId(itemId);
	};

	const handleTabClose = (tabId: string) => {
		if (tabId === 'worker-about') return;
		setOpenTabs(prev => prev.filter(tab => tab.id !== tabId));
		if (activeTabId === tabId) {
			const remaining = openTabs.filter(tab => tab.id !== tabId);
			setActiveTabId(remaining.length > 0 ? remaining[remaining.length - 1].id : null);
		}
	};

	const handleTabChange = (tabId: string) => setActiveTabId(tabId);

	// Moved summary/status into About tab content

	return (
		<FloatingCard title={`${worker?.name || 'Worker'} - Profile`} breadcrumbs={breadcrumbs} layout="custom" topBarRightActions={topBarActions}>
			<div className="h-full flex flex-col min-w-0">
				{/* VSCode-like Interface */}
				<div className="flex-1 min-h-0">
					<VSCodeInterface
						explorerItems={explorerItems}
						tabs={openTabs}
						activeTabId={activeTabId}
						selectedItem={selectedItem}
						onItemSelect={handleItemSelect}
						onTabChange={handleTabChange}
						onTabClose={handleTabClose}
						renderTabContent={renderTabContent}
              />
            </div>
          </div>
    </FloatingCard>
  );
};

export default WorkerProfile;
