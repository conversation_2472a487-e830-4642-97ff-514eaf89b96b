import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Bell,
  Search,
  Check,
  Clock,
  User,
  CheckCircle,
  Calendar,
  AlertTriangle,
  ExternalLink,
  Star,
  Trash2
} from 'lucide-react';
import { useTenantContext } from '../hooks/useTenantContext';
import FloatingCard from '../components/layout/FloatingCard';

interface Notification {
  id: string;
  type: 'training_expiring' | 'training_expired' | 'training_assigned' | 'training_completed' | 'system' | 'reminder' | 'overtime_request' | 'permit_approval' | 'safety_alert';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  actionUrl?: string;
  actionLabel?: string;
  isStarred?: boolean;
  category: 'training' | 'workforce' | 'safety' | 'system';
  metadata?: {
    workerId?: number;
    workerName?: string;
    trainingId?: number;
    trainingName?: string;
    siteId?: string;
    siteName?: string;
    daysUntilExpiry?: number;
    requestId?: string;
    amount?: number;
  };
}

const NotificationsPage: React.FC = () => {
  const { tenantId } = useTenantContext();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [selected, setSelected] = useState<'all' | 'unread' | 'starred' | 'training' | 'workforce' | 'safety' | 'system'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'priority'>('newest');

  // Mock notifications with more variety
  const mockNotifications: Notification[] = [
    {
      id: '1',
      type: 'training_expiring',
      title: 'Training Certification Expiring',
      message: 'David Kamau\'s Working at Heights certification expires in 5 days. Please schedule renewal training.',
      timestamp: new Date().toISOString(),
      isRead: false,
      priority: 'high',
      category: 'training',
      actionUrl: '/sites/site1/training#worker-status',
      actionLabel: 'Schedule Renewal',
      metadata: {
        workerId: 1,
        workerName: 'David Kamau',
        trainingId: 1,
        trainingName: 'Working at Heights',
        daysUntilExpiry: 5
      }
    },
    {
      id: '2',
      type: 'overtime_request',
      title: 'Overtime Request Pending',
      message: 'Mary Wanjiku has requested 8 hours of overtime for weekend work on Site Alpha. Requires supervisor approval.',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      isRead: false,
      priority: 'medium',
      category: 'workforce',
      actionUrl: '/workforce/overtime/requests/2',
      actionLabel: 'Review Request',
      isStarred: true,
      metadata: {
        workerId: 2,
        workerName: 'Mary Wanjiku',
        requestId: 'OT-2024-001',
        amount: 8
      }
    },
    {
      id: '3',
      type: 'safety_alert',
      title: 'Safety Incident Reported',
      message: 'Minor safety incident reported at Site Beta. Equipment malfunction in Zone C requires immediate attention.',
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      isRead: true,
      priority: 'critical',
      category: 'safety',
      actionUrl: '/safety/incidents/3',
      actionLabel: 'View Report',
      metadata: {
        siteId: 'site-beta',
        siteName: 'Site Beta'
      }
    },
    {
      id: '4',
      type: 'permit_approval',
      title: 'Work Permit Requires Approval',
      message: 'Hot work permit for welding operations in Building 2 is pending your approval. Scheduled for tomorrow.',
      timestamp: new Date(Date.now() - 10800000).toISOString(),
      isRead: false,
      priority: 'high',
      category: 'safety',
      actionUrl: '/permits/approval/4',
      actionLabel: 'Review Permit',
      metadata: {
        siteId: 'site-alpha',
        siteName: 'Site Alpha'
      }
    },
    {
      id: '5',
      type: 'training_completed',
      title: 'Training Successfully Completed',
      message: 'John Mwangi has successfully completed First Aid Level 1 certification with a score of 95%.',
      timestamp: new Date(Date.now() - 86400000).toISOString(),
      isRead: true,
      priority: 'low',
      category: 'training',
      actionUrl: '/sites/site1/workers/5',
      actionLabel: 'View Certificate',
      metadata: {
        workerId: 5,
        workerName: 'John Mwangi',
        trainingId: 2,
        trainingName: 'First Aid Level 1'
      }
    },
    {
      id: '6',
      type: 'system',
      title: 'System Maintenance Scheduled',
      message: 'Scheduled system maintenance will occur this Sunday from 2:00 AM to 6:00 AM. Some features may be unavailable.',
      timestamp: new Date(Date.now() - 172800000).toISOString(),
      isRead: false,
      priority: 'medium',
      category: 'system',
      actionUrl: '/system/maintenance',
      actionLabel: 'View Details'
    }
  ];

  useEffect(() => {
    // Initialize with mock notifications
    setNotifications(mockNotifications);
    setFilteredNotifications(mockNotifications);
  }, [tenantId]);

  //and search logic
  useEffect(() => {
    let filtered = notifications;

    // Apply filter
    switch (selected) {
      case 'unread':
        filtered = filtered.filter(n => !n.isRead);
        break;
      case 'starred':
        filtered = filtered.filter(n => n.isStarred);
        break;
      case 'training':
      case 'workforce':
      case 'safety':
      case 'system':
        filtered = filtered.filter(n => n.category === selected);
        break;
    }

    // Apply search
    if (searchTerm) {
      filtered = filtered.filter(n => 
        n.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        n.message.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'oldest':
          return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
        case 'priority':
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        default: // newest
          return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      }
    });

    setFilteredNotifications(filtered);
  }, [notifications, selected, searchTerm, sortBy]);

  const getNotificationIcon = (type: Notification['type']) => {
    const iconClass = "h-5 w-5";
    switch (type) {
      case 'training_expiring':
        return <Clock className={iconClass} />;
      case 'training_expired':
        return <AlertTriangle className={iconClass} />;
      case 'training_assigned':
      case 'training_completed':
        return <User className={iconClass} />;
      case 'overtime_request':
        return <Clock className={iconClass} />;
      case 'permit_approval':
        return <CheckCircle className={iconClass} />;
      case 'safety_alert':
        return <AlertTriangle className={iconClass} />;
      case 'reminder':
        return <Calendar className={iconClass} />;
      default:
        return <Bell className={iconClass} />;
    }
  };

  const getPriorityStyles = (priority: string) => {
    switch (priority) {
      case 'critical': 
        return {
          icon: 'text-red-600 bg-red-100',
          border: 'border-l-red-500',
          badge: 'bg-red-100 text-red-800'
        };
      case 'high': 
        return {
          icon: 'text-orange-600 bg-orange-100',
          border: 'border-l-orange-500',
          badge: 'bg-orange-100 text-orange-800'
        };
      case 'medium': 
        return {
          icon: 'text-yellow-600 bg-yellow-100',
          border: 'border-l-yellow-500',
          badge: 'bg-yellow-100 text-yellow-800'
        };
      case 'low': 
        return {
          icon: 'text-blue-600 bg-blue-100',
          border: 'border-l-blue-500',
          badge: 'bg-blue-100 text-blue-800'
        };
      default: 
        return {
          icon: 'text-gray-600 bg-gray-100',
          border: 'border-l-gray-500',
          badge: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'training': return <User className="h-4 w-4" />;
      case 'workforce': return <Clock className="h-4 w-4" />;
      case 'safety': return <AlertTriangle className="h-4 w-4" />;
      case 'system': return <Bell className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, isRead: true } : n
      )
    );
  };

  const toggleStar = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, isStarred: !n.isStarred } : n
      )
    );
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
  };

  const getCount = (filter: string) => {
    switch (filter) {
      case 'unread': return notifications.filter(n => !n.isRead).length;
      case 'starred': return notifications.filter(n => n.isStarred).length;
      case 'training': return notifications.filter(n => n.category === 'training').length;
      case 'workforce': return notifications.filter(n => n.category === 'workforce').length;
      case 'safety': return notifications.filter(n => n.category === 'safety').length;
      case 'system': return notifications.filter(n => n.category === 'system').length;
      default: return notifications.length;
    }
  };

  return (
    <FloatingCard title="Notifications">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <p className="text-gray-600">Stay updated with important alerts and updates</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={markAllAsRead}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors"
            >
              <Check className="h-4 w-4 mr-2" />
              Mark all read
            </button>
          </div>
        </div>

          {/* Stats */}
          <div className="grid grid-cols-4 gap-4 mb-6">
            {[
              { label: 'Total', count: notifications.length, color: 'bg-blue-100 text-blue-800' },
              { label: 'Unread', count: getCount('unread'), color: 'bg-red-100 text-red-800' },
              { label: 'Starred', count: getCount('starred'), color: 'bg-yellow-100 text-yellow-800' },
              { label: 'High Priority', count: notifications.filter(n => n.priority === 'high' || n.priority === 'critical').length, color: 'bg-orange-100 text-orange-800' }
            ].map((stat) => (
              <div key={stat.label} className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">{stat.label}</span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${stat.color}`}>
                    {stat.count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-12 gap-6">
          {/* Sidebars */}
          <div className="col-span-3">
            <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
              <h3 className="text-sm font-semibold text-gray-900 mb-4">s</h3>
              
              {/* Search */}
              <div className="mb-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>

              {/*Options */}
              <div className="space-y-1">
                {[
                  { key: 'all', label: 'All Notifications', icon: Bell },
                  { key: 'unread', label: 'Unread', icon: Bell },
                  { key: 'starred', label: 'Starred', icon: Star },
                  { key: 'training', label: 'Training', icon: User },
                  { key: 'workforce', label: 'Workforce', icon: Clock },
                  { key: 'safety', label: 'Safety', icon: AlertTriangle },
                  { key: 'system', label: 'System', icon: Bell }
                ].map((filter) => {
                  const IconComponent = filter.icon;
                  const count = getCount(filter.key);
                  return (
                    <button
                      key={filter.key}
                      onClick={() => setSelected(filter.key as any)}
                      className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors ${
                        selected=== filter.key
                          ? 'bg-green-100 text-green-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <IconComponent className="h-4 w-4" />
                        <span>{filter.label}</span>
                      </div>
                      {count > 0 && (
                        <span className="text-xs bg-gray-200 text-gray-600 px-2 py-0.5 rounded-full">
                          {count}
                        </span>
                      )}
                    </button>
                  );
                })}
              </div>

              {/* Sort Options */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <h4 className="text-xs font-semibold text-gray-900 mb-2">Sort by</h4>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="newest">Newest first</option>
                  <option value="oldest">Oldest first</option>
                  <option value="priority">Priority</option>
                </select>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="col-span-9">
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
              {filteredNotifications.length === 0 ? (
                <div className="p-12 text-center">
                  <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
                  <p className="text-gray-500">
                    {searchTerm ? 'Try adjusting your search terms' : 'You\'re all caught up!'}
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredNotifications.map((notification) => {
                    const priorityStyles = getPriorityStyles(notification.priority);
                    return (
                      <div
                        key={notification.id}
                        className={`relative p-6 border-l-4 ${priorityStyles.border} hover:bg-gray-50 transition-colors ${
                          !notification.isRead ? 'bg-blue-50/30' : 'bg-white'
                        }`}
                      >
                        {/* Unread indicator */}
                        {!notification.isRead && (
                          <div className="absolute top-6 right-6">
                            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                          </div>
                        )}

                        <div className="flex items-start space-x-4">
                          {/* Icon */}
                          <div className={`flex-shrink-0 p-3 rounded-xl ${priorityStyles.icon}`}>
                            {getNotificationIcon(notification.type)}
                          </div>

                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            {/* Header */}
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                  {notification.title}
                                </h3>
                                <div className="flex items-center space-x-3 mb-2">
                                  <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${priorityStyles.badge}`}>
                                    {notification.priority.charAt(0).toUpperCase() + notification.priority.slice(1)}
                                  </span>
                                  <span className="inline-flex items-center text-xs text-gray-500">
                                    {getCategoryIcon(notification.category)}
                                    <span className="ml-1 capitalize">{notification.category}</span>
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {formatTimestamp(notification.timestamp)}
                                  </span>
                                </div>
                              </div>
                              
                              {/* Actions */}
                              <div className="flex items-center space-x-2">
                                <button
                                  onClick={() => toggleStar(notification.id)}
                                  className={`p-2 rounded-lg transition-colors ${
                                    notification.isStarred 
                                      ? 'text-yellow-500 hover:text-yellow-600' 
                                      : 'text-gray-400 hover:text-gray-600'
                                  }`}
                                  title={notification.isStarred ? 'Remove star' : 'Add star'}
                                >
                                  <Star className={`h-4 w-4 ${notification.isStarred ? 'fill-current' : ''}`} />
                                </button>
                                <button
                                  onClick={() => markAsRead(notification.id)}
                                  className="p-2 rounded-lg text-gray-400 hover:text-gray-600 transition-colors"
                                  title="Mark as read"
                                >
                                  <Check className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() => deleteNotification(notification.id)}
                                  className="p-2 rounded-lg text-gray-400 hover:text-red-600 transition-colors"
                                  title="Delete notification"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>

                            {/* Message */}
                            <p className="text-gray-700 mb-4 leading-relaxed">
                              {notification.message}
                            </p>

                            {/* Metadata */}
                            {notification.metadata && (
                              <div className="bg-gray-50 rounded-lg p-3 mb-4">
                                <div className="grid grid-cols-2 gap-3 text-sm">
                                  {notification.metadata.workerName && (
                                    <div>
                                      <span className="font-medium text-gray-600">Worker:</span>
                                      <span className="ml-2 text-gray-900">{notification.metadata.workerName}</span>
                                    </div>
                                  )}
                                  {notification.metadata.trainingName && (
                                    <div>
                                      <span className="font-medium text-gray-600">Training:</span>
                                      <span className="ml-2 text-gray-900">{notification.metadata.trainingName}</span>
                                    </div>
                                  )}
                                  {notification.metadata.siteName && (
                                    <div>
                                      <span className="font-medium text-gray-600">Site:</span>
                                      <span className="ml-2 text-gray-900">{notification.metadata.siteName}</span>
                                    </div>
                                  )}
                                  {notification.metadata.requestId && (
                                    <div>
                                      <span className="font-medium text-gray-600">Request ID:</span>
                                      <span className="ml-2 text-gray-900">{notification.metadata.requestId}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Actions */}
                            {notification.actionUrl && (
                              <div className="flex items-center space-x-3">
                                <Link
                                  to={notification.actionUrl}
                                  onClick={() => markAsRead(notification.id)}
                                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
                                >
                                  {notification.actionLabel || 'View Details'}
                                  <ExternalLink className="h-4 w-4 ml-2" />
                                </Link>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
    </FloatingCard>
  );
};

export default NotificationsPage;
