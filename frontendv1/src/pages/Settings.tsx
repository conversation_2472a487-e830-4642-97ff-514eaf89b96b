import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
	LayoutDashboard,
	Users,
	Shield,
	Building,
	Settings as Settings<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Plug,
	Bell,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import SettingsOverview from "../components/settings/SettingsOverview";
import UserManagement from "../components/settings/UserManagement";
import RolesPermissions from "../components/settings/RolesPermissions";
import CompanyProfile from "../components/settings/CompanyProfile";
import SystemConfig from "../components/settings/SystemConfig";
import ComplianceSettings from "../components/settings/ComplianceSettings";
import IntegrationSettings from "../components/settings/IntegrationSettings";
import NotificationSettings from "../components/settings/NotificationSettings";

interface SettingsTab {
	id: string;
	label: string;
	icon: React.ComponentType<{ className?: string }>;
	component: React.ComponentType<any>;
	permissions: string[];
}

const SETTINGS_TABS: SettingsTab[] = [
	{
		id: "overview",
		label: "Overview",
		icon: LayoutDashboard,
		component: SettingsOverview,
		permissions: ["settings.view"],
	},
	{
		id: "users",
		label: "Users",
		icon: Users,
		component: UserManagement,
		permissions: ["users.manage"],
	},
	{
		id: "roles",
		label: "Roles",
		icon: Shield,
		component: RolesPermissions,
		permissions: ["roles.manage"],
	},
	{
		id: "company",
		label: "Company",
		icon: Building,
		component: CompanyProfile,
		permissions: ["company.manage"],
	},
	{
		id: "system",
		label: "System",
		icon: SettingsIcon,
		component: SystemConfig,
		permissions: ["system.manage"],
	},
	{
		id: "compliance",
		label: "Compliance",
		icon: FileCheck,
		component: ComplianceSettings,
		permissions: ["compliance.manage"],
	},
	{
		id: "integrations",
		label: "Integrations",
		icon: Plug,
		component: IntegrationSettings,
		permissions: ["integrations.manage"],
	},
	{
		id: "notifications",
		label: "Notifications",
		icon: Bell,
		component: NotificationSettings,
		permissions: ["notifications.manage"],
	},
];

const Settings = () => {
	const location = useLocation();
	const navigate = useNavigate();
	const [activeTab, setActiveTab] = useState("overview");

	// Handle hash-based navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && SETTINGS_TABS.some((tab) => tab.id === hash)) {
			setActiveTab(hash);
		}
	}, [location.hash]);

	// TODO: Replace with actual permission check
	const hasPermission = (_permissions: string[]) => {
		// For now, return true. In production, check user permissions
		return true;
	};

	const handleTabChange = (tabId: string) => {
		setActiveTab(tabId);
		navigate(`/settings#${tabId}`, { replace: true });
	};

	const visibleTabs = SETTINGS_TABS.filter((tab) =>
		hasPermission(tab.permissions),
	);
	const activeTabData =
		visibleTabs.find((tab) => tab.id === activeTab) || visibleTabs[0];

	return (
		<FloatingCard title="Settings">
			{/* Tab Navigation */}
			<div className="border-b border-gray-200 mb-6">
				<nav className="-mb-px flex space-x-8">
					{visibleTabs.map((tab) => {
						const Icon = tab.icon;
						const isActive = activeTab === tab.id;

						return (
							<button
								key={tab.id}
								onClick={() => handleTabChange(tab.id)}
								className={`
                  flex items-center py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                  ${
										isActive
											? "border-green-500 text-green-600"
											: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
									}
                `}
							>
								<Icon className="h-4 w-4 mr-2" />
								{tab.label}
							</button>
						);
					})}
				</nav>
			</div>

			{/* Tab Content */}
			<div className="min-h-96">
				{activeTabData && (
					<activeTabData.component onNavigateToTab={handleTabChange} />
				)}
			</div>
		</FloatingCard>
	);
};

export default Settings;
