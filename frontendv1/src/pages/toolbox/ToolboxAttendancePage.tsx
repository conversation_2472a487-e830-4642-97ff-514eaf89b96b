import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  Users,
  Search,
  Plus,
  Trash2,
  Save,
  UserCheck,
  FileSignature
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { ADD_ATTENDEES } from '../../graphql/mutations';
import { GET_ALL_WORKERS } from '../../graphql/queries';
import { WorkerForAttendance } from '../../types/toolbox';
import { FILE_BASE_URL } from '../../utils/constants';

const ToolboxAttendancePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const toolboxId = searchParams.get('toolboxId');

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedWorkers, setSelectedWorkers] = useState<WorkerForAttendance[]>([]);
  const [filteredWorkers, setFilteredWorkers] = useState<WorkerForAttendance[]>([]);

  // GraphQL hooks
  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const [addAttendees, { loading: adding }] = useMutation(ADD_ATTENDEES);

  const workers: WorkerForAttendance[] = workersData?.allWorkers || [];

  // Filter workers based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredWorkers([]);
      return;
    }

    const filtered = workers.filter(worker =>
      worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      worker.company.toLowerCase().includes(searchTerm.toLowerCase())
    ).filter(worker => 
      !selectedWorkers.some(selected => selected.id === worker.id)
    );

    setFilteredWorkers(filtered);
  }, [searchTerm, workers, selectedWorkers]);

  const addWorkerToAttendance = (worker: WorkerForAttendance) => {
    setSelectedWorkers(prev => [...prev, worker]);
    setSearchTerm('');
    setFilteredWorkers([]);
  };

  const removeWorkerFromAttendance = (workerId: number) => {
    setSelectedWorkers(prev => prev.filter(worker => worker.id !== workerId));
  };

  const getWorkerTrainings = (worker: WorkerForAttendance) => {
    if (!worker.trainings || worker.trainings.length === 0) {
      return 'No trainings';
    }
    return worker.trainings.map(training => training.name).join(', ');
  };

  const getSignatureUrl = (worker: WorkerForAttendance) => {
    if (!worker.signatureFileId) return null;
    return `${FILE_BASE_URL}/files/${worker.signatureFileId}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!toolboxId) {
      toast.error('Toolbox ID is required');
      return;
    }

    if (selectedWorkers.length === 0) {
      toast.error('Please add at least one worker to the attendance');
      return;
    }

    try {
      const workerIds = selectedWorkers.map(worker => worker.id);
      
      await addAttendees({
        variables: {
          toolboxId: parseInt(toolboxId),
          workerIds
        }
      });

      toast.success('Attendance added successfully!');
      navigate(`/sites/:siteId/toolbox/summarize?toolboxId=${toolboxId}`);
    } catch (error) {
      console.error('Error adding attendees:', error);
      toast.error('Failed to add attendees. Please try again.');
    }
  };

  const breadcrumbs = [
    { name: 'Toolbox', path: '/toolbox' },
    { name: 'Attendance', path: '/toolbox/attendance' }
  ];

  if (workersLoading) {
    return (
      <FloatingCard title="Toolbox Attendance" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (!toolboxId) {
    return (
      <FloatingCard title="Toolbox Attendance" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Toolbox ID is required to manage attendance</p>
          <button
            onClick={() => navigate('/sites/:siteId/toolbox')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Go to Toolbox
          </button>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Toolbox Attendance" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Worker Search Section */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Search and Add Workers
          </h3>
          
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search workers by name or company..."
            />
          </div>

          {/* Search Results */}
          {filteredWorkers.length > 0 && (
            <div className="mt-4 border border-gray-200 rounded-md max-h-60 overflow-y-auto">
              {filteredWorkers.map(worker => (
                <div
                  key={worker.id}
                  className="p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 cursor-pointer"
                  onClick={() => addWorkerToAttendance(worker)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{worker.name}</p>
                      <p className="text-sm text-gray-500">{worker.company}</p>
                    </div>
                    <Plus className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
              ))}
            </div>
          )}

          {searchTerm.trim() && filteredWorkers.length === 0 && (
            <div className="mt-4 text-center py-4 text-gray-500">
              No workers found matching "{searchTerm}"
            </div>
          )}
        </div>

        {/* Attendance Table */}
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <UserCheck className="h-5 w-5 mr-2" />
            Attendance List ({selectedWorkers.length} workers)
          </h3>

          {selectedWorkers.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">No workers added to attendance yet</p>
              <p className="text-sm text-gray-400 mt-2">Use the search above to add workers</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      S/No
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Training
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Signature
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {selectedWorkers.map((worker, index) => (
                    <tr key={worker.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{worker.name}</div>
                          <div className="text-sm text-gray-500">{worker.company}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        <div className="max-w-xs truncate" title={getWorkerTrainings(worker)}>
                          {getWorkerTrainings(worker)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getSignatureUrl(worker) ? (
                          <div className="flex items-center">
                            <img
                              src={getSignatureUrl(worker)!}
                              alt={`${worker.name} signature`}
                              className="h-8 w-16 object-contain border border-gray-200 rounded"
                            />
                            <FileSignature className="h-4 w-4 ml-2 text-green-600" />
                          </div>
                        ) : (
                          <div className="flex items-center text-gray-400">
                            <span className="text-sm">No signature</span>
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          type="button"
                          onClick={() => removeWorkerFromAttendance(worker.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 p-1 rounded"
                          title="Remove from attendance"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/sites/:siteId/toolbox')}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={adding || selectedWorkers.length === 0}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {adding ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Adding...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Attendance
              </>
            )}
          </button>
        </div>
      </form>
    </FloatingCard>
  );
};

export default ToolboxAttendancePage;
