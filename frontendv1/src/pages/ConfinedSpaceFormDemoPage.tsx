import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Shield, CheckCircle, AlertTriangle, Users, FileText, Clock, MapPin } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';

const ConfinedSpaceFormDemoPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  const handleTryForm = () => {
    navigate('/confined-space/form');
  };

  return (
    <FloatingCard title="Confined Space Entry System Demo">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <Shield className="h-6 w-6 text-blue-600" />
                <h1 className="text-2xl font-bold text-gray-900">Confined Space Entry System</h1>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-6xl mx-auto p-6">
          {/* Hero Section */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-8 text-white mb-8">
            <div className="max-w-3xl">
              <h2 className="text-3xl font-bold mb-4">Confined Space Entry Permit System</h2>
              <p className="text-xl mb-6 text-blue-100">
                Comprehensive digital solution for managing confined space entry permits with safety protocols, 
                atmospheric monitoring, and emergency procedures.
              </p>
              <button
                onClick={handleTryForm}
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                Try the Form
              </button>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
                <h3 className="text-lg font-semibold">Safety Compliance</h3>
              </div>
              <p className="text-gray-600">
                Ensures all safety protocols are followed with mandatory training verification and atmospheric testing requirements.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <AlertTriangle className="h-8 w-8 text-yellow-600 mr-3" />
                <h3 className="text-lg font-semibold">Atmospheric Monitoring</h3>
              </div>
              <p className="text-gray-600">
                Three-level atmospheric testing for oxygen, explosive gases, toxic substances, and CO2 levels with safety thresholds.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <Users className="h-8 w-8 text-blue-600 mr-3" />
                <h3 className="text-lg font-semibold">Team Management</h3>
              </div>
              <p className="text-gray-600">
                Tracks entrants, observers, and supervisors with digital signatures and time-stamped entries.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <FileText className="h-8 w-8 text-purple-600 mr-3" />
                <h3 className="text-lg font-semibold">Emergency Procedures</h3>
              </div>
              <p className="text-gray-600">
                Built-in emergency guidelines and customizable emergency arrangements for specific confined space scenarios.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <Clock className="h-8 w-8 text-orange-600 mr-3" />
                <h3 className="text-lg font-semibold">Real-time Tracking</h3>
              </div>
              <p className="text-gray-600">
                Auto-generated timestamps and permit validity tracking for enhanced safety monitoring.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <MapPin className="h-8 w-8 text-red-600 mr-3" />
                <h3 className="text-lg font-semibold">Location Specific</h3>
              </div>
              <p className="text-gray-600">
                Site-specific configurations and location-based safety requirements for different confined spaces.
              </p>
            </div>
          </div>

          {/* Form Preview */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Form Structure</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Key Sections:</h4>
                <ul className="space-y-2 text-gray-600">
                  <li>• Basic permit information and project details</li>
                  <li>• Training verification for entrants and observers</li>
                  <li>• Three-level atmospheric testing results</li>
                  <li>• Emergency guidelines and procedures</li>
                  <li>• Digital sign-off with observer details</li>
                  <li>• Multi-person approval workflow</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Safety Features:</h4>
                <ul className="space-y-2 text-gray-600">
                  <li>• Mandatory atmospheric testing at top, mid, and bottom levels</li>
                  <li>• Oxygen level monitoring (19.5-23.5%)</li>
                  <li>• Explosive gas detection (LEL&lt;20%)</li>
                  <li>• Toxic substance monitoring (&lt;10ppm H2S)</li>
                  <li>• CO2 level tracking (&lt;1%)</li>
                  <li>• Emergency contact and rescue procedures</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Technical Details */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-xl font-semibold mb-4">Technical Implementation</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Form Features:</h4>
                <div className="text-gray-600 space-y-1">
                  <p>• Single-sheet form design for streamlined workflow</p>
                  <p>• Auto-populated date and time fields</p>
                  <p>• Dynamic table rows for multiple personnel</p>
                  <p>• Digital signature integration</p>
                  <p>• Real-time form validation</p>
                  <p>• Mobile-responsive design</p>
                  <p>• Data persistence and backup</p>
                  <p>• Export capabilities for compliance records</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Integration Ready:</h4>
                <div className="text-gray-600 space-y-1">
                  <p>• Backend API integration points</p>
                  <p>• Database storage for permit records</p>
                  <p>• User authentication and authorization</p>
                  <p>• Site-specific configuration management</p>
                  <p>• Notification system for permit status</p>
                  <p>• Audit trail and compliance reporting</p>
                  <p>• Integration with existing safety systems</p>
                  <p>• Customizable approval workflows</p>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-8">
            <button
              onClick={handleTryForm}
              className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              Experience the Confined Space Entry Form
            </button>
            <p className="text-gray-500 mt-2">
              Try the interactive form to see all features in action
            </p>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default ConfinedSpaceFormDemoPage;
