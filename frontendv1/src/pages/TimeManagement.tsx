import React, { useState, useEffect } from "react";
import { useParams, useLocation } from "react-router-dom";
import { Clock, Users, BarChart3, Settings } from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import AttendanceTab from "../components/time/AttendanceTab";
import OvertimeTab from "../components/time/OvertimeTab";
import ReportsTab from "../components/time/ReportsTab";
import SystemTab from "../components/time/SystemTab";
import { SiteInfo } from "../types";

// Mock data
const mockSite: SiteInfo = {
	id: "site1",
	name: "Westlands Construction Site",
	healthStatus: "green",
	workersOnSite: 42,
	activePermits: 8,
	openIncidents: 0,
	projectManager: "John Mwangi",
	location: "Waiyaki Way, Westlands, Nairobi",
	timeline: "Jan 2025 - Dec 2026",
	currentPhase: "Foundation",
	progressPercentage: 25,
	tenantId: "",
	status: "active",
	createdAt: new Date()
};

const TimeManagement: React.FC = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [site] = useState<SiteInfo>(mockSite);
	const [activeTab, setActiveTab] = useState("attendance");

	const validTabs = ["attendance", "overtime", "reports", "system"];

	// Handle URL hash navigation - this effect runs whenever the location changes
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("attendance");
		}
	}, [location.hash]);

	// Also listen for direct hash changes (for browser back/forward)
	useEffect(() => {
		const handleHashChange = () => {
			const newHash = window.location.hash.replace("#", "");
			if (newHash && validTabs.includes(newHash)) {
				setActiveTab(newHash);
			} else if (!newHash) {
				setActiveTab("attendance");
			}
		};

		window.addEventListener("hashchange", handleHashChange);
		return () => window.removeEventListener("hashchange", handleHashChange);
	}, []);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Update URL hash without triggering a page reload
		window.history.replaceState(null, "", `#${tabId}`);
	};

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: site.name, path: `/sites/${siteId}/dashboard` },
		{ name: "Time Management", path: `/sites/${siteId}/time` },
	];

	const tabs: Tab[] = [
		{
			id: "attendance",
			label: "Daily Attendance",
			icon: <Users className="h-4 w-4" />,
			content: <AttendanceTab siteId={siteId || ""} />,
		},
		{
			id: "overtime",
			label: "Overtime Requests",
			icon: <Clock className="h-4 w-4" />,
			content: <OvertimeTab siteId={siteId || ""} />,
		},
		{
			id: "reports",
			label: "Time Reports",
			icon: <BarChart3 className="h-4 w-4" />,
			content: <ReportsTab siteId={siteId || ""} />,
		},
		{
			id: "system",
			label: "System",
			icon: <Settings className="h-4 w-4" />,
			content: <SystemTab siteId={siteId || ""} />,
		},
	];

	return (
		<FloatingCard title="Time Management" breadcrumbs={breadcrumbs}>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default TimeManagement;
