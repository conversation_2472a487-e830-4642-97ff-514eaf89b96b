import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  User,
  Users,
  Calendar,
  Clock,
  FileText,
  Save,
  X,
  Search,
  Plus,
  Minus
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { CREATE_JOB } from '../graphql/mutations';
import { GET_ALL_WORKERS } from '../graphql/queries';

interface Worker {
  id: number;
  name: string;
  company: string;
}

interface FormData {
  title: string;
  description: string;
  workerIds: number[];
  chiefEngineerId: number | null;
  timeForCompletion: string;
  startDate: string;
}

const NewTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    workerIds: [],
    chiefEngineerId: null,
    timeForCompletion: '1',
    startDate: ''
  });

  const [workerSearchTerm, setWorkerSearchTerm] = useState('');
  const [chiefEngineerSearchTerm, setChiefEngineerSearchTerm] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Set default start date to next day
  useEffect(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    setFormData(prev => ({ ...prev, startDate: tomorrowStr }));
  }, []);

  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const [createJob] = useMutation(CREATE_JOB, {
    onCompleted: (data) => {
      toast.success(`Task "${data.createJob.title}" created successfully!`);
      navigate(`/sites/${siteId}/tasks`);
    },
    onError: (error) => {
      toast.error(`Failed to create task: ${error.message}`);
    }
  });

  const workers = workersData?.allWorkers || [];
  
  const filteredWorkers = workers.filter((worker: Worker) =>
    worker.name.toLowerCase().includes(workerSearchTerm.toLowerCase()) ||
    worker.company.toLowerCase().includes(workerSearchTerm.toLowerCase())
  );

  const filteredChiefEngineers = workers.filter((worker: Worker) =>
    worker.name.toLowerCase().includes(chiefEngineerSearchTerm.toLowerCase()) ||
    worker.company.toLowerCase().includes(chiefEngineerSearchTerm.toLowerCase())
  );

  const selectedWorkers = workers.filter((worker: Worker) => 
    formData.workerIds.includes(worker.id)
  );

  const selectedChiefEngineer = workers.find((worker: Worker) => 
    worker.id === formData.chiefEngineerId
  );

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (formData.workerIds.length === 0) {
      newErrors.workers = 'At least one worker must be selected';
    }

    if (!formData.chiefEngineerId) {
      newErrors.chiefEngineer = 'Chief Engineer must be selected';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }

    if (!formData.timeForCompletion || parseInt(formData.timeForCompletion) < 1) {
      newErrors.timeForCompletion = 'Time for completion must be at least 1 day';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await createJob({
        variables: {
          input: {
            title: formData.title,
            description: formData.description,
            workerIds: formData.workerIds,
            chiefEngineerId: formData.chiefEngineerId!,
            timeForCompletion: formData.timeForCompletion,
            startDate: new Date(formData.startDate).toISOString()
          }
        }
      });
    } catch (error) {
      console.error('Error creating task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleWorkerToggle = (workerId: number) => {
    setFormData(prev => ({
      ...prev,
      workerIds: prev.workerIds.includes(workerId)
        ? prev.workerIds.filter(id => id !== workerId)
        : [...prev.workerIds, workerId]
    }));
  };

  const handleChiefEngineerSelect = (workerId: number) => {
    setFormData(prev => ({ ...prev, chiefEngineerId: workerId }));
    setChiefEngineerSearchTerm('');
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'New Task', path: `/sites/${siteId}/tasks/new` }
  ];

  return (
    <FloatingCard title="Create New Task" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FileText className="h-4 w-4 inline mr-2" />
            Task Title *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.title ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter task title"
          />
          {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FileText className="h-4 w-4 inline mr-2" />
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.description ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter task description"
          />
          {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
        </div>

        {/* Start Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Calendar className="h-4 w-4 inline mr-2" />
            Start Date *
          </label>
          <input
            type="date"
            value={formData.startDate}
            onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.startDate ? 'border-red-500' : 'border-gray-300'
            }`}
          />
          {errors.startDate && <p className="text-red-500 text-sm mt-1">{errors.startDate}</p>}
        </div>

        {/* Time for Completion */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Clock className="h-4 w-4 inline mr-2" />
            Time for Completion (Days) *
          </label>
          <input
            type="number"
            min="1"
            value={formData.timeForCompletion}
            onChange={(e) => setFormData(prev => ({ ...prev, timeForCompletion: e.target.value }))}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.timeForCompletion ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="1"
          />
          {errors.timeForCompletion && <p className="text-red-500 text-sm mt-1">{errors.timeForCompletion}</p>}
        </div>

        {/* Chief Engineer Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <User className="h-4 w-4 inline mr-2" />
            Chief Engineer *
          </label>
          {selectedChiefEngineer ? (
            <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div>
                <p className="font-medium">{selectedChiefEngineer.name}</p>
                <p className="text-sm text-gray-600">{selectedChiefEngineer.company}</p>
              </div>
              <button
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, chiefEngineerId: null }))}
                className="text-red-500 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  value={chiefEngineerSearchTerm}
                  onChange={(e) => setChiefEngineerSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Search for chief engineer..."
                />
              </div>
              {chiefEngineerSearchTerm && (
                <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md">
                  {filteredChiefEngineers.map((worker: Worker) => (
                    <button
                      key={worker.id}
                      type="button"
                      onClick={() => handleChiefEngineerSelect(worker.id)}
                      className="w-full text-left p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                    >
                      <p className="font-medium">{worker.name}</p>
                      <p className="text-sm text-gray-600">{worker.company}</p>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
          {errors.chiefEngineer && <p className="text-red-500 text-sm mt-1">{errors.chiefEngineer}</p>}
        </div>

        {/* Worker Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Users className="h-4 w-4 inline mr-2" />
            Select Workers * ({selectedWorkers.length} selected)
          </label>

          {/* Selected Workers */}
          {selectedWorkers.length > 0 && (
            <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
              <p className="text-sm font-medium text-gray-700 mb-2">Selected Workers:</p>
              <div className="space-y-2">
                {selectedWorkers.map((worker: Worker) => (
                  <div key={worker.id} className="flex items-center justify-between p-2 bg-white border border-gray-200 rounded">
                    <div>
                      <p className="font-medium text-sm">{worker.name}</p>
                      <p className="text-xs text-gray-600">{worker.company}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleWorkerToggle(worker.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Minus className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Worker Search */}
          <div className="space-y-2">
            <div className="relative">
              <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                value={workerSearchTerm}
                onChange={(e) => setWorkerSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Search for workers..."
              />
            </div>

            {workerSearchTerm && (
              <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md">
                {filteredWorkers.map((worker: Worker) => {
                  const isSelected = formData.workerIds.includes(worker.id);
                  return (
                    <div
                      key={worker.id}
                      className={`flex items-center justify-between p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 ${
                        isSelected ? 'bg-blue-50' : ''
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleWorkerToggle(worker.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <div>
                          <p className="font-medium">{worker.name}</p>
                          <p className="text-sm text-gray-600">{worker.company}</p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleWorkerToggle(worker.id)}
                        className={`p-1 rounded ${
                          isSelected
                            ? 'text-red-500 hover:text-red-700'
                            : 'text-green-500 hover:text-green-700'
                        }`}
                      >
                        {isSelected ? <Minus className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                      </button>
                    </div>
                  );
                })}
                {filteredWorkers.length === 0 && (
                  <div className="p-3 text-center text-gray-500">
                    No workers found matching "{workerSearchTerm}"
                  </div>
                )}
              </div>
            )}
          </div>
          {errors.workers && <p className="text-red-500 text-sm mt-1">{errors.workers}</p>}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate(`/sites/${siteId}/tasks`)}
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <X className="h-4 w-4 inline mr-2" />
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting || workersLoading}
            className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400 rounded-md transition-colors"
          >
            <Save className="h-4 w-4 inline mr-2" />
            {isSubmitting ? 'Creating...' : 'Create Task'}
          </button>
        </div>
      </form>
    </FloatingCard>
  );
};

export default NewTaskPage;
