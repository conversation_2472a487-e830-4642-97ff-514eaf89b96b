import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import {
  BarChart3,
  TrendingUp,
  AlertTriangle,
  GraduationCap,
  Building,
  Users,
  ShieldCheck
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import TabContainer, { Tab } from '../components/data/shared/TabContainer';
import KPICard from '../components/dashboard/KPICard';
import CrossSitePerformance from '../components/reports/CrossSitePerformance';
import IncidentStatistics from '../components/reports/IncidentStatistics';
import TrainingCompliance from '../components/reports/TrainingCompliance';

// Company Reports Overview Component
const CompanyReportsOverview: React.FC = () => {
  // Mock company-level KPI data
  const companyKPIs = {
    totalSites: 8,
    totalWorkers: 245,
    overallSafetyScore: 92,
    trainingCompliance: 87,
    openIncidents: 3,
    avgSitePerformance: 89
  };

  return (
    <div className="space-y-6">

      {/* Company KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <KPICard
          title="Total Sites"
          value={companyKPIs.totalSites}
          icon={<Building className="h-6 w-6 text-blue-500" />}
        />
        <KPICard
          title="Total Workers"
          value={companyKPIs.totalWorkers}
          icon={<Users className="h-6 w-6 text-green-500" />}
        />
        <KPICard
          title="Safety Score"
          value={`${companyKPIs.overallSafetyScore}%`}
          icon={<ShieldCheck className="h-6 w-6 text-emerald-500" />}
        />
        <KPICard
          title="Training Compliance"
          value={`${companyKPIs.trainingCompliance}%`}
          icon={<GraduationCap className="h-6 w-6 text-purple-500" />}
        />
        <KPICard
          title="Open Incidents"
          value={companyKPIs.openIncidents}
          icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
        />
        <KPICard
          title="Avg Site Performance"
          value={`${companyKPIs.avgSitePerformance}%`}
          icon={<TrendingUp className="h-6 w-6 text-indigo-500" />}
        />
      </div>

      {/* Quick Report Access */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3 mb-4">
            <TrendingUp className="h-8 w-8 text-blue-500" />
            <h3 className="text-lg font-medium">Site Performance</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Compare performance metrics across all sites including productivity, safety, and compliance.
          </p>
          <div className="text-blue-600 text-sm font-medium">View Report →</div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle className="h-8 w-8 text-red-500" />
            <h3 className="text-lg font-medium">Incident Statistics</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Analyze incident trends, severity patterns, and safety performance across the organization.
          </p>
          <div className="text-blue-600 text-sm font-medium">View Report →</div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3 mb-4">
            <GraduationCap className="h-8 w-8 text-purple-500" />
            <h3 className="text-lg font-medium">Training Compliance</h3>
          </div>
          <p className="text-gray-600 text-sm mb-4">
            Monitor training completion rates, certification status, and compliance across all sites.
          </p>
          <div className="text-blue-600 text-sm font-medium">View Report →</div>
        </div>
      </div>
    </div>
  );
};

const CompanyReports: React.FC = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('overview');

  const validTabs = ['overview', 'cross-site-performance', 'incident-statistics', 'training-compliance'];

  // Handle URL hash navigation - this effect runs whenever the location changes
  useEffect(() => {
    const hash = location.hash.replace('#', '');
    if (hash && validTabs.includes(hash)) {
      setActiveTab(hash);
    } else if (!hash) {
      setActiveTab('overview');
    }
  }, [location.hash]);

  // Also listen for direct hash changes (for browser back/forward)
  useEffect(() => {
    const handleHashChange = () => {
      const newHash = window.location.hash.replace('#', '');
      if (newHash && validTabs.includes(newHash)) {
        setActiveTab(newHash);
      } else if (!newHash) {
        setActiveTab('overview');
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    // Update URL hash without triggering a page reload
    if (tabId === 'overview') {
      window.history.replaceState(null, '', window.location.pathname);
    } else {
      window.history.replaceState(null, '', `#${tabId}`);
    }
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Company Reports', path: '/company-reports' }
  ];

  const tabs: Tab[] = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <BarChart3 className="h-4 w-4" />,
      content: <CompanyReportsOverview />
    },
    {
      id: 'cross-site-performance',
      label: 'Site Performance',
      icon: <TrendingUp className="h-4 w-4" />,
      content: <CrossSitePerformance />
    },
    {
      id: 'incident-statistics',
      label: 'Incident Statistics',
      icon: <AlertTriangle className="h-4 w-4" />,
      content: <IncidentStatistics />
    },
    {
      id: 'training-compliance',
      label: 'Training Compliance',
      icon: <GraduationCap className="h-4 w-4" />,
      content: <TrainingCompliance />
    }
  ];

  return (
    <FloatingCard title="Company Reports" breadcrumbs={breadcrumbs}>
      <TabContainer
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />
    </FloatingCard>
  );
};

export default CompanyReports;
