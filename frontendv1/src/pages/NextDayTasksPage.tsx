import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import {
  // LayoutDashboard,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import TabContainer, { Tab } from '../components/data/shared/TabContainer';
import { SiteInfo } from '../types';
import TaskScheduling from '../components/nextday/TaskScheduling';
import DisapprovedTasks from '../components/nextday/DisapprovedTasks';

// Mock site data - replace with actual API call
const mockSite: SiteInfo = {
  id: "site-1",
  name: "Downtown Construction Site",
  location: "Nairobi, Kenya",
  projectManager: "John Doe",
  status: "active",
  startDate: new Date("2024-01-15"),
  endDate: new Date("2024-12-31"),
  phase: "Construction",
  progress: 65,
  totalWorkers: 45,
  activeWorkers: 42,
  healthStatus: "green",
  workersOnSite: 0,
  activePermits: 0,
  openIncidents: 0,
  tenantId: '',
  createdAt: new Date('2024-09-01T00:00:00Z')
};

const NextDayTasksPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const location = useLocation();
  const [site] = useState<SiteInfo>(mockSite);
  const [activeTab, setActiveTab] = useState("schedule");

  const validTabs = ['schedule', 'disapproved'];

  // Handle URL hash navigation - this effect runs whenever the location changes
  useEffect(() => {
    const hash = location.hash.replace("#", "");
    if (hash && validTabs.includes(hash)) {
      setActiveTab(hash);
    } else if (!hash) {
      setActiveTab("schedule");
    }
  }, [location.hash]);

  // Also listen for direct hash changes (for browser back/forward)
  useEffect(() => {
    const handleHashChange = () => {
      const newHash = window.location.hash.replace("#", "");
      if (newHash && validTabs.includes(newHash)) {
        setActiveTab(newHash);
      } else if (!newHash) {
        setActiveTab("schedule");
      }
    };

    window.addEventListener("hashchange", handleHashChange);
    return () => window.removeEventListener("hashchange", handleHashChange);
  }, []);

  const handleNavigateToTab = (tabId: string) => {
    setActiveTab(tabId);
    // Update URL hash without triggering a page reload
    window.history.replaceState(null, "", `#${tabId}`);
  };

  const breadcrumbs = [
    { name: "Dashboard", path: "/" },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: "Next Day Tasks", path: `/sites/${siteId}/nextday` },
  ];

  const tabs: Tab[] = [
    {
      id: 'schedule',
      label: 'Schedule Tasks',
      icon: <Calendar className="h-4 w-4" />,
      content: <TaskScheduling siteId={siteId || ''} />
    },
    {
      id: 'disapproved',
      label: 'Disapproved Tasks',
      icon: <AlertTriangle className="h-4 w-4" />,
      content: <DisapprovedTasks siteId={siteId || ''} />
    }
  ];

  return (
    <FloatingCard title="Next Day Task Planning" breadcrumbs={breadcrumbs}>
      <TabContainer
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleNavigateToTab}
      />
    </FloatingCard>
  );
};

export default NextDayTasksPage;

