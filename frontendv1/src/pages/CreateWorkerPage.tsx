import React, { useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  Users,
  Search,
  Filter,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  X,
  Plus,
  MapPin
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import CreateWorkerForm from '../components/workers/CreateWorkerForm';
import UniversalFilterModal, { FilterValues } from '../components/common/UniversalFilterModal';
import { WorkerTable } from '../components/ui/worker-table';
import {
  getAvailableWorkers,
  CompanyWorker,
  TrainingComplianceStatus,
  canWorkerBeAssignedToSite
} from '../data/workers';
// import { mockTrades } from '../data/mockData';

type TabType = 'create' | 'import';

const CreateWorkerPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  // Tab state
  const [activeTab, setActiveTab] = useState<TabType>('create');

  // Import tab state
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [selectedWorkers, setSelectedWorkers] = useState<number[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const [viewingWorker, setViewingWorker] = useState<CompanyWorker | null>(null);
  const [, setImportResults] = useState<{success: number[], failed: number[]}>({success: [], failed: []});

  // Get available workers from company database
  const availableWorkers = useMemo(() => {
    return getAvailableWorkers();
  }, []);

  // Dynamic filter configuration based on available workers
  const filterConfig = useMemo(() => {
    const uniqueTrades = new Set<string>();
    const uniqueSkills = new Set<string>();

    availableWorkers.forEach(worker => {
      worker.trades.forEach(trade => {
        uniqueTrades.add(trade.name);
      });
      // Get skills from worker.skills array directly
      if (worker.skills) {
        worker.skills.forEach(skill => uniqueSkills.add(skill.name));
      }
    });

    const tradeOptions = Array.from(uniqueTrades).map(trade => ({
      value: trade,
      label: trade,
      count: availableWorkers.filter(w => w.trades.some(t => t.name === trade)).length
    }));

    const skillOptions = Array.from(uniqueSkills).map(skill => ({
      value: skill,
      label: skill,
      count: availableWorkers.filter(w => w.skills && w.skills.some(s => s.name === skill)).length
    }));

    return [
      {
        id: 'trade',
        label: 'Trade',
        type: 'multiselect' as const,
        options: tradeOptions
      },
      {
        id: 'skill',
        label: 'Skills',
        type: 'multiselect' as const,
        options: skillOptions
      },
      {
        id: 'complianceStatus',
        label: 'Compliance Status',
        type: 'dropdown' as const,
        placeholder: 'Select compliance status',
        options: [
          { value: 'compliant', label: 'Compliant', count: availableWorkers.filter(w => w.complianceStatus === 'compliant').length },
          { value: 'pending_training', label: 'Pending Training', count: availableWorkers.filter(w => w.complianceStatus === 'pending_training').length },
          { value: 'non_compliant', label: 'Non-Compliant', count: availableWorkers.filter(w => w.complianceStatus === 'non_compliant').length },
          { value: 'expired', label: 'Expired', count: availableWorkers.filter(w => w.complianceStatus === 'expired').length },
        ]
      },
      {
        id: 'onlyCompliant',
        label: 'Show Only Compliant Workers',
        type: 'checkbox' as const,
        options: [
          { value: 'true', label: 'Show only workers with compliant status', count: availableWorkers.filter(w => w.complianceStatus === 'compliant').length }
        ]
      }
    ];
  }, [availableWorkers]);

  // Filter workers based on search and active filters
  const filteredWorkers = useMemo(() => {
    return availableWorkers.filter(worker => {
      // Search filter
      const matchesSearch = worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           worker.employeeNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           worker.nationalId.includes(searchTerm);

      // Trade filter (multiselect)
      const matchesTrade = !activeFilters.trade ||
                          !Array.isArray(activeFilters.trade) ||
                          activeFilters.trade.length === 0 ||
                          activeFilters.trade.some(trade => worker.trades.some(t => t.name === trade));

      // Skill filter (multiselect)
      const matchesSkill = !activeFilters.skill ||
                          !Array.isArray(activeFilters.skill) ||
                          activeFilters.skill.length === 0 ||
                          (worker.skills && activeFilters.skill.some(skill => worker.skills.some(s => s.name === skill)));

      // Compliance status filter
      const matchesCompliance = !activeFilters.complianceStatus ||
                               worker.complianceStatus === activeFilters.complianceStatus;

      // Only compliant checkbox filter
      const matchesOnlyCompliant = !activeFilters.onlyCompliant ||
                                  !Array.isArray(activeFilters.onlyCompliant) ||
                                  activeFilters.onlyCompliant.length === 0 ||
                                  (activeFilters.onlyCompliant.includes('true') && worker.complianceStatus === 'compliant');

      // Site assignment eligibility
      const canBeAssigned = canWorkerBeAssignedToSite(worker.id);

      return matchesSearch && matchesTrade && matchesSkill && matchesCompliance && matchesOnlyCompliant && canBeAssigned;
    });
  }, [availableWorkers, searchTerm, activeFilters]);

  // Filter handlers
  const handleApplyFilters = (values: FilterValues) => {
    setActiveFilters(values);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
  };

  // Count active filters for display
  const activeFilterCount = Object.values(activeFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== '' && v !== null);
    }
    return value !== '' && value !== null && value !== false;
  }).length;

  const handleWorkerCreated = (worker: any) => {
    toast.success(`Worker "${worker.name}" has been created and assigned to ${siteId}!`);
    navigate(`/sites/${siteId}/workers`);
  };

  const handleCancel = () => {
    navigate(`/sites/${siteId}/workers`);
  };

  // Helper functions for compliance status
  const getComplianceStatusIcon = (status: TrainingComplianceStatus) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending_training':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'non_compliant':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'expired':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getComplianceStatusText = (status: TrainingComplianceStatus) => {
    switch (status) {
      case 'compliant':
        return 'Compliant';
      case 'pending_training':
        return 'Pending Training';
      case 'non_compliant':
        return 'Non-Compliant';
      case 'expired':
        return 'Expired';
      default:
        return 'Unknown';
    }
  };

  const getComplianceStatusColor = (status: TrainingComplianceStatus) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800';
      case 'pending_training':
        return 'bg-yellow-100 text-yellow-800';
      case 'non_compliant':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-red-100 text-red-900';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Import functionality
  const handleWorkerSelect = (workerId: number) => {
    setSelectedWorkers(prev =>
      prev.includes(workerId)
        ? prev.filter(id => id !== workerId)
        : [...prev, workerId]
    );
  };

  const handleSelectAll = () => {
    const allFilteredIds = filteredWorkers.map(w => w.id);
    setSelectedWorkers(prev => {
      const hasAll = allFilteredIds.every(id => prev.includes(id));
      if (hasAll) {
        return prev.filter(id => !allFilteredIds.includes(id));
      } else {
        return [...new Set([...prev, ...allFilteredIds])];
      }
    });
  };

  const handleImportWorkers = async (workerIds: number[] = selectedWorkers) => {
    if (workerIds.length === 0) return;

    setIsImporting(true);
    setImportResults({success: [], failed: []});

    try {
      // Simulate API call with individual worker processing
      const results = {success: [] as number[], failed: [] as number[]};

      for (const workerId of workerIds) {
        try {
          // Simulate individual worker import
          await new Promise(resolve => setTimeout(resolve, 300));

          // Simulate 90% success rate
          if (Math.random() > 0.1) {
            results.success.push(workerId);
          } else {
            results.failed.push(workerId);
          }
        } catch {
          results.failed.push(workerId);
        }
      }

      setImportResults(results);

      if (results.success.length > 0) {
        // Remove successfully imported workers from selection
        setSelectedWorkers(prev => prev.filter(id => !results.success.includes(id)));

        if (results.failed.length === 0) {
          toast.success(`Successfully imported ${results.success.length} worker${results.success.length !== 1 ? 's' : ''} to ${siteId}!`);
        } else {
          toast.warning(`Imported ${results.success.length} workers successfully. ${results.failed.length} failed to import.`);
        }
      } else {
        toast.error('Failed to import workers. Please try again.');
      }

    } catch (error) {
      toast.error('Failed to import workers. Please try again.');
    } finally {
      setIsImporting(false);
    }
  };

  const removeSelectedWorker = (workerId: number) => {
    setSelectedWorkers(prev => prev.filter(id => id !== workerId));
  };

  const clearAllSelections = () => {
    setSelectedWorkers([]);
  };



  const handleViewWorker = (worker: CompanyWorker) => {
    setViewingWorker(worker);
  };

  const handleCloseWorkerView = () => {
    setViewingWorker(null);
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: `Site ${siteId}`, path: `/sites/${siteId}/dashboard` },
    { name: 'Workers', path: `/sites/${siteId}/workers` },
    { name: 'Add Worker', path: `/sites/${siteId}/workers/create` }
  ];

  return (
    <FloatingCard
      title="Add Worker to Site"
      subtitle={`Add workers to ${siteId} by creating new workers or importing from company database`}
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-6">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex justify-center">
            <div className="flex space-x-8">
              <button
                onClick={() => setActiveTab('create')}
                className={`py-3 px-6 border-b-2 font-medium text-sm ${
                  activeTab === 'create'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Create New Worker
              </button>
              <button
                onClick={() => setActiveTab('import')}
                className={`py-3 px-6 border-b-2 font-medium text-sm ${
                  activeTab === 'import'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Import Existing Workers
              </button>
            </div>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'create' && (
          <div>
            <CreateWorkerForm
              onSuccess={handleWorkerCreated}
              onCancel={handleCancel}
            />
          </div>
        )}

        {activeTab === 'import' && !viewingWorker && (
          <div className="space-y-6">
            {/* Search and Filter Controls */}
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              {/* Search and Filters */}
              <div className="flex flex-1 items-center space-x-4">
                {/* Search */}
                <div className="relative flex-1 max-w-md">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-500" />
                  </div>
                  <input
                    type="text"
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                    placeholder="Search by name, employee number, or national ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                {/* Filter Button */}
                <button
                  onClick={() => setIsFilterOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                  {activeFilterCount > 0 && (
                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {activeFilterCount}
                    </span>
                  )}
                </button>
              </div>

              <div className="text-sm text-gray-500">
                Found {filteredWorkers.length} of {availableWorkers.length} available workers
              </div>
            </div>

            {/* Active Filters Display */}
            {activeFilterCount > 0 && (
              <div className="mb-6 flex flex-wrap gap-2">
                {Object.entries(activeFilters).map(([key, value]) => {
                  if (!value || (Array.isArray(value) && value.length === 0)) return null;

                  const filter = filterConfig.find(f => f.id === key);
                  if (!filter) return null;

                  let displayValue = '';
                  if (Array.isArray(value)) {
                    if (key === 'onlyCompliant' && value.includes('true')) {
                      displayValue = 'Compliant Only';
                    } else {
                      displayValue = value.map(v => {
                        const option = filter.options?.find(opt => opt.value === v);
                        return option ? option.label : v;
                      }).join(', ');
                    }
                  } else if (typeof value === 'object' && value.start) {
                    displayValue = `${value.start} - ${value.end || 'Present'}`;
                  } else {
                    const option = filter.options?.find(opt => opt.value === value);
                    displayValue = option ? option.label : value.toString();
                  }

                  return (
                    <span
                      key={key}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800"
                    >
                      {filter.label}: {displayValue}
                      <button
                        onClick={() => {
                          const newFilters = { ...activeFilters };
                          delete newFilters[key];
                          setActiveFilters(newFilters);
                        }}
                        className="ml-2 text-green-600 hover:text-green-800"
                      >
                        ×
                      </button>
                    </span>
                  );
                })}
              </div>
            )}

            {/* Modern Selected Workers Summary */}
            {selectedWorkers.length > 0 && (
              <div className="sticky top-4 z-10 bg-white border border-green-200 rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900">
                        {selectedWorkers.length} Worker{selectedWorkers.length !== 1 ? 's' : ''} Selected
                      </h4>
                      <p className="text-sm text-gray-500">Ready to import to {siteId}</p>
                    </div>
                  </div>
                  <button
                    onClick={clearAllSelections}
                    className="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-100"
                  >
                    Clear All
                  </button>
                </div>

                <div className="flex flex-wrap gap-2 mb-6 max-h-24 overflow-y-auto">
                  {selectedWorkers.map(workerId => {
                    const worker = availableWorkers.find(w => w.id === workerId);
                    if (!worker) return null;

                    return (
                      <div key={workerId} className="inline-flex items-center bg-green-50 border border-green-200 rounded-lg px-3 py-2 group hover:bg-green-100 transition-colors">
                        <img
                          className="w-6 h-6 rounded-full mr-2"
                          src={worker.photoUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(worker.name)}&background=10B981&color=fff&size=24`}
                          alt={worker.name}
                        />
                        <span className="text-sm font-medium text-gray-700">{worker.name}</span>
                        <button
                          onClick={() => removeSelectedWorker(workerId)}
                          className="ml-2 text-green-600 hover:text-green-800 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    );
                  })}
                </div>

                <button
                  onClick={() => handleImportWorkers()}
                  disabled={isImporting}
                  className="w-full bg-green-600 text-white py-3 px-6 rounded-xl font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  {isImporting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Importing...</span>
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4" />
                      <span>Import {selectedWorkers.length} Worker{selectedWorkers.length !== 1 ? 's' : ''} to {siteId}</span>
                    </>
                  )}
                </button>
              </div>
            )}

            {/* Workers Table */}
            <WorkerTable
              workers={filteredWorkers}
              selectedWorkers={selectedWorkers}
              onWorkerSelect={handleWorkerSelect}
              onSelectAll={handleSelectAll}
              onClearSelection={clearAllSelections}
              onViewWorker={handleViewWorker}
              onImportWorkers={handleImportWorkers}
              isImporting={isImporting}
            />
          </div>
        )}

        {/* Worker Detail View */}
        {activeTab === 'import' && viewingWorker && (
          <div className="space-y-6">
            {/* Back Button */}
            <button
              onClick={handleCloseWorkerView}
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to worker list
            </button>

            {/* Worker Detail Card */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
              {/* Header */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 px-8 py-6 border-b border-gray-200">
                <div className="flex items-start space-x-6">
                  <img
                    className="h-20 w-20 rounded-xl object-cover"
                    src={viewingWorker.photoUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(viewingWorker.name)}&background=10B981&color=fff&size=80`}
                    alt={viewingWorker.name}
                  />
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-gray-900">{viewingWorker.name}</h2>
                    <p className="text-lg text-gray-600 mb-2">{viewingWorker.employeeNumber}</p>
                    <p className="text-gray-600">{viewingWorker.email}</p>

                    <div className="flex items-center space-x-4 mt-4">
                      {/* Selection Toggle */}
                      <button
                        onClick={() => handleWorkerSelect(viewingWorker.id)}
                        className={`inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                          selectedWorkers.includes(viewingWorker.id)
                            ? 'bg-green-600 text-white hover:bg-green-700'
                            : 'bg-white text-green-600 border border-green-600 hover:bg-green-50'
                        }`}
                      >
                        {selectedWorkers.includes(viewingWorker.id) ? (
                          <>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Selected for Import
                          </>
                        ) : (
                          <>
                            <Plus className="w-4 h-4 mr-2" />
                            Select for Import
                          </>
                        )}
                      </button>

                      {/* Compliance Status */}
                      <span className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium ${getComplianceStatusColor(viewingWorker.complianceStatus)}`}>
                        {getComplianceStatusIcon(viewingWorker.complianceStatus)}
                        <span className="ml-2">{getComplianceStatusText(viewingWorker.complianceStatus)}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Comprehensive Details Grid */}
              <div className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {/* Personal Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Full Name</label>
                        <p className="text-gray-900">{viewingWorker.name}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Employee Number</label>
                        <p className="text-gray-900">{viewingWorker.employeeNumber}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">National ID</label>
                        <p className="text-gray-900">{viewingWorker.nationalId}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Gender</label>
                        <p className="text-gray-900">{viewingWorker.gender}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                        <p className="text-gray-900">{viewingWorker.dateOfBirth ? new Date(viewingWorker.dateOfBirth).toLocaleDateString() : 'Not provided'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Age</label>
                        <p className="text-gray-900">{viewingWorker.age} years</p>
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Email</label>
                        <p className="text-gray-900">{viewingWorker.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Phone Number</label>
                        <p className="text-gray-900">{viewingWorker.phoneNumber}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Company</label>
                        <p className="text-gray-900">{viewingWorker.company}</p>
                      </div>
                    </div>
                  </div>

                  {/* Employment Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Employment</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Hire Date</label>
                        <p className="text-gray-900">{new Date(viewingWorker.hireDate).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Status</label>
                        <p className="text-gray-900 capitalize">{viewingWorker.status}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Performance Rating</label>
                        <div className="flex items-center space-x-2">
                          <span className="text-xl font-bold text-gray-900">{viewingWorker.performanceRating.toFixed(1)}</span>
                          <span className="text-gray-500">/5.0</span>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Man Hours</label>
                        <p className="text-gray-900">{viewingWorker.manHours.toLocaleString()} hours</p>
                      </div>
                    </div>
                  </div>

                  {/* Trades & Skills */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Trades</h3>
                    <div className="space-y-2">
                      {viewingWorker.trades.map(trade => (
                        <div key={trade.id} className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="font-medium text-gray-900">{trade.name}</span>
                        </div>
                      ))}
                    </div>

                    <h4 className="text-md font-semibold text-gray-900 mt-6 mb-3">Skills</h4>
                    <div className="flex flex-wrap gap-2">
                      {viewingWorker.skills.map(skill => (
                        <span key={skill.id} className="inline-flex items-center px-2.5 py-1 rounded-lg text-xs font-medium bg-blue-100 text-blue-700">
                          {skill.name}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Current Assignment */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Assignment</h3>
                    {viewingWorker.currentSiteId ? (
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3">
                          <MapPin className="w-5 h-5 text-blue-500" />
                          <span className="text-gray-900">Assigned to {viewingWorker.currentSiteId}</span>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-500">Total Site Assignments</label>
                          <p className="text-gray-900">{viewingWorker.totalSiteAssignments}</p>
                        </div>
                        {viewingWorker.lastAssignmentDate && (
                          <div>
                            <label className="text-sm font-medium text-gray-500">Last Assignment Date</label>
                            <p className="text-gray-900">{new Date(viewingWorker.lastAssignmentDate).toLocaleDateString()}</p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        </div>
                        <span className="text-gray-900">Available for assignment</span>
                      </div>
                    )}
                  </div>

                  {/* Medical & Safety */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Medical & Safety</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Induction Date</label>
                        <p className="text-gray-900">{viewingWorker.inductionDate ? new Date(viewingWorker.inductionDate).toLocaleDateString() : 'Not provided'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Medical Check Date</label>
                        <p className="text-gray-900">{viewingWorker.medicalCheckDate ? new Date(viewingWorker.medicalCheckDate).toLocaleDateString() : 'Not provided'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Trainings Completed</label>
                        <p className="text-gray-900">{viewingWorker.trainingsCompleted}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Training Compliance Section */}
                {viewingWorker.trainingCompliance && viewingWorker.trainingCompliance.length > 0 && (
                  <div className="mt-8 pt-8 border-t border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Training Compliance</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {viewingWorker.trainingCompliance.map(training => (
                        <div key={training.id} className="bg-gray-50 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-gray-900">{training.trainingName}</h4>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getComplianceStatusColor(training.complianceStatus)}`}>
                              {getComplianceStatusIcon(training.complianceStatus)}
                              <span className="ml-1">{getComplianceStatusText(training.complianceStatus)}</span>
                            </span>
                          </div>
                          {training.completionDate && (
                            <p className="text-sm text-gray-600">
                              Completed: {new Date(training.completionDate).toLocaleDateString()}
                            </p>
                          )}
                          {training.expiryDate && (
                            <p className="text-sm text-gray-600">
                              Expires: {new Date(training.expiryDate).toLocaleDateString()}
                            </p>
                          )}
                          {training.notes && (
                            <p className="text-sm text-gray-500 mt-2">{training.notes}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Notes Section */}
                {viewingWorker.notes && (
                  <div className="mt-8 pt-8 border-t border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Notes</h3>
                    <p className="text-gray-700 bg-gray-50 rounded-lg p-4">{viewingWorker.notes}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Universal Filter Modal */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Available Workers"
        filters={filterConfig}
        initialValues={activeFilters}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        size="xl"
      />
    </FloatingCard>
  );
};

export default CreateWorkerPage;
