import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Building,
  MapPin,
  Grid3X3,
  Users,
  Shield,
  Save,
  X,
  ChevronLeft,
  ChevronRight,
  Loader2
} from 'lucide-react';

// Import layout components
import FloatingCard from '../components/layout/FloatingCard';

// Import step components
import BasicInfoStep from '../components/site-creation/BasicInfoStep';
import SiteBoundaryStep from '../components/site-creation/SiteBoundaryStep';
import AreasStep from '../components/site-creation/AreasStep';
import StakeholdersStep from '../components/site-creation/StakeholdersStep';
import RegulatoryStep from '../components/site-creation/RegulatoryStep';

// Import supporting components
import StepSidebar from '../components/site-creation/StepSidebar';

// Types for site creation data
export interface SiteCreationData {
  basicInfo: {
    siteName: string;
    projectType: string;
    constructionType: string;
    plannedStartDate: string;
    plannedEndDate: string;
    estimatedBudget: number;
    currency: string;
    clientName: string;
    projectManagerName: string;
    description?: string;
  };
  siteBoundary: {
    searchQuery: string;
    displayName: string;
    addressStreet: string;
    addressCity: string;
    addressCounty: string;
    addressPostalCode: string;
    addressCountry: string;
    latitude: number;
    longitude: number;
    accuracy: string;
    osmPlaceId: string;
    osmType: string;
    osmId: string;
    geometry: {
      type: 'Polygon';
      coordinates: number[][][];
    } | null;
    drawingComplete: boolean;
    lastModified: string;
    calculatedArea?: number;
    calculatedPerimeter?: number;
  };
  areas: {
    areaNames: string[];
  };
  stakeholders: {
    clientEmail: string;
    clientPhone: string;
    projectManagerEmail: string;
    projectManagerPhone: string;
    mainContractorName: string;
    mainContractorEmail: string;
    mainContractorCompany: string;
    architectName: string;
    architectEmail: string;
    architectCompany: string;
    additionalStakeholders: any[];
  };
  regulatory: {
    buildingPermitRequired: boolean;
    buildingPermitStatus: string;
    buildingPermitNumber: string;
    buildingPermitAuthority: string;
    buildingPermitDate: string;
    buildingPermitExpiry: string;
    buildingPermitDocument: any;
    nemaEiaRequired: boolean;
    nemaEiaStatus: string;
    nemaEiaNumber: string;
    nemaEiaDate: string;
    nemaEiaDocument: any;
    ncaRegistrationRequired: boolean;
    ncaRegistrationStatus: string;
    ncaRegistrationNumber: string;
    ncaRegistrationDate: string;
    ncaRegistrationDocument: any;
    fireSafetyRequired: boolean;
    fireSafetyStatus: string;
    fireSafetyNumber: string;
    fireSafetyDate: string;
    fireSafetyDocument: any;
    overallComplianceStatus: string;
    additionalApprovals: any[];
  };
}

const STEPS = [
  {
    id: 1,
    name: 'Basic Info',
    description: 'Project details & timeline',
    icon: Building,
    component: BasicInfoStep
  },
  {
    id: 2,
    name: 'Site Boundary',
    description: 'Location & boundary drawing',
    icon: MapPin,
    component: SiteBoundaryStep
  },
  {
    id: 3,
    name: 'Areas',
    description: 'Define site areas',
    icon: Grid3X3,
    component: AreasStep
  },
  {
    id: 4,
    name: 'Stakeholders',
    description: 'Key contacts & roles',
    icon: Users,
    component: StakeholdersStep
  },
  {
    id: 5,
    name: 'Regulatory',
    description: 'Required approvals',
    icon: Shield,
    component: RegulatoryStep
  }
];

// Simple workflow state management hook
const useSiteCreationWorkflow = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [siteData, setSiteData] = useState<SiteCreationData>({
    basicInfo: {
      siteName: '',
      projectType: '',
      constructionType: '',
      plannedStartDate: '',
      plannedEndDate: '',
      estimatedBudget: 0,
      currency: 'KES',
      clientName: '',
      projectManagerName: '',
      description: ''
    },
    siteBoundary: {
      searchQuery: '',
      displayName: '',
      addressStreet: '',
      addressCity: '',
      addressCounty: '',
      addressPostalCode: '',
      addressCountry: 'Kenya',
      latitude: 0,
      longitude: 0,
      accuracy: '',
      osmPlaceId: '',
      osmType: '',
      osmId: '',
      geometry: null,
      drawingComplete: false,
      lastModified: new Date().toISOString(),
      calculatedArea: undefined,
      calculatedPerimeter: undefined
    },
    areas: {
      areaNames: []
    },
    stakeholders: {
      clientEmail: '',
      clientPhone: '',
      projectManagerEmail: '',
      projectManagerPhone: '',
      mainContractorName: '',
      mainContractorEmail: '',
      mainContractorCompany: '',
      architectName: '',
      architectEmail: '',
      architectCompany: '',
      additionalStakeholders: []
    },
    regulatory: {
      buildingPermitRequired: true,
      buildingPermitStatus: 'Not Started',
      buildingPermitNumber: '',
      buildingPermitAuthority: '',
      buildingPermitDate: '',
      buildingPermitExpiry: '',
      buildingPermitDocument: null,
      nemaEiaRequired: false,
      nemaEiaStatus: 'Not Started',
      nemaEiaNumber: '',
      nemaEiaDate: '',
      nemaEiaDocument: null,
      ncaRegistrationRequired: true,
      ncaRegistrationStatus: 'Not Started',
      ncaRegistrationNumber: '',
      ncaRegistrationDate: '',
      ncaRegistrationDocument: null,
      fireSafetyRequired: true,
      fireSafetyStatus: 'Not Started',
      fireSafetyNumber: '',
      fireSafetyDate: '',
      fireSafetyDocument: null,
      overallComplianceStatus: 'Incomplete',
      additionalApprovals: []
    }
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const goToStep = useCallback((stepId: number) => {
    setCurrentStep(stepId);
  }, []);

  const nextStep = useCallback(() => {
    if (currentStep < STEPS.length) {
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep]);
      }
      setCurrentStep(prev => prev + 1);
    }
  }, [currentStep, completedSteps]);

  const previousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  }, [currentStep]);

  const updateStepData = useCallback((stepId: number, stepData: any) => {
    const stepKey = getStepDataKey(stepId);
    setSiteData(prev => ({
      ...prev,
      [stepKey]: { ...prev[stepKey], ...stepData }
    }));
    setHasUnsavedChanges(true);
  }, []);

  const getStepDataKey = (stepId: number): keyof SiteCreationData => {
    const keyMap: Record<number, keyof SiteCreationData> = {
      1: 'basicInfo',
      2: 'siteBoundary',
      3: 'areas',
      4: 'stakeholders',
      5: 'regulatory'
    };
    return keyMap[stepId] || 'basicInfo';
  };

  const saveDraft = useCallback(async () => {
    setIsSaving(true);
    try {
      // Mock save operation
      await new Promise(resolve => setTimeout(resolve, 500));
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error('Failed to save draft:', error);
    } finally {
      setIsSaving(false);
    }
  }, []);

  const createSite = useCallback(async (): Promise<string> => {
    setIsLoading(true);
    try {
      // Mock site creation
      await new Promise(resolve => setTimeout(resolve, 2000));
      const siteId = `site_${Date.now()}`;
      return siteId;
    } catch (error) {
      console.error('Failed to create site:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearSession = useCallback(() => {
    setCurrentStep(1);
    setCompletedSteps([]);
    setHasUnsavedChanges(false);
  }, []);

  return {
    currentStep,
    completedSteps,
    siteData,
    isLoading,
    isSaving,
    goToStep,
    nextStep,
    previousStep,
    updateStepData,
    saveDraft,
    createSite,
    hasUnsavedChanges: () => hasUnsavedChanges,
    clearSession
  };
};

const NewSitePage: React.FC = () => {
  const navigate = useNavigate();
  const [showExitConfirm, setShowExitConfirm] = useState(false);



  // Use site creation workflow hook
  const {
    currentStep,
    completedSteps,
    siteData,
    isLoading,
    isSaving,
    goToStep,
    nextStep,
    previousStep,
    updateStepData,
    saveDraft,
    createSite,
    hasUnsavedChanges,
    clearSession
  } = useSiteCreationWorkflow();



  // Auto-save every 30 seconds
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (hasUnsavedChanges()) {
        saveDraft();
      }
    }, 30000);

    return () => clearInterval(autoSaveInterval);
  }, [hasUnsavedChanges, saveDraft]);

  const handleStepClick = useCallback((stepId: number) => {
    // Only allow navigation to completed steps or the next step
    if (completedSteps.includes(stepId) || stepId === currentStep || stepId === currentStep + 1) {
      goToStep(stepId);
    }
  }, [completedSteps, currentStep, goToStep]);

  const handleStepComplete = useCallback((stepData: any) => {
    updateStepData(currentStep, stepData);
    // Auto-save after step completion
    setTimeout(() => saveDraft(), 500);
  }, [currentStep, updateStepData, saveDraft]);

  const handleNext = useCallback(() => {
    nextStep();
  }, [nextStep]);

  const handlePrevious = useCallback(() => {
    previousStep();
  }, [previousStep]);

  const handleExit = useCallback(() => {
    if (hasUnsavedChanges()) {
      setShowExitConfirm(true);
    } else {
      navigate('/');
    }
  }, [hasUnsavedChanges, navigate]);

  const confirmExit = useCallback(() => {
    clearSession();
    navigate('/');
  }, [clearSession, navigate]);

  const handleCreateSite = useCallback(async () => {
    try {
      const siteId = await createSite();
      navigate(`/sites/${siteId}/dashboard`);
    } catch (error) {
      console.error('Site creation failed:', error);
    }
  }, [createSite, navigate]);

  // Calculate progress percentage
  const progressPercentage = (completedSteps.length / STEPS.length) * 100;

  return (
    <>
      <FloatingCard
        layout="custom"
        topBarOnBack={handleExit}
        topBarRightActions={
          <>
            {isSaving && (
              <span className="hidden md:inline-flex items-center text-xs text-gray-500 mr-2">
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Auto-saving...
              </span>
            )}
            <button
              onClick={handleExit}
              className="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <Save className="h-4 w-4 mr-2" />
              Save and Close
            </button>
          </>
        }
      >
        {/* Site Creation Layout with Sidebar */}
        <div className="flex h-full flex-col">
          <div className="flex flex-1 overflow-hidden">
            {/* Step Sidebar - Following design specifications */}
            <div className="step-sidebar">
              <StepSidebar
                steps={STEPS.map(step => ({
                  id: step.id,
                  title: step.name,
                  description: step.description,
                  isCompleted: completedSteps.includes(step.id),
                  isActive: step.id === currentStep,
                  hasErrors: false,
                  errorCount: 0
                }))}
                currentStep={currentStep}
                onStepClick={handleStepClick}
                progressPercentage={Math.round((completedSteps.length / STEPS.length) * 100)}
              />
            </div>

            {/* Main Content Area */}
            <div className="main-content">
              {/* Mobile Step Indicator */}
              <div className="lg:hidden mb-6">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">
                    Step {currentStep} of {STEPS.length}
                  </span>
                  <span className="text-sm text-gray-500">
                    {Math.round(progressPercentage)}% Complete
                  </span>
                </div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
              </div>

              {/* Step Component */}
              <div className="form-container">
                {currentStep === 1 && (
                  <BasicInfoStep
                    data={siteData.basicInfo}
                    onComplete={handleStepComplete}
                    onNext={handleNext}
                    onPrevious={handlePrevious}
                    isFirstStep={true}
                    isLastStep={false}
                  />
                )}
                {currentStep === 2 && (
                  <SiteBoundaryStep
                    data={siteData.siteBoundary}
                    onComplete={handleStepComplete}
                    onNext={handleNext}
                    onPrevious={handlePrevious}
                    isFirstStep={false}
                    isLastStep={false}
                  />
                )}
                {currentStep === 3 && (
                  <AreasStep
                    data={siteData.areas}
                    onComplete={handleStepComplete}
                    onNext={handleNext}
                    onPrevious={handlePrevious}
                    isFirstStep={false}
                    isLastStep={false}
                  />
                )}
                {currentStep === 4 && (
                  <StakeholdersStep
                    data={siteData.stakeholders}
                    onComplete={handleStepComplete}
                    onNext={handleNext}
                    onPrevious={handlePrevious}
                    isFirstStep={false}
                    isLastStep={false}
                  />
                )}
                {currentStep === 5 && (
                  <RegulatoryStep
                    data={siteData.regulatory}
                    onComplete={handleStepComplete}
                    onNext={handleNext}
                    onPrevious={handlePrevious}
                    isFirstStep={false}
                    isLastStep={true}
                    onCreateSite={handleCreateSite}
                    isCreating={isLoading}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Form Navigation Footer - Connected to FloatingCard bottom */}
          <div className="border-t border-gray-200 bg-white px-8 py-4 flex-shrink-0" style={{ marginLeft: '320px' }}>
            <div className="flex items-center justify-between">
              {/* Cancel Button */}
              <button
                onClick={() => setShowExitConfirm(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </button>

              {/* Navigation Buttons */}
              <div className="flex items-center space-x-3">
                <button
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Previous
                </button>

                {currentStep < STEPS.length ? (
                  <button
                    onClick={handleNext}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </button>
                ) : (
                  <button
                    onClick={handleCreateSite}
                    disabled={isLoading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Creating Site...
                      </>
                    ) : (
                      'Create Site'
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </FloatingCard>

      {/* Exit Confirmation Modal */}
      {showExitConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <X className="h-6 w-6 text-red-600 mr-3" />
              <h3 className="text-lg font-semibold">Exit Site Creation?</h3>
            </div>
            <p className="text-gray-600 mb-6">
              You have unsaved changes. Your progress will be automatically saved and you can continue later.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowExitConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Continue Editing
              </button>
              <button
                onClick={confirmExit}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Exit Anyway
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default NewSitePage;
