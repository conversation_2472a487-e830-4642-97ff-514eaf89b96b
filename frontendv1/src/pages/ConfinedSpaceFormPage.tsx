import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, X } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { confinedSpaceFormData, title } from '../utils/confinedSpaceEntrPTW';

interface ConfinedSpaceFormData {
  serialNumber: string;
  formData: { [key: string]: any };
  signoffData: Array<{ [key: string]: any }>;
}

const ConfinedSpaceFormPage: React.FC = () => {
  const {} = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Generate random serial number
  const [serialNumber] = useState(() => Math.floor(Math.random() * 1000000).toString());

  const [formData, setFormData] = useState<ConfinedSpaceFormData>({
    serialNumber,
    formData: {
      // Auto-populate date and time fields with current time and 24 hours later
      'Basic Information_Starting from': new Date().toISOString().slice(0, 16),
      'Basic Information_Ending at': new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 24 hours later
    },
    signoffData: [
      { Name: '', Designation: '', Signature: '', Time: '' },
      { Name: '', Designation: '', Signature: '', Time: '' },
      { Name: '', Designation: '', Signature: '', Time: '' }
    ]
  });

  // State for additional emergency arrangements
  const [additionalEmergencyArrangements, setAdditionalEmergencyArrangements] = useState<string[]>([]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      formData: { ...prev.formData, [field]: value }
    }));
  };

  const handleTableChange = (rowIndex: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      signoffData: prev.signoffData.map((row, index) =>
        index === rowIndex ? { ...row, [field]: value } : row
      )
    }));
  };

  const addTableRow = () => {
    const newRow = { Name: '', Designation: '', Signature: '', Time: '' };
    setFormData(prev => ({
      ...prev,
      signoffData: [...prev.signoffData, newRow]
    }));
  };

  const removeTableRow = (rowIndex: number) => {
    if (formData.signoffData.length > 1) {
      setFormData(prev => ({
        ...prev,
        signoffData: prev.signoffData.filter((_, index) => index !== rowIndex)
      }));
    }
  };

  const addEmergencyArrangement = () => {
    const arrangementText = formData.formData['Emergency Guidelines_Additional Emergency Arrangements'];
    if (arrangementText && arrangementText.trim()) {
      setAdditionalEmergencyArrangements(prev => [...prev, arrangementText.trim()]);
      // Clear the input field after adding
      setFormData(prev => ({
        ...prev,
        formData: {
          ...prev.formData,
          'Emergency Guidelines_Additional Emergency Arrangements': ''
        }
      }));
    }
  };

  const removeEmergencyArrangement = (index: number) => {
    setAdditionalEmergencyArrangements(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Confined Space Form submitted:', formData);
      alert('Confined Space Entry form submitted successfully!');
      navigate(-1);
    } catch (error) {
      console.error('Error submitting Confined Space form:', error);
      alert('Error submitting form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderField = (field: any, sectionKey: string, disabled: boolean = false) => {
    const fieldKey = `${sectionKey}_${field.name}`;
    let value = formData.formData[fieldKey] || '';

    // Handle name mirroring from Permit Issue to Permit Return
    if (disabled && sectionKey.includes('Permit Return') && field.name === 'Name') {
      const issueKey = sectionKey.replace('Permit Return', 'Permit Issue');
      const issueValue = formData.formData[issueKey] || '';
      value = issueValue;
    }

    const baseClassName = `w-full px-2 py-1 text-sm border border-gray-300 rounded-md ${disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'focus:ring-green-500 focus:border-green-500'}`;

    switch (field.type) {
      case 'text':
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'textarea':
        return (
          <textarea
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            rows={2}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'checkbox':
        return (
          <input
            type="checkbox"
            checked={value || false}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.checked)}
            className={`h-4 w-4 text-green-600 border-gray-300 rounded ${disabled ? 'cursor-not-allowed' : 'focus:ring-green-500'}`}
            required={field.required}
            disabled={disabled}
          />
        );
      case 'date':
        return (
          <input
            type="date"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'time':
        return (
          <input
            type="time"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'datetime':
        return (
          <input
            type="datetime-local"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'signature':
        return (
          <div className={`border border-gray-300 rounded-md p-2 text-center text-xs ${disabled ? 'bg-gray-100 text-gray-400' : 'bg-gray-50 text-gray-500'}`}>
            Signature Pad
          </div>
        );
      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
    }
  };

  return (
    <FloatingCard title="Confined Space Entry Form">
      <div className="h-full bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Confined Space Entry Form</h1>
                <p className="text-xs text-gray-500">{title}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="w-full p-4">
          <div className="space-y-3">
            {/* Header Section */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-2">
              <div className="text-center">
                <div className="flex items-center justify-center gap-4 mb-1">
                  <h2 className="text-base font-bold text-blue-800">
                    CONFINED SPACE ENTRY PERMIT
                  </h2>
                  <span className="text-xs text-gray-500 font-light">Serial No: {formData.serialNumber}</span>
                </div>
                <div className="text-xs text-red-600 font-medium">
                  This permit is valid for the specified work period only.
                </div>
              </div>
            </div>

            {/* Form Sections */}
            {confinedSpaceFormData.map((section, sectionIndex) => {
              // Handle special sections
              if (section.specialSection) {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">{section.title}</h3>
                    
                    {section.items && section.items.map((item: any, itemIndex: number) => {
                      // Each item contains multiple reading levels (Top, Mid, Bottom)
                      return Object.keys(item).map((itemKey, keyIndex) => {
                        const itemFields = item[itemKey];

                        return (
                          <div key={`${itemIndex}-${keyIndex}`} className="mb-4">
                            <h4 className="text-sm font-medium text-gray-800 mb-2">{itemKey}</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                              {itemFields.map((field: any, fieldIndex: number) => (
                                <div key={fieldIndex}>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">
                                    {field.name} {field.extraDetails && <span className="text-gray-500 text-xs">{field.extraDetails}</span>} {field.required && '*'}
                                  </label>
                                  {renderField(field, `${section.title}_${itemKey}`)}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      });
                    })}
                  </div>
                );
              }

              // Handle guidelines section
              if (section.type === 'guidelines') {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">{section.title}</h3>
                    <div className="space-y-1 mb-3">
                      {section.points?.map((point: string, pointIndex: number) => (
                        <div key={pointIndex} className="flex items-start space-x-2">
                          <span className="text-xs font-medium text-gray-600 mt-1">{pointIndex + 1}.</span>
                          <p className="text-xs text-gray-700">{point}</p>
                        </div>
                      ))}
                      {/* Display additional emergency arrangements */}
                      {additionalEmergencyArrangements.map((arrangement, arrIndex) => (
                        <div key={`additional-${arrIndex}`} className="flex items-start space-x-2">
                          <span className="text-xs font-medium text-gray-600 mt-1">{(section.points?.length ?? 0) + arrIndex + 1}.</span>
                          <p className="text-xs text-gray-700 flex-1">{arrangement}</p>
                          <button
                            onClick={() => removeEmergencyArrangement(arrIndex)}
                            className="text-red-600 hover:text-red-800 text-xs"
                            title="Remove arrangement"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                    {section.boldedPoint && (
                      <div className="mt-4">
                        <p className="font-semibold text-gray-900 mb-2">{section.boldedPoint}</p>
                        {section.additionalArrangements && (
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              {section.additionalArrangements.name}
                            </label>
                            <div className="flex space-x-2">
                              <div className="flex-1">
                                {renderField(section.additionalArrangements, 'Emergency Guidelines')}
                              </div>
                              <button
                                onClick={addEmergencyArrangement}
                                className="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200"
                                type="button"
                              >
                                Add
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              }

              // Handle signoff section
              if (section.type === 'signoff') {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">{section.title}</h3>
                    {section.description && (
                      <p className="text-xs text-gray-600 mb-3">{section.description}</p>
                    )}
                    
                    {/* Observer Information */}
                    {section.items && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                        {section.items.map((field: any, fieldIndex: number) => (
                          <div key={fieldIndex}>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              {field.name} {field.required && '*'}
                            </label>
                            {renderField(field, section.title)}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Signoff Table */}
                    <div className="overflow-x-auto">
                      <table className="min-w-full border border-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                              S/No
                            </th>
                            {section.tableHeader?.map((header: any, headerIndex: number) => (
                              <th key={headerIndex} className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                {header.name}
                              </th>
                            ))}
                            <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Action
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {formData.signoffData.map((row, rowIndex) => (
                            <tr key={rowIndex}>
                              <td className="px-2 py-1 whitespace-nowrap text-sm text-gray-900 border-r border-gray-300">
                                {rowIndex + 1}
                              </td>
                              {section.tableHeader?.map((header: any, headerIndex: number) => (
                                <td key={headerIndex} className="px-2 py-1 whitespace-nowrap border-r border-gray-300">
                                  {header.type === 'signature' ? (
                                    <div className="w-20 h-6 border border-gray-300 rounded bg-gray-50 text-xs text-center leading-6 text-gray-500">
                                      Sign
                                    </div>
                                  ) : header.type === 'time' ? (
                                    <input
                                      type="time"
                                      value={row[header.name] || ''}
                                      onChange={(e) => handleTableChange(rowIndex, header.name, e.target.value)}
                                      className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                                    />
                                  ) : (
                                    <input
                                      type="text"
                                      value={row[header.name] || ''}
                                      onChange={(e) => handleTableChange(rowIndex, header.name, e.target.value)}
                                      className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                                    />
                                  )}
                                </td>
                              ))}
                              <td className="px-2 py-1 whitespace-nowrap">
                                <button
                                  onClick={() => removeTableRow(rowIndex)}
                                  className="text-red-600 hover:text-red-800"
                                  disabled={formData.signoffData.length <= 1}
                                >
                                  <X className="h-3 w-3" />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    <button
                      onClick={addTableRow}
                      className="mt-2 px-3 py-1 text-xs font-medium text-green-700 bg-green-100 border border-green-300 rounded-md hover:bg-green-200"
                    >
                      Add Row
                    </button>
                  </div>
                );
              }

              // Handle Permit Issue section
              if (section['Permit Issue']) {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">Permit Issue</h3>
                    {section['Permit Issue'].description && (
                      <p className="text-xs text-gray-600 mb-3">{section['Permit Issue'].description}</p>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {section['Permit Issue'].items?.map((item: any, itemIndex: number) => {
                        const itemKey = Object.keys(item)[0];
                        const itemFields = item[itemKey];

                        return (
                          <div key={itemIndex} className="border border-gray-200 rounded p-3">
                            <h4 className="text-sm font-medium text-gray-800 mb-2">{itemKey}</h4>
                            <div className="space-y-2">
                              {itemFields.map((field: any, fieldIndex: number) => (
                                <div key={fieldIndex}>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">
                                    {field.name} {field.required && '*'}
                                  </label>
                                  {renderField(field, `Permit Issue_${itemKey}`)}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              }

              // Handle Permit Return section (grayed out and disabled)
              if (section['Permit Return']) {
                return (
                  <div key={sectionIndex} className="bg-gray-100 border border-gray-200 rounded-lg p-3 opacity-60">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">Permit Return</h3>
                    {section['Permit Return'].description && (
                      <p className="text-xs text-gray-600 mb-3">{section['Permit Return'].description}</p>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {section['Permit Return'].items?.map((item: any, itemIndex: number) => {
                        const itemKey = Object.keys(item)[0];
                        const itemFields = item[itemKey];

                        return (
                          <div key={itemIndex} className="border border-gray-200 rounded p-3 bg-gray-50">
                            <h4 className="text-sm font-medium text-gray-800 mb-2">{itemKey}</h4>
                            <div className="space-y-2">
                              {itemFields.map((field: any, fieldIndex: number) => (
                                <div key={fieldIndex}>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">
                                    {field.name} {field.required && '*'}
                                  </label>
                                  {renderField(field, `Permit Return_${itemKey}`, true)}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              }

              // Handle regular sections with field arrays
              const sectionKey = Object.keys(section)[0];
              const sectionData = section[sectionKey as keyof typeof section];
              const fields = sectionData as any[];

              return (
                <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                  <h3 className="text-base font-semibold text-gray-900 mb-2">{sectionKey}</h3>

                  {/* For checkbox sections, use grid layout */}
                  {fields.every(field => field.type === 'checkbox') ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-1">
                      {fields.map((field, fieldIndex) => (
                        <div key={fieldIndex} className="flex items-center space-x-1">
                          {renderField(field, sectionKey)}
                          <label className="text-xs text-gray-700 leading-tight">{field.name}</label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    /* For other field types, use standard layout */
                    <div className="space-y-2">
                      {fields.map((field, fieldIndex) => (
                        <div key={fieldIndex}>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            {field.name} {field.required && '*'}
                          </label>
                          {renderField(field, sectionKey)}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={() => navigate(-1)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Form'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default ConfinedSpaceFormPage;
