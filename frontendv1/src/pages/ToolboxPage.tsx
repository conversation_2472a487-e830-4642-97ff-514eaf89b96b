import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';import {
  LayoutDashboard,  ListChecks,
  Users,  FileText,
  Calendar,
  Settings} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import TabContainer, { Tab } from '../components/data/shared/TabContainer';
import ToolboxDashboard from '../components/toolbox/ToolboxDashboard';
import ToolboxTasks from '../components/toolbox/ToolboxTasks';
import ToolboxInductions from '../components/toolbox/ToolboxInductions';
import ToolboxAttendance from '../components/toolbox/ToolboxAttendance';
import ToolboxReports from '../components/toolbox/ToolboxReports';
import ToolboxManagement from '../components/toolbox/ToolboxManagement';
import { SiteInfo } from '../types';
import { mockSite } from '../mock/siteData';
const ToolboxPage: React.FC = () => {  const { siteId } = useParams<{ siteId: string }>();
  const location = useLocation();  const [site] = useState<SiteInfo>(mockSite);
  const [activeTab, setActiveTab] = useState("dashboard");  const [selectedInduction, setSelectedInduction] = useState<string | null>(null);
  const validTabs = ['dashboard', 'tasks', 'inductions', 'attendance', 'reports', 'management'];
  // Handle URL hash navigation
  useEffect(() => {    const hash = location.hash.replace("#", "");
    if (hash && validTabs.includes(hash)) {      setActiveTab(hash);
    } else if (!hash) {      setActiveTab("dashboard");
    }  }, [location.hash]);
  // Handle tab navigation
  const handleNavigateToTab = (tabId: string) => {    setActiveTab(tabId);
    window.history.pushState(null, '', `#${tabId}`);  };
  // Handle induction selection
  const handleSelectInduction = (inductionId: string) => {    setSelectedInduction(inductionId);
    handleNavigateToTab('attendance');  };
  const breadcrumbs = [
    { name: "Dashboard", path: "/" },    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: "Toolbox Talks", path: `/sites/${siteId}/toolbox` },  ];
  const tabs: Tab[] = [
    {      id: 'dashboard',
      label: 'Dashboard',      icon: <LayoutDashboard className="h-4 w-4" />,
      content: <ToolboxDashboard siteId={siteId || ''} onNavigateToTab={handleNavigateToTab} />    },
    {      id: 'tasks',
      label: 'Tasks',      icon: <ListChecks className="h-4 w-4" />,
      content: <ToolboxTasks siteId={siteId || ''} />    },
    {      id: 'inductions',
      label: 'Inductions',      icon: <Calendar className="h-4 w-4" />,
      content: <ToolboxInductions siteId={siteId || ''} onSelectInduction={handleSelectInduction} />    },
    {      id: 'attendance',
      label: 'Attendance',      icon: <Users className="h-4 w-4" />,
      content: <ToolboxAttendance siteId={siteId || ''} selectedInduction={selectedInduction} />    },
    {      id: 'reports',
      label: 'Reports',      icon: <FileText className="h-4 w-4" />,
      content: <ToolboxReports siteId={siteId || ''} />    },
    {      id: 'management',
      label: 'Management',      icon: <Settings className="h-4 w-4" />,
      content: <ToolboxManagement siteId={siteId || ''} />    }
  ];
  return (    <FloatingCard title="Toolbox Talks" breadcrumbs={breadcrumbs}>
      <TabContainer        tabs={tabs}
        activeTab={activeTab}        onTabChange={handleNavigateToTab}
      />    </FloatingCard>
  );};

export default ToolboxPage;



















































