# Permit to Work (PTW) Form - Usage Guide

This guide explains how to access and use the Permit to Work (PTW) form system based on the General PTW sheet images structure.

## Available Routes

### 1. Demo Page (Recommended for Testing)
**URL:** `/demo/ptw-form`

This is a standalone demo page that showcases the PTW form system with:
- Overview of the 4-step process
- Feature explanations and safety notices
- Technical implementation details
- Easy access to start a new permit

**How to access:**
1. Start the development server: `npm run dev`
2. Navigate to: `http://localhost:5173/demo/ptw-form`
3. Click "Start New Permit" to begin the form process

### 2. General PTW Form
**URL:** `/ptw/form` or `/ptw/form/new`

Direct access to the PTW form for creating new permits.

### 3. Site-Specific PTW Form
**URL:** `/sites/{siteId}/ptw/form` or `/sites/{siteId}/ptw/form/{id}`

Access PTW forms within a specific site context.

**Example URLs:**
- `/sites/site-1/ptw/form`
- `/sites/westlands-site/ptw/form/new`

## 3-Step Form Process (Based on PTW Sheets)

The PTW form is divided into 3 distinct steps that correspond to the actual PTW sheet structure:

### Step 1: General Work Permit (GWP) - Sheet 1
**Purpose:** Complete permit application with all work details, team information, and safety requirements

**Sections:**
- **Header Information**: Permit number, type, site, area
- **Work Details**: Description, location, start/end dates and times, estimated duration
- **Applicant Information**: Name, company, position, phone, application date/time, signature
- **Work Team**: Number of workers, team member names, supervisor details
- **Hazard Identification**: Risk assessment, identified hazards, PPE requirements, control measures
- **Isolation Requirements**: Electrical, mechanical, process isolation details
- **Hot Work Permit**: If applicable - welding, cutting, fire watch requirements
- **Confined Space Entry**: If applicable - atmosphere testing, ventilation, emergency procedures

### Step 2: Work Area Inspection & Permit Renewal
**Purpose:** Authorized person inspection, safety verification, and permit renewal procedures

**Sections:**
- **Work Area Inspection**: Date/time (auto-generated), inspector name and position
- **Inspection Checklist**:
  - Area condition is safe for work
  - All identified hazards are controlled
  - Emergency equipment is available
  - Workers are competent for the task
- **Inspector Signature**: Digital signature for inspection approval
- **Permit Renewal**: If required - renewal date/time, renewal inspector details and signature

### Step 3: Authorizations & Sign-offs - Sheet 2
**Purpose:** Final approvals, work completion, and area handback documentation

**Sections:**
- **Permit Issuer Authorization**:
  - Issuer name, position, issue date/time (auto-generated), signature
- **Area Authority Authorization**:
  - Area authority name, position, authorization date/time (auto-generated), signature
- **Work Completion & Area Handback**:
  - Work completed by, completion date/time, completion signature
  - Area handed back by, handback date/time, handback signature
- **Additional Comments**: Final observations and conditions
- **Permit Summary**: Complete overview of all permit details including permit number, type, site, area, applicant, company, work period, inspector, and risk level

## Key Features

### 🔒 **Automated Timestamps**
- All date and time fields are automatically populated with current values
- Fields are read-only to prevent tampering
- Ensures accurate audit trail and compliance

### 📋 **Multi-Step Workflow**
- Sequential step completion with progress tracking
- Visual step indicator showing current position
- Navigation controls for moving between steps
- Form data persistence across steps

### ✅ **Validation & Safety**
- Required field validation
- Comprehensive hazard assessment
- Safety checklist verification
- Digital signature capture

### 📱 **Responsive Design**
- Works on desktop, tablet, and mobile devices
- Touch-friendly interface elements
- Optimized for field use

## Form Data Structure

```typescript
interface PTWFormData {
  // Basic Information
  permitNumber: string;
  workDescription: string;
  location: string;
  startDate: string;        // Auto-generated
  startTime: string;        // Auto-generated
  endDate: string;
  endTime: string;
  
  // Personnel Information
  applicantName: string;
  applicantSignature: string;
  contractorCompany: string;
  supervisorName: string;
  
  // Work Area Inspection
  inspectionDate: string;   // Auto-generated
  inspectionTime: string;   // Auto-generated
  inspectorName: string;
  inspectorSignature: string;
  inspectionComments: string;
  
  // Hazard Assessment
  hazards: { [key: string]: boolean };
  controlMeasures: { [key: string]: boolean };
  
  // Final Approval
  approverName: string;
  approverSignature: string;
  approvalDate: string;     // Auto-generated
  approvalTime: string;     // Auto-generated
  finalComments: string;
}
```

## Usage Workflow

### 1. **Start New Permit**
- Access the form via demo page or direct URL
- System generates unique permit number
- Current date/time automatically populated

### 2. **Complete Step 1: General Work Permit**
- Fill in work description and location
- Set end date and time for work completion
- Enter personnel information
- Provide applicant signature
- Click "Next" to proceed

### 3. **Complete Step 2: Work Area Inspection**
- Inspector reviews the work area
- Complete inspection checklist
- Add detailed inspection comments
- Provide inspector signature
- Click "Next" to proceed

### 4. **Complete Step 3: Hazard Assessment**
- Identify all relevant hazards
- Select appropriate control measures
- Review risk assessment summary
- Click "Next" to proceed

### 5. **Complete Step 4: Final Approval**
- Review permit summary
- Add final comments if needed
- Provide approver signature
- Submit the completed permit

## Integration Examples

### Adding to Site Navigation

```tsx
import { Link } from 'react-router-dom';
import { Shield } from 'lucide-react';

<Link 
  to={`/sites/${siteId}/ptw/form`}
  className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100"
>
  <Shield className="h-5 w-5 mr-3" />
  New PTW Permit
</Link>
```

### Adding to Quick Actions

```tsx
import QuickActionCard from '../components/data/shared/QuickActionCard';

<QuickActionCard
  title="Permit to Work"
  description="Create new work permit"
  icon={<Shield className="h-5 w-5" />}
  onClick={() => navigate('/ptw/form')}
/>
```

## Safety Compliance

### **Regulatory Requirements**
- Comprehensive hazard identification
- Authorized personnel approval
- Digital audit trail
- Time-stamped documentation

### **Best Practices**
- Complete all form sections thoroughly
- Ensure all hazards are identified
- Verify control measures are in place
- Obtain proper authorizations before work begins

### **Audit Trail**
- Auto-generated timestamps for compliance
- Digital signatures for accountability
- Comprehensive permit summary
- Immutable record keeping

## Technical Implementation

### **Based on General PTW.htm**
- Form structure derived from utils/General PTW.htm
- Multi-sheet concept translated to multi-step workflow
- Work area inspection separated into dedicated step
- Maintains original form logic and requirements

### **Modern Web Technologies**
- React with TypeScript for type safety
- Multi-step form with state management
- Responsive design with Tailwind CSS
- Form validation and error handling

### **Backend Integration Ready**
- Structured data format for API calls
- Error handling and loading states
- Success feedback and navigation
- Audit trail data capture
