import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import FloatingCard from '../components/layout/FloatingCard';
import TaskExplorer from '../components/tasks/TaskExplorer';
import TaskTabs from '../components/tasks/TaskTabs';
import { SiteTask } from '../types/tasks';
import { SiteInfo } from '../types';
import { mockSite, getMockTask } from '../mock/taskData';

const TaskDetailPage: React.FC = () => {
  const { siteId, taskId } = useParams<{ siteId: string; taskId: string }>();
  const [site] = useState<SiteInfo>(mockSite);
  const [task, setTask] = useState<SiteTask | null>(null);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [openTabs, setOpenTabs] = useState<Array<{
    id: string;
    title: string;
    type: 'details' | 'document' | 'hazards' | 'control-measures' | 'audit-trail' | 'requester' | 'schedule' | 'location';
    data?: any;
  }>>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);

  useEffect(() => {
    // Fetch task details
    if (taskId) {
      // Use centralized mock data
      const mockTask = getMockTask(taskId);
      setTask(mockTask);

      // Auto-open task details tab
      const detailsTab = {
        id: 'task-details',
        title: 'Task Details',
        type: 'details' as const,
        data: mockTask
      };
      setOpenTabs([detailsTab]);
      setActiveTabId('task-details');
    }
  }, [taskId, siteId]);



  const handleItemSelect = (itemId: string, itemType: string, itemData?: any) => {
    setSelectedItem(itemId);

    // Check if tab is already open
    const existingTab = openTabs.find(tab => tab.id === itemId);
    if (!existingTab) {
      const newTab = {
        id: itemId,
        title: itemData?.name || itemData?.title || `${itemType} ${itemId}`,
        type: itemType as any,
        data: itemData
      };
      setOpenTabs(prev => [...prev, newTab]);
    }
    setActiveTabId(itemId);
  };

  const handleTabClose = (tabId: string) => {
    setOpenTabs(prev => prev.filter(tab => tab.id !== tabId));
    if (activeTabId === tabId) {
      const remainingTabs = openTabs.filter(tab => tab.id !== tabId);
      setActiveTabId(remainingTabs.length > 0 ? remainingTabs[remainingTabs.length - 1].id : null);
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTabId(tabId);
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: task?.name || 'Task Details', path: `/sites/${siteId}/tasks/${taskId}` },
  ];

  if (!task) {
    return (
      <FloatingCard title="Task Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading task details...</div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Task Details" breadcrumbs={breadcrumbs} layout="custom">
      <div className="h-full flex overflow-hidden min-w-0">
        {/* Left Panel - Task Explorer */}
        <div className="w-80 flex-shrink-0 border-r border-gray-200 bg-[#fafaf8] flex flex-col">
          <TaskExplorer
            task={task}
            onItemSelect={handleItemSelect}
            selectedItem={selectedItem}
          />
        </div>

        {/* Right Panel - Task Tabs */}
        <div className="flex-1 bg-white min-w-0">
          <TaskTabs
            tabs={openTabs}
            activeTabId={activeTabId}
            onTabChange={handleTabChange}
            onTabClose={handleTabClose}
            task={task}
          />
        </div>
      </div>
    </FloatingCard>
  );
};

export default TaskDetailPage;
