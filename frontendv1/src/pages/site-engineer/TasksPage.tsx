import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  ChevronDown,
  ChevronRight,
  Calendar,
  MapPin,
  Clock,

  FileText,
  User,
  CheckCircle,
  XCircle,
  Pause
} from 'lucide-react';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';
import SiteEngineerLayout from '../../components/site-engineer/SiteEngineerLayout';

// Mock tasks data
const TASKS_DATA = [
  {
    id: 'task-001',
    name: 'Electrical Panel Installation',
    category: 'Electrical',
    status: 'in-progress',
    priority: 'high',
    dueDate: '2024-08-06',
    startTime: '08:00',
    estimatedDuration: 6,
    location: 'Building A - Ground Floor',
    assignedTo: '<PERSON>',
    description: 'Install main electrical panel for Building A ground floor. Includes connecting main power lines and setting up distribution circuits.',
    requirements: [
      'Electrical safety equipment required',
      'Power shutdown needed from 8:00 AM - 2:00 PM',
      'Coordination with facilities team'
    ],
    permitId: 'PRM-2024-001',
    permitStatus: 'approved',
    riskLevel: 'high',
    notes: 'Weather conditions are favorable. All materials are on site.'
  },
  {
    id: 'task-002',
    name: 'Foundation Excavation',
    category: 'Excavation',
    status: 'pending',
    priority: 'normal',
    dueDate: '2024-08-07',
    startTime: '07:00',
    estimatedDuration: 8,
    location: 'Site Compound - North Section',
    assignedTo: 'Mike Johnson',
    description: 'Excavate foundation area for new storage building. Depth: 2.5m, Area: 15m x 10m.',
    requirements: [
      'Heavy machinery operator certification',
      'Utility location verification completed',
      'Soil testing results available'
    ],
    permitId: 'PRM-2024-002',
    permitStatus: 'pending',
    riskLevel: 'medium',
    notes: 'Waiting for final permit approval. Equipment is ready.'
  },
  {
    id: 'task-003',
    name: 'Safety Inspection - Weekly',
    category: 'Safety',
    status: 'completed',
    priority: 'normal',
    dueDate: '2024-08-05',
    startTime: '09:00',
    estimatedDuration: 3,
    location: 'Entire Site',
    assignedTo: 'Sarah Wilson',
    description: 'Weekly safety inspection covering all work areas, equipment, and safety protocols.',
    requirements: [
      'Safety inspection checklist',
      'Digital camera for documentation',
      'Access to all work areas'
    ],
    permitId: null,
    permitStatus: null,
    riskLevel: 'low',
    notes: 'Inspection completed successfully. Minor issues documented and addressed.'
  },
  {
    id: 'task-004',
    name: 'HVAC System Maintenance',
    category: 'Maintenance',
    status: 'overdue',
    priority: 'high',
    dueDate: '2024-08-04',
    startTime: '10:00',
    estimatedDuration: 4,
    location: 'Main Building - Roof',
    assignedTo: 'David Brown',
    description: 'Quarterly maintenance of HVAC system including filter replacement and system diagnostics.',
    requirements: [
      'HVAC technician certification',
      'Replacement filters and parts',
      'Safety harness for roof work'
    ],
    permitId: 'PRM-2024-003',
    permitStatus: 'approved',
    riskLevel: 'medium',
    notes: 'OVERDUE: Rescheduled due to weather conditions. Priority completion required.'
  }
];

const TasksPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [site] = useState<SiteInfo>(mockSite);
  const [expandedTask, setExpandedTask] = useState<string | null>(null);

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTask(expandedTask === taskId ? null : taskId);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'in-progress':
        return <Pause className="h-5 w-5 text-blue-600" />;
      case 'overdue':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'in-progress': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'pending': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'overdue': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'normal': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const handleViewPermit = (permitId: string) => {
    navigate(`/sites/${siteId}/engineer/permits/${permitId}`);
  };

  return (
    <SiteEngineerLayout site={site} title="Tasks" showBackButton={true}>
      <div className="px-4 sm:px-6 py-4 sm:py-6">
        {/* Tasks Header */}
        <div className="flex items-center justify-between mb-4 sm:mb-6">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">All Tasks</h2>
            <p className="text-xs sm:text-sm text-gray-600 mt-1">{TASKS_DATA.length} tasks total</p>
          </div>
          <button
            onClick={() => navigate(`/sites/${siteId}/engineer/tasks/create`)}
            className="bg-blue-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-xs sm:text-sm font-medium"
          >
            <span className="hidden sm:inline">New Task</span>
            <span className="sm:hidden">New</span>
          </button>
        </div>

        {/* Task List - FAQ Style */}
        <div className="space-y-3">
          {TASKS_DATA.map((task) => (
            <div key={task.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              {/* Task Header - Clickable */}
              <button
                onClick={() => toggleTaskExpansion(task.id)}
                className="w-full p-3 sm:p-4 text-left hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2 sm:space-x-3 flex-1 min-w-0">
                    <div className="flex-shrink-0 mt-0.5">
                      {getStatusIcon(task.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-1 sm:mb-2">
                        <h3 className="font-semibold text-gray-900 text-sm sm:text-base leading-tight pr-2">{task.name}</h3>
                        <div className="flex items-center space-x-1 flex-shrink-0">
                          {/* Mobile: Priority and overdue as dots */}
                          <div className="sm:hidden flex items-center space-x-1">
                            <div className={`w-2 h-2 rounded-full ${task.priority === 'high' ? 'bg-red-500' : 'bg-blue-500'}`}></div>
                            {task.status === 'overdue' && (
                              <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
                            )}
                          </div>
                          {/* Desktop: Priority and overdue badges */}
                          <div className="hidden sm:flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                              {task.priority}
                            </span>
                            {task.status === 'overdue' && (
                              <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                                OVERDUE
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Mobile: Simplified info */}
                      <div className="sm:hidden">
                        <div className="text-xs text-gray-500 mb-1">{task.category}</div>
                        <div className="flex items-center justify-between text-xs text-gray-600">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(task.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span className="truncate max-w-[100px]">{task.location.split(' - ')[0]}</span>
                          </div>
                        </div>
                      </div>

                      {/* Desktop: Full info */}
                      <div className="hidden sm:flex items-center space-x-4 text-sm text-gray-600">
                        <span>{task.category}</span>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span className="truncate">{task.location}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 flex-shrink-0 ml-2">
                    {/* Mobile: Status dot */}
                    <div className={`w-3 h-3 rounded-full sm:hidden ${
                      task.status === 'completed' ? 'bg-green-500' :
                      task.status === 'in-progress' ? 'bg-blue-500' :
                      task.status === 'pending' ? 'bg-yellow-500' :
                      task.status === 'overdue' ? 'bg-red-500' : 'bg-gray-500'
                    }`}></div>
                    {/* Desktop: Status badge */}
                    <span className={`hidden sm:inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                      {task.status.replace('-', ' ')}
                    </span>
                    {expandedTask === task.id ? (
                      <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                    ) : (
                      <ChevronRight className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </button>

              {/* Expanded Task Details */}
              {expandedTask === task.id && (
                <div className="border-t border-gray-200 p-4 bg-gray-50">
                  <div className="space-y-4">
                    {/* Task Description */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                      <p className="text-sm text-gray-700 bg-white rounded-lg p-3 border">
                        {task.description}
                      </p>
                    </div>

                    {/* Task Details Grid */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Assigned To</span>
                          <div className="flex items-center space-x-2 mt-1">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-900">{task.assignedTo}</span>
                          </div>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Duration</span>
                          <div className="flex items-center space-x-2 mt-1">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-900">{task.estimatedDuration} hours</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Start Time</span>
                          <div className="text-sm text-gray-900 mt-1">{task.startTime}</div>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Risk Level</span>
                          <div className="mt-1">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(task.riskLevel)}`}>
                              {task.riskLevel} risk
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Requirements */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Requirements</h4>
                      <ul className="space-y-1">
                        {task.requirements.map((req, index) => (
                          <li key={index} className="text-sm text-gray-700 flex items-start space-x-2">
                            <span className="text-blue-600 mt-1">•</span>
                            <span>{req}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Permit Information */}
                    {task.permitId && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Permit Information</h4>
                        <div className="flex items-center justify-between bg-white rounded-lg p-3 border">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-4 w-4 text-gray-400" />
                            <div>
                              <div className="text-sm font-medium text-gray-900">{task.permitId}</div>
                              <div className="text-xs text-gray-500">
                                Status: <span className={`font-medium ${task.permitStatus === 'approved' ? 'text-green-600' : 'text-yellow-600'}`}>
                                  {task.permitStatus}
                                </span>
                              </div>
                            </div>
                          </div>
                          <button
                            onClick={() => handleViewPermit(task.permitId!)}
                            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                          >
                            View Permit
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Notes */}
                    {task.notes && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Notes</h4>
                        <p className="text-sm text-gray-700 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          {task.notes}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </SiteEngineerLayout>
  );
};

export default TasksPage;
