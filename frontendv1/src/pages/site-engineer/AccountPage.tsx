import React, { useState } from 'react';

import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Shield,
  Building,
  Clock,
  Award,

  Settings
} from 'lucide-react';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';
import SiteEngineerLayout from '../../components/site-engineer/SiteEngineerLayout';

// Mock user data
const USER_DATA = {
  id: 'eng-001',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  role: 'Site Engineer',
  department: 'Construction Operations',
  employeeId: 'ENG-2024-001',
  startDate: '2024-01-15',
  location: 'Downtown Construction Site',
  supervisor: '<PERSON>',
  certifications: [
    {
      name: 'Licensed Professional Engineer',
      issuer: 'State Engineering Board',
      expiryDate: '2025-12-31',
      status: 'active'
    },
    {
      name: 'OSHA 30-Hour Construction',
      issuer: 'OSHA Training Institute',
      expiryDate: '2025-06-15',
      status: 'active'
    },
    {
      name: 'First Aid/CPR Certified',
      issuer: 'American Red Cross',
      expiryDate: '2024-11-20',
      status: 'expiring_soon'
    }
  ],
  recentActivity: [
    {
      action: 'Created task',
      description: 'Electrical Panel Installation',
      timestamp: '2024-08-05T14:30:00Z'
    },
    {
      action: 'Viewed permit',
      description: 'PRM-2024-001',
      timestamp: '2024-08-05T12:15:00Z'
    },
    {
      action: 'Updated task status',
      description: 'Foundation Excavation - In Progress',
      timestamp: '2024-08-05T10:45:00Z'
    }
  ]
};

// Mock site assignment data
const SITE_ASSIGNMENT = {
  siteName: mockSite.name,
  siteId: mockSite.id,
  assignedDate: '2024-01-20',
  role: 'Lead Site Engineer',
  responsibilities: [
    'Daily task coordination and oversight',
    'Safety compliance monitoring',
    'Quality control inspections',
    'Progress reporting to management',
    'Permit and documentation management'
  ],
  workingHours: '7:00 AM - 5:00 PM',
  emergencyContact: '+1 (555) 999-0123'
};

const AccountPage: React.FC = () => {
  const [site] = useState<SiteInfo>(mockSite);

  const getCertificationStatus = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'expiring_soon':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'expired':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  return (
    <SiteEngineerLayout site={site} title="Account" showBackButton={true}>
      <div className="px-4 sm:px-6 py-4 sm:py-6 space-y-6">
        
        {/* User Profile Card */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-start space-x-4">
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 sm:h-10 sm:w-10 text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-1">{USER_DATA.name}</h2>
              <p className="text-sm sm:text-base text-gray-600 mb-2">{USER_DATA.role}</p>
              <p className="text-xs sm:text-sm text-gray-500">{USER_DATA.department}</p>
              <div className="mt-3 flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>Since {formatDate(USER_DATA.startDate)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Shield className="h-4 w-4" />
                  <span>ID: {USER_DATA.employeeId}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Mail className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">{USER_DATA.email}</div>
                <div className="text-xs text-gray-500">Work Email</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Phone className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">{USER_DATA.phone}</div>
                <div className="text-xs text-gray-500">Work Phone</div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <MapPin className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">{USER_DATA.location}</div>
                <div className="text-xs text-gray-500">Current Assignment</div>
              </div>
            </div>
          </div>
        </div>

        {/* Site Assignment */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Building className="h-5 w-5 mr-2" />
            Site Assignment
          </h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-gray-900">{SITE_ASSIGNMENT.siteName}</div>
                <div className="text-xs text-gray-500">Site Name</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900">{SITE_ASSIGNMENT.role}</div>
                <div className="text-xs text-gray-500">Role</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900">{formatDate(SITE_ASSIGNMENT.assignedDate)}</div>
                <div className="text-xs text-gray-500">Assigned Date</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900">{SITE_ASSIGNMENT.workingHours}</div>
                <div className="text-xs text-gray-500">Working Hours</div>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Key Responsibilities</h4>
              <ul className="space-y-1">
                {SITE_ASSIGNMENT.responsibilities.map((responsibility, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                    <span className="text-blue-600 mt-1">•</span>
                    <span>{responsibility}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Certifications */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Award className="h-5 w-5 mr-2" />
            Certifications
          </h3>
          <div className="space-y-4">
            {USER_DATA.certifications.map((cert, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-semibold text-gray-900 mb-1">{cert.name}</h4>
                    <p className="text-xs text-gray-600 mb-2">{cert.issuer}</p>
                    <p className="text-xs text-gray-500">Expires: {formatDate(cert.expiryDate)}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getCertificationStatus(cert.status)}`}>
                    {cert.status === 'expiring_soon' ? 'Expiring Soon' : cert.status.replace('_', ' ')}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Recent Activity
          </h3>
          <div className="space-y-3">
            {USER_DATA.recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900">{activity.action}</div>
                  <div className="text-sm text-gray-600">{activity.description}</div>
                  <div className="text-xs text-gray-500 mt-1">{formatTimestamp(activity.timestamp)}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* System Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-blue-800 mb-3 flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Account Information
          </h3>
          <div className="space-y-2 text-sm text-blue-700">
            <p>
              <strong>Account Type:</strong> Site Engineer - Read Only Access
            </p>
            <p>
              <strong>Data Access:</strong> This page displays your current account information. 
              Contact your supervisor or HR department to update personal details.
            </p>
            <p>
              <strong>Emergency Contact:</strong> {SITE_ASSIGNMENT.emergencyContact}
            </p>
          </div>
        </div>
      </div>
    </SiteEngineerLayout>
  );
};

export default AccountPage;
