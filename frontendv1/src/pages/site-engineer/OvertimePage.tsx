import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  Clock,
  MapPin,
  User,
  AlertTriangle,
  Check
} from 'lucide-react';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';
import SiteEngineerLayout from '../../components/site-engineer/SiteEngineerLayout';

// Approved task interface
interface ApprovedTask {
  id: string;
  name: string;
  description: string;
  location: string;
  assignedTo: string;
  estimatedHours: number;
  startTime: string;
  endTime: string;
  category: string;
  priority: 'normal' | 'high' | 'urgent';
}

// Overtime request interface
interface OvertimeRequest {
  id: string;
  selectedTasks: string[];
  reason: string;
  estimatedOvertimeHours: number;
  urgencyLevel: 'normal' | 'urgent';
  additionalNotes?: string;
}

// Mock approved tasks for today
const APPROVED_TASKS_TODAY: ApprovedTask[] = [
  {
    id: 'task-001',
    name: 'Electrical System Inspection',
    description: 'Inspect electrical installations and connections in Building A',
    location: 'Building A - Floor 2',
    assignedTo: '<PERSON>',
    estimatedHours: 4,
    startTime: '08:00',
    endTime: '12:00',
    category: 'Electrical',
    priority: 'high'
  },
  {
    id: 'task-002',
    name: 'Concrete Pour Inspection',
    description: 'Monitor concrete pouring quality and compliance',
    location: 'Foundation Area',
    assignedTo: 'John Smith',
    estimatedHours: 6,
    startTime: '13:00',
    endTime: '19:00',
    category: 'Structural',
    priority: 'normal'
  },
  {
    id: 'task-003',
    name: 'Safety Equipment Check',
    description: 'Verify safety equipment and protocols compliance',
    location: 'Construction Zone',
    assignedTo: 'John Smith',
    estimatedHours: 2,
    startTime: '09:00',
    endTime: '11:00',
    category: 'Safety',
    priority: 'normal'
  },
  {
    id: 'task-004',
    name: 'HVAC Installation Review',
    description: 'Review HVAC installation progress and quality',
    location: 'Building B - Floor 1',
    assignedTo: 'John Smith',
    estimatedHours: 3,
    startTime: '14:00',
    endTime: '17:00',
    category: 'HVAC',
    priority: 'high'
  }
];

const OvertimePage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [site] = useState<SiteInfo>(mockSite);

  // Form state
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [reason, setReason] = useState('');
  const [estimatedOvertimeHours, setEstimatedOvertimeHours] = useState(2);
  const [urgencyLevel, setUrgencyLevel] = useState<'normal' | 'urgent'>('normal');
  const [additionalNotes, setAdditionalNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Toggle task selection
  const toggleTaskSelection = (taskId: string) => {
    setSelectedTasks(prev => 
      prev.includes(taskId) 
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    );
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  // Submit overtime request
  const handleSubmitRequest = async () => {
    if (selectedTasks.length === 0) {
      toast.error('Please select at least one task for overtime.');
      return;
    }

    if (!reason.trim()) {
      toast.error('Please provide a reason for overtime request.');
      return;
    }

    setIsSubmitting(true);

    try {
      const overtimeRequest: OvertimeRequest = {
        id: `overtime-${Date.now()}`,
        selectedTasks,
        reason: reason.trim(),
        estimatedOvertimeHours,
        urgencyLevel,
        additionalNotes: additionalNotes.trim()
      };

      // Simulate API call
      console.log('Submitting overtime request:', overtimeRequest);
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast.success('Overtime request submitted successfully! HSE team will review and respond.', {
        position: 'top-right',
        autoClose: 6000,
      });

      // Navigate back to dashboard
      navigate(`/sites/${siteId}/engineer`);

    } catch (error) {
      console.error('Error submitting overtime request:', error);
      toast.error('Failed to submit overtime request. Please try again.', {
        position: 'top-right',
        autoClose: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SiteEngineerLayout site={site} title="Overtime Request" showBackButton={true}>
      <div className="px-4 sm:px-6 py-4 sm:py-6 space-y-6">

        {/* Header Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <Clock className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="text-sm font-semibold text-blue-800">Overtime Request</h3>
              <p className="text-xs text-blue-700 mt-1">
                Select the approved tasks you need overtime for and provide justification. 
                Your request will be sent to the HSE team for review and approval.
              </p>
            </div>
          </div>
        </div>

        {/* Today's Approved Tasks */}
        <div className="bg-white rounded-xl border border-gray-200 p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Today's Approved Tasks</h2>
          <p className="text-sm text-gray-600 mb-4">Select the tasks that require overtime work:</p>
          
          <div className="space-y-3">
            {APPROVED_TASKS_TODAY.map((task) => (
              <div
                key={task.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedTasks.includes(task.id)
                    ? 'border-blue-300 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => toggleTaskSelection(task.id)}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                      selectedTasks.includes(task.id)
                        ? 'bg-blue-600 border-blue-600'
                        : 'border-gray-300'
                    }`}>
                      {selectedTasks.includes(task.id) && (
                        <Check className="h-3 w-3 text-white" />
                      )}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 text-sm">{task.name}</h3>
                        <p className="text-xs text-gray-600 mt-1 line-clamp-2">{task.description}</p>
                        
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span>{task.location}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{task.startTime} - {task.endTime}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{task.estimatedHours}h</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <span className="text-xs text-gray-500">{task.category}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {selectedTasks.length > 0 && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-700">
                <strong>{selectedTasks.length} task{selectedTasks.length > 1 ? 's' : ''} selected</strong> for overtime request
              </p>
            </div>
          )}
        </div>

        {/* Overtime Request Form */}
        <div className="bg-white rounded-xl border border-gray-200 p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Overtime Details</h2>
          
          <div className="space-y-4">
            {/* Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason for Overtime *
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
                placeholder="Explain why overtime is needed for the selected tasks..."
              />
            </div>

            {/* Estimated Hours and Urgency */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Estimated Overtime Hours
                </label>
                <input
                  type="number"
                  value={estimatedOvertimeHours}
                  onChange={(e) => setEstimatedOvertimeHours(parseInt(e.target.value) || 2)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  min="1"
                  max="12"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Urgency Level
                </label>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setUrgencyLevel('normal')}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      urgencyLevel === 'normal'
                        ? 'bg-blue-100 text-blue-700 border border-blue-300'
                        : 'bg-gray-100 text-gray-700 border border-gray-300'
                    }`}
                  >
                    Normal
                  </button>
                  <button
                    onClick={() => setUrgencyLevel('urgent')}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      urgencyLevel === 'urgent'
                        ? 'bg-red-100 text-red-700 border border-red-300'
                        : 'bg-gray-100 text-gray-700 border border-gray-300'
                    }`}
                  >
                    <AlertTriangle className="h-3 w-3 inline mr-1" />
                    Urgent
                  </button>
                </div>
              </div>
            </div>

            {/* Additional Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Additional Notes (Optional)
              </label>
              <textarea
                value={additionalNotes}
                onChange={(e) => setAdditionalNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={2}
                placeholder="Any additional information or special requirements..."
              />
            </div>
          </div>
        </div>

        {/* Submit Section */}
        <div className="bg-white rounded-xl border border-gray-200 p-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="font-semibold text-gray-900">Submit Request</h3>
              <p className="text-sm text-gray-600">Review your overtime request before submitting</p>
            </div>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={handleSubmitRequest}
              disabled={isSubmitting || selectedTasks.length === 0 || !reason.trim()}
              className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2 font-medium"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Submitting...</span>
                </>
              ) : (
                <>
                  <Clock className="h-4 w-4" />
                  <span>Submit Overtime Request</span>
                </>
              )}
            </button>
            
            <button
              onClick={() => navigate(`/sites/${siteId}/engineer`)}
              className="px-4 py-3 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
          </div>
          
          <p className="text-xs text-gray-500 text-center mt-3">
            Request will be sent to HSE team for review and approval
          </p>
        </div>
      </div>
    </SiteEngineerLayout>
  );
};

export default OvertimePage;
