import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  CheckSquare,
  Square,
  Calendar,
  User,
  Users,
  Clock,
  AlertTriangle,
  Shield,
  FileText,
  ClipboardCheck,
  X,
  Eye,
  CheckCircle
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { FINISH_JOBS } from '../graphql/mutations';
import { GET_APPROVED_JOBS } from '../graphql/queries';

interface Job {
  id: number;
  title: string;
  description: string;
  status: string;
  requiredPermits: string[];
  timeForCompletion: string;
  startDate: string;
  requestedBy: {
    id: number;
    name: string;
  };
  reviewedBy: {
    id: number;
    name: string;
  };
  approvedBy: {
    id: number;
    name: string;
  };
  chiefEngineer: {
    id: number;
    name: string;
  };
  workers: Array<{
    id: number;
    name: string;
    company: string;
  }>;
  hazards: Array<{
    id: number;
    description: string;
    controlMeasures: Array<{
      id: number;
      description: string;
      closed: boolean;
    }>;
  }>;
  documents: Array<{
    id: number;
    name: string;
    url: string;
  }>;
  requestedDate: string;
  reviewedDate: string;
  approvedDate: string;
  createdAt: string;
}

const PERMIT_LABELS = {
  GENERAL_WORK: 'General Work Permit',
  HOT_WORK: 'Hot Work Permit',
  CONFINED_SPACE: 'Confined Space Permit',
  WORK_AT_HEIGHT: 'Work at Height Permit',
  EXCAVATION: 'Excavation Permit'
};

const CloseTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  
  const [selectedTasks, setSelectedTasks] = useState<Set<number>>(new Set());
  const [expandedTasks, setExpandedTasks] = useState<Set<number>>(new Set());
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: jobsData, loading: jobsLoading, refetch } = useQuery(GET_APPROVED_JOBS);
  
  const [finishJobs] = useMutation(FINISH_JOBS, {
    onCompleted: () => {
      toast.success('Tasks closed successfully!');
      refetch();
      setSelectedTasks(new Set());
    },
    onError: (error) => {
      toast.error(`Failed to close tasks: ${error.message}`);
    }
  });

  const jobs: Job[] = jobsData?.approvedJobs || [];

  const toggleTaskSelection = (jobId: number) => {
    setSelectedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(jobId)) {
        newSet.delete(jobId);
      } else {
        newSet.add(jobId);
      }
      return newSet;
    });
  };

  const toggleTaskExpansion = (jobId: number) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(jobId)) {
        newSet.delete(jobId);
      } else {
        newSet.add(jobId);
      }
      return newSet;
    });
  };

  const selectAllTasks = () => {
    if (selectedTasks.size === jobs.length) {
      setSelectedTasks(new Set());
    } else {
      setSelectedTasks(new Set(jobs.map(job => job.id)));
    }
  };

  const handleCloseTasks = async () => {
    if (selectedTasks.size === 0) {
      toast.warning('Please select at least one task to close.');
      return;
    }

    const finishInputs = Array.from(selectedTasks).map(jobId => ({
      jobId,
      finishedById: 1 // Using 1 as per requirement
    }));

    setIsSubmitting(true);
    try {
      await finishJobs({ variables: { inputs: finishInputs } });
    } catch (error) {
      console.error('Error closing tasks:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'Close Tasks', path: `/sites/${siteId}/tasks/close` }
  ];

  if (jobsLoading) {
    return (
      <FloatingCard title="Close Tasks" breadcrumbs={breadcrumbs}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Close Tasks" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {jobs.length === 0 ? (
          <div className="text-center py-12">
            <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Approved Tasks</h3>
            <p className="text-gray-500">There are currently no approved tasks available to close.</p>
          </div>
        ) : (
          <>
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Close Tasks Instructions
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      Select the approved tasks that have been completed and are ready to be closed. 
                      You can expand tasks to view detailed information. Click "Close Selected Tasks" 
                      to finish the selected tasks.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Selection Controls */}
            <div className="flex items-center justify-between p-4 bg-gray-50 border border-gray-200 rounded-md">
              <div className="flex items-center space-x-4">
                <button
                  onClick={selectAllTasks}
                  className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900"
                >
                  {selectedTasks.size === jobs.length ? (
                    <CheckSquare className="h-4 w-4 text-blue-600" />
                  ) : (
                    <Square className="h-4 w-4" />
                  )}
                  <span>
                    {selectedTasks.size === jobs.length ? 'Deselect All' : 'Select All'}
                  </span>
                </button>
                <span className="text-sm text-gray-500">
                  {selectedTasks.size} of {jobs.length} tasks selected
                </span>
              </div>
              <button
                onClick={handleCloseTasks}
                disabled={isSubmitting || selectedTasks.size === 0}
                className="px-4 py-2 bg-green-600 text-white hover:bg-green-700 disabled:bg-green-400 rounded-md transition-colors"
              >
                <CheckCircle className="h-4 w-4 inline mr-2" />
                {isSubmitting ? 'Closing...' : `Close Selected Tasks (${selectedTasks.size})`}
              </button>
            </div>

            {jobs.map((job) => {
              const isSelected = selectedTasks.has(job.id);
              const isExpanded = expandedTasks.has(job.id);
              
              return (
                <div key={job.id} className={`border rounded-lg overflow-hidden ${
                  isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}>
                  <div className="px-6 py-4 bg-white">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 flex-1">
                        <button
                          onClick={() => toggleTaskSelection(job.id)}
                          className="flex-shrink-0"
                        >
                          {isSelected ? (
                            <CheckSquare className="h-5 w-5 text-blue-600" />
                          ) : (
                            <Square className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                          )}
                        </button>
                        <div className="flex-1">
                          <div className="flex items-center space-x-4">
                            <h3 className="text-lg font-medium text-gray-900">{job.title}</h3>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Approved
                            </span>
                          </div>
                          <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                            {/* <div className="flex items-center">
                              <User className="h-4 w-4 mr-1" />
                              Requested by: {job.requestedBy.name}
                            </div> */}
                            {/* <div className="flex items-center">
                              <User className="h-4 w-4 mr-1" />
                              Approved by: {job.approvedBy.name}
                            </div> */}
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-1" />
                              Chief Engineer: {job.chiefEngineer.name}
                            </div>
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              Start: {new Date(job.startDate).toLocaleDateString()}
                            </div>
                            <div className="flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              Duration: {job.timeForCompletion} days
                            </div>
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => toggleTaskExpansion(job.id)}
                        className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        {isExpanded ? 'Collapse' : 'View Details'}
                      </button>
                    </div>
                  </div>

                  {isExpanded && (
                    <div className="px-6 py-4 space-y-6 bg-gray-50 border-t border-gray-200">
                      {/* Task Description */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                        <p className="text-sm text-gray-700 bg-white p-3 rounded-md">{job.description}</p>
                      </div>

                      {/* Workers */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">
                          <Users className="h-4 w-4 inline mr-1" />
                          Assigned Workers ({job.workers.length})
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {job.workers.map((worker) => (
                            <div key={worker.id} className="bg-white p-2 rounded text-sm border border-gray-200">
                              <p className="font-medium">{worker.name}</p>
                              <p className="text-gray-600">{worker.company}</p>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Required Permits */}
                      {job.requiredPermits && job.requiredPermits.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <ClipboardCheck className="h-4 w-4 inline mr-1" />
                            Required Permits
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {job.requiredPermits.map((permit) => (
                              <span
                                key={permit}
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {PERMIT_LABELS[permit as keyof typeof PERMIT_LABELS] || permit}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Hazards and Control Measures */}
                      {job.hazards && job.hazards.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <AlertTriangle className="h-4 w-4 inline mr-1" />
                            Hazards & Control Measures
                          </h4>
                          <div className="space-y-4">
                            {job.hazards.map((hazard) => (
                              <div key={hazard.id} className="border border-gray-200 rounded-md p-4 bg-white">
                                <div className="mb-3">
                                  <h5 className="text-sm font-medium text-gray-900 mb-1">Hazard</h5>
                                  <p className="text-sm text-gray-700 bg-orange-50 p-2 rounded">{hazard.description}</p>
                                </div>
                                {hazard.controlMeasures && hazard.controlMeasures.length > 0 && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-900 mb-2">
                                      <Shield className="h-4 w-4 inline mr-1" />
                                      Control Measures
                                    </h5>
                                    <ul className="space-y-1">
                                      {hazard.controlMeasures.map((measure) => (
                                        <li key={measure.id} className="flex items-start space-x-2">
                                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                                            measure.closed ? 'bg-green-500' : 'bg-yellow-500'
                                          }`}></div>
                                          <span className="text-sm text-gray-700">{measure.description}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Documents */}
                      {job.documents && job.documents.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <FileText className="h-4 w-4 inline mr-1" />
                            Documents
                          </h4>
                          <div className="space-y-2">
                            {job.documents.map((doc) => (
                              <div key={doc.id} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md">
                                <div className="flex items-center space-x-3">
                                  <FileText className="h-4 w-4 text-gray-500" />
                                  <span className="text-sm font-medium">{doc.name}</span>
                                </div>
                                <a
                                  href={doc.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 text-sm"
                                >
                                  View
                                </a>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Timeline */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Timeline</h4>
                        <div className="bg-white p-3 rounded-md text-sm border border-gray-200">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-600">Requested:</span>
                            <span className="font-medium">{new Date(job.requestedDate).toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-600">Reviewed:</span>
                            <span className="font-medium">{new Date(job.reviewedDate).toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Approved:</span>
                            <span className="font-medium">{new Date(job.approvedDate).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Bottom Action Bar */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200">
              <div className="text-sm text-gray-500">
                {selectedTasks.size > 0 && (
                  <span>{selectedTasks.size} task{selectedTasks.size !== 1 ? 's' : ''} selected for closing</span>
                )}
              </div>
              <div className="flex space-x-4">
                <button
                  onClick={() => navigate(`/sites/${siteId}/tasks`)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  <X className="h-4 w-4 inline mr-2" />
                  Cancel
                </button>
                <button
                  onClick={handleCloseTasks}
                  disabled={isSubmitting || selectedTasks.size === 0}
                  className="px-4 py-2 bg-green-600 text-white hover:bg-green-700 disabled:bg-green-400 rounded-md transition-colors"
                >
                  <CheckCircle className="h-4 w-4 inline mr-2" />
                  {isSubmitting ? 'Closing...' : `Close Selected Tasks (${selectedTasks.size})`}
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </FloatingCard>
  );
};

export default CloseTaskPage;
