import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, FileText, Shield, AlertTriangle, CheckCircle, Users, Flame } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';

const HotWorkFormDemoPage: React.FC = () => {
  const navigate = useNavigate();

  const handleStartForm = () => {
    navigate('/hot-work/form/new');
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <FloatingCard title="Hot Work Permit Form">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Hot Work Permit System</h1>
                <p className="text-gray-600">Comprehensive safety permit for hot work activities</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Flame className="h-8 w-8 text-red-600" />
              <Shield className="h-8 w-8 text-red-600" />
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Overview */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-red-900 mb-2">
                    Hot Work Safety Permit
                  </h2>
                  <p className="text-red-800 mb-4">
                    This permit system ensures comprehensive safety planning and fire prevention for all hot work activities 
                    including welding, cutting, grinding, and brazing. Valid for ONE day only and requires proper fire safety measures.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-red-600" />
                      <span>Fire hazard assessment</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-red-600" />
                      <span>Hot work avoidance evaluation</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-red-600" />
                      <span>Fire extinguisher requirements</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-red-600" />
                      <span>Fire watch procedures</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Key Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <FileText className="h-6 w-6 text-red-600" />
                  <h3 className="font-semibold text-gray-900">Comprehensive Assessment</h3>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Hot work avoidance evaluation</li>
                  <li>• Nature of work identification</li>
                  <li>• Fire and safety hazard assessment</li>
                  <li>• Required documentation checklist</li>
                </ul>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <Shield className="h-6 w-6 text-red-600" />
                  <h3 className="font-semibold text-gray-900">Fire Safety Measures</h3>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Fire extinguisher selection</li>
                  <li>• Work area exclusion barriers</li>
                  <li>• Safety signage requirements</li>
                  <li>• Competent welder verification</li>
                </ul>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <Users className="h-6 w-6 text-red-600" />
                  <h3 className="font-semibold text-gray-900">Authorization Process</h3>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Fire watch before work</li>
                  <li>• Permit issue authorization</li>
                  <li>• Fire marshal after work</li>
                  <li>• Permit return process</li>
                </ul>
              </div>
            </div>

            {/* Form Sections Preview */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Form Sections</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Hot Work Avoidance</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Work Details & Timing</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Other Permits in Use</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Nature of Work</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Fire & Safety Hazards</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Precautions Required</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Required PPE</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Fire Extinguisher Type</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Fire Watch & Authorization</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Sign-off Section</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Safety Notice */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-900 mb-1">Critical Fire Safety Notice</h4>
                  <p className="text-sm text-red-800">
                    This permit is valid for ONE day only. All hot work must be properly planned with fire prevention 
                    measures in place. Fire watch must be maintained before, during, and after work completion. 
                    Emergency fire suppression equipment must be readily available.
                  </p>
                </div>
              </div>
            </div>

            {/* Technical Details */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Technical Implementation
              </h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Built with React and TypeScript for type safety and reliability</p>
                <p>• Single-sheet form workflow optimized for hot work permits</p>
                <p>• Based on hotWorksPTW.ts structure from utils folder</p>
                <p>• Comprehensive sections: work avoidance, nature of work, fire hazards, precautions</p>
                <p>• Automated date/time stamping for audit trail compliance</p>
                <p>• Fire safety specific PPE and extinguisher selection</p>
                <p>• Multi-stage authorization: fire watch, permit issue, fire marshal, permit return</p>
                <p>• Sign-off table with fire safety supervisor tracking</p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <button
                onClick={handleStartForm}
                className="flex-1 bg-red-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
              >
                Start New Hot Work Permit
              </button>
              <button
                onClick={handleBack}
                className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
              >
                Back to Dashboard
              </button>
            </div>

            {/* Quick Access Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Quick Access URLs</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <p>• Direct form access: <code className="bg-blue-100 px-1 rounded">/hot-work/form</code></p>
                <p>• Site-specific form: <code className="bg-blue-100 px-1 rounded">/sites/[siteId]/hot-work/form</code></p>
                <p>• Demo page: <code className="bg-blue-100 px-1 rounded">/demo/hot-work-form</code></p>
              </div>
            </div>
          </div>
        </FloatingCard>
      </div>
    </div>
  );
};

export default HotWorkFormDemoPage;
