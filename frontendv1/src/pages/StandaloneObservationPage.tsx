import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { CheckCircle, AlertTriangle, ArrowLeft } from 'lucide-react';
import StandaloneObservationForm from '../components/safety/StandaloneObservationForm';
import { SafetyObservation } from '../components/safety/types/safety';

const StandaloneObservationPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleObservationSubmit = async (observationData: Partial<SafetyObservation>) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      // In a real implementation, this would call an API to save the observation
      console.log('Submitting standalone observation:', observationData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setIsSubmitted(true);
    } catch (err) {
      setError('Failed to submit observation. Please try again.');
      console.error('Error submitting observation:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    // For standalone page, we might want to redirect to a thank you page or close the window
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      window.close();
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="mb-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Observation Submitted
            </h1>
            <p className="text-gray-600">
              Thank you for your safety observation. Your submission has been recorded and will be reviewed by the safety team.
            </p>
          </div>
          
          <div className="space-y-3">
            <button
              onClick={() => {
                setIsSubmitted(false);
                setError(null);
              }}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Submit Another Observation
            </button>
            <button
              onClick={() => window.close()}
              className="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="mb-6">
            <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Submission Error
            </h1>
            <p className="text-gray-600 mb-4">
              {error}
            </p>
          </div>
          
          <div className="space-y-3">
            <button
              onClick={() => setError(null)}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={handleCancel}
              className="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Safety Observation Form
              </h1>
              <p className="text-gray-600 mt-1">
                Report safety observations to help improve workplace safety
              </p>
            </div>
            <button
              onClick={handleCancel}
              className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-4">
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-2">
                Submit Your Observation
              </h2>
              <p className="text-gray-600">
                Your observations help us maintain a safe work environment. All submissions are reviewed by our safety team.
                You can submit anonymously if you prefer.
              </p>
            </div>

            {/* Safety Guidelines */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-blue-900 mb-2">What to Report:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Unsafe conditions or behaviors</li>
                <li>• Near misses or potential hazards</li>
                <li>• Positive safety behaviors worth recognizing</li>
                <li>• Equipment or environmental concerns</li>
                <li>• Suggestions for safety improvements</li>
              </ul>
            </div>

            {/* Emergency Notice */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-red-900 mb-2">⚠️ For Immediate Safety Concerns:</h3>
              <p className="text-sm text-red-800">
                If you observe an immediate safety hazard or emergency situation, 
                <strong> do not use this form</strong>. Contact your supervisor or emergency services immediately.
              </p>
            </div>
          </div>
        </div>

        {/* Observation Form */}
        <div className="mt-6">
          <div className="bg-white rounded-lg shadow-sm border">
            <StandaloneObservationForm
              siteId={siteId || 'unknown'}
              onSubmit={handleObservationSubmit}
              onCancel={handleCancel}
              isSubmitting={isSubmitting}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white border-t mt-8">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center text-sm text-gray-500">
            <p>
              This form is for safety observations only. For other concerns, please contact your supervisor.
            </p>
            <p className="mt-1">
              All submissions are confidential and will be handled according to company safety policies.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StandaloneObservationPage;
