import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  User,
  Users,
  Clock,
  AlertTriangle,
  Shield,
  FileText,
  ClipboardCheck,
  Save,
  X
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { APPROVE_JOBS, DISAPPROVE_JOBS } from '../graphql/mutations';
import { GET_PENDING_APPROVAL_JOBS } from '../graphql/queries';

interface Job {
  id: number;
  title: string;
  description: string;
  status: string;
  requiredPermits: string[];
  timeForCompletion: string;
  startDate: string;
  requestedBy: {
    id: number;
    name: string;
  };
  reviewedBy: {
    id: number;
    name: string;
  };
  chiefEngineer: {
    id: number;
    name: string;
  };
  workers: Array<{
    id: number;
    name: string;
    company: string;
  }>;
  hazards: Array<{
    id: number;
    description: string;
    controlMeasures: Array<{
      id: number;
      description: string;
      closed: boolean;
    }>;
  }>;
  documents: Array<{
    id: number;
    name: string;
    url: string;
  }>;
  requestedDate: string;
  reviewedDate: string;
  createdAt: string;
}

interface TaskApproval {
  jobId: number;
  action: 'approve' | 'disapprove' | null;
}

const PERMIT_LABELS = {
  GENERAL_WORK: 'General Work Permit',
  HOT_WORK: 'Hot Work Permit',
  CONFINED_SPACE: 'Confined Space Permit',
  WORK_AT_HEIGHT: 'Work at Height Permit',
  EXCAVATION: 'Excavation Permit'
};

const ApproveTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  
  const [taskApprovals, setTaskApprovals] = useState<Record<number, TaskApproval>>({});
  const [expandedTasks, setExpandedTasks] = useState<Set<number>>(new Set());
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: jobsData, loading: jobsLoading, refetch } = useQuery(GET_PENDING_APPROVAL_JOBS);
  
  const [approveJobs] = useMutation(APPROVE_JOBS, {
    onCompleted: () => {
      toast.success('Tasks approved successfully!');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to approve tasks: ${error.message}`);
    }
  });

  const [disapproveJobs] = useMutation(DISAPPROVE_JOBS, {
    onCompleted: () => {
      toast.success('Tasks disapproved successfully!');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to disapprove tasks: ${error.message}`);
    }
  });

  const jobs: Job[] = jobsData?.pendingApprovalJobs || [];

  const toggleTaskExpansion = (jobId: number) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(jobId)) {
        newSet.delete(jobId);
      } else {
        newSet.add(jobId);
      }
      return newSet;
    });
  };

  const setTaskAction = (jobId: number, action: 'approve' | 'disapprove' | null) => {
    setTaskApprovals(prev => ({
      ...prev,
      [jobId]: { jobId, action }
    }));
  };

  const handleSaveChanges = async () => {
    const approveInputs = [];
    const disapproveInputs = [];

    for (const approval of Object.values(taskApprovals)) {
      if (approval.action === 'approve') {
        approveInputs.push({
          jobId: approval.jobId,
          approvedById: 1 // Using 1 as per requirement
        });
      } else if (approval.action === 'disapprove') {
        disapproveInputs.push({
          jobId: approval.jobId,
          disapprovedById: 1 // Using 1 as per requirement
        });
      }
    }

    if (approveInputs.length === 0 && disapproveInputs.length === 0) {
      toast.warning('Please select at least one task to approve or disapprove.');
      return;
    }

    setIsSubmitting(true);
    try {
      if (approveInputs.length > 0) {
        await approveJobs({ variables: { inputs: approveInputs } });
      }
      if (disapproveInputs.length > 0) {
        await disapproveJobs({ variables: { inputs: disapproveInputs } });
      }
      
      // Clear the approvals after successful submission
      setTaskApprovals({});
      setExpandedTasks(new Set());
    } catch (error) {
      console.error('Error saving changes:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'Approve Tasks', path: `/sites/${siteId}/tasks/approve` }
  ];

  if (jobsLoading) {
    return (
      <FloatingCard title="Approve Tasks" breadcrumbs={breadcrumbs}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Approve Tasks" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {jobs.length === 0 ? (
          <div className="text-center py-12">
            <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Tasks Pending Approval</h3>
            <p className="text-gray-500">There are currently no tasks waiting for approval.</p>
          </div>
        ) : (
          <>
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800">
                    Approval Instructions
                  </h3>
                  <div className="mt-2 text-sm text-green-700">
                    <p>
                      Review each task below and decide whether to approve or disapprove it. 
                      You can expand tasks to see detailed information including hazards, control measures, 
                      documents, and required permits. Click "Save Changes" to apply your decisions.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {jobs.map((job) => {
              const isExpanded = expandedTasks.has(job.id);
              const approval = taskApprovals[job.id];
              
              return (
                <div key={job.id} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className={`px-6 py-4 ${
                    approval?.action === 'approve' ? 'bg-green-50' : 
                    approval?.action === 'disapprove' ? 'bg-red-50' : 'bg-gray-50'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4">
                          <h3 className="text-lg font-medium text-gray-900">{job.title}</h3>
                          {approval?.action && (
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              approval.action === 'approve' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {approval.action === 'approve' ? (
                                <><CheckCircle className="h-3 w-3 mr-1" />To Approve</>
                              ) : (
                                <><XCircle className="h-3 w-3 mr-1" />To Disapprove</>
                              )}
                            </span>
                          )}
                        </div>
                        <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                          {/* <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            Requested by: {job.requestedBy.name}
                          </div> */}
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            Reviewed by: {job.reviewedBy.name}
                          </div>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            Chief Engineer: {job.chiefEngineer.name}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Start: {new Date(job.startDate).toLocaleDateString()}
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            Duration: {job.timeForCompletion} days
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => toggleTaskExpansion(job.id)}
                          className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          {isExpanded ? 'Collapse' : 'View Details'}
                        </button>
                        <button
                          onClick={() => setTaskAction(job.id, approval?.action === 'approve' ? null : 'approve')}
                          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                            approval?.action === 'approve'
                              ? 'bg-green-600 text-white hover:bg-green-700'
                              : 'bg-green-100 text-green-700 hover:bg-green-200'
                          }`}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {approval?.action === 'approve' ? 'Approved' : 'Approve'}
                        </button>
                        <button
                          onClick={() => setTaskAction(job.id, approval?.action === 'disapprove' ? null : 'disapprove')}
                          className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                            approval?.action === 'disapprove'
                              ? 'bg-red-600 text-white hover:bg-red-700'
                              : 'bg-red-100 text-red-700 hover:bg-red-200'
                          }`}
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          {approval?.action === 'disapprove' ? 'Disapproved' : 'Disapprove'}
                        </button>
                      </div>
                    </div>
                  </div>

                  {isExpanded && (
                    <div className="px-6 py-4 space-y-6 bg-white">
                      {/* Task Description */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                        <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">{job.description}</p>
                      </div>

                      {/* Workers */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">
                          <Users className="h-4 w-4 inline mr-1" />
                          Assigned Workers ({job.workers.length})
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {job.workers.map((worker) => (
                            <div key={worker.id} className="bg-gray-50 p-2 rounded text-sm">
                              <p className="font-medium">{worker.name}</p>
                              <p className="text-gray-600">{worker.company}</p>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Required Permits */}
                      {job.requiredPermits && job.requiredPermits.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <ClipboardCheck className="h-4 w-4 inline mr-1" />
                            Required Permits
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {job.requiredPermits.map((permit) => (
                              <span
                                key={permit}
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {PERMIT_LABELS[permit as keyof typeof PERMIT_LABELS] || permit}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Hazards and Control Measures */}
                      {job.hazards && job.hazards.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <AlertTriangle className="h-4 w-4 inline mr-1" />
                            Hazards & Control Measures
                          </h4>
                          <div className="space-y-4">
                            {job.hazards.map((hazard) => (
                              <div key={hazard.id} className="border border-gray-200 rounded-md p-4">
                                <div className="mb-3">
                                  <h5 className="text-sm font-medium text-gray-900 mb-1">Hazard</h5>
                                  <p className="text-sm text-gray-700 bg-orange-50 p-2 rounded">{hazard.description}</p>
                                </div>
                                {hazard.controlMeasures && hazard.controlMeasures.length > 0 && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-900 mb-2">
                                      <Shield className="h-4 w-4 inline mr-1" />
                                      Control Measures
                                    </h5>
                                    <ul className="space-y-1">
                                      {hazard.controlMeasures.map((measure) => (
                                        <li key={measure.id} className="flex items-start space-x-2">
                                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                                            measure.closed ? 'bg-green-500' : 'bg-yellow-500'
                                          }`}></div>
                                          <span className="text-sm text-gray-700">{measure.description}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Documents */}
                      {job.documents && job.documents.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <FileText className="h-4 w-4 inline mr-1" />
                            Documents
                          </h4>
                          <div className="space-y-2">
                            {job.documents.map((doc) => (
                              <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md">
                                <div className="flex items-center space-x-3">
                                  <FileText className="h-4 w-4 text-gray-500" />
                                  <span className="text-sm font-medium">{doc.name}</span>
                                </div>
                                <a
                                  href={doc.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 text-sm"
                                >
                                  View
                                </a>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Timeline */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Timeline</h4>
                        <div className="bg-gray-50 p-3 rounded-md text-sm">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-600">Requested:</span>
                            <span className="font-medium">{new Date(job.requestedDate).toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Reviewed:</span>
                            <span className="font-medium">{new Date(job.reviewedDate).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Save Changes Button */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                onClick={() => navigate(`/sites/${siteId}/tasks`)}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                <X className="h-4 w-4 inline mr-2" />
                Cancel
              </button>
              <button
                onClick={handleSaveChanges}
                disabled={isSubmitting || Object.keys(taskApprovals).length === 0}
                className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400 rounded-md transition-colors"
              >
                <Save className="h-4 w-4 inline mr-2" />
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </>
        )}
      </div>
    </FloatingCard>
  );
};

export default ApproveTaskPage;
