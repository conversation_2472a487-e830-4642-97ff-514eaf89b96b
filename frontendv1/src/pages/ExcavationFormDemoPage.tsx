import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Shovel, CheckCircle, AlertTriangle, Users, FileText, Clock, MapPin } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';

const ExcavationFormDemoPage: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  const handleTryForm = () => {
    navigate('/excavation/form');
  };

  return (
    <FloatingCard title="Excavation Permit to Work Demo">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <Shovel className="h-6 w-6 text-orange-600" />
                <h1 className="text-2xl font-bold text-gray-900">Excavation Permit to Work System</h1>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-6xl mx-auto p-6">
          {/* Hero Section */}
          <div className="bg-gradient-to-r from-orange-600 to-orange-800 rounded-lg p-8 text-white mb-8">
            <div className="max-w-3xl">
              <h2 className="text-3xl font-bold mb-4">Excavation Permit to Work System</h2>
              <p className="text-xl mb-6 text-orange-100">
                Comprehensive digital solution for managing excavation permits with safety protocols, 
                equipment tracking, and inspection procedures for safe excavation operations.
              </p>
              <button
                onClick={handleTryForm}
                className="bg-white text-orange-600 px-6 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-colors"
              >
                Try the Form
              </button>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
                <h3 className="text-lg font-semibold">Safety Compliance</h3>
              </div>
              <p className="text-gray-600">
                Ensures all excavation safety protocols are followed with comprehensive precaution checklists and equipment verification.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <AlertTriangle className="h-8 w-8 text-yellow-600 mr-3" />
                <h3 className="text-lg font-semibold">Hazard Assessment</h3>
              </div>
              <p className="text-gray-600">
                Comprehensive hazard identification including underground utilities, soil conditions, and environmental factors.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <Users className="h-8 w-8 text-blue-600 mr-3" />
                <h3 className="text-lg font-semibold">Team Management</h3>
              </div>
              <p className="text-gray-600">
                Tracks supervisors, operators, and safety personnel with digital signatures and authorization workflows.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <FileText className="h-8 w-8 text-purple-600 mr-3" />
                <h3 className="text-lg font-semibold">Equipment Tracking</h3>
              </div>
              <p className="text-gray-600">
                Detailed equipment lists, inspection requirements, and maintenance verification for all excavation machinery.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <Clock className="h-8 w-8 text-orange-600 mr-3" />
                <h3 className="text-lg font-semibold">Time Management</h3>
              </div>
              <p className="text-gray-600">
                One-day validity permits with clear start/end times and automatic expiration tracking for enhanced safety.
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <MapPin className="h-8 w-8 text-red-600 mr-3" />
                <h3 className="text-lg font-semibold">Location Specific</h3>
              </div>
              <p className="text-gray-600">
                Site-specific configurations and location-based safety requirements for different excavation zones.
              </p>
            </div>
          </div>

          {/* Form Preview */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Form Structure</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Key Sections:</h4>
                <ul className="space-y-2 text-gray-600">
                  <li>• Project details and excavation specifications</li>
                  <li>• Comprehensive hazard identification checklist</li>
                  <li>• Required precautions and safety measures</li>
                  <li>• Equipment list and inspection requirements</li>
                  <li>• Permit issue and return authorization</li>
                  <li>• Multi-level approval workflow</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Safety Features:</h4>
                <ul className="space-y-2 text-gray-600">
                  <li>• Underground utility location verification</li>
                  <li>• Soil stability and shoring requirements</li>
                  <li>• Emergency preparedness protocols</li>
                  <li>• PPE requirements and verification</li>
                  <li>• Atmospheric testing for confined spaces</li>
                  <li>• Access and egress safety measures</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Precautions Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Standard Precautions Checklist</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Structural Safety:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Shoring systems</li>
                  <li>• Benching techniques</li>
                  <li>• Edge protection</li>
                  <li>• Access ladders</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Environmental:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Atmosphere testing</li>
                  <li>• Emergency preparedness</li>
                  <li>• Work area barriers</li>
                  <li>• Signage requirements</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Personnel:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• PPE requirements</li>
                  <li>• Buddy system</li>
                  <li>• Job rotation</li>
                  <li>• Supervision protocols</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Technical Details */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-xl font-semibold mb-4">Technical Implementation</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Form Features:</h4>
                <div className="text-gray-600 space-y-1">
                  <p>• One-day validity permit system</p>
                  <p>• Auto-populated date and time fields</p>
                  <p>• Dynamic precautions checklist</p>
                  <p>• Equipment tracking and verification</p>
                  <p>• Digital signature integration</p>
                  <p>• Real-time form validation</p>
                  <p>• Mobile-responsive design</p>
                  <p>• Inspection workflow management</p>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Integration Ready:</h4>
                <div className="text-gray-600 space-y-1">
                  <p>• Backend API integration points</p>
                  <p>• Database storage for permit records</p>
                  <p>• User authentication and authorization</p>
                  <p>• Site-specific configuration management</p>
                  <p>• Notification system for permit status</p>
                  <p>• Audit trail and compliance reporting</p>
                  <p>• Integration with existing safety systems</p>
                  <p>• Customizable approval workflows</p>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-8">
            <button
              onClick={handleTryForm}
              className="bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors"
            >
              Experience the Excavation Permit Form
            </button>
            <p className="text-gray-500 mt-2">
              Try the interactive form to see all features in action
            </p>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default ExcavationFormDemoPage;
