import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { excavationFormData, description } from '../utils/ExcavationPTW';

interface ExcavationFormData {
  serialNumber: string;
  formData: { [key: string]: any };
  precautionsData: Array<{ [key: string]: any }>;
  permitIssueData: { [key: string]: any };
  permitReturnData: { [key: string]: any };
}

const ExcavationFormPage: React.FC = () => {
  const {} = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Generate random serial number
  const [serialNumber] = useState(() => Math.floor(Math.random() * 1000000).toString());

  const [formData, setFormData] = useState<ExcavationFormData>({
    serialNumber,
    formData: {
      // Auto-populate date and time fields with current time and 24 hours later
      'Details_Starting from': new Date().toISOString().slice(0, 16),
      'Details_Ending at': new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 24 hours later
    },
    precautionsData: [
      { precaution: 'Shoring', required: false, initial: '' },
      { precaution: 'Pre excavation assessment', required: false, initial: '' },
      { precaution: 'Benching', required: false, initial: '' },
      { precaution: 'Access ladders', required: false, initial: '' },
      { precaution: 'Emergency preparedness and response', required: false, initial: '' },
      { precaution: 'Regular atmosphere testing', required: false, initial: '' },
      { precaution: 'Barrication of work area', required: false, initial: '' },
      { precaution: 'Post signages', required: false, initial: '' },
      { precaution: 'Job rotation and rests', required: false, initial: '' },
      { precaution: 'Inspection of equipment', required: false, initial: '' },
      { precaution: 'Supervision', required: false, initial: '' },
      { precaution: 'PPE(steel toe safety shoes, hard hats, clear impact resistant glasses, overalls)', required: false, initial: '' },
      { precaution: 'Buddy system', required: false, initial: '' },
      { precaution: 'Edge protection', required: false, initial: '' }
    ],
    permitIssueData: {},
    permitReturnData: {}
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      formData: { ...prev.formData, [field]: value }
    }));
  };

  const handlePrecautionChange = (rowIndex: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      precautionsData: prev.precautionsData.map((row, index) =>
        index === rowIndex ? { ...row, [field]: value } : row
      )
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Excavation Form submitted:', formData);
      alert('Excavation Permit to Work form submitted successfully!');
      navigate(-1);
    } catch (error) {
      console.error('Error submitting Excavation form:', error);
      alert('Error submitting form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderField = (field: any, sectionKey: string, disabled: boolean = false) => {
    const fieldKey = `${sectionKey}_${field.name}`;
    let value = formData.formData[fieldKey] || '';

    // Handle name mirroring from Permit Issue to Permit Return
    if (disabled && sectionKey.includes('Permit Return') && field.name === 'Name') {
      const issueKey = sectionKey.replace('Permit Return', 'Permit Issue');
      const issueValue = formData.formData[issueKey] || '';
      value = issueValue;
    }

    const baseClassName = `w-full px-2 py-1 text-sm border border-gray-300 rounded-md ${disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'focus:ring-green-500 focus:border-green-500'}`;

    switch (field.type) {
      case 'text':
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'textarea':
        return (
          <textarea
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            rows={2}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'checkbox':
        return (
          <input
            type="checkbox"
            checked={value || false}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.checked)}
            className={`h-4 w-4 text-green-600 border-gray-300 rounded ${disabled ? 'cursor-not-allowed' : 'focus:ring-green-500'}`}
            required={field.required}
            disabled={disabled}
          />
        );
      case 'datetime':
        return (
          <input
            type="datetime-local"
            value={value || ''}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'date':
        return (
          <input
            type="date"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'time':
        return (
          <input
            type="time"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'signature':
        return (
          <div className={`border border-gray-300 rounded-md p-2 text-center text-xs ${disabled ? 'bg-gray-100 text-gray-400' : 'bg-gray-50 text-gray-500'}`}>
            Signature Pad
          </div>
        );
      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
    }
  };

  return (
    <FloatingCard title="Excavation Permit to Work">
      <div className="h-full bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Excavation Permit to Work</h1>
                <p className="text-xs text-gray-500">{description}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="w-full p-4">
          <div className="space-y-3">
            {/* Header Section */}
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-2">
              <div className="text-center">
                <div className="flex items-center justify-center gap-4 mb-1">
                  <h2 className="text-base font-bold text-orange-800">
                    ECAVATION PERMIT TO WORK
                  </h2>
                  <span className="text-xs text-gray-500 font-light">Serial No: {formData.serialNumber}</span>
                </div>
                <div className="text-xs text-red-600 font-medium">
                  This permit is valid for one day ONLY. Tick as appropriate.
                </div>
              </div>
            </div>

            {/* Form Sections */}
            {excavationFormData.map((section, sectionIndex) => {
              // Handle special sections
              if (section['Precautions Required']) {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">Precautions Required</h3>
                    
                    <div className="overflow-x-auto">
                      <table className="min-w-full border border-gray-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                              Precaution Required
                            </th>
                            <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                              Required
                            </th>
                            <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Initial (e.g V.K)
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {formData.precautionsData.map((row, rowIndex) => (
                            <tr key={rowIndex}>
                              <td className="px-2 py-1 text-xs text-gray-900 border-r border-gray-300">
                                {row.precaution}
                              </td>
                              <td className="px-2 py-1 border-r border-gray-300 text-center">
                                <input
                                  type="checkbox"
                                  checked={row.required || false}
                                  onChange={(e) => handlePrecautionChange(rowIndex, 'required', e.target.checked)}
                                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                />
                              </td>
                              <td className="px-2 py-1">
                                <input
                                  type="text"
                                  value={row.initial || ''}
                                  onChange={(e) => handlePrecautionChange(rowIndex, 'initial', e.target.value)}
                                  className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                                />
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                );
              }

              // Handle List of equipment section
              if (section['List of equipment to be engaged']) {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-2">List of equipment to be engaged</h3>
                    <textarea
                      value={formData.formData['List of equipment to be engaged'] || ''}
                      onChange={(e) => handleInputChange('List of equipment to be engaged', e.target.value)}
                      rows={3}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                      placeholder="List all equipment to be used..."
                    />
                  </div>
                );
              }

              // Handle Inspections section
              if (section['Inspections']) {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">Inspections</h3>
                    {section['Inspections'].description && (
                      <p className="text-xs text-gray-600 mb-3">{section['Inspections'].description}</p>
                    )}
                    
                    {/* Inspection Items */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-1 mb-4">
                      {section['Inspections'].items?.map((item: any, itemIndex: number) => (
                        <div key={itemIndex} className="flex items-center space-x-1">
                          {item.type === 'checkbox' ? (
                            <input
                              type="checkbox"
                              checked={formData.formData[`Inspections_${item.name}`] || false}
                              onChange={(e) => handleInputChange(`Inspections_${item.name}`, e.target.checked)}
                              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                            />
                          ) : (
                            <input
                              type="text"
                              value={formData.formData[`Inspections_${item.name}`] || ''}
                              onChange={(e) => handleInputChange(`Inspections_${item.name}`, e.target.value)}
                              className="w-full px-1 py-1 text-xs border border-gray-300 rounded focus:ring-green-500 focus:border-green-500"
                              placeholder={item.name}
                            />
                          )}
                          <label className="text-xs text-gray-700 leading-tight">{item.name}</label>
                        </div>
                      ))}
                    </div>

                    {/* Authorization */}
                    <div className="border-t pt-3">
                      <h4 className="text-sm font-medium text-gray-800 mb-2">Inspector Authorization</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                        {section['Inspections'].authorization?.map((field: any, fieldIndex: number) => (
                          <div key={fieldIndex}>
                            <label className="block text-xs font-medium text-gray-700 mb-1">
                              {field.name} {field.required && '*'}
                            </label>
                            {renderField(field, 'Inspections_Authorization')}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                );
              }

              // Handle Permit Issue section
              if (section['Permit Issue']) {
                return (
                  <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">Permit Issue</h3>
                    {section['Permit Issue'].description && (
                      <p className="text-xs text-gray-600 mb-3">{section['Permit Issue'].description}</p>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {section['Permit Issue'].items?.map((item: any, itemIndex: number) => {
                        const itemKey = Object.keys(item)[0];
                        const itemFields = item[itemKey];

                        return (
                          <div key={itemIndex} className="border border-gray-200 rounded p-3">
                            <h4 className="text-sm font-medium text-gray-800 mb-2">{itemKey}</h4>
                            <div className="space-y-2">
                              {itemFields.map((field: any, fieldIndex: number) => (
                                <div key={fieldIndex}>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">
                                    {field.name} {field.required && '*'}
                                  </label>
                                  {renderField(field, `Permit Issue_${itemKey}`)}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              }

              // Handle Permit Return section (grayed out and disabled)
              if (section['Permit Return']) {
                return (
                  <div key={sectionIndex} className="bg-gray-100 border border-gray-200 rounded-lg p-3 opacity-60">
                    <h3 className="text-base font-semibold text-gray-900 mb-3">Permit Return</h3>
                    {section['Permit Return'].description && (
                      <p className="text-xs text-gray-600 mb-3">{section['Permit Return'].description}</p>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {section['Permit Return'].items?.map((item: any, itemIndex: number) => {
                        const itemKey = Object.keys(item)[0];
                        const itemFields = item[itemKey];

                        return (
                          <div key={itemIndex} className="border border-gray-200 rounded p-3 bg-gray-50">
                            <h4 className="text-sm font-medium text-gray-800 mb-2">{itemKey}</h4>
                            <div className="space-y-2">
                              {itemFields.map((field: any, fieldIndex: number) => (
                                <div key={fieldIndex}>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">
                                    {field.name} {field.required && '*'}
                                  </label>
                                  {renderField(field, `Permit Return_${itemKey}`, true)}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              }

              // Handle regular sections with field arrays
              const sectionKey = Object.keys(section)[0];
              const sectionData = section[sectionKey as keyof typeof section];
              const fields = sectionData as any[];

              return (
                <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-3">
                  <h3 className="text-base font-semibold text-gray-900 mb-2">{sectionKey}</h3>

                  {/* For checkbox sections, use grid layout */}
                  {fields.every(field => field.type === 'checkbox') ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-1">
                      {fields.map((field, fieldIndex) => (
                        <div key={fieldIndex} className="flex items-center space-x-1">
                          {renderField(field, sectionKey)}
                          <label className="text-xs text-gray-700 leading-tight">{field.name}</label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    /* For other field types, use standard layout */
                    <div className="space-y-2">
                      {fields.map((field, fieldIndex) => (
                        <div key={fieldIndex}>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            {field.name} {field.required && '*'}
                          </label>
                          {renderField(field, sectionKey)}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={() => navigate(-1)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 disabled:opacity-50"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Form'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default ExcavationFormPage;
