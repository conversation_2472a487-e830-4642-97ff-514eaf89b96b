import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Calendar, Users, Clock, Building, MapPin, User, FileText, Shield, Wrench, FileCheck,
  AlertTriangle, TrendingUp, Activity} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import TabContainer, { Tab } from '../components/data/shared/TabContainer';
import { SiteInfo } from '../types';

// Mock site committee data
interface CommitteeMember {
  id: string;
  name: string;
  role: string;
  email: string;
  phone: string;
}

interface SiteTimeline {
  phase: string;
  startDate: string;
  endDate: string;
  status: 'completed' | 'in-progress' | 'upcoming';
  description: string;
}



// Static information interfaces
interface ProjectDetails {
  projectType: string;
  clientName: string;
  architect: string;
  structuralEngineer: string;
  contractor: string;
  subcontractors: string[];
  projectValue: string;
  contractType: string;
  startDate: string;
  plannedEndDate: string;
  actualEndDate?: string;
}

interface SiteSpecifications {
  plotNumber: string;
  coordinates: { latitude: number; longitude: number };
  siteArea: string;
  buildingFootprint: string;
  floors: number;
  basements: number;
  totalBuiltArea: string;
  parkingSpaces: number;
  accessRoads: string[];
  utilities: {
    water: string;
    electricity: string;
    sewer: string;
    internet: string;
  };
}

interface RegulatoryInfo {
  permitNumber: string;
  zoneClassification: string;
  landUse: string;
  buildingClass: string;
  occupancyType: string;
  constructionType: string;
  fireRating: string;
  accessibilityCompliance: string;
  environmentalClearance: string;
}

interface EmergencyContacts {
  id: string;
  category: 'medical' | 'fire' | 'police' | 'utility' | 'environmental';
  organization: string;
  contactPerson?: string;
  phone: string;
  email?: string;
  address?: string;
  availability: string;
}

const SiteInfoPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const [site, setSite] = useState<SiteInfo | null>(null);
  const [projectDetails, setProjectDetails] = useState<ProjectDetails | null>(null);
  const [siteSpecifications, setSiteSpecifications] = useState<SiteSpecifications | null>(null);
  const [regulatoryInfo, setRegulatoryInfo] = useState<RegulatoryInfo | null>(null);
  const [committeeMembers, setCommitteeMembers] = useState<CommitteeMember[]>([]);
  const [emergencyContacts, setEmergencyContacts] = useState<EmergencyContacts[]>([]);
  const [timeline, setTimeline] = useState<SiteTimeline[]>([]);
  const [activeTab, setActiveTab] = useState<'project' | 'specifications' | 'regulatory' | 'team' | 'emergency' | 'timeline'>('project');

  useEffect(() => {
    // In a real app, fetch site data based on siteId
    // For now, using mock data
    setSite({
      id: siteId || '',
      name: "Westlands Construction Site",
      healthStatus: "green",
      workersOnSite: 42,
      activePermits: 8,
      openIncidents: 0,
      projectManager: "John Mwangi",
      location: "Waiyaki Way, Westlands, Nairobi",
      timeline: "Jan 2025 - Dec 2026",
      currentPhase: "Foundation",
      progressPercentage: 25,
      tenantId: '',
      status: 'active',
      createdAt: new Date()
    });

    // Mock committee members
    setCommitteeMembers([
      { id: '1', name: 'Jane Doe', role: 'Safety Officer', email: '<EMAIL>', phone: '+254 712 345 678' },
      { id: '2', name: 'John Smith', role: 'Site Manager', email: '<EMAIL>', phone: '+254 723 456 789' },
      { id: '3', name: 'Alice Johnson', role: 'Environmental Officer', email: '<EMAIL>', phone: '+254 734 567 890' },
      { id: '4', name: 'Bob Brown', role: 'Worker Representative', email: '<EMAIL>', phone: '+254 745 678 901' },
    ]);

    // Mock timeline data
    setTimeline([
      { 
        phase: 'Planning & Approval', 
        startDate: 'Jan 2025', 
        endDate: 'Mar 2025', 
        status: 'completed', 
        description: 'Site planning, permit acquisition, and initial approvals' 
      },
      { 
        phase: 'Foundation', 
        startDate: 'Apr 2025', 
        endDate: 'Jul 2025', 
        status: 'in-progress', 
        description: 'Excavation and foundation construction' 
      },
      { 
        phase: 'Structural Work', 
        startDate: 'Aug 2025', 
        endDate: 'Feb 2026', 
        status: 'upcoming', 
        description: 'Main structural elements and framework' 
      },
      { 
        phase: 'Interior & Finishing', 
        startDate: 'Mar 2026', 
        endDate: 'Oct 2026', 
        status: 'upcoming', 
        description: 'Interior systems, fixtures, and finishing work' 
      },
      { 
        phase: 'Final Inspection & Handover', 
        startDate: 'Nov 2026', 
        endDate: 'Dec 2026', 
        status: 'upcoming', 
        description: 'Final inspections, certifications, and project handover' 
      },
    ]);

    // Mock project details
    setProjectDetails({
      projectType: 'Mixed-Use Commercial Development',
      clientName: 'Westlands Development Corporation',
      architect: 'Nairobi Architectural Associates',
      structuralEngineer: 'Kenya Structural Engineers Ltd.',
      contractor: 'Apex Construction Ltd.',
      subcontractors: ['Elite Electrical Services', 'Premier Plumbing Co.', 'Modern HVAC Systems', 'Skyline Elevators'],
      projectValue: 'KES 2.8 Billion',
      contractType: 'Design-Build',
      startDate: '2025-01-15',
      plannedEndDate: '2026-12-30' });

    // Mock site specifications
    setSiteSpecifications({
      plotNumber: 'LR No. 209/10542',
      coordinates: { latitude: -1.2676, longitude: 36.8108 },
      siteArea: '12,500 sq. meters',
      buildingFootprint: '8,200 sq. meters',
      floors: 22,
      basements: 3,
      totalBuiltArea: '156,000 sq. meters',
      parkingSpaces: 450,
      accessRoads: ['Waiyaki Way (Main)', 'Ring Road Parklands (Secondary)'],
      utilities: {
        water: 'Nairobi City Water & Sewerage Co.',
        electricity: 'Kenya Power & Lighting Co.',
        sewer: 'Nairobi City Water & Sewerage Co.',
        internet: 'Safaricom Fiber'
      }
    });

    // Mock regulatory information
    setRegulatoryInfo({
      permitNumber: 'NBI-2025-0042',
      zoneClassification: 'Commercial (C2)',
      landUse: 'Mixed Commercial/Residential',
      buildingClass: 'Class A Commercial',
      occupancyType: 'Mixed Use (Business/Mercantile/Residential)',
      constructionType: 'Type I-A (Fire Resistant)',
      fireRating: '2-Hour Fire Resistance',
      accessibilityCompliance: 'ADA Compliant / Kenya Building Code 2030',
      environmentalClearance: 'EIA License No. NEMA/EIA/2024/1542'
    });

    // Mock emergency contacts
    setEmergencyContacts([
      { id: '1', category: 'medical', organization: 'Nairobi Hospital Emergency', phone: '+254-20-2845000', email: '<EMAIL>', availability: '24/7' },
      { id: '2', category: 'fire', organization: 'Nairobi Fire Brigade', phone: '+254-20-222222', availability: '24/7' },
      { id: '3', category: 'police', organization: 'Parklands Police Station', phone: '+254-20-3748000', availability: '24/7' },
      { id: '4', category: 'utility', organization: 'Kenya Power Emergency', contactPerson: 'John Kiprotich', phone: '+254-703-070707', email: '<EMAIL>', availability: '24/7' },
      { id: '5', category: 'environmental', organization: 'NEMA Emergency Response', contactPerson: 'Dr. Sarah Wanjiku', phone: '+254-20-2183804', email: '<EMAIL>', availability: 'Mon-Fri 8AM-5PM' }
    ]);
  }, [siteId]);

  if (!site) {
    return <div>Loading...</div>;
  }

  const breadcrumbs = [
    { name: "Dashboard", path: "/" },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: "Site Information", path: `/sites/${siteId}/info` },
  ];

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId as 'project' | 'specifications' | 'regulatory' | 'team' | 'emergency' | 'timeline');
  };

  const tabs: Tab[] = [
    {
      id: 'project',
      label: 'Project Details',
      icon: <Building className="h-4 w-4" />,
      content: projectDetails && (
        <div className="space-y-6">
          {/* Bento Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-min">

            {/* Hero Card - Project Overview */}
            <div className="md:col-span-2 lg:col-span-2 bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{projectDetails.projectType}</h3>
                  <p className="text-blue-600 font-medium mb-4">{projectDetails.clientName}</p>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2 text-blue-500" />
                      <span>{new Date(projectDetails.startDate).toLocaleDateString()} - {new Date(projectDetails.plannedEndDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
                      <span className="font-semibold text-green-600">{projectDetails.projectValue}</span>
                    </div>
                  </div>
                </div>
                <Building className="h-12 w-12 text-gray-300" />
              </div>
            </div>

            {/* Contract Type Card */}
            <div className="bg-white p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-gray-500">Contract Type</h4>
                <FileText className="h-4 w-4 text-gray-400" />
              </div>
              <p className="text-lg font-semibold text-gray-900">{projectDetails.contractType}</p>
            </div>

            {/* Key Personnel Card */}
            <div className="bg-white p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                <User className="h-4 w-4 mr-1" />
                Key Personnel
              </h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="text-gray-500">Architect:</span>
                  <p className="font-medium text-gray-900 truncate">{projectDetails.architect}</p>
                </div>
                <div>
                  <span className="text-gray-500">Engineer:</span>
                  <p className="font-medium text-gray-900 truncate">{projectDetails.structuralEngineer}</p>
                </div>
              </div>
            </div>

            {/* Main Contractor Card */}
            <div className="md:col-span-2 bg-white p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                <Wrench className="h-4 w-4 mr-1 text-green-500" />
                Main Contractor
              </h4>
              <p className="text-xl font-bold text-green-600">{projectDetails.contractor}</p>
            </div>

            {/* Subcontractors Card */}
            <div className="md:col-span-1 lg:col-span-2 bg-white p-4 rounded-xl border border-gray-200 shadow-sm">
              <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                <Users className="h-4 w-4 mr-1" />
                Subcontractors ({projectDetails.subcontractors.length})
              </h4>
              <div className="grid grid-cols-1 gap-2">
                {projectDetails.subcontractors.map((sub, index) => (
                  <div key={index} className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mr-2 flex-shrink-0"></div>
                    <span className="text-gray-700 truncate">{sub}</span>
                  </div>
                ))}
              </div>
            </div>

          </div>
        </div>
      )
    },
    {
      id: 'specifications',
      label: 'Site Specifications',
      icon: <MapPin className="h-4 w-4" />,
      content: siteSpecifications && (
        <div className="space-y-6">
          {/* Bento Grid Layout for Site Specifications */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-min">

            {/* Location Hero Card */}
            <div className="md:col-span-2 bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2 flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-blue-500" />
                    Site Location
                  </h3>
                  <p className="text-blue-600 font-medium mb-3">{siteSpecifications.plotNumber}</p>
                  <div className="text-sm text-gray-600">
                    <span className="font-mono bg-gray-50 px-2 py-1 rounded text-xs border">
                      {siteSpecifications.coordinates.latitude}, {siteSpecifications.coordinates.longitude}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">{siteSpecifications.siteArea}</p>
                  <p className="text-sm text-gray-500">Total Area</p>
                </div>
              </div>
            </div>

            {/* Building Stats */}
            <div className="md:col-span-1 bg-white p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                <Building className="h-4 w-4 mr-1" />
                Building Stats
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Floors:</span>
                  <span className="font-semibold text-blue-600">{siteSpecifications.floors}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Basements:</span>
                  <span className="font-semibold text-blue-600">{siteSpecifications.basements}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Parking:</span>
                  <span className="font-semibold text-blue-600">{siteSpecifications.parkingSpaces}</span>
                </div>
              </div>
            </div>

            {/* Building Footprint */}
            <div className="bg-white p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Building Footprint</h4>
              <p className="text-xl font-bold text-gray-900">{siteSpecifications.buildingFootprint}</p>
              <p className="text-sm text-gray-600 mt-1">Built Area: {siteSpecifications.totalBuiltArea}</p>
            </div>

            {/* Access Roads */}
            <div className="md:col-span-2 bg-white p-4 rounded-xl border border-gray-200 shadow-sm">
              <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                <Activity className="h-4 w-4 mr-1" />
                Access Roads
              </h4>
              <div className="space-y-2">
                {siteSpecifications.accessRoads.map((road, index) => (
                  <div key={index} className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-3 ${index === 0 ? 'bg-green-400' : 'bg-blue-400'}`}></div>
                    <span className="text-sm text-gray-700">{road}</span>
                    {index === 0 && <span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">Primary</span>}
                  </div>
                ))}
              </div>
            </div>

            {/* Utilities Grid */}
            <div className="md:col-span-2 bg-white p-4 rounded-xl border border-gray-200 shadow-sm">
              <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                <Wrench className="h-4 w-4 mr-1" />
                Utilities & Services
              </h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-50 p-3 rounded-lg border">
                  <p className="text-xs text-blue-600 font-medium">Water</p>
                  <p className="text-sm text-gray-900 truncate">{siteSpecifications.utilities.water}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg border">
                  <p className="text-xs text-yellow-600 font-medium">Electricity</p>
                  <p className="text-sm text-gray-900 truncate">{siteSpecifications.utilities.electricity}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg border">
                  <p className="text-xs text-green-600 font-medium">Sewer</p>
                  <p className="text-sm text-gray-900 truncate">{siteSpecifications.utilities.sewer}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg border">
                  <p className="text-xs text-purple-600 font-medium">Internet</p>
                  <p className="text-sm text-gray-900 truncate">{siteSpecifications.utilities.internet}</p>
                </div>
              </div>
            </div>

          </div>
        </div>
      )
    },
    {
      id: 'regulatory',
      label: 'Regulatory & Compliance',
      icon: <FileCheck className="h-4 w-4" />,
      content: regulatoryInfo && (
        <div className="space-y-6">
          {/* Bento Grid Layout for Regulatory Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-min">

            {/* Primary Permit Hero Card */}
            <div className="md:col-span-2 bg-white p-6 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2 flex items-center">
                    <FileCheck className="h-5 w-5 mr-2 text-green-500" />
                    Building Permit
                  </h3>
                  <p className="text-2xl font-bold text-green-600 mb-2">{regulatoryInfo.permitNumber}</p>
                  <div className="space-y-1">
                    <p className="text-sm text-green-600">{regulatoryInfo.zoneClassification}</p>
                    <p className="text-sm text-gray-600">{regulatoryInfo.landUse}</p>
                  </div>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg border">
                  <Shield className="h-8 w-8 text-green-500" />
                </div>
              </div>
            </div>

            {/* Building Classification */}
            <div className="bg-white p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                <Building className="h-4 w-4 mr-1" />
                Classification
              </h4>
              <div className="space-y-2">
                <div>
                  <p className="text-xs text-gray-500">Building Class</p>
                  <p className="font-semibold text-gray-900 text-sm">{regulatoryInfo.buildingClass}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Construction Type</p>
                  <p className="font-semibold text-gray-900 text-sm">{regulatoryInfo.constructionType}</p>
                </div>
              </div>
            </div>

            {/* Occupancy Type */}
            <div className="bg-white p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-2">Occupancy Type</h4>
              <p className="text-sm font-bold text-blue-600">{regulatoryInfo.occupancyType}</p>
            </div>

            {/* Fire Safety */}
            <div className="md:col-span-2 bg-white p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <h4 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-1 text-red-500" />
                Fire Safety Rating
              </h4>
              <p className="text-lg font-bold text-red-600">{regulatoryInfo.fireRating}</p>
            </div>

            {/* Compliance Cards */}
            <div className="md:col-span-2 bg-white p-4 rounded-xl border border-gray-200 shadow-sm">
              <h4 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
                <FileCheck className="h-4 w-4 mr-1" />
                Compliance Standards
              </h4>
              <div className="space-y-3">
                <div className="bg-gray-50 p-3 rounded-lg border">
                  <p className="text-xs text-green-600 font-medium">Accessibility</p>
                  <p className="text-sm text-gray-900">{regulatoryInfo.accessibilityCompliance}</p>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg border">
                  <p className="text-xs text-blue-600 font-medium">Environmental</p>
                  <p className="text-sm text-gray-900">{regulatoryInfo.environmentalClearance}</p>
                </div>
              </div>
            </div>

          </div>
        </div>
      )
    },
    {
      id: 'team',
      label: 'Site Committee',
      icon: <Users className="h-4 w-4" />,
      content: (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Site Committee Members</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {committeeMembers.map((member) => (
                  <tr key={member.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="font-medium text-gray-900">{member.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{member.role}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>{member.email}</div>
                      <div>{member.phone}</div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )
    },
    {
      id: 'emergency',
      label: 'Emergency Contacts',
      icon: <AlertTriangle className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          {/* Bento Grid Layout for Emergency Contacts */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-min">

            {emergencyContacts.map((contact) => {
              const categoryConfig = {
                medical: { accent: 'text-red-600' },
                fire: { accent: 'text-orange-600' },
                police: { accent: 'text-blue-600' },
                utility: { accent: 'text-yellow-600' },
                environmental: { accent: 'text-green-600' }
              };

              const config = categoryConfig[contact.category] || categoryConfig.utility;

              return (
                <div key={contact.id} className="bg-white p-5 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-2 ${
                        contact.category === 'medical' ? 'bg-red-500' :
                        contact.category === 'fire' ? 'bg-orange-500' :
                        contact.category === 'police' ? 'bg-blue-500' :
                        contact.category === 'utility' ? 'bg-yellow-500' :
                        'bg-green-500'
                      }`}></div>
                      <h4 className={`font-semibold capitalize ${config.accent}`}>{contact.category}</h4>
                    </div>
                    {contact.availability === '24/7' && (
                      <span className="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full font-medium">24/7</span>
                    )}
                  </div>

                  <div className="space-y-2">
                    <h5 className="font-bold text-gray-900 text-sm leading-tight">{contact.organization}</h5>

                    {contact.contactPerson && (
                      <p className="text-sm text-gray-700 flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        {contact.contactPerson}
                      </p>
                    )}

                    <div className="bg-gray-50 p-2 rounded-lg border">
                      <p className="font-bold text-gray-900 text-lg">{contact.phone}</p>
                      {contact.email && (
                        <p className="text-sm text-gray-600 truncate">{contact.email}</p>
                      )}
                    </div>

                    {contact.availability !== '24/7' && (
                      <p className="text-xs text-gray-600 mt-2">{contact.availability}</p>
                    )}
                  </div>
                </div>
              );
            })}

          </div>
        </div>
      )
    },
    {
      id: 'timeline',
      label: 'Project Timeline',
      icon: <Clock className="h-4 w-4" />,
      content: (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Project Timeline</h3>
          <div className="space-y-6">
            {timeline.map((phase, index) => (
              <div key={index} className="relative">
                {/* Timeline connector */}
                {index < timeline.length - 1 && (
                  <div className="absolute top-8 left-4 w-0.5 h-full bg-gray-200"></div>
                )}

                <div className="flex">
                  {/* Status indicator */}
                  <div className="flex-shrink-0 mr-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      phase.status === 'completed' ? 'bg-green-100 text-green-600' :
                      phase.status === 'in-progress' ? 'bg-blue-100 text-blue-600' :
                      'bg-gray-100 text-gray-400'
                    }`}>
                      {phase.status === 'completed' ? (
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : phase.status === 'in-progress' ? (
                        <Clock className="w-4 h-4" />
                      ) : (
                        <span className="text-xs font-bold">●</span>
                      )}
                    </div>
                  </div>

                  {/* Phase details */}
                  <div className="flex-1 pb-6">
                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                      <div className="flex justify-between items-start">
                        <h4 className="text-md font-medium text-gray-900">{phase.phase}</h4>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          phase.status === 'completed' ? 'bg-green-100 text-green-800' :
                          phase.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {phase.status === 'completed' ? 'Completed' :
                           phase.status === 'in-progress' ? 'In Progress' :
                           'Upcoming'}
                        </span>
                      </div>
                      <div className="mt-1 text-sm text-gray-500">
                        {phase.startDate} - {phase.endDate}
                      </div>
                      <p className="mt-2 text-sm text-gray-600">
                        {phase.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )
    }
  ];

  return (
    <FloatingCard title="Site Information" breadcrumbs={breadcrumbs}>
      <TabContainer
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />
    </FloatingCard>
  );
};

export default SiteInfoPage;
