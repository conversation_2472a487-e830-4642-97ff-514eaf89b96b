import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { ApolloClient, InM<PERSON>oryCache, ApolloProvider, from, HttpLink } from "@apollo/client";
import { onError } from "@apollo/client/link/error";
// import { createUploadLink } from "apollo-upload-client/esm";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import App from "./App.tsx";
import "./index.css";
import {  createUploadLinkWithFormData } from "./utils/graphql-upload/createCustomUploadLink.ts";

const uri = import.meta.env.VITE_GRAPHQL_URI_1 || "http://localhost:3000/graphql";

// Error handling link
const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors)
    graphQLErrors.forEach(({ message, locations, path }) =>
      console.log(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      )
    );
  if (networkError) {
    console.log(`[Network error]: ${networkError}`);
    // Don't throw error for connection refused - just log it
    if (networkError.message.includes('fetch')) {
      console.log('GraphQL server not available - using mock data');
    }
  }
});

// Create a fallback link for when the main server is not available
const fallbackLink = new HttpLink({
  uri: 'http://localhost:3000/graphql', // Fallback to a mock endpoint
});

const client = new ApolloClient({
	link: from([
    errorLink,
    createUploadLinkWithFormData(uri)
  ]),
	cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
    },
    query: {
      errorPolicy: 'all',
    },
  },
});

createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<ApolloProvider client={client}>
			<App />
			<ToastContainer position="top-right" autoClose={3000} />
		</ApolloProvider>
	</StrictMode>,
);
