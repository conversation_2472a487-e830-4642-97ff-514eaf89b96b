import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  Cloud, 
  CloudRain, 
  Sun, 
  CloudSnow, 
  Zap, 
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { useWeather } from '../../hooks/useWeather';
import { extractSiteId } from '../../utils/routeUtils';

interface WeatherIconProps {
  className?: string;
}

export const WeatherIcon: React.FC<WeatherIconProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const currentSiteId = extractSiteId(location.pathname);
  
  const { weatherData, loading, getWeatherForSite } = useWeather();
  
  // Get weather for current site or first available site
  const weather = currentSiteId ? getWeatherForSite(currentSiteId) : weatherData[0];

  const getWeatherIcon = (condition: string, size: string = "h-5 w-5") => {
    const iconClass = `${size}`;
    switch (condition?.toLowerCase()) {
      case "clear":
        return <Sun className={`${iconClass} text-yellow-500`} />;
      case "clouds":
        return <Cloud className={`${iconClass} text-gray-500`} />;
      case "rain":
      case "drizzle":
        return <CloudRain className={`${iconClass} text-blue-500`} />;
      case "snow":
        return <CloudSnow className={`${iconClass} text-blue-300`} />;
      case "thunderstorm":
        return <Zap className={`${iconClass} text-purple-500`} />;
      default:
        return <Cloud className={`${iconClass} text-gray-500`} />;
    }
  };

  const getTemperatureColor = (temp: number) => {
    if (temp < 0) return "text-blue-600";
    if (temp < 10) return "text-blue-500";
    if (temp < 20) return "text-green-500";
    if (temp < 30) return "text-yellow-500";
    return "text-red-500";
  };

  const handleClick = () => {
    if (currentSiteId) {
      navigate(`/sites/${currentSiteId}/weather`);
    }
  };

  // Don't render if no site context
  if (!currentSiteId) {
    return null;
  }

  // Loading state
  if (loading) {
    return (
      <div className={`inline-flex items-center justify-center p-2 rounded-lg ${className}`}>
        <RefreshCw className="h-5 w-5 text-gray-400 animate-spin" />
      </div>
    );
  }

  // No weather data
  if (!weather) {
    return (
      <button
        onClick={handleClick}
        className={`inline-flex items-center justify-center p-2 rounded-lg text-gray-600 hover:text-green-600 hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${className}`}
        title="Weather data unavailable - Click to view weather page"
      >
        <Cloud className="h-5 w-5" />
      </button>
    );
  }

  const hasAlerts = weather.alerts && weather.alerts.length > 0;

  return (
    <button
      onClick={handleClick}
      className={`inline-flex items-center space-x-2 p-2 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-green-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${className}`}
      title={`Current weather: ${weather.current.conditions.description} - Click to view detailed weather`}
    >
      {/* Weather Icon */}
      <div className="relative">
        {getWeatherIcon(weather.current.conditions.main)}
        {hasAlerts && (
          <div className="absolute -top-1 -right-1">
            <AlertTriangle className="h-3 w-3 text-red-500" />
          </div>
        )}
      </div>

      {/* Temperature - Always visible */}
      <span className={`text-sm font-medium ${getTemperatureColor(weather.current.temperature)}`}>
        {Math.round(weather.current.temperature)}°C
      </span>
    </button>
  );
};
