import React from "react";
import {
	Clipboard<PERSON>he<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>,
	Plus,
	Play,
} from "lucide-react";
import QuickActionCard from "./shared/QuickActionCard";
import MetricCard from "./shared/MetricCard";
import RecentInspections from "./shared/RecentInspections";
import UpcomingInspections from "./shared/UpcomingInspections";

interface InspectionsDashboardProps {
	siteId: string;
	onNavigateToTab: (tabId: string) => void;
}

const InspectionsDashboard: React.FC<InspectionsDashboardProps> = ({
	siteId,
	onNavigateToTab,
}) => {
	// Mock data - replace with actual API calls
	const metrics = {
		totalScheduled: 24,
		overdue: 3,
		completed: 18,
		inProgress: 2,
		complianceRate: 87,
	};

	const handleStartInspection = () => {
		// Navigate to conduct inspection or show template selection modal
		console.log("Start new inspection");
	};

	const handleScheduleInspection = () => {
		// Navigate to schedule tab or show scheduling modal
		onNavigateToTab("schedule");
	};

	const handleViewOverdue = () => {
		// Navigate to schedule tab with overdue filter
		onNavigateToTab("schedule");
	};

	return (
		<div className="space-y-6">
			{/* Metrics Overview */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
				<MetricCard
					title="Total Scheduled"
					value={metrics.totalScheduled}
					icon={<ClipboardCheck className="h-6 w-6" />}
					color="blue"
				/>
				<MetricCard
					title="Overdue"
					value={metrics.overdue}
					icon={<AlertTriangle className="h-6 w-6" />}
					color="red"
					onClick={handleViewOverdue}
				/>
				<MetricCard
					title="Completed"
					value={metrics.completed}
					icon={<CheckCircle className="h-6 w-6" />}
					color="green"
				/>
				<MetricCard
					title="In Progress"
					value={metrics.inProgress}
					icon={<Clock className="h-6 w-6" />}
					color="yellow"
				/>
				<MetricCard
					title="Compliance Rate"
					value={`${metrics.complianceRate}%`}
					icon={<CheckCircle className="h-6 w-6" />}
					color="green"
				/>
			</div>

			{/* Quick Actions */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<QuickActionCard
					title="Start New Inspection"
					description="Begin a new site safety or equipment inspection"
					icon={<Play className="h-6 w-6" />}
					onClick={handleStartInspection}
				/>
				<QuickActionCard
					title="Schedule Inspection"
					description="Schedule recurring or one-time inspections"
					icon={<Plus className="h-6 w-6" />}
					onClick={handleScheduleInspection}
				/>
			</div>

			{/* Recent Activity and Upcoming */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<RecentInspections siteId={siteId} />
				<UpcomingInspections
					siteId={siteId}
					onNavigateToTab={onNavigateToTab}
				/>
			</div>
		</div>
	);
};

export default InspectionsDashboard;
