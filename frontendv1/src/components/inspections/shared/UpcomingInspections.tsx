import React from "react";
import { Calendar, Clock, AlertTriangle, User } from "lucide-react";

interface UpcomingInspectionsProps {
	siteId: string;
	onNavigateToTab: (tabId: string) => void;
}

interface UpcomingInspection {
	id: string;
	templateName: string;
	target: string;
	assignedTo: string;
	dueDate: string;
	dueTime: string;
	priority: "high" | "medium" | "low";
	isOverdue: boolean;
}

const UpcomingInspections: React.FC<UpcomingInspectionsProps> = ({
	siteId:_siteId,
	onNavigateToTab,
}) => {
	// Mock data - replace with actual API call
	const upcomingInspections: UpcomingInspection[] = [
		{
			id: "1",
			templateName: "Daily Site Safety",
			target: "Construction Zone B",
			assignedTo: "John Mwangi",
			dueDate: "2024-01-16",
			dueTime: "09:00",
			priority: "high",
			isOverdue: false,
		},
		{
			id: "2",
			templateName: "Equipment Inspection",
			target: "Crane CR-002",
			assignedTo: "<PERSON>",
			dueDate: "2024-01-16",
			dueTime: "14:00",
			priority: "medium",
			isOverdue: false,
		},
		{
			id: "3",
			templateName: "Fire Safety Check",
			target: "Site Office",
			assignedTo: "<PERSON> Kiprotich",
			dueDate: "2024-01-15",
			dueTime: "16:00",
			priority: "high",
			isOverdue: true,
		},
		{
			id: "4",
			templateName: "Electrical Safety",
			target: "Main Panel Room",
			assignedTo: "Sarah Njeri",
			dueDate: "2024-01-17",
			dueTime: "10:30",
			priority: "medium",
			isOverdue: false,
		},
	];

	const getPriorityColor = (priority: string, isOverdue: boolean) => {
		if (isOverdue) return "text-red-700 bg-red-50 border-red-200";

		switch (priority) {
			case "high":
				return "text-orange-700 bg-orange-50 border-orange-200";
			case "medium":
				return "text-yellow-700 bg-yellow-50 border-yellow-200";
			case "low":
				return "text-green-700 bg-green-50 border-green-200";
			default:
				return "text-gray-700 bg-gray-50 border-gray-200";
		}
	};

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		const today = new Date();
		const tomorrow = new Date(today);
		tomorrow.setDate(tomorrow.getDate() + 1);

		if (date.toDateString() === today.toDateString()) {
			return "Today";
		} else if (date.toDateString() === tomorrow.toDateString()) {
			return "Tomorrow";
		} else {
			return date.toLocaleDateString("en-US", {
				month: "short",
				day: "numeric",
			});
		}
	};

	const handleViewSchedule = () => {
		onNavigateToTab("schedule");
	};

	return (
		<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
			<div className="p-6 border-b border-gray-200">
				<h3 className="text-lg font-semibold text-gray-900">
					Upcoming Inspections
				</h3>
				<p className="text-sm text-gray-600 mt-1">
					Scheduled inspections for the next few days
				</p>
			</div>
			<div className="p-6">
				<div className="space-y-4">
					{upcomingInspections.map((inspection) => (
						<div
							key={inspection.id}
							className={`p-3 rounded-lg border ${getPriorityColor(inspection.priority, inspection.isOverdue)}`}
						>
							<div className="flex items-start justify-between">
								<div className="flex-1 min-w-0">
									<div className="flex items-center space-x-2 mb-1">
										{inspection.isOverdue && (
											<AlertTriangle className="h-4 w-4 text-red-500" />
										)}
										<h4 className="text-sm font-medium text-gray-900 truncate">
											{inspection.templateName}
										</h4>
									</div>
									<p className="text-xs text-gray-600 mb-2">
										{inspection.target}
									</p>
									<div className="flex items-center space-x-4 text-xs text-gray-500">
										<div className="flex items-center space-x-1">
											<Calendar className="h-3 w-3" />
											<span>{formatDate(inspection.dueDate)}</span>
										</div>
										<div className="flex items-center space-x-1">
											<Clock className="h-3 w-3" />
											<span>{inspection.dueTime}</span>
										</div>
										<div className="flex items-center space-x-1">
											<User className="h-3 w-3" />
											<span>{inspection.assignedTo}</span>
										</div>
									</div>
								</div>
								<div className="flex-shrink-0 ml-4">
									<span
										className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(inspection.priority, inspection.isOverdue)}`}
									>
										{inspection.isOverdue
											? "Overdue"
											: inspection.priority.charAt(0).toUpperCase() +
												inspection.priority.slice(1)}
									</span>
								</div>
							</div>
						</div>
					))}
				</div>
				<div className="mt-4 text-center">
					<button
						onClick={handleViewSchedule}
						className="text-sm text-green-600 hover:text-green-700 font-medium"
					>
						View Full Schedule
					</button>
				</div>
			</div>
		</div>
	);
};

export default UpcomingInspections;
