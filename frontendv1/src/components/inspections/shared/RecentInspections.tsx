import React from "react";
import { CheckCircle, XCir<PERSON>, <PERSON>, Al<PERSON><PERSON>riangle } from "lucide-react";

interface RecentInspectionsProps {
	siteId: string;
}

interface InspectionItem {
	id: string;
	templateName: string;
	target: string;
	inspector: string;
	completedAt: string;
	status: "passed" | "failed" | "in-progress" | "overdue";
	criticalIssues?: number;
}

const RecentInspections: React.FC<RecentInspectionsProps> = ({ siteId: _siteId }) => {
	// Mock data - replace with actual API call
	const recentInspections: InspectionItem[] = [
		{
			id: "1",
			templateName: "Daily Site Safety",
			target: "Main Construction Area",
			inspector: "<PERSON>",
			completedAt: "2024-01-15 14:30",
			status: "passed",
		},
		{
			id: "2",
			templateName: "Equipment Inspection",
			target: "Excavator CAT-001",
			inspector: "<PERSON>",
			completedAt: "2024-01-15 11:15",
			status: "failed",
			criticalIssues: 2,
		},
		{
			id: "3",
			templateName: "Scaffold Safety Check",
			target: "Building A - Level 3",
			inspector: "<PERSON>",
			completedAt: "2024-01-15 09:45",
			status: "passed",
		},
		{
			id: "4",
			templateName: "PPE Compliance",
			target: "Site Entrance",
			inspector: "Sarah Njeri",
			completedAt: "2024-01-14 16:20",
			status: "in-progress",
		},
	];

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "passed":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "failed":
				return <XCircle className="h-4 w-4 text-red-500" />;
			case "in-progress":
				return <Clock className="h-4 w-4 text-yellow-500" />;
			case "overdue":
				return <AlertTriangle className="h-4 w-4 text-red-500" />;
			default:
				return <Clock className="h-4 w-4 text-gray-500" />;
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case "passed":
				return "Passed";
			case "failed":
				return "Failed";
			case "in-progress":
				return "In Progress";
			case "overdue":
				return "Overdue";
			default:
				return "Unknown";
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "passed":
				return "text-green-700 bg-green-50";
			case "failed":
				return "text-red-700 bg-red-50";
			case "in-progress":
				return "text-yellow-700 bg-yellow-50";
			case "overdue":
				return "text-red-700 bg-red-50";
			default:
				return "text-gray-700 bg-gray-50";
		}
	};

	return (
		<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
			<div className="p-6 border-b border-gray-200">
				<h3 className="text-lg font-semibold text-gray-900">
					Recent Inspections
				</h3>
				<p className="text-sm text-gray-600 mt-1">Latest inspection activity</p>
			</div>
			<div className="p-6">
				<div className="space-y-4">
					{recentInspections.map((inspection) => (
						<div
							key={inspection.id}
							className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
						>
							<div className="flex-1 min-w-0">
								<div className="flex items-center space-x-2 mb-1">
									{getStatusIcon(inspection.status)}
									<h4 className="text-sm font-medium text-gray-900 truncate">
										{inspection.templateName}
									</h4>
								</div>
								<p className="text-xs text-gray-600 mb-1">
									{inspection.target}
								</p>
								<p className="text-xs text-gray-500">
									{inspection.inspector} • {inspection.completedAt}
								</p>
								{inspection.criticalIssues && (
									<p className="text-xs text-red-600 mt-1">
										{inspection.criticalIssues} critical issue
										{inspection.criticalIssues > 1 ? "s" : ""}
									</p>
								)}
							</div>
							<div className="flex-shrink-0 ml-4">
								<span
									className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(inspection.status)}`}
								>
									{getStatusText(inspection.status)}
								</span>
							</div>
						</div>
					))}
				</div>
				<div className="mt-4 text-center">
					<button className="text-sm text-green-600 hover:text-green-700 font-medium">
						View All Recent Inspections
					</button>
				</div>
			</div>
		</div>
	);
};

export default RecentInspections;
