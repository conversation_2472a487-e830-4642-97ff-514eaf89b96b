import React from "react";
import { useNavigate } from "react-router-dom";
import {
	Clipboard<PERSON>he<PERSON>,
	FileText,
	ChevronRight,
} from "lucide-react";
import { inspectionFormTypes } from "../../data/inspectionFormTemplate";

interface MyInspectionsProps {
	siteId: string;
}

const MyInspections: React.FC<MyInspectionsProps> = ({ siteId }) => {
	const navigate = useNavigate();

	const handleFormSelect = (formId: string) => {
		if (siteId) {
			navigate(`/sites/${siteId}/inspections/form/${formId}`);
		} else {
			navigate(`/inspections/form/${formId}`);
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">
						My Inspections
					</h2>
					<p className="text-sm text-gray-600 mt-1">
						Available inspection forms ({inspectionFormTypes.length} forms)
					</p>
				</div>
			</div>

			{/* Introduction */}
			<div className="bg-white rounded-lg shadow p-6">
				<div className="flex items-center mb-4">
					<ClipboardCheck className="h-8 w-8 text-green-600 mr-3" />
					<div>
						<h3 className="text-xl font-bold text-gray-900">Equipment Inspection Forms</h3>
						<p className="text-gray-600">Select an equipment type to perform safety inspection</p>
					</div>
				</div>

				<div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
					<div className="flex">
						<div className="ml-3">
							<p className="text-sm text-blue-700">
								<strong>Important:</strong> All equipment must be inspected regularly according to safety regulations.
								Complete all inspection points and provide detailed remarks where necessary.
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Forms List */}
			<div className="bg-white rounded-lg shadow overflow-hidden">
				<div className="px-6 py-4 border-b border-gray-200">
					<h3 className="text-lg font-medium text-gray-900">Available Inspection Forms</h3>
					<p className="text-sm text-gray-600 mt-1">
						{inspectionFormTypes.length} equipment types available for inspection
					</p>
				</div>

				<div className="divide-y divide-gray-200">
					{inspectionFormTypes.map((form) => (
						<div
							key={form.id}
							className="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors group"
							onClick={() => handleFormSelect(form.id)}
						>
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<div className="flex-shrink-0">
										<div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
											<FileText className="h-5 w-5 text-green-600" />
										</div>
									</div>

									<div className="flex-1 min-w-0">
										<h4 className="text-lg font-medium text-gray-900 group-hover:text-green-600 transition-colors">
											{form.name}
										</h4>
										<div className="flex items-center space-x-4 mt-1">
											<div className="flex items-center text-sm text-gray-500">
												<ClipboardCheck className="h-4 w-4 mr-1" />
												{form.information.length} inspection points
											</div>
											<div className="flex items-center text-sm text-gray-500">
												<FileText className="h-4 w-4 mr-1" />
												Form ID: {form.id}
											</div>
										</div>
									</div>
								</div>

								<div className="flex-shrink-0">
									<ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-green-600 transition-colors" />
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
	</div>
);
};

export default MyInspections;
