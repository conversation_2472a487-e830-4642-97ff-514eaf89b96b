import React, { useState, useEffect } from "react";
import {
	BarChart3,
	Download,
	FileText,
	CheckCircle,
	AlertTriangle,
	Clock,
	RefreshCw,
	Users,
	Building,
} from "lucide-react";
import { DocumentCategory } from "../../types/documents";
import { useSiteContext } from "../../hooks/useSiteContext";

interface SiteDocumentReportsProps {
	siteId: string;
}

interface SiteDocumentAnalytics {
	totalDocuments: number;
	documentsByCategory: Record<DocumentCategory, number>;
	complianceOverview: {
		compliant: number;
		expiring: number;
		expired: number;
		complianceRate: number;
	};
	recentActivity: Array<{
		action: string;
		document: string;
		user: string;
		timestamp: string;
	}>;
	criticalDocuments: Array<{
		name: string;
		category: DocumentCategory;
		daysUntilExpiry: number;
		urgency: "critical" | "warning";
	}>;
	storageUsage: {
		used: number;
		percentage: number;
	};
}

const SiteDocumentReports: React.FC<SiteDocumentReportsProps> = ({
	siteId,
}) => {
	const { siteName } = useSiteContext();
	const [analytics, setAnalytics] = useState<SiteDocumentAnalytics | null>(
		null,
	);
	const [loading, setLoading] = useState(true);
	const [activeTab, setActiveTab] = useState<
		"overview" | "compliance" | "activity"
	>("overview");

	useEffect(() => {
		loadSiteAnalytics();
	}, [siteId]);

	const loadSiteAnalytics = async () => {
		setLoading(true);
		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 800));

			// Mock site-specific analytics data
			const mockAnalytics: SiteDocumentAnalytics = {
				totalDocuments: 6,
				documentsByCategory: {
					[DocumentCategory.SAFETY]: 2,
					[DocumentCategory.TRAINING]: 0,
					[DocumentCategory.PROJECT]: 0,
					[DocumentCategory.WORKER]: 0,
					[DocumentCategory.COMPLIANCE]: 2,
					[DocumentCategory.EQUIPMENT]: 1,
					[DocumentCategory.ADMINISTRATIVE]: 1,
				},
				complianceOverview: {
					compliant: 4,
					expiring: 2,
					expired: 0,
					complianceRate: 67,
				},
				recentActivity: [
					{
						action: "uploaded",
						document: "Site Safety RAMS v2.1",
						user: "Safety Manager",
						timestamp: "2024-12-10T10:30:00Z",
					},
					{
						action: "renewed",
						document: "Building Permit",
						user: "Compliance Officer",
						timestamp: "2024-12-09T14:20:00Z",
					},
					{
						action: "viewed",
						document: "Emergency Response Plan",
						user: "Site Foreman",
						timestamp: "2024-12-09T09:15:00Z",
					},
					{
						action: "uploaded",
						document: "Environmental Impact Assessment",
						user: "Compliance Officer",
						timestamp: "2024-12-08T16:45:00Z",
					},
				],
				criticalDocuments: [
					{
						name: "Crane Inspection Certificate",
						category: DocumentCategory.EQUIPMENT,
						daysUntilExpiry: 5,
						urgency: "critical",
					},
					{
						name: "Site Insurance Policy",
						category: DocumentCategory.ADMINISTRATIVE,
						daysUntilExpiry: 41,
						urgency: "warning",
					},
				],
				storageUsage: {
					used: 12.5 * 1024 * 1024, // ~12.5MB
					percentage: 1.2,
				},
			};

			setAnalytics(mockAnalytics);
		} catch (error) {
			console.error("Failed to load site analytics:", error);
		} finally {
			setLoading(false);
		}
	};

	const formatFileSize = (bytes: number): string => {
		const sizes = ["Bytes", "KB", "MB", "GB"];
		if (bytes === 0) return "0 Bytes";
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
	};

	const getCategoryColor = (category: DocumentCategory) => {
		const colors = {
			[DocumentCategory.SAFETY]: "bg-red-100 text-red-800",
			[DocumentCategory.TRAINING]: "bg-blue-100 text-blue-800",
			[DocumentCategory.PROJECT]: "bg-green-100 text-green-800",
			[DocumentCategory.WORKER]: "bg-purple-100 text-purple-800",
			[DocumentCategory.COMPLIANCE]: "bg-yellow-100 text-yellow-800",
			[DocumentCategory.EQUIPMENT]: "bg-indigo-100 text-indigo-800",
			[DocumentCategory.ADMINISTRATIVE]: "bg-gray-100 text-gray-800",
		};
		return colors[category] || "bg-gray-100 text-gray-800";
	};

	const exportSiteReport = () => {
		const reportData = {
			site: siteName,
			generatedAt: new Date().toISOString(),
			analytics,
		};

		const blob = new Blob([JSON.stringify(reportData, null, 2)], {
			type: "application/json",
		});
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `site-${siteId}-document-report-${new Date().toISOString().split("T")[0]}.json`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	const formatTimestamp = (timestamp: string) => {
		return new Date(timestamp).toLocaleDateString("en-US", {
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	if (loading) {
		return (
			<div className="flex justify-center py-12">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			</div>
		);
	}

	if (!analytics) {
		return (
			<div className="text-center py-12">
				<AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
				<h3 className="text-lg font-medium text-gray-900 mb-2">
					Failed to Load Site Analytics
				</h3>
				<p className="text-gray-600">
					Unable to load document analytics for this site.
				</p>
				<button
					onClick={loadSiteAnalytics}
					className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
				>
					Retry
				</button>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold">Site Document Reports</h2>
					<p className="text-gray-600 text-sm mt-1">
						Document analytics for {siteName || "this site"}
					</p>
				</div>
				<div className="flex space-x-3">
					<button
						onClick={loadSiteAnalytics}
						className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
					>
						<RefreshCw className="h-4 w-4" />
						<span>Refresh</span>
					</button>
					<button
						onClick={exportSiteReport}
						className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
					>
						<Download className="h-4 w-4" />
						<span>Export Report</span>
					</button>
				</div>
			</div>

			{/* Tab Navigation */}
			<div className="border-b border-gray-200">
				<nav className="-mb-px flex space-x-8">
					{[
						{ id: "overview", label: "Overview", icon: BarChart3 },
						{ id: "compliance", label: "Compliance", icon: CheckCircle },
						{ id: "activity", label: "Activity", icon: Clock },
					].map((tab) => (
						<button
							key={tab.id}
							onClick={() => setActiveTab(tab.id as any)}
							className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
								activeTab === tab.id
									? "border-blue-500 text-blue-600"
									: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
							}`}
						>
							<tab.icon className="h-4 w-4" />
							<span>{tab.label}</span>
						</button>
					))}
				</nav>
			</div>

			{/* Tab Content */}
			{activeTab === "overview" && (
				<div className="space-y-6">
					{/* Key Metrics */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Total Documents</p>
									<p className="text-2xl font-semibold text-gray-900">
										{analytics.totalDocuments}
									</p>
								</div>
								<FileText className="h-8 w-8 text-blue-500" />
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Compliance Rate</p>
									<p className="text-2xl font-semibold text-green-600">
										{analytics.complianceOverview.complianceRate}%
									</p>
								</div>
								<CheckCircle className="h-8 w-8 text-green-500" />
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Expiring Soon</p>
									<p className="text-2xl font-semibold text-yellow-600">
										{analytics.complianceOverview.expiring}
									</p>
								</div>
								<Clock className="h-8 w-8 text-yellow-500" />
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Storage Used</p>
									<p className="text-2xl font-semibold text-purple-600">
										{formatFileSize(analytics.storageUsage.used)}
									</p>
								</div>
								<Building className="h-8 w-8 text-purple-500" />
							</div>
						</div>
					</div>

					{/* Documents by Category */}
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<h3 className="text-lg font-medium text-gray-900 mb-4">
								Documents by Category
							</h3>
							<div className="space-y-3">
								{Object.entries(analytics.documentsByCategory)
									.filter(([_, count]) => count > 0)
									.map(([category, count]) => (
										<div
											key={category}
											className="flex items-center justify-between"
										>
											<div className="flex items-center space-x-3">
												<span
													className={`inline-block px-2 py-1 text-xs rounded-full ${getCategoryColor(category as DocumentCategory)}`}
												>
													{category}
												</span>
											</div>
											<span className="text-sm font-medium text-gray-900">
												{count}
											</span>
										</div>
									))}
							</div>
							{Object.values(analytics.documentsByCategory).every(
								(count) => count === 0,
							) && (
								<p className="text-sm text-gray-500 text-center py-4">
									No documents in any category
								</p>
							)}
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<h3 className="text-lg font-medium text-gray-900 mb-4">
								Critical Documents
							</h3>
							<div className="space-y-3">
								{analytics.criticalDocuments.length > 0 ? (
									analytics.criticalDocuments.map((doc, index) => (
										<div
											key={index}
											className="flex items-center justify-between p-3 bg-red-50 rounded-lg"
										>
											<div className="flex items-center space-x-3">
												<AlertTriangle
													className={`h-4 w-4 ${doc.urgency === "critical" ? "text-red-500" : "text-yellow-500"}`}
												/>
												<div>
													<p className="text-sm font-medium text-gray-900">
														{doc.name}
													</p>
													<p className="text-xs text-gray-500">
														Expires in {doc.daysUntilExpiry} days
													</p>
												</div>
											</div>
											<span
												className={`inline-block px-2 py-1 text-xs rounded-full ${getCategoryColor(doc.category)}`}
											>
												{doc.category}
											</span>
										</div>
									))
								) : (
									<div className="text-center py-4">
										<CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
										<p className="text-sm text-gray-500">
											No critical documents
										</p>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			)}

			{activeTab === "compliance" && (
				<div className="space-y-6">
					{/* Compliance Overview */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Compliant</p>
									<p className="text-2xl font-semibold text-green-600">
										{analytics.complianceOverview.compliant}
									</p>
								</div>
								<CheckCircle className="h-8 w-8 text-green-500" />
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Expiring Soon</p>
									<p className="text-2xl font-semibold text-yellow-600">
										{analytics.complianceOverview.expiring}
									</p>
								</div>
								<Clock className="h-8 w-8 text-yellow-500" />
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Expired</p>
									<p className="text-2xl font-semibold text-red-600">
										{analytics.complianceOverview.expired}
									</p>
								</div>
								<AlertTriangle className="h-8 w-8 text-red-500" />
							</div>
						</div>
					</div>

					{/* Compliance Rate Visualization */}
					<div className="bg-white border border-gray-200 rounded-lg p-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							Compliance Rate
						</h3>
						<div className="space-y-4">
							<div className="flex justify-between items-center">
								<span className="text-sm text-gray-600">
									Overall Compliance
								</span>
								<span className="text-sm font-medium">
									{analytics.complianceOverview.complianceRate}%
								</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-3">
								<div
									className="bg-green-600 h-3 rounded-full"
									style={{
										width: `${analytics.complianceOverview.complianceRate}%`,
									}}
								></div>
							</div>
							<div className="grid grid-cols-3 gap-4 text-center text-sm">
								<div>
									<div className="text-green-600 font-semibold">
										{analytics.complianceOverview.compliant}
									</div>
									<div className="text-gray-500">Compliant</div>
								</div>
								<div>
									<div className="text-yellow-600 font-semibold">
										{analytics.complianceOverview.expiring}
									</div>
									<div className="text-gray-500">Expiring</div>
								</div>
								<div>
									<div className="text-red-600 font-semibold">
										{analytics.complianceOverview.expired}
									</div>
									<div className="text-gray-500">Expired</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			)}

			{activeTab === "activity" && (
				<div className="space-y-6">
					{/* Recent Activity */}
					<div className="bg-white border border-gray-200 rounded-lg p-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							Recent Document Activity
						</h3>
						<div className="space-y-4">
							{analytics.recentActivity.map((activity, index) => (
								<div
									key={index}
									className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg"
								>
									<div className="flex-shrink-0">
										{activity.action === "uploaded" && (
											<FileText className="h-5 w-5 text-blue-500" />
										)}
										{activity.action === "renewed" && (
											<RefreshCw className="h-5 w-5 text-green-500" />
										)}
										{activity.action === "viewed" && (
											<Users className="h-5 w-5 text-gray-500" />
										)}
									</div>
									<div className="flex-1 min-w-0">
										<p className="text-sm text-gray-900">
											<span className="font-medium">{activity.user}</span>{" "}
											{activity.action}
											<span className="font-medium"> {activity.document}</span>
										</p>
										<p className="text-xs text-gray-500">
											{formatTimestamp(activity.timestamp)}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>

					{/* Activity Summary */}
					<div className="bg-white border border-gray-200 rounded-lg p-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							Activity Summary
						</h3>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<div className="text-center">
								<div className="text-2xl font-semibold text-blue-600">
									{
										analytics.recentActivity.filter(
											(a) => a.action === "uploaded",
										).length
									}
								</div>
								<div className="text-sm text-gray-600">Documents Uploaded</div>
							</div>
							<div className="text-center">
								<div className="text-2xl font-semibold text-green-600">
									{
										analytics.recentActivity.filter(
											(a) => a.action === "renewed",
										).length
									}
								</div>
								<div className="text-sm text-gray-600">Documents Renewed</div>
							</div>
							<div className="text-center">
								<div className="text-2xl font-semibold text-gray-600">
									{
										analytics.recentActivity.filter(
											(a) => a.action === "viewed",
										).length
									}
								</div>
								<div className="text-sm text-gray-600">Documents Viewed</div>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default SiteDocumentReports;
