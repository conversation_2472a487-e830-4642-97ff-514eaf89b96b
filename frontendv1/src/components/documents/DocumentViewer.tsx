import React from "react";
import { X, Download, Edit, Trash2, Eye } from "lucide-react";
import { DocumentViewerProps } from "../../types/documents";

const DocumentViewer: React.FC<DocumentViewerProps> = ({
	document,
	onClose,
	onEdit,
	onDelete,
}) => {
	const handleDownload = () => {
		// Create a temporary link to download the file
		const link = document.createElement("a") as HTMLAnchorElement;
		link.href = document.fileUrl;
		link.download = document.fileName;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	const formatFileSize = (bytes: number): string => {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
	};

	const formatDate = (dateString: string): string => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "long",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
			<div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b">
					<div className="flex-1 min-w-0">
						<h2 className="text-lg font-semibold text-gray-900 truncate">
							{document.name}
						</h2>
						<p className="text-sm text-gray-600 mt-1">
							{document.category} • {formatFileSize(document.fileSize)} •{" "}
							{document.version}
						</p>
					</div>
					<div className="flex items-center space-x-2 ml-4">
						<button
							onClick={handleDownload}
							className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
							title="Download"
						>
							<Download className="h-5 w-5" />
						</button>
						{onEdit && (
							<button
								onClick={() => onEdit(document)}
								className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
								title="Edit"
							>
								<Edit className="h-5 w-5" />
							</button>
						)}
						{onDelete && (
							<button
								onClick={() => onDelete(document.id)}
								className="p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-gray-100"
								title="Delete"
							>
								<Trash2 className="h-5 w-5" />
							</button>
						)}
						<button
							onClick={onClose}
							className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
							title="Close"
						>
							<X className="h-5 w-5" />
						</button>
					</div>
				</div>

				{/* Content */}
				<div className="flex-1 overflow-auto">
					<div className="p-6">
						{/* Document Preview */}
						<div className="bg-gray-100 rounded-lg p-8 text-center mb-6">
							{document.mimeType.startsWith("image/") ? (
								<img
									src={document.fileUrl}
									alt={document.name}
									className="max-w-full max-h-96 mx-auto rounded"
								/>
							) : document.mimeType === "application/pdf" ? (
								<div className="space-y-4">
									<Eye className="h-16 w-16 text-gray-400 mx-auto" />
									<p className="text-gray-600">PDF Preview</p>
									<p className="text-sm text-gray-500">
										Click download to view the full document
									</p>
								</div>
							) : (
								<div className="space-y-4">
									<Eye className="h-16 w-16 text-gray-400 mx-auto" />
									<p className="text-gray-600">
										Document Preview Not Available
									</p>
									<p className="text-sm text-gray-500">
										Click download to view the document
									</p>
								</div>
							)}
						</div>

						{/* Document Details */}
						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<h3 className="text-lg font-medium text-gray-900 mb-4">
									Document Information
								</h3>
								<dl className="space-y-3">
									<div>
										<dt className="text-sm font-medium text-gray-500">Name</dt>
										<dd className="text-sm text-gray-900">{document.name}</dd>
									</div>
									{document.description && (
										<div>
											<dt className="text-sm font-medium text-gray-500">
												Description
											</dt>
											<dd className="text-sm text-gray-900">
												{document.description}
											</dd>
										</div>
									)}
									<div>
										<dt className="text-sm font-medium text-gray-500">
											Category
										</dt>
										<dd className="text-sm text-gray-900 capitalize">
											{document.category}
										</dd>
									</div>
									<div>
										<dt className="text-sm font-medium text-gray-500">Type</dt>
										<dd className="text-sm text-gray-900">{document.type}</dd>
									</div>
									<div>
										<dt className="text-sm font-medium text-gray-500">
											Version
										</dt>
										<dd className="text-sm text-gray-900">
											{document.version}
										</dd>
									</div>
									<div>
										<dt className="text-sm font-medium text-gray-500">
											Status
										</dt>
										<dd className="text-sm text-gray-900 capitalize">
											{document.status}
										</dd>
									</div>
								</dl>
							</div>

							<div>
								<h3 className="text-lg font-medium text-gray-900 mb-4">
									File Details
								</h3>
								<dl className="space-y-3">
									<div>
										<dt className="text-sm font-medium text-gray-500">
											File Name
										</dt>
										<dd className="text-sm text-gray-900">
											{document.fileName}
										</dd>
									</div>
									<div>
										<dt className="text-sm font-medium text-gray-500">
											File Size
										</dt>
										<dd className="text-sm text-gray-900">
											{formatFileSize(document.fileSize)}
										</dd>
									</div>
									<div>
										<dt className="text-sm font-medium text-gray-500">
											File Type
										</dt>
										<dd className="text-sm text-gray-900">
											{document.mimeType}
										</dd>
									</div>
									{document.expiryDate && (
										<div>
											<dt className="text-sm font-medium text-gray-500">
												Expiry Date
											</dt>
											<dd className="text-sm text-gray-900">
												{new Date(document.expiryDate).toLocaleDateString()}
											</dd>
										</div>
									)}
									<div>
										<dt className="text-sm font-medium text-gray-500">
											Created
										</dt>
										<dd className="text-sm text-gray-900">
											{formatDate(document.createdAt)} by{" "}
											{document.createdByName}
										</dd>
									</div>
									<div>
										<dt className="text-sm font-medium text-gray-500">
											Last Modified
										</dt>
										<dd className="text-sm text-gray-900">
											{formatDate(document.updatedAt)} by{" "}
											{document.updatedByName}
										</dd>
									</div>
								</dl>
							</div>
						</div>

						{/* Tags */}
						{document.tags && document.tags.length > 0 && (
							<div className="mt-6">
								<h3 className="text-lg font-medium text-gray-900 mb-2">Tags</h3>
								<div className="flex flex-wrap gap-2">
									{document.tags.map((tag, index) => (
										<span
											key={index}
											className="inline-block px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full"
										>
											{tag}
										</span>
									))}
								</div>
							</div>
						)}
					</div>
				</div>

				{/* Footer */}
				<div className="flex justify-end space-x-3 p-6 border-t bg-gray-50">
					<button
						onClick={onClose}
						className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
					>
						Close
					</button>
					<button
						onClick={handleDownload}
						className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center space-x-2"
					>
						<Download className="h-4 w-4" />
						<span>Download</span>
					</button>
				</div>
			</div>
		</div>
	);
};

export default DocumentViewer;
