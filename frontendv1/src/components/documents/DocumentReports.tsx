import React, { useState, useEffect } from "react";
import {
	Bar<PERSON>hart3,
	TrendingUp,
	Download,
	FileText,
	<PERSON><PERSON><PERSON>riangle,
	<PERSON><PERSON><PERSON><PERSON>,
	Clock,
	Filter,
	RefreshCw,
	PieChart,
	Activity,
} from "lucide-react";
import {
	DocumentCategory,
	DocumentStatus,
} from "../../types/documents";

interface DocumentAnalytics {
	totalDocuments: number;
	documentsByCategory: Record<DocumentCategory, number>;
	documentsByStatus: Record<DocumentStatus, number>;
	complianceOverview: {
		compliant: number;
		expiring: number;
		expired: number;
	};
	uploadTrends: {
		thisMonth: number;
		lastMonth: number;
		growth: number;
	};
	topUploaders: Array<{
		name: string;
		count: number;
		role: string;
	}>;
	expiryTrends: Array<{
		month: string;
		expiring: number;
		expired: number;
	}>;
	storageUsage: {
		used: number;
		total: number;
		percentage: number;
	};
}

interface ReportFilter {
	dateRange: "last7days" | "last30days" | "last90days" | "lastyear" | "custom";
	category?: DocumentCategory;
	status?: DocumentStatus;
	customStartDate?: string;
	customEndDate?: string;
}

const DocumentReports: React.FC = () => {
	const [analytics, setAnalytics] = useState<DocumentAnalytics | null>(null);
	const [loading, setLoading] = useState(true);
	const [filter, setFilter] = useState<ReportFilter>({
		dateRange: "last30days",
	});
	const [activeTab, setActiveTab] = useState<
		"overview" | "compliance" | "usage" | "trends"
	>("overview");

	useEffect(() => {
		loadAnalytics();
	}, [filter]);

	const loadAnalytics = async () => {
		setLoading(true);
		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 1000));

			// Mock analytics data
			const mockAnalytics: DocumentAnalytics = {
				totalDocuments: 20,
				documentsByCategory: {
					[DocumentCategory.SAFETY]: 5,
					[DocumentCategory.TRAINING]: 4,
					[DocumentCategory.PROJECT]: 2,
					[DocumentCategory.WORKER]: 2,
					[DocumentCategory.COMPLIANCE]: 3,
					[DocumentCategory.EQUIPMENT]: 2,
					[DocumentCategory.ADMINISTRATIVE]: 2,
				},
				documentsByStatus: {
					active: 19,
					archived: 1,
					expired: 0,
					draft: 0,
				},
				complianceOverview: {
					compliant: 15,
					expiring: 4,
					expired: 1,
				},
				uploadTrends: {
					thisMonth: 8,
					lastMonth: 5,
					growth: 60,
				},
				topUploaders: [
					{ name: "Safety Manager", count: 6, role: "Safety" },
					{ name: "HR Manager", count: 4, role: "Human Resources" },
					{ name: "Project Manager", count: 3, role: "Project Management" },
					{ name: "Equipment Manager", count: 2, role: "Equipment" },
					{ name: "Compliance Officer", count: 2, role: "Compliance" },
				],
				expiryTrends: [
					{ month: "Jan", expiring: 2, expired: 0 },
					{ month: "Feb", expiring: 3, expired: 1 },
					{ month: "Mar", expiring: 4, expired: 0 },
					{ month: "Apr", expiring: 2, expired: 1 },
					{ month: "May", expiring: 5, expired: 2 },
					{ month: "Jun", expiring: 3, expired: 0 },
				],
				storageUsage: {
					used: 89.5 * 1024 * 1024, // ~89.5MB
					total: 1024 * 1024 * 1024, // 1GB
					percentage: 8.7,
				},
			};

			setAnalytics(mockAnalytics);
		} catch (error) {
			console.error("Failed to load analytics:", error);
		} finally {
			setLoading(false);
		}
	};

	const formatFileSize = (bytes: number): string => {
		const sizes = ["Bytes", "KB", "MB", "GB"];
		if (bytes === 0) return "0 Bytes";
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
	};

	const getCategoryColor = (category: DocumentCategory) => {
		const colors = {
			[DocumentCategory.SAFETY]: "bg-red-500",
			[DocumentCategory.TRAINING]: "bg-blue-500",
			[DocumentCategory.PROJECT]: "bg-green-500",
			[DocumentCategory.WORKER]: "bg-purple-500",
			[DocumentCategory.COMPLIANCE]: "bg-yellow-500",
			[DocumentCategory.EQUIPMENT]: "bg-indigo-500",
			[DocumentCategory.ADMINISTRATIVE]: "bg-gray-500",
		};
		return colors[category] || "bg-gray-500";
	};

	const exportReport = () => {
		// Mock export functionality
		const reportData = {
			generatedAt: new Date().toISOString(),
			filter,
			analytics,
		};

		const blob = new Blob([JSON.stringify(reportData, null, 2)], {
			type: "application/json",
		});
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `document-report-${new Date().toISOString().split("T")[0]}.json`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	if (loading) {
		return (
			<div className="flex justify-center py-12">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			</div>
		);
	}

	if (!analytics) {
		return (
			<div className="text-center py-12">
				<AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
				<h3 className="text-lg font-medium text-gray-900 mb-2">
					Failed to Load Analytics
				</h3>
				<p className="text-gray-600">Unable to load document analytics data.</p>
				<button
					onClick={loadAnalytics}
					className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
				>
					Retry
				</button>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold">
						Document Reports & Analytics
					</h2>
					<p className="text-gray-600 text-sm mt-1">
						Comprehensive insights into document management and compliance
					</p>
				</div>
				<div className="flex space-x-3">
					<button
						onClick={loadAnalytics}
						className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
					>
						<RefreshCw className="h-4 w-4" />
						<span>Refresh</span>
					</button>
					<button
						onClick={exportReport}
						className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
					>
						<Download className="h-4 w-4" />
						<span>Export Report</span>
					</button>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white border border-gray-200 rounded-lg p-4">
				<div className="flex items-center space-x-4">
					<div className="flex items-center space-x-2">
						<Filter className="h-4 w-4 text-gray-500" />
						<span className="text-sm font-medium text-gray-700">Filters:</span>
					</div>
					<select
						value={filter.dateRange}
						onChange={(e) =>
							setFilter((prev) => ({
								...prev,
								dateRange: e.target.value as any,
							}))
						}
						className="px-3 py-1 border border-gray-300 rounded-md text-sm"
					>
						<option value="last7days">Last 7 days</option>
						<option value="last30days">Last 30 days</option>
						<option value="last90days">Last 90 days</option>
						<option value="lastyear">Last year</option>
					</select>
					<select
						value={filter.category || ""}
						onChange={(e) =>
							setFilter((prev) => ({
								...prev,
								category: (e.target.value as DocumentCategory) || undefined,
							}))
						}
						className="px-3 py-1 border border-gray-300 rounded-md text-sm"
					>
						<option value="">All Categories</option>
						{Object.values(DocumentCategory).map((category) => (
							<option key={category} value={category}>
								{category.charAt(0).toUpperCase() + category.slice(1)}
							</option>
						))}
					</select>
				</div>
			</div>

			{/* Tab Navigation */}
			<div className="border-b border-gray-200">
				<nav className="-mb-px flex space-x-8">
					{[
						{ id: "overview", label: "Overview", icon: BarChart3 },
						{ id: "compliance", label: "Compliance", icon: CheckCircle },
						{ id: "usage", label: "Usage", icon: Activity },
						{ id: "trends", label: "Trends", icon: TrendingUp },
					].map((tab) => (
						<button
							key={tab.id}
							onClick={() => setActiveTab(tab.id as any)}
							className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
								activeTab === tab.id
									? "border-blue-500 text-blue-600"
									: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
							}`}
						>
							<tab.icon className="h-4 w-4" />
							<span>{tab.label}</span>
						</button>
					))}
				</nav>
			</div>

			{/* Tab Content */}
			{activeTab === "overview" && (
				<div className="space-y-6">
					{/* Key Metrics */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Total Documents</p>
									<p className="text-2xl font-semibold text-gray-900">
										{analytics.totalDocuments}
									</p>
								</div>
								<FileText className="h-8 w-8 text-blue-500" />
							</div>
							<div className="mt-2">
								<span
									className={`text-sm ${analytics.uploadTrends.growth >= 0 ? "text-green-600" : "text-red-600"}`}
								>
									{analytics.uploadTrends.growth >= 0 ? "+" : ""}
									{analytics.uploadTrends.growth}% from last month
								</span>
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Uploads This Month</p>
									<p className="text-2xl font-semibold text-green-600">
										{analytics.uploadTrends.thisMonth}
									</p>
								</div>
								<TrendingUp className="h-8 w-8 text-green-500" />
							</div>
							<div className="mt-2">
								<span className="text-sm text-gray-600">
									vs {analytics.uploadTrends.lastMonth} last month
								</span>
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Storage Used</p>
									<p className="text-2xl font-semibold text-purple-600">
										{formatFileSize(analytics.storageUsage.used)}
									</p>
								</div>
								<PieChart className="h-8 w-8 text-purple-500" />
							</div>
							<div className="mt-2">
								<span className="text-sm text-gray-600">
									{analytics.storageUsage.percentage}% of total
								</span>
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Compliance Rate</p>
									<p className="text-2xl font-semibold text-green-600">
										{Math.round(
											(analytics.complianceOverview.compliant /
												analytics.totalDocuments) *
												100,
										)}
										%
									</p>
								</div>
								<CheckCircle className="h-8 w-8 text-green-500" />
							</div>
							<div className="mt-2">
								<span className="text-sm text-gray-600">
									{analytics.complianceOverview.compliant} compliant documents
								</span>
							</div>
						</div>
					</div>

					{/* Documents by Category */}
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<h3 className="text-lg font-medium text-gray-900 mb-4">
								Documents by Category
							</h3>
							<div className="space-y-3">
								{Object.entries(analytics.documentsByCategory).map(
									([category, count]) => (
										<div
											key={category}
											className="flex items-center justify-between"
										>
											<div className="flex items-center space-x-3">
												<div
													className={`w-3 h-3 rounded-full ${getCategoryColor(category as DocumentCategory)}`}
												></div>
												<span className="text-sm text-gray-700 capitalize">
													{category}
												</span>
											</div>
											<div className="flex items-center space-x-2">
												<span className="text-sm font-medium text-gray-900">
													{count}
												</span>
												<span className="text-xs text-gray-500">
													(
													{Math.round((count / analytics.totalDocuments) * 100)}
													%)
												</span>
											</div>
										</div>
									),
								)}
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<h3 className="text-lg font-medium text-gray-900 mb-4">
								Top Contributors
							</h3>
							<div className="space-y-3">
								{analytics.topUploaders.map((uploader, index) => (
									<div
										key={uploader.name}
										className="flex items-center justify-between"
									>
										<div className="flex items-center space-x-3">
											<div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
												<span className="text-sm font-medium text-blue-600">
													{index + 1}
												</span>
											</div>
											<div>
												<p className="text-sm font-medium text-gray-900">
													{uploader.name}
												</p>
												<p className="text-xs text-gray-500">{uploader.role}</p>
											</div>
										</div>
										<span className="text-sm font-medium text-gray-900">
											{uploader.count} docs
										</span>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>
			)}

			{activeTab === "compliance" && (
				<div className="space-y-6">
					{/* Compliance Overview */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Compliant</p>
									<p className="text-2xl font-semibold text-green-600">
										{analytics.complianceOverview.compliant}
									</p>
								</div>
								<CheckCircle className="h-8 w-8 text-green-500" />
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Expiring Soon</p>
									<p className="text-2xl font-semibold text-yellow-600">
										{analytics.complianceOverview.expiring}
									</p>
								</div>
								<Clock className="h-8 w-8 text-yellow-500" />
							</div>
						</div>

						<div className="bg-white border border-gray-200 rounded-lg p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm text-gray-600">Expired</p>
									<p className="text-2xl font-semibold text-red-600">
										{analytics.complianceOverview.expired}
									</p>
								</div>
								<AlertTriangle className="h-8 w-8 text-red-500" />
							</div>
						</div>
					</div>

					{/* Compliance Trends Chart */}
					<div className="bg-white border border-gray-200 rounded-lg p-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							Expiry Trends
						</h3>
						<div className="h-64 flex items-end justify-between space-x-2">
							{analytics.expiryTrends.map((trend) => (
								<div
									key={trend.month}
									className="flex flex-col items-center space-y-2 flex-1"
								>
									<div className="flex flex-col items-center space-y-1 w-full">
										<div
											className="bg-yellow-500 w-full rounded-t"
											style={{ height: `${(trend.expiring / 5) * 100}px` }}
											title={`${trend.expiring} expiring`}
										></div>
										<div
											className="bg-red-500 w-full rounded-b"
											style={{ height: `${(trend.expired / 5) * 100}px` }}
											title={`${trend.expired} expired`}
										></div>
									</div>
									<span className="text-xs text-gray-600">{trend.month}</span>
								</div>
							))}
						</div>
						<div className="flex items-center justify-center space-x-6 mt-4">
							<div className="flex items-center space-x-2">
								<div className="w-3 h-3 bg-yellow-500 rounded"></div>
								<span className="text-sm text-gray-600">Expiring</span>
							</div>
							<div className="flex items-center space-x-2">
								<div className="w-3 h-3 bg-red-500 rounded"></div>
								<span className="text-sm text-gray-600">Expired</span>
							</div>
						</div>
					</div>
				</div>
			)}

			{activeTab === "usage" && (
				<div className="space-y-6">
					{/* Storage Usage */}
					<div className="bg-white border border-gray-200 rounded-lg p-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							Storage Usage
						</h3>
						<div className="space-y-4">
							<div className="flex justify-between items-center">
								<span className="text-sm text-gray-600">Used Storage</span>
								<span className="text-sm font-medium">
									{formatFileSize(analytics.storageUsage.used)} /{" "}
									{formatFileSize(analytics.storageUsage.total)}
								</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-2">
								<div
									className="bg-blue-600 h-2 rounded-full"
									style={{ width: `${analytics.storageUsage.percentage}%` }}
								></div>
							</div>
							<p className="text-xs text-gray-500">
								{analytics.storageUsage.percentage}% of total storage used
							</p>
						</div>
					</div>

					{/* Document Status Distribution */}
					<div className="bg-white border border-gray-200 rounded-lg p-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							Document Status Distribution
						</h3>
						<div className="space-y-3">
							{Object.entries(analytics.documentsByStatus).map(
								([status, count]) => {
									const percentage = (count / analytics.totalDocuments) * 100;
									const getStatusColor = (status: string) => {
										switch (status) {
											case "active":
												return "bg-green-500";
											case "archived":
												return "bg-gray-500";
											case "expired":
												return "bg-red-500";
											case "draft":
												return "bg-yellow-500";
											default:
												return "bg-blue-500";
										}
									};

									return (
										<div key={status} className="space-y-1">
											<div className="flex justify-between items-center">
												<span className="text-sm text-gray-700 capitalize">
													{status}
												</span>
												<span className="text-sm font-medium">
													{count} ({percentage.toFixed(1)}%)
												</span>
											</div>
											<div className="w-full bg-gray-200 rounded-full h-2">
												<div
													className={`h-2 rounded-full ${getStatusColor(status)}`}
													style={{ width: `${percentage}%` }}
												></div>
											</div>
										</div>
									);
								},
							)}
						</div>
					</div>
				</div>
			)}

			{activeTab === "trends" && (
				<div className="space-y-6">
					{/* Upload Trends */}
					<div className="bg-white border border-gray-200 rounded-lg p-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							Upload Activity
						</h3>
						<div className="grid grid-cols-2 gap-4">
							<div>
								<p className="text-sm text-gray-600">This Month</p>
								<p className="text-3xl font-semibold text-blue-600">
									{analytics.uploadTrends.thisMonth}
								</p>
							</div>
							<div>
								<p className="text-sm text-gray-600">Last Month</p>
								<p className="text-3xl font-semibold text-gray-600">
									{analytics.uploadTrends.lastMonth}
								</p>
							</div>
						</div>
						<div className="mt-4">
							<div
								className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
									analytics.uploadTrends.growth >= 0
										? "bg-green-100 text-green-800"
										: "bg-red-100 text-red-800"
								}`}
							>
								<TrendingUp className="h-4 w-4 mr-1" />
								{analytics.uploadTrends.growth >= 0 ? "+" : ""}
								{analytics.uploadTrends.growth}% growth
							</div>
						</div>
					</div>

					{/* Future Projections */}
					<div className="bg-white border border-gray-200 rounded-lg p-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							Projections
						</h3>
						<div className="space-y-4">
							<div className="p-4 bg-blue-50 rounded-lg">
								<h4 className="font-medium text-blue-900">
									Storage Projection
								</h4>
								<p className="text-sm text-blue-700 mt-1">
									At current upload rate, you'll reach 50% storage capacity in
									approximately 6 months.
								</p>
							</div>
							<div className="p-4 bg-yellow-50 rounded-lg">
								<h4 className="font-medium text-yellow-900">
									Compliance Alert
								</h4>
								<p className="text-sm text-yellow-700 mt-1">
									{analytics.complianceOverview.expiring} documents will expire
									in the next 30 days. Plan renewal activities.
								</p>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default DocumentReports;
