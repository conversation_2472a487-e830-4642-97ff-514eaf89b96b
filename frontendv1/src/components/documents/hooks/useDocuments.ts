import { useState, useCallback } from "react";
import {
	ComplianceStatus,
	Document,
	DocumentCategory,
	DocumentFilter,
	DocumentStatus,
	EntityType,
} from "../../../types/documents";

// Mock data for development
const mockDocuments: Document[] = [
	// Safety Documents
	{
		id: "doc-1",
		name: "Site Safety RAMS v2.1",
		description: "Risk Assessment Method Statement for excavation work",

		type: "rams",
		version: "2.1",

		fileName: "site-safety-rams-v2.1.pdf",
		fileSize: 2048576, // 2MB
		mimeType: "application/pdf",
		fileUrl: "/documents/site-safety-rams-v2.1.pdf",
		tags: ["excavation", "safety", "rams"],
		customFields: {},
		expiryDate: "2025-12-31",
		renewalRequired: true,


		entityId: "site1",
		createdAt: "2024-01-15T10:30:00Z",
		createdBy: "user-1",
		createdByName: "Safety Manager",
		updatedAt: "2024-01-15T10:30:00Z",
		updatedBy: "user-1",
		updatedByName: "Safety Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-2",
		name: "Emergency Response Plan",
		description: "Site emergency response procedures and contact information",

		type: "emergency-plan",
		version: "1.3",

		fileName: "emergency-response-plan-v1.3.pdf",
		fileSize: 1536000, // 1.5MB
		mimeType: "application/pdf",
		fileUrl: "/documents/emergency-response-plan-v1.3.pdf",
		tags: ["emergency", "response", "safety", "procedures"],
		customFields: {},
		expiryDate: "2025-06-30",
		renewalRequired: true,


		entityId: "site1",
		createdAt: "2024-02-01T09:00:00Z",
		createdBy: "user-1",
		createdByName: "Safety Manager",
		updatedAt: "2024-02-01T09:00:00Z",
		updatedBy: "user-1",
		updatedByName: "Safety Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (__arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-3",
		name: "Incident Report - Minor Injury",
		description: "Report for minor cut injury on 2024-11-15",

		type: "incident-report",
		version: "1.0",

		fileName: "incident-report-20241115.pdf",
		fileSize: 512000, // 512KB
		mimeType: "application/pdf",
		fileUrl: "/documents/incident-report-20241115.pdf",
		tags: ["incident", "injury", "report"],
		customFields: { incidentType: "Minor Injury", severity: "Low" },
		renewalRequired: false,


		entityId: "site1",
		createdAt: "2024-11-15T14:30:00Z",
		createdBy: "user-1",
		createdByName: "Safety Manager",
		updatedAt: "2024-11-15T14:30:00Z",
		updatedBy: "user-1",
		updatedByName: "Safety Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (__arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},

	// Training Documents
	{
		id: "doc-4",
		name: "Worker Safety Certificate - John Doe",
		description: "OSHA 30-hour construction safety certificate",

		type: "safety-certificate",
		version: "1.0",

		fileName: "john-doe-safety-cert.pdf",
		fileSize: 1024000, // 1MB
		mimeType: "application/pdf",
		fileUrl: "/documents/john-doe-safety-cert.pdf",
		tags: ["osha", "safety", "certificate"],
		customFields: { workerName: "John Doe", certificateNumber: "OSHA-123456" },
		expiryDate: "2025-06-15",
		renewalRequired: true,


		entityId: "worker-1",
		createdAt: "2024-01-10T14:20:00Z",
		createdBy: "user-2",
		createdByName: "HR Manager",
		updatedAt: "2024-01-10T14:20:00Z",
		updatedBy: "user-2",
		updatedByName: "HR Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (__arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-5",
		name: "Scaffolding Training Manual",
		description: "Comprehensive training manual for scaffolding safety and procedures",

		type: "training-material",
		version: "2.0",

		fileName: "scaffolding-training-manual-v2.pdf",
		fileSize: 8388608, // 8MB
		mimeType: "application/pdf",
		fileUrl: "/documents/scaffolding-training-manual-v2.pdf",
		tags: ["scaffolding", "training", "manual", "safety"],
		customFields: {},
		renewalRequired: false,


		entityId: "company-1",
		createdAt: "2024-03-01T11:00:00Z",
		createdBy: "user-3",
		createdByName: "Training Coordinator",
		updatedAt: "2024-03-01T11:00:00Z",
		updatedBy: "user-3",
		updatedByName: "Training Coordinator",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (__arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-6",
		name: "First Aid Certification - Sarah Wilson",
		description: "Red Cross First Aid and CPR certification",

		type: "first-aid-certificate",
		version: "1.0",

		fileName: "sarah-wilson-first-aid-cert.pdf",
		fileSize: 768000, // 768KB
		mimeType: "application/pdf",
		fileUrl: "/documents/sarah-wilson-first-aid-cert.pdf",
		tags: ["first-aid", "cpr", "certificate", "red-cross"],
		customFields: {
			workerName: "Sarah Wilson",
			certificateNumber: "RC-789012",
		},
		expiryDate: "2025-03-20",
		renewalRequired: true,


		entityId: "worker-2",
		createdAt: "2024-03-20T16:45:00Z",
		createdBy: "user-2",
		createdByName: "HR Manager",
		updatedAt: "2024-03-20T16:45:00Z",
		updatedBy: "user-2",
		updatedByName: "HR Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},

	// Project Documents
	{
		id: "doc-7",
		name: "Project Specifications",
		description: "Detailed project specifications and requirements",

		type: "specification",
		version: "1.0",

		fileName: "project-specifications.pdf",
		fileSize: 5242880, // 5MB
		mimeType: "application/pdf",
		fileUrl: "/documents/project-specifications.pdf",
		tags: ["specifications", "requirements", "project"],
		customFields: {},
		renewalRequired: false,


		entityId: "site1",
		createdAt: "2024-01-05T09:15:00Z",
		createdBy: "user-4",
		createdByName: "Project Manager",
		updatedAt: "2024-01-05T09:15:00Z",
		updatedBy: "user-4",
		updatedByName: "Project Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-8",
		name: "Architectural Drawings - Foundation",
		description: "Foundation and structural drawings for building construction",

		type: "drawing",
		version: "3.1",

		fileName: "foundation-drawings-v3.1.dwg",
		fileSize: 12582912, // 12MB
		mimeType: "application/acad",
		fileUrl: "/documents/foundation-drawings-v3.1.dwg",
		tags: ["drawings", "foundation", "structural", "architecture"],
		customFields: { drawingNumber: "FND-001", scale: "1:100" },
		renewalRequired: false,


		entityId: "site1",
		createdAt: "2024-02-10T13:20:00Z",
		createdBy: "user-5",
		createdByName: "Architect",
		updatedAt: "2024-02-10T13:20:00Z",
		updatedBy: "user-5",
		updatedByName: "Architect",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},

	// Compliance Documents
	{
		id: "doc-9",
		name: "Building Permit",
		description: "Municipal building permit for construction project",

		type: "permit",
		version: "1.0",

		fileName: "building-permit-2024.pdf",
		fileSize: 1024000, // 1MB
		mimeType: "application/pdf",
		fileUrl: "/documents/building-permit-2024.pdf",
		tags: ["permit", "building", "municipal", "compliance"],
		customFields: {
			permitNumber: "BP-2024-001",
			authority: "Nairobi City Council",
		},
		expiryDate: "2025-12-31",
		renewalRequired: true,


		entityId: "site1",
		createdAt: "2024-01-03T08:00:00Z",
		createdBy: "user-6",
		createdByName: "Compliance Officer",
		updatedAt: "2024-01-03T08:00:00Z",
		updatedBy: "user-6",
		updatedByName: "Compliance Officer",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-10",
		name: "Environmental Impact Assessment",
		description: "Environmental impact assessment report",

		type: "environmental-report",
		version: "1.0",

		fileName: "environmental-impact-assessment.pdf",
		fileSize: 3145728, // 3MB
		mimeType: "application/pdf",
		fileUrl: "/documents/environmental-impact-assessment.pdf",
		tags: ["environmental", "impact", "assessment", "compliance"],
		customFields: {
			assessmentDate: "2024-01-15",
			consultant: "Green Earth Consultants",
		},
		expiryDate: "2026-01-15",
		renewalRequired: false,


		entityId: "site1",
		createdAt: "2024-01-15T10:00:00Z",
		createdBy: "user-6",
		createdByName: "Compliance Officer",
		updatedAt: "2024-01-15T10:00:00Z",
		updatedBy: "user-6",
		updatedByName: "Compliance Officer",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},

	// Worker Documents
	{
		id: "doc-11",
		name: "Employment Contract - Michael Brown",
		description: "Employment contract for site foreman position",

		type: "contract",
		version: "1.0",

		fileName: "michael-brown-contract.pdf",
		fileSize: 512000, // 512KB
		mimeType: "application/pdf",
		fileUrl: "/documents/michael-brown-contract.pdf",
		tags: ["contract", "employment", "foreman"],
		customFields: { position: "Site Foreman", startDate: "2024-01-01" },
		renewalRequired: false,


		entityId: "worker-3",
		createdAt: "2024-01-01T09:00:00Z",
		createdBy: "user-2",
		createdByName: "HR Manager",
		updatedAt: "2024-01-01T09:00:00Z",
		updatedBy: "user-2",
		updatedByName: "HR Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-12",
		name: "Medical Fitness Certificate - David Lee",
		description: "Medical fitness certificate for construction work",

		type: "medical-certificate",
		version: "1.0",

		fileName: "david-lee-medical-cert.pdf",
		fileSize: 256000, // 256KB
		mimeType: "application/pdf",
		fileUrl: "/documents/david-lee-medical-cert.pdf",
		tags: ["medical", "fitness", "certificate", "health"],
		customFields: {
			doctorName: "Dr. Jane Smith",
			clinicName: "City Medical Center",
		},
		expiryDate: "2025-01-15",
		renewalRequired: true,


		entityId: "worker-4",
		createdAt: "2024-01-15T11:30:00Z",
		createdBy: "user-2",
		createdByName: "HR Manager",
		updatedAt: "2024-01-15T11:30:00Z",
		updatedBy: "user-2",
		updatedByName: "HR Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},

	// Equipment Documents
	{
		id: "doc-13",
		name: "Crane Inspection Certificate",
		description: "Annual inspection certificate for tower crane TC-001",

		type: "inspection-certificate",
		version: "1.0",

		fileName: "crane-tc001-inspection-cert.pdf",
		fileSize: 1024000, // 1MB
		mimeType: "application/pdf",
		fileUrl: "/documents/crane-tc001-inspection-cert.pdf",
		tags: ["crane", "inspection", "certificate", "equipment"],
		customFields: {
			equipmentId: "TC-001",
			inspector: "Certified Crane Inspector Ltd",
		},
		expiryDate: "2024-12-15",
		renewalRequired: true,


		entityId: "site1",
		createdAt: "2023-12-15T14:00:00Z",
		createdBy: "user-7",
		createdByName: "Equipment Manager",
		updatedAt: "2023-12-15T14:00:00Z",
		updatedBy: "user-7",
		updatedByName: "Equipment Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-14",
		name: "Excavator Manual - CAT 320",
		description: "Operation and maintenance manual for Caterpillar 320 excavator",

		type: "manual",
		version: "2.1",

		fileName: "cat320-excavator-manual.pdf",
		fileSize: 15728640, // 15MB
		mimeType: "application/pdf",
		fileUrl: "/documents/cat320-excavator-manual.pdf",
		tags: ["excavator", "manual", "caterpillar", "maintenance"],
		customFields: { model: "CAT 320", serialNumber: "CAT123456789" },
		renewalRequired: false,


		entityId: "site1",
		createdAt: "2024-02-20T10:15:00Z",
		createdBy: "user-7",
		createdByName: "Equipment Manager",
		updatedAt: "2024-02-20T10:15:00Z",
		updatedBy: "user-7",
		updatedByName: "Equipment Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},

	// Administrative Documents
	{
		id: "doc-15",
		name: "Site Insurance Policy",
		description: "Comprehensive site insurance policy documentation",

		type: "insurance-policy",
		version: "1.0",

		fileName: "site-insurance-policy-2024.pdf",
		fileSize: 2097152, // 2MB
		mimeType: "application/pdf",
		fileUrl: "/documents/site-insurance-policy-2024.pdf",
		tags: ["insurance", "policy", "coverage", "administrative"],
		customFields: {
			policyNumber: "INS-2024-001",
			insurer: "Construction Insurance Ltd",
		},
		expiryDate: "2025-01-31",
		renewalRequired: true,


		entityId: "site1",
		createdAt: "2024-01-31T12:00:00Z",
		createdBy: "user-8",
		createdByName: "Administrative Manager",
		updatedAt: "2024-01-31T12:00:00Z",
		updatedBy: "user-8",
		updatedByName: "Administrative Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},

	// Additional documents for other sites
	{
		id: "doc-16",
		name: "Site Safety RAMS - Mombasa Road",
		description: "Risk Assessment Method Statement for Mombasa Road project",

		type: "rams",
		version: "1.0",

		fileName: "mombasa-road-rams-v1.pdf",
		fileSize: 1843200, // 1.8MB
		mimeType: "application/pdf",
		fileUrl: "/documents/mombasa-road-rams-v1.pdf",
		tags: ["rams", "safety", "mombasa", "road"],
		customFields: {},
		expiryDate: "2025-08-31",
		renewalRequired: true,


		entityId: "site2",
		createdAt: "2024-03-01T09:30:00Z",
		createdBy: "user-1",
		createdByName: "Safety Manager",
		updatedAt: "2024-03-01T09:30:00Z",
		updatedBy: "user-1",
		updatedByName: "Safety Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-17",
		name: "Expired Safety Certificate - Tom Johnson",
		description: "Expired OSHA safety certificate requiring renewal",

		type: "safety-certificate",
		version: "1.0",

		fileName: "tom-johnson-expired-cert.pdf",
		fileSize: 896000, // 896KB
		mimeType: "application/pdf",
		fileUrl: "/documents/tom-johnson-expired-cert.pdf",
		tags: ["osha", "safety", "certificate", "expired"],
		customFields: {
			workerName: "Tom Johnson",
			certificateNumber: "OSHA-654321",
		},
		expiryDate: "2024-11-01",
		renewalRequired: true,


		entityId: "worker-5",
		createdAt: "2023-11-01T10:00:00Z",
		createdBy: "user-2",
		createdByName: "HR Manager",
		updatedAt: "2023-11-01T10:00:00Z",
		updatedBy: "user-2",
		updatedByName: "HR Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},

	// Company-level documents
	{
		id: "doc-18",
		name: "Company Safety Policy",
		description: "Comprehensive company-wide safety policy and procedures",

		type: "policy",
		version: "3.0",

		fileName: "company-safety-policy-v3.pdf",
		fileSize: 4194304, // 4MB
		mimeType: "application/pdf",
		fileUrl: "/documents/company-safety-policy-v3.pdf",
		tags: ["policy", "safety", "company", "procedures"],
		customFields: {},
		renewalRequired: false,


		entityId: "company-1",
		createdAt: "2024-01-01T08:00:00Z",
		createdBy: "user-9",
		createdByName: "CEO",
		updatedAt: "2024-01-01T08:00:00Z",
		updatedBy: "user-9",
		updatedByName: "CEO",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-19",
		name: "PPE Standards Manual",
		description: "Personal Protective Equipment standards and requirements",

		type: "manual",
		version: "2.0",

		fileName: "ppe-standards-manual-v2.pdf",
		fileSize: 6291456, // 6MB
		mimeType: "application/pdf",
		fileUrl: "/documents/ppe-standards-manual-v2.pdf",
		tags: ["ppe", "standards", "manual", "safety"],
		customFields: {},
		renewalRequired: false,


		entityId: "company-1",
		createdAt: "2024-02-15T11:00:00Z",
		createdBy: "user-1",
		createdByName: "Safety Manager",
		updatedAt: "2024-02-15T11:00:00Z",
		updatedBy: "user-1",
		updatedByName: "Safety Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
	{
		id: "doc-20",
		name: "Quality Management System",
		description: "ISO 9001 Quality Management System documentation",

		type: "quality-system",
		version: "1.2",

		fileName: "qms-iso9001-v1.2.pdf",
		fileSize: 8388608, // 8MB
		mimeType: "application/pdf",
		fileUrl: "/documents/qms-iso9001-v1.2.pdf",
		tags: ["quality", "iso9001", "management", "system"],
		customFields: {
			standard: "ISO 9001:2015",
			certificationBody: "Quality Assurance International",
		},
		expiryDate: "2026-03-15",
		renewalRequired: true,


		entityId: "company-1",
		createdAt: "2024-03-15T14:30:00Z",
		createdBy: "user-10",
		createdByName: "Quality Manager",
		updatedAt: "2024-03-15T14:30:00Z",
		updatedBy: "user-10",
		updatedByName: "Quality Manager",
		category: DocumentCategory.SAFETY,
		status: DocumentStatus.ACTIVE,
		complianceStatus: ComplianceStatus.VALID,
		entityType: EntityType.COMPANY,
		createElement: function (_arg0: string): unknown {
			throw new Error("Function not implemented.");
		},
		body: undefined
	},
];

interface UseDocumentsReturn {
	documents: Document[];
	loading: boolean;
	error: string | null;
	totalCount: number;
	loadDocuments: () => Promise<void>;
	deleteDocument: (documentId: string) => Promise<void>;
	updateDocument: (
		documentId: string,
		updates: Partial<Document>,
	) => Promise<void>;
}

export const useDocuments = (filters: DocumentFilter): UseDocumentsReturn => {
	const [documents, setDocuments] = useState<Document[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [totalCount, setTotalCount] = useState(0);

	const loadDocuments = useCallback(async () => {
		setLoading(true);
		setError(null);

		try {
			// Simulate API delay
			await new Promise((resolve) => setTimeout(resolve, 500));

			// Filter mock documents based on filters
			let filteredDocuments = mockDocuments.filter((doc) => {
				// Entity type and ID filter
				if (filters.entityType && doc.entityType !== filters.entityType)
					return false;
				if (filters.entityId && doc.entityId !== filters.entityId) return false;

				// Category filter
				if (filters.category && doc.category !== filters.category) return false;

				// Status filter
				if (filters.status && doc.status !== filters.status) return false;

				// Compliance status filter
				if (
					filters.complianceStatus &&
					doc.complianceStatus !== filters.complianceStatus
				)
					return false;

				// Search filter
				if (filters.search) {
					const searchLower = filters.search.toLowerCase();
					const matchesName = doc.name.toLowerCase().includes(searchLower);
					const matchesDescription = doc.description
						?.toLowerCase()
						.includes(searchLower);
					const matchesTags = doc.tags.some((tag) =>
						tag.toLowerCase().includes(searchLower),
					);

					if (!matchesName && !matchesDescription && !matchesTags) return false;
				}

				// Date filters
				if (
					filters.dateFrom &&
					new Date(doc.createdAt) < new Date(filters.dateFrom)
				)
					return false;
				if (
					filters.dateTo &&
					new Date(doc.createdAt) > new Date(filters.dateTo)
				)
					return false;

				// Expiry date filters
				if (
					filters.expiryFrom &&
					doc.expiryDate &&
					new Date(doc.expiryDate) < new Date(filters.expiryFrom)
				)
					return false;
				if (
					filters.expiryTo &&
					doc.expiryDate &&
					new Date(doc.expiryDate) > new Date(filters.expiryTo)
				)
					return false;

				// File type filter
				if (filters.fileType) {
					switch (filters.fileType) {
						case "pdf":
							if (!doc.mimeType.includes("pdf")) return false;
							break;
						case "image":
							if (!doc.mimeType.startsWith("image/")) return false;
							break;
						case "document":
							if (
								!doc.mimeType.includes("document") &&
								!doc.mimeType.includes("word")
							)
								return false;
							break;
						case "video":
							if (!doc.mimeType.startsWith("video/")) return false;
							break;
					}
				}

				// Tags filter
				if (filters.tags && filters.tags.length > 0) {
					const hasMatchingTag = filters.tags.some((filterTag) =>
						doc.tags.some((docTag) =>
							docTag.toLowerCase().includes(filterTag.toLowerCase()),
						),
					);
					if (!hasMatchingTag) return false;
				}

				return true;
			});

			setDocuments(filteredDocuments);
			setTotalCount(filteredDocuments.length);
		} catch (err) {
			setError("Failed to load documents");
			console.error("Error loading documents:", err);
		} finally {
			setLoading(false);
		}
	}, [filters]);

	const deleteDocument = useCallback(async (documentId: string) => {
		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 300));

			// Remove from mock data
			const index = mockDocuments.findIndex((doc) => doc.id === documentId);
			if (index > -1) {
				mockDocuments.splice(index, 1);
			}

			// Update local state
			setDocuments((prev) => prev.filter((doc) => doc.id !== documentId));
			setTotalCount((prev) => prev - 1);
		} catch (err) {
			throw new Error("Failed to delete document");
		}
	}, []);

	const updateDocument = useCallback(
		async (documentId: string, updates: Partial<Document>) => {
			try {
				// Simulate API call
				await new Promise((resolve) => setTimeout(resolve, 300));

				// Update mock data
				const index = mockDocuments.findIndex((doc) => doc.id === documentId);
				if (index > -1) {
					mockDocuments[index] = { ...mockDocuments[index], ...updates };
				}

				// Update local state
				setDocuments((prev) =>
					prev.map((doc) =>
						doc.id === documentId ? { ...doc, ...updates } : doc,
					),
				);
			} catch (err) {
				throw new Error("Failed to update document");
			}
		},
		[],
	);

	return {
		documents,
		loading,
		error,
		totalCount,
		loadDocuments,
		deleteDocument,
		updateDocument,
	};
};
