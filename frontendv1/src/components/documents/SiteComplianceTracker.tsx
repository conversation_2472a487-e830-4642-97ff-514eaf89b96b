import React, { useState, useEffect } from "react";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	AlertTriangle,
	Clock,
	FileText,
	User,
	Calendar,
	TrendingUp,
	AlertCircle,
	Download,
	RefreshCw,
} from "lucide-react";
import {
	Document,
	ComplianceStatus,
	DocumentCategory,
} from "../../types/documents";

interface SiteComplianceTrackerProps {
	siteId: string;
}

interface SiteComplianceItem {
	document: Document;
	daysUntilExpiry: number;
	urgencyLevel: "critical" | "warning" | "info";
	category: DocumentCategory;
}

interface ComplianceStats {
	totalDocuments: number;
	compliantDocuments: number;
	expiringDocuments: number;
	expiredDocuments: number;
	complianceRate: number;
	criticalIssues: number;
}

const SiteComplianceTracker: React.FC<SiteComplianceTrackerProps> = ({
	siteId,
}) => {
	const [complianceItems, setComplianceItems] = useState<SiteComplianceItem[]>(
		[],
	);
	const [stats, setStats] = useState<ComplianceStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [filter, setFilter] = useState<
		"all" | "critical" | "warning" | "expired"
	>("all");

	useEffect(() => {
		loadSiteComplianceData();
	}, [siteId]);

	const loadSiteComplianceData = async () => {
		setLoading(true);
		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 800));

			// Mock site-specific compliance data
			const mockItems: SiteComplianceItem[] = [
				{
					document: {
						id: "doc-1",
						name: "Site Safety RAMS v2.1",
						category: "safety",
						type: "rams",
						expiryDate: "2025-12-31",
						complianceStatus: ComplianceStatus.VALID,
						entityType: "site",
						entityId: siteId,
						createdByName: "Safety Manager",
						fileName: "site-safety-rams-v2.1.pdf",
						fileSize: 2048576,
						mimeType: "application/pdf",
					} as Document,
					daysUntilExpiry: 365,
					urgencyLevel: "info",
					category: DocumentCategory.SAFETY,
				},
				{
					document: {
						id: "doc-2",
						name: "Emergency Response Plan",
						category: "safety",
						type: "emergency-plan",
						expiryDate: "2025-06-30",
						complianceStatus: ComplianceStatus.VALID,
						entityType: "site",
						entityId: siteId,
						createdByName: "Safety Manager",
						fileName: "emergency-response-plan-v1.3.pdf",
						fileSize: 1536000,
						mimeType: "application/pdf",
					} as Document,
					daysUntilExpiry: 180,
					urgencyLevel: "info",
					category: DocumentCategory.SAFETY,
				},
				{
					document: {
						id: "doc-9",
						name: "Building Permit",
						category: "compliance",
						type: "permit",
						expiryDate: "2025-12-31",
						complianceStatus: ComplianceStatus.VALID,
						entityType: "site",
						entityId: siteId,
						createdByName: "Compliance Officer",
						fileName: "building-permit-2024.pdf",
						fileSize: 1024000,
						mimeType: "application/pdf",
					} as Document,
					daysUntilExpiry: 365,
					urgencyLevel: "info",
					category: DocumentCategory.COMPLIANCE,
				},
				{
					document: {
						id: "doc-13",
						name: "Crane Inspection Certificate",
						category: "equipment",
						type: "inspection-certificate",
						expiryDate: "2024-12-15",
						complianceStatus: ComplianceStatus.EXPIRING,
						entityType: "site",
						entityId: siteId,
						createdByName: "Equipment Manager",
						fileName: "crane-tc001-inspection-cert.pdf",
						fileSize: 1024000,
						mimeType: "application/pdf",
					} as Document,
					daysUntilExpiry: 5,
					urgencyLevel: "critical",
					category: DocumentCategory.EQUIPMENT,
				},
				{
					document: {
						id: "doc-15",
						name: "Site Insurance Policy",
						category: "administrative",
						type: "insurance-policy",
						expiryDate: "2025-01-31",
						complianceStatus: ComplianceStatus.EXPIRING,
						entityType: "site",
						entityId: siteId,
						createdByName: "Administrative Manager",
						fileName: "site-insurance-policy-2024.pdf",
						fileSize: 2097152,
						mimeType: "application/pdf",
					} as Document,
					daysUntilExpiry: 41,
					urgencyLevel: "warning",
					category: DocumentCategory.ADMINISTRATIVE,
				},
				{
					document: {
						id: "doc-10",
						name: "Environmental Impact Assessment",
						category: "compliance",
						type: "environmental-report",
						expiryDate: "2026-01-15",
						complianceStatus: ComplianceStatus.VALID,
						entityType: "site",
						entityId: siteId,
						createdByName: "Compliance Officer",
						fileName: "environmental-impact-assessment.pdf",
						fileSize: 3145728,
						mimeType: "application/pdf",
					} as Document,
					daysUntilExpiry: 400,
					urgencyLevel: "info",
					category: DocumentCategory.COMPLIANCE,
				},
			];

			setComplianceItems(mockItems);

			// Calculate stats
			const totalDocs = mockItems.length;
			const expiredDocs = mockItems.filter(
				(item) => item.daysUntilExpiry < 0,
			).length;
			const expiringDocs = mockItems.filter(
				(item) => item.daysUntilExpiry >= 0 && item.daysUntilExpiry <= 30,
			).length;
			const compliantDocs = mockItems.filter(
				(item) => item.daysUntilExpiry > 30,
			).length;
			const criticalIssues = mockItems.filter(
				(item) => item.urgencyLevel === "critical",
			).length;

			setStats({
				totalDocuments: totalDocs,
				compliantDocuments: compliantDocs,
				expiringDocuments: expiringDocs,
				expiredDocuments: expiredDocs,
				complianceRate: Math.round((compliantDocs / totalDocs) * 100),
				criticalIssues,
			});
		} catch (error) {
			console.error("Failed to load site compliance data:", error);
		} finally {
			setLoading(false);
		}
	};

	const getUrgencyColor = (urgencyLevel: string) => {
		switch (urgencyLevel) {
			case "critical":
				return "border-red-200 bg-red-50";
			case "warning":
				return "border-yellow-200 bg-yellow-50";
			default:
				return "border-green-200 bg-green-50";
		}
	};

	const getUrgencyIcon = (urgencyLevel: string) => {
		switch (urgencyLevel) {
			case "critical":
				return <AlertTriangle className="h-5 w-5 text-red-500" />;
			case "warning":
				return <Clock className="h-5 w-5 text-yellow-500" />;
			default:
				return <CheckCircle className="h-5 w-5 text-green-500" />;
		}
	};

	const formatDaysUntilExpiry = (days: number) => {
		if (days < 0) {
			return `Expired ${Math.abs(days)} days ago`;
		} else if (days === 0) {
			return "Expires today";
		} else {
			return `Expires in ${days} days`;
		}
	};

	const getCategoryColor = (category: DocumentCategory) => {
		switch (category) {
			case DocumentCategory.SAFETY:
				return "bg-red-100 text-red-800";
			case DocumentCategory.COMPLIANCE:
				return "bg-blue-100 text-blue-800";
			case DocumentCategory.EQUIPMENT:
				return "bg-purple-100 text-purple-800";
			case DocumentCategory.ADMINISTRATIVE:
				return "bg-gray-100 text-gray-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const filteredItems = complianceItems.filter((item) => {
		switch (filter) {
			case "critical":
				return item.urgencyLevel === "critical";
			case "warning":
				return item.urgencyLevel === "warning";
			case "expired":
				return item.daysUntilExpiry < 0;
			default:
				return true;
		}
	});

	if (loading) {
		return (
			<div className="flex justify-center py-12">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold">Site Compliance Tracking</h2>
					<p className="text-gray-600 text-sm mt-1">
						Monitor compliance status for site-specific documents
					</p>
				</div>
				<div className="flex space-x-3">
					<button
						onClick={loadSiteComplianceData}
						className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
					>
						<RefreshCw className="h-4 w-4" />
						<span>Refresh</span>
					</button>
					<button className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
						<Download className="h-4 w-4" />
						<span>Export Report</span>
					</button>
				</div>
			</div>

			{/* Compliance Overview Stats */}
			{stats && (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
					<div className="bg-white border border-gray-200 rounded-lg p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm text-gray-600">Total Documents</p>
								<p className="text-2xl font-semibold text-gray-900">
									{stats.totalDocuments}
								</p>
							</div>
							<FileText className="h-8 w-8 text-blue-500" />
						</div>
					</div>

					<div className="bg-white border border-gray-200 rounded-lg p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm text-gray-600">Compliance Rate</p>
								<p className="text-2xl font-semibold text-green-600">
									{stats.complianceRate}%
								</p>
							</div>
							<TrendingUp className="h-8 w-8 text-green-500" />
						</div>
					</div>

					<div className="bg-white border border-gray-200 rounded-lg p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm text-gray-600">Expiring Soon</p>
								<p className="text-2xl font-semibold text-yellow-600">
									{stats.expiringDocuments}
								</p>
							</div>
							<Clock className="h-8 w-8 text-yellow-500" />
						</div>
					</div>

					<div className="bg-white border border-gray-200 rounded-lg p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm text-gray-600">Expired</p>
								<p className="text-2xl font-semibold text-red-600">
									{stats.expiredDocuments}
								</p>
							</div>
							<AlertCircle className="h-8 w-8 text-red-500" />
						</div>
					</div>

					<div className="bg-white border border-gray-200 rounded-lg p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm text-gray-600">Critical Issues</p>
								<p className="text-2xl font-semibold text-red-600">
									{stats.criticalIssues}
								</p>
							</div>
							<AlertTriangle className="h-8 w-8 text-red-500" />
						</div>
					</div>
				</div>
			)}

			{/* Filter Buttons */}
			<div className="flex space-x-2">
				<button
					onClick={() => setFilter("all")}
					className={`px-3 py-1 rounded-md text-sm ${
						filter === "all"
							? "bg-blue-100 text-blue-800"
							: "bg-gray-100 text-gray-600 hover:bg-gray-200"
					}`}
				>
					All ({complianceItems.length})
				</button>
				<button
					onClick={() => setFilter("critical")}
					className={`px-3 py-1 rounded-md text-sm ${
						filter === "critical"
							? "bg-red-100 text-red-800"
							: "bg-gray-100 text-gray-600 hover:bg-gray-200"
					}`}
				>
					Critical (
					{complianceItems.filter((i) => i.urgencyLevel === "critical").length})
				</button>
				<button
					onClick={() => setFilter("warning")}
					className={`px-3 py-1 rounded-md text-sm ${
						filter === "warning"
							? "bg-yellow-100 text-yellow-800"
							: "bg-gray-100 text-gray-600 hover:bg-gray-200"
					}`}
				>
					Warning (
					{complianceItems.filter((i) => i.urgencyLevel === "warning").length})
				</button>
				<button
					onClick={() => setFilter("expired")}
					className={`px-3 py-1 rounded-md text-sm ${
						filter === "expired"
							? "bg-red-100 text-red-800"
							: "bg-gray-100 text-gray-600 hover:bg-gray-200"
					}`}
				>
					Expired ({complianceItems.filter((i) => i.daysUntilExpiry < 0).length}
					)
				</button>
			</div>

			{/* Compliance Items */}
			{filteredItems.length === 0 ? (
				<div className="text-center py-12">
					<CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						{filter === "all" ? "No documents found" : `No ${filter} documents`}
					</h3>
					<p className="text-gray-600">
						{filter === "all"
							? "No compliance documents are available for this site."
							: `No documents match the ${filter} filter criteria.`}
					</p>
				</div>
			) : (
				<div className="space-y-4">
					{filteredItems.map((item) => (
						<div
							key={item.document.id}
							className={`border rounded-lg p-4 ${getUrgencyColor(item.urgencyLevel)}`}
						>
							<div className="flex items-start justify-between">
								<div className="flex items-start space-x-3 flex-1">
									{getUrgencyIcon(item.urgencyLevel)}
									<div className="flex-1 min-w-0">
										<div className="flex items-center space-x-2 mb-2">
											<h3 className="font-medium text-gray-900 truncate">
												{item.document.name}
											</h3>
											<span
												className={`inline-block px-2 py-1 text-xs rounded-full ${getCategoryColor(item.category)}`}
											>
												{item.category}
											</span>
										</div>
										<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
											<div className="flex items-center space-x-1">
												<FileText className="h-4 w-4" />
												<span>{item.document.type}</span>
											</div>
											<div className="flex items-center space-x-1">
												<User className="h-4 w-4" />
												<span>{item.document.createdByName}</span>
											</div>
											<div className="flex items-center space-x-1">
												<Calendar className="h-4 w-4" />
												<span>
													{formatDaysUntilExpiry(item.daysUntilExpiry)}
												</span>
											</div>
										</div>
										{item.document.expiryDate && (
											<p className="text-sm text-gray-600 mt-1">
												Expiry Date:{" "}
												{new Date(
													item.document.expiryDate,
												).toLocaleDateString()}
											</p>
										)}
									</div>
								</div>
								<div className="flex space-x-2 ml-4">
									<button className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
										View Document
									</button>
									{item.daysUntilExpiry <= 30 && (
										<button className="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700">
											Renew
										</button>
									)}
								</div>
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default SiteComplianceTracker;
