import React, { useState, useEffect } from "react";
import {
	FileText,
	Upload,
	AlertTriangle,
	Check<PERSON>ir<PERSON>,
	Clock,
	TrendingUp,
	Shield,
} from "lucide-react";
import {
	DocumentStats,
	DocumentCategory,
} from "../../types/documents";

interface DocumentsDashboardProps {
	onNavigateToTab: (tabId: string) => void;
}

const DocumentsDashboard: React.FC<DocumentsDashboardProps> = ({
	onNavigateToTab,
}) => {
	const [stats, setStats] = useState<DocumentStats | null>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		loadStats();
	}, []);

	const loadStats = async () => {
		setLoading(true);
		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 500));

			// Mock stats data based on our 20 mock documents
			const mockStats: DocumentStats = {
				totalDocuments: 20,
				documentsByCategory: {
					[DocumentCategory.SAFETY]: 5,
					[DocumentCategory.TRAINING]: 4,
					[DocumentCategory.PROJECT]: 2,
					[DocumentCategory.WORKER]: 2,
					[DocumentCategory.COMPLIANCE]: 3,
					[DocumentCategory.EQUIPMENT]: 2,
					[DocumentCategory.ADMINISTRATIVE]: 2,
				},
				documentsByStatus: {
					active: 20,
					archived: 0,
					expired: 0,
					draft: 0,
				},
				expiringDocuments: 5,
				expiredDocuments: 1,
				recentUploads: 3,
				storageUsed: 89.5 * 1024 * 1024, // ~89.5MB total from our mock documents
			};

			setStats(mockStats);
		} catch (error) {
			console.error("Failed to load stats:", error);
		} finally {
			setLoading(false);
		}
	};

	const formatStorageSize = (bytes: number): string => {
		const sizes = ["Bytes", "KB", "MB", "GB"];
		if (bytes === 0) return "0 Bytes";
		const i = Math.floor(Math.log(bytes) / Math.log(1024));
		return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
	};

	if (loading || !stats) {
		return (
			<div className="flex justify-center py-12">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Overview Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<div className="bg-white border border-gray-200 rounded-lg p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Documents</p>
							<p className="text-2xl font-semibold text-gray-900">
								{stats.totalDocuments}
							</p>
						</div>
						<FileText className="h-8 w-8 text-blue-500" />
					</div>
					<div className="mt-2">
						<span className="text-sm text-green-600">
							+{stats.recentUploads} this week
						</span>
					</div>
				</div>

				<div className="bg-white border border-gray-200 rounded-lg p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Expiring Soon</p>
							<p className="text-2xl font-semibold text-yellow-600">
								{stats.expiringDocuments}
							</p>
						</div>
						<Clock className="h-8 w-8 text-yellow-500" />
					</div>
					<div className="mt-2">
						<button
							onClick={() => onNavigateToTab("compliance")}
							className="text-sm text-blue-600 hover:text-blue-800"
						>
							View details →
						</button>
					</div>
				</div>

				<div className="bg-white border border-gray-200 rounded-lg p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Expired</p>
							<p className="text-2xl font-semibold text-red-600">
								{stats.expiredDocuments}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-red-500" />
					</div>
					<div className="mt-2">
						<button
							onClick={() => onNavigateToTab("compliance")}
							className="text-sm text-red-600 hover:text-red-800"
						>
							Needs attention →
						</button>
					</div>
				</div>

				<div className="bg-white border border-gray-200 rounded-lg p-6">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Storage Used</p>
							<p className="text-2xl font-semibold text-gray-900">
								{formatStorageSize(stats.storageUsed)}
							</p>
						</div>
						<TrendingUp className="h-8 w-8 text-green-500" />
					</div>
					<div className="mt-2">
						<span className="text-sm text-gray-600">of unlimited storage</span>
					</div>
				</div>
			</div>

			{/* Quick Actions */}
			<div className="bg-white border border-gray-200 rounded-lg p-6">
				<h3 className="text-lg font-medium text-gray-900 mb-4">
					Quick Actions
				</h3>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<button
						onClick={() => onNavigateToTab("library")}
						className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
					>
						<FileText className="h-6 w-6 text-blue-500" />
						<div className="text-left">
							<p className="font-medium text-gray-900">Browse Library</p>
							<p className="text-sm text-gray-600">View all documents</p>
						</div>
					</button>

					<button
						onClick={() => onNavigateToTab("library")}
						className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
					>
						<Upload className="h-6 w-6 text-green-500" />
						<div className="text-left">
							<p className="font-medium text-gray-900">Upload Document</p>
							<p className="text-sm text-gray-600">Add new document</p>
						</div>
					</button>

					<button
						onClick={() => onNavigateToTab("compliance")}
						className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
					>
						<Shield className="h-6 w-6 text-yellow-500" />
						<div className="text-left">
							<p className="font-medium text-gray-900">Check Compliance</p>
							<p className="text-sm text-gray-600">Review expiring docs</p>
						</div>
					</button>

					<button
						onClick={() => onNavigateToTab("reports")}
						className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
					>
						<TrendingUp className="h-6 w-6 text-purple-500" />
						<div className="text-left">
							<p className="font-medium text-gray-900">View Reports</p>
							<p className="text-sm text-gray-600">Document analytics</p>
						</div>
					</button>
				</div>
			</div>

			{/* Documents by Category */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<div className="bg-white border border-gray-200 rounded-lg p-6">
					<h3 className="text-lg font-medium text-gray-900 mb-4">
						Documents by Category
					</h3>
					<div className="space-y-3">
						{Object.entries(stats.documentsByCategory).map(
							([category, count]) => (
								<div
									key={category}
									className="flex items-center justify-between"
								>
									<div className="flex items-center space-x-2">
										<div className="w-3 h-3 bg-blue-500 rounded-full"></div>
										<span className="text-sm text-gray-700 capitalize">
											{category}
										</span>
									</div>
									<span className="text-sm font-medium text-gray-900">
										{count}
									</span>
								</div>
							),
						)}
					</div>
				</div>

				<div className="bg-white border border-gray-200 rounded-lg p-6">
					<h3 className="text-lg font-medium text-gray-900 mb-4">
						Document Status
					</h3>
					<div className="space-y-3">
						{Object.entries(stats.documentsByStatus).map(([status, count]) => {
							const getStatusColor = (status: string) => {
								switch (status) {
									case "active":
										return "bg-green-500";
									case "archived":
										return "bg-gray-500";
									case "expired":
										return "bg-red-500";
									case "draft":
										return "bg-yellow-500";
									default:
										return "bg-blue-500";
								}
							};

							return (
								<div key={status} className="flex items-center justify-between">
									<div className="flex items-center space-x-2">
										<div
											className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}
										></div>
										<span className="text-sm text-gray-700 capitalize">
											{status}
										</span>
									</div>
									<span className="text-sm font-medium text-gray-900">
										{count}
									</span>
								</div>
							);
						})}
					</div>
				</div>
			</div>

			{/* Recent Activity */}
			<div className="bg-white border border-gray-200 rounded-lg p-6">
				<h3 className="text-lg font-medium text-gray-900 mb-4">
					Recent Activity
				</h3>
				<div className="space-y-4">
					<div className="flex items-center space-x-3">
						<Upload className="h-5 w-5 text-green-500" />
						<div className="flex-1">
							<p className="text-sm text-gray-900">
								Quality Management System uploaded
							</p>
							<p className="text-xs text-gray-500">
								2 hours ago by Quality Manager
							</p>
						</div>
					</div>
					<div className="flex items-center space-x-3">
						<AlertTriangle className="h-5 w-5 text-yellow-500" />
						<div className="flex-1">
							<p className="text-sm text-gray-900">
								Crane Inspection Certificate expiring in 5 days
							</p>
							<p className="text-xs text-gray-500">1 day ago</p>
						</div>
					</div>
					<div className="flex items-center space-x-3">
						<Upload className="h-5 w-5 text-green-500" />
						<div className="flex-1">
							<p className="text-sm text-gray-900">
								PPE Standards Manual v2.0 uploaded
							</p>
							<p className="text-xs text-gray-500">
								3 days ago by Safety Manager
							</p>
						</div>
					</div>
					<div className="flex items-center space-x-3">
						<AlertTriangle className="h-5 w-5 text-red-500" />
						<div className="flex-1">
							<p className="text-sm text-gray-900">
								Tom Johnson's Safety Certificate expired
							</p>
							<p className="text-xs text-gray-500">40 days ago</p>
						</div>
					</div>
					<div className="flex items-center space-x-3">
						<CheckCircle className="h-5 w-5 text-blue-500" />
						<div className="flex-1">
							<p className="text-sm text-gray-900">
								Building Permit renewed successfully
							</p>
							<p className="text-xs text-gray-500">
								1 week ago by Compliance Officer
							</p>
						</div>
					</div>
				</div>
				<div className="mt-4 pt-4 border-t border-gray-200">
					<button
						onClick={() => onNavigateToTab("reports")}
						className="text-sm text-blue-600 hover:text-blue-800"
					>
						View all activity →
					</button>
				</div>
			</div>
		</div>
	);
};

export default DocumentsDashboard;
