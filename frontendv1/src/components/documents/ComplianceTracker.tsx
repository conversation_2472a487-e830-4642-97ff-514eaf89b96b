import React, { useState, useEffect } from "react";
import {
	AlertTriangle,
	Clock,
	CheckCircle,
	Calendar,
	User,
	FileText,
} from "lucide-react";
import { Document, ComplianceStatus } from "../../types/documents";

interface ComplianceItem {
	document: Document;
	daysUntilExpiry: number;
	urgencyLevel: "critical" | "warning" | "info";
}

const ComplianceTracker: React.FC = () => {
	const [complianceItems, setComplianceItems] = useState<ComplianceItem[]>([]);
	const [loading, setLoading] = useState(true);
	const [filter, setFilter] = useState<
		"all" | "critical" | "warning" | "expired"
	>("all");

	useEffect(() => {
		loadComplianceData();
	}, []);

	const loadComplianceData = async () => {
		setLoading(true);
		try {
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 500));

			// Mock compliance data
			const mockItems: ComplianceItem[] = [
				{
					document: {
						id: "doc-4",
						name: "Worker Safety Certificate - John Doe",
						category: "training",
						type: "safety-certificate",
						expiryDate: "2025-06-15",
						complianceStatus: ComplianceStatus.EXPIRING,
						entityType: "worker",
						entityId: "worker-1",
						createdByName: "HR Manager",
					} as Document,
					daysUntilExpiry: 45,
					urgencyLevel: "warning",
				},
				{
					document: {
						id: "doc-13",
						name: "Crane Inspection Certificate",
						category: "equipment",
						type: "inspection-certificate",
						expiryDate: "2024-12-15",
						complianceStatus: ComplianceStatus.EXPIRING,
						entityType: "site",
						entityId: "site1",
						createdByName: "Equipment Manager",
					} as Document,
					daysUntilExpiry: 5,
					urgencyLevel: "critical",
				},
				{
					document: {
						id: "doc-17",
						name: "Expired Safety Certificate - Tom Johnson",
						category: "training",
						type: "safety-certificate",
						expiryDate: "2024-11-01",
						complianceStatus: ComplianceStatus.EXPIRED,
						entityType: "worker",
						entityId: "worker-5",
						createdByName: "HR Manager",
					} as Document,
					daysUntilExpiry: -40,
					urgencyLevel: "critical",
				},
				{
					document: {
						id: "doc-12",
						name: "Medical Fitness Certificate - David Lee",
						category: "worker",
						type: "medical-certificate",
						expiryDate: "2025-01-15",
						complianceStatus: ComplianceStatus.EXPIRING,
						entityType: "worker",
						entityId: "worker-4",
						createdByName: "HR Manager",
					} as Document,
					daysUntilExpiry: 25,
					urgencyLevel: "warning",
				},
				{
					document: {
						id: "doc-9",
						name: "Building Permit",
						category: "compliance",
						type: "permit",
						expiryDate: "2025-12-31",
						complianceStatus: ComplianceStatus.VALID,
						entityType: "site",
						entityId: "site1",
						createdByName: "Compliance Officer",
					} as Document,
					daysUntilExpiry: 365,
					urgencyLevel: "info",
				},
				{
					document: {
						id: "doc-15",
						name: "Site Insurance Policy",
						category: "administrative",
						type: "insurance-policy",
						expiryDate: "2025-01-31",
						complianceStatus: ComplianceStatus.EXPIRING,
						entityType: "site",
						entityId: "site1",
						createdByName: "Administrative Manager",
					} as Document,
					daysUntilExpiry: 41,
					urgencyLevel: "warning",
				},
			];

			setComplianceItems(mockItems);
		} catch (error) {
			console.error("Failed to load compliance data:", error);
		} finally {
			setLoading(false);
		}
	};

	const getUrgencyColor = (urgencyLevel: string) => {
		switch (urgencyLevel) {
			case "critical":
				return "border-red-200 bg-red-50";
			case "warning":
				return "border-yellow-200 bg-yellow-50";
			default:
				return "border-gray-200 bg-white";
		}
	};

	const getUrgencyIcon = (urgencyLevel: string) => {
		switch (urgencyLevel) {
			case "critical":
				return <AlertTriangle className="h-5 w-5 text-red-500" />;
			case "warning":
				return <Clock className="h-5 w-5 text-yellow-500" />;
			default:
				return <CheckCircle className="h-5 w-5 text-green-500" />;
		}
	};

	const formatDaysUntilExpiry = (days: number) => {
		if (days < 0) {
			return `Expired ${Math.abs(days)} days ago`;
		} else if (days === 0) {
			return "Expires today";
		} else {
			return `Expires in ${days} days`;
		}
	};

	const filteredItems = complianceItems.filter((item) => {
		switch (filter) {
			case "critical":
				return item.urgencyLevel === "critical";
			case "warning":
				return item.urgencyLevel === "warning";
			case "expired":
				return item.daysUntilExpiry < 0;
			default:
				return true;
		}
	});

	if (loading) {
		return (
			<div className="flex justify-center py-12">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Compliance Tracker</h2>
				<div className="flex space-x-2">
					<button
						onClick={() => setFilter("all")}
						className={`px-3 py-1 rounded-md text-sm ${
							filter === "all"
								? "bg-blue-100 text-blue-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						All ({complianceItems.length})
					</button>
					<button
						onClick={() => setFilter("critical")}
						className={`px-3 py-1 rounded-md text-sm ${
							filter === "critical"
								? "bg-red-100 text-red-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Critical (
						{
							complianceItems.filter((i) => i.urgencyLevel === "critical")
								.length
						}
						)
					</button>
					<button
						onClick={() => setFilter("warning")}
						className={`px-3 py-1 rounded-md text-sm ${
							filter === "warning"
								? "bg-yellow-100 text-yellow-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Warning (
						{complianceItems.filter((i) => i.urgencyLevel === "warning").length}
						)
					</button>
					<button
						onClick={() => setFilter("expired")}
						className={`px-3 py-1 rounded-md text-sm ${
							filter === "expired"
								? "bg-red-100 text-red-800"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						Expired (
						{complianceItems.filter((i) => i.daysUntilExpiry < 0).length})
					</button>
				</div>
			</div>

			{/* Compliance Items */}
			{filteredItems.length === 0 ? (
				<div className="text-center py-12">
					<CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						All documents are compliant
					</h3>
					<p className="text-gray-600">
						No documents require immediate attention.
					</p>
				</div>
			) : (
				<div className="space-y-4">
					{filteredItems.map((item) => (
						<div
							key={item.document.id}
							className={`border rounded-lg p-4 ${getUrgencyColor(item.urgencyLevel)}`}
						>
							<div className="flex items-start justify-between">
								<div className="flex items-start space-x-3">
									{getUrgencyIcon(item.urgencyLevel)}
									<div className="flex-1">
										<h3 className="font-medium text-gray-900">
											{item.document.name}
										</h3>
										<div className="mt-1 space-y-1">
											<div className="flex items-center space-x-4 text-sm text-gray-600">
												<span className="flex items-center space-x-1">
													<FileText className="h-4 w-4" />
													<span className="capitalize">
														{item.document.category}
													</span>
												</span>
												<span className="flex items-center space-x-1">
													<User className="h-4 w-4" />
													<span>{item.document.createdByName}</span>
												</span>
												<span className="flex items-center space-x-1">
													<Calendar className="h-4 w-4" />
													<span>
														{formatDaysUntilExpiry(item.daysUntilExpiry)}
													</span>
												</span>
											</div>
											{item.document.expiryDate && (
												<p className="text-sm text-gray-600">
													Expiry Date:{" "}
													{new Date(
														item.document.expiryDate,
													).toLocaleDateString()}
												</p>
											)}
										</div>
									</div>
								</div>
								<div className="flex space-x-2">
									<button className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
										View Document
									</button>
									{item.daysUntilExpiry <= 30 && (
										<button className="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700">
											Renew
										</button>
									)}
								</div>
							</div>
						</div>
					))}
				</div>
			)}

			{/* Summary Stats */}
			<div className="bg-white border border-gray-200 rounded-lg p-6">
				<h3 className="text-lg font-medium text-gray-900 mb-4">
					Compliance Summary
				</h3>
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<div className="text-center">
						<div className="text-2xl font-semibold text-green-600">
							{complianceItems.filter((i) => i.daysUntilExpiry > 30).length}
						</div>
						<div className="text-sm text-gray-600">Compliant</div>
					</div>
					<div className="text-center">
						<div className="text-2xl font-semibold text-yellow-600">
							{
								complianceItems.filter(
									(i) => i.daysUntilExpiry <= 30 && i.daysUntilExpiry > 0,
								).length
							}
						</div>
						<div className="text-sm text-gray-600">Expiring Soon</div>
					</div>
					<div className="text-center">
						<div className="text-2xl font-semibold text-red-600">
							{complianceItems.filter((i) => i.daysUntilExpiry < 0).length}
						</div>
						<div className="text-sm text-gray-600">Expired</div>
					</div>
					<div className="text-center">
						<div className="text-2xl font-semibold text-gray-900">
							{complianceItems.length}
						</div>
						<div className="text-sm text-gray-600">Total Tracked</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ComplianceTracker;
