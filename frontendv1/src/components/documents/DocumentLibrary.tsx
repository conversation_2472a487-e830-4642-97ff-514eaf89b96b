import React, { useState, useEffect } from "react";
import {
	FileText,
	Upload,
	Grid,
	List,
	Plus,
} from "lucide-react";
import {
	Document,
	DocumentFilter,
	DocumentLibraryProps,
	EntityType,
} from "../../types/documents";
import DocumentCard from "./shared/DocumentCard";
import DocumentUpload from "./DocumentUpload";
import DocumentSearch from "./DocumentSearch";
import DocumentViewer from "./DocumentViewer";
import { useDocuments } from "./hooks/useDocuments";

const DocumentLibrary: React.FC<DocumentLibraryProps> = ({
	entityType,
	entityId,
	category,
	showUpload = true,
	showFilters = true,
	viewMode: initialViewMode = "grid",
	onDocumentSelect,
}) => {
	const [viewMode, setViewMode] = useState<"grid" | "list">(initialViewMode);
	const [showUploadModal, setShowUploadModal] = useState(false);
	const [showViewer, setShowViewer] = useState(false);
	const [selectedDocument, setSelectedDocument] = useState<Document | null>(
		null,
	);
	const [filters, setFilters] = useState<DocumentFilter>({
		entityType,
		entityId,
		...(category && { category }),
	});

	const {
		documents,
		loading,
		error,
		totalCount,
		loadDocuments,
		deleteDocument,
	} = useDocuments(filters);

	useEffect(() => {
		loadDocuments();
	}, [filters]);

	const handleDocumentUploaded = (_newDocument: Document) => {
		setShowUploadModal(false);
		loadDocuments(); // Refresh the list
	};

	const handleDocumentDeleted = async (documentId: string) => {
		if (window.confirm("Are you sure you want to delete this document?")) {
			try {
				await deleteDocument(documentId);
				loadDocuments(); // Refresh the list
			} catch (error) {
				console.error("Failed to delete document:", error);
				// Show error message to user
			}
		}
	};

	const handleDocumentView = (document: Document) => {
		setSelectedDocument(document);
		setShowViewer(true);
		if (onDocumentSelect) {
			onDocumentSelect(document);
		}
	};

	const handleFiltersChange = (newFilters: Partial<DocumentFilter>) => {
		setFilters((prev) => ({
			...prev,
			...newFilters,
		}));
	};

	const getCategoryTitle = () => {
		if (category) {
			return `${category.charAt(0).toUpperCase() + category.slice(1)} Documents`;
		}
		return "All Documents";
	};

	const getEntityTitle = () => {
		switch (entityType) {
			case EntityType.COMPANY:
				return "Company";
			case EntityType.SITE:
				return "Site";
			case EntityType.WORKER:
				return "Worker";
			default:
				return "";
		}
	};

	if (error) {
		return (
			<div className="bg-red-50 border border-red-200 rounded-md p-4">
				<div className="text-red-800">
					<h3 className="font-medium">Error loading documents</h3>
					<p className="text-sm mt-1">{error}</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold">{getCategoryTitle()}</h2>
					<p className="text-gray-600 text-sm mt-1">
						{getEntityTitle()} • {totalCount} document
						{totalCount !== 1 ? "s" : ""}
					</p>
				</div>
				<div className="flex items-center space-x-3">
					{/* View Mode Toggle */}
					<div className="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
						<button
							onClick={() => setViewMode("grid")}
							className={`p-2 rounded transition-colors ${
								viewMode === "grid"
									? "bg-white shadow text-gray-900"
									: "text-gray-600 hover:text-gray-900"
							}`}
							title="Grid view"
						>
							<Grid className="h-4 w-4" />
						</button>
						<button
							onClick={() => setViewMode("list")}
							className={`p-2 rounded transition-colors ${
								viewMode === "list"
									? "bg-white shadow text-gray-900"
									: "text-gray-600 hover:text-gray-900"
							}`}
							title="List view"
						>
							<List className="h-4 w-4" />
						</button>
					</div>

					{/* Upload Button */}
					{showUpload && (
						<button
							onClick={() => setShowUploadModal(true)}
							className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
						>
							<Upload className="h-4 w-4" />
							<span>Upload Document</span>
						</button>
					)}
				</div>
			</div>

			{/* Search and Filters */}
			{showFilters && (
				<DocumentSearch
					onFiltersChange={handleFiltersChange}
					initialFilters={filters}
				/>
			)}

			{/* Document Grid/List */}
			{loading ? (
				<div className="flex justify-center py-12">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
				</div>
			) : documents.length === 0 ? (
				<div className="text-center py-12">
					<FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						No documents found
					</h3>
					<p className="text-gray-600 mb-4">
						{filters.search || Object.keys(filters).length > 2
							? "Try adjusting your search or filters"
							: "Get started by uploading your first document"}
					</p>
					{showUpload && (
						<button
							onClick={() => setShowUploadModal(true)}
							className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
						>
							<Plus className="h-4 w-4 mr-2" />
							Upload Document
						</button>
					)}
				</div>
			) : (
				<div
					className={
						viewMode === "grid"
							? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
							: "space-y-2"
					}
				>
					{documents.map((document) => (
						<DocumentCard
							key={document.id}
							document={document}
							viewMode={viewMode}
							onView={handleDocumentView}
							onDelete={handleDocumentDeleted}
							showActions={true}
						/>
					))}
				</div>
			)}

			{/* Upload Modal */}
			{showUploadModal && (
				<DocumentUpload
					entityType={entityType}
					entityId={entityId}
					category={category}
					onClose={() => setShowUploadModal(false)}
					onUploaded={handleDocumentUploaded}
				/>
			)}

			{/* Document Viewer */}
			{showViewer && selectedDocument && (
				<DocumentViewer
					document={selectedDocument}
					onClose={() => {
						setShowViewer(false);
						setSelectedDocument(null);
					}}
					onDelete={handleDocumentDeleted}
				/>
			)}
		</div>
	);
};

export default DocumentLibrary;
