import React, { useState } from 'react';
import { X, User, Calendar, AlertTriangle } from 'lucide-react';
import { SafetyObservation, ObservationAssignment } from './types/safety';

interface ObservationAssignmentModalProps {
  observation: SafetyObservation;
  isOpen: boolean;
  onClose: () => void;
  onSubmitAssignment: (assignment: ObservationAssignment) => void;
  safetyPersonnel: Array<{ id: string; name: string; role: string }>;
}

const ObservationAssignmentModal: React.FC<ObservationAssignmentModalProps> = ({
  observation,
  isOpen,
  onClose,
  onSubmitAssignment,
  safetyPersonnel,
}) => {
  const [assignmentData, setAssignmentData] = useState({
    assignedToUserId: '',
    dueDate: '',
    priority: observation.priority || 'medium',
    notes: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!assignmentData.assignedToUserId) {
      newErrors.assignedToUserId = 'Please select a team member to assign this observation to';
    }

    if (!assignmentData.dueDate) {
      newErrors.dueDate = 'Please set a due date for this assignment';
    } else {
      const dueDate = new Date(assignmentData.dueDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (dueDate < today) {
        newErrors.dueDate = 'Due date cannot be in the past';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const assignedPerson = safetyPersonnel.find(p => p.id === assignmentData.assignedToUserId);
    
    const assignment: ObservationAssignment = {
      observationId: observation.id,
      assignedToUserId: assignmentData.assignedToUserId,
      assignedToName: assignedPerson?.name || '',
      assignedByUserId: 'current-user-id', // In real app, get from auth context
      assignedByName: 'Current User', // In real app, get from auth context
      assignedAt: new Date().toISOString(),
      dueDate: assignmentData.dueDate,
      priority: assignmentData.priority as "low" | "medium" | "high" | "critical",
      notes: assignmentData.notes,
      status: 'pending',
    };

    onSubmitAssignment(assignment);
    onClose();
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-red-500 text-red-700 bg-red-50';
      case 'high': return 'border-orange-500 text-orange-700 bg-orange-50';
      case 'medium': return 'border-yellow-500 text-yellow-700 bg-yellow-50';
      case 'low': return 'border-green-500 text-green-700 bg-green-50';
      default: return 'border-gray-500 text-gray-700 bg-gray-50';
    }
  };

  const getSuggestedDueDate = (priority: string) => {
    const today = new Date();
    switch (priority) {
      case 'critical':
        today.setDate(today.getDate() + 1); // 1 day
        break;
      case 'high':
        today.setDate(today.getDate() + 3); // 3 days
        break;
      case 'medium':
        today.setDate(today.getDate() + 7); // 1 week
        break;
      case 'low':
        today.setDate(today.getDate() + 30); // 1 month
        break;
    }
    return today.toISOString().split('T')[0];
  };

  const handlePriorityChange = (priority: string) => {
    setAssignmentData(prev => ({
      ...prev,
      priority: priority as "low" | "medium" | "high" | "critical",
      dueDate: getSuggestedDueDate(priority)
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b px-6 py-4 flex justify-between items-center">
          <h3 className="text-lg font-semibold">Assign Safety Observation</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6">
          {/* Observation Summary */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Observation Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Date:</span>
                <span className="ml-2">{new Date(observation.dateOfObservation).toLocaleDateString()}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Location:</span>
                <span className="ml-2">{observation.locationOnSite}</span>
              </div>
              <div className="col-span-2">
                <span className="font-medium text-gray-700">Type:</span>
                <span className={`ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  observation.observationType.includes('safe') 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {observation.observationType.replace('-', ' ')}
                </span>
              </div>
            </div>
            <div className="mt-3">
              <span className="font-medium text-gray-700">Description:</span>
              <p className="mt-1 text-gray-900">{observation.description}</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Team Member Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Assign To *
              </label>
              <div className="space-y-2">
                {safetyPersonnel.map((person) => (
                  <label key={person.id} className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="assignedTo"
                      value={person.id}
                      checked={assignmentData.assignedToUserId === person.id}
                      onChange={(e) => setAssignmentData(prev => ({ ...prev, assignedToUserId: e.target.value }))}
                      className="text-green-600 focus:ring-green-500"
                    />
                    <div className="ml-3 flex-1">
                      <div className="flex items-center">
                        <User className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="font-medium text-gray-900">{person.name}</span>
                      </div>
                      <span className="text-sm text-gray-500">{person.role}</span>
                    </div>
                  </label>
                ))}
              </div>
              {errors.assignedToUserId && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  {errors.assignedToUserId}
                </p>
              )}
            </div>

            {/* Priority Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority Level
              </label>
              <div className="grid grid-cols-2 gap-3">
                {['low', 'medium', 'high', 'critical'].map((priority) => (
                  <label key={priority} className="cursor-pointer">
                    <input
                      type="radio"
                      name="priority"
                      value={priority}
                      checked={assignmentData.priority === priority}
                      onChange={(e) => handlePriorityChange(e.target.value)}
                      className="sr-only"
                    />
                    <div className={`border-2 rounded-lg p-3 text-center transition-colors ${
                      assignmentData.priority === priority 
                        ? getPriorityColor(priority)
                        : 'border-gray-200 hover:border-gray-300'
                    }`}>
                      <div className="font-medium capitalize">{priority}</div>
                      {priority === 'critical' && <div className="text-xs mt-1">Immediate action</div>}
                      {priority === 'high' && <div className="text-xs mt-1">Within 3 days</div>}
                      {priority === 'medium' && <div className="text-xs mt-1">Within 1 week</div>}
                      {priority === 'low' && <div className="text-xs mt-1">Within 1 month</div>}
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Due Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Due Date *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="date"
                  value={assignmentData.dueDate}
                  onChange={(e) => setAssignmentData(prev => ({ ...prev, dueDate: e.target.value }))}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    errors.dueDate ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
              </div>
              {errors.dueDate && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  {errors.dueDate}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Suggested due date based on priority level
              </p>
            </div>

            {/* Assignment Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Assignment Notes (Optional)
              </label>
              <textarea
                value={assignmentData.notes}
                onChange={(e) => setAssignmentData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Add any specific instructions or context for the assignee..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
            </div>

            {/* Assignment Summary */}
            {assignmentData.assignedToUserId && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Assignment Summary</h4>
                <div className="text-sm text-blue-800">
                  <p>
                    <strong>{safetyPersonnel.find(p => p.id === assignmentData.assignedToUserId)?.name}</strong> will be assigned this observation
                  </p>
                  {assignmentData.dueDate && (
                    <p>Due: <strong>{new Date(assignmentData.dueDate).toLocaleDateString()}</strong></p>
                  )}
                  <p>Priority: <strong className="capitalize">{assignmentData.priority}</strong></p>
                </div>
              </div>
            )}

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Assign Observation
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ObservationAssignmentModal;
