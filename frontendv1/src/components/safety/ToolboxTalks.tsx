import React, { useState } from 'react';
import { ListChecks, /*Plus,*/ Users, Clock, CheckCircle } from 'lucide-react';
import KPICard from '../dashboard/KPICard';
import SafetyTable from './shared/SafetyTable';
import { ToolboxSessionForm } from '../toolbox/ToolboxSessionForm';
// import { ToolboxSessionList } from '../toolbox/ToolboxSessionList';
import { useWorkers, useToolboxSessions, useCreateToolboxSession } from '../../hooks/useGraphQL';
import { ToolboxSession } from '../../types';

interface ToolboxTalksProps {
	siteId: string;
}

export const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
	const getStatusConfig = () => {
		switch (status) {
			case "scheduled":
				return { className: "bg-blue-100 text-blue-800" };
			case "in-progress":
				return { className: "bg-yellow-100 text-yellow-800" };
			case "completed":
				return { className: "bg-green-100 text-green-800" };
			default:
				return { className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getStatusConfig();
	return (
		<span
			className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${config.className}`}
		>
			{status.replace("-", " ")}
		</span>
	);
};



const ToolboxTalks: React.FC<ToolboxTalksProps> = ({ siteId }) => {
  const [showNewTalkModal, setShowNewTalkModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  // Fetch data using GraphQL hooks
  const { data: workersData, loading: _workersLoading } = useWorkers({siteId, tenantId: ''});
  const { data: sessionsData, loading: sessionsLoading, refetch: refetchSessions } = useToolboxSessions(siteId);
  const { mutate: createToolboxSession } = useCreateToolboxSession();

  const workers = workersData?.workers || [];
  const sessions = sessionsData?.toolboxSessions || [];

  // Get today's session
  const today = new Date().toISOString().split('T')[0];
  const todaySession = sessions.find((session: ToolboxSession) => {
    const sessionDate = new Date(session.sessionTime).toISOString().split('T')[0];
    return sessionDate === today;
  });

	const handleStartNewTalk = () => {
		setShowNewTalkModal(true);
	};

  // const handleTalkCompleted = () => {
  //   setShowNewTalkModal(false);
  //   // Refresh data would happen here
  // };

  const handleSaveToolboxSession = async (sessionData: {
    sessionTime: string;
    topic: string;
    conductor: string;
    notes?: string;
    photoFile?: File;
    attendeeIds: number[];
  }) => {
    try {
      await createToolboxSession({
        input: sessionData
      });

      setShowNewTalkModal(false);
      refetchSessions(); // Refresh the sessions list
    } catch (error) {
      console.error('Failed to save toolbox session:', error);
      alert('Failed to save toolbox session. Please try again.');
    }
  };

  const columns = [
    {
      header: 'Date',
      accessor: 'sessionTime',
      sortable: true,
      renderCell: (session: ToolboxSession) => new Date(session.sessionTime).toLocaleDateString()
    },
    {
      header: 'Time',
      accessor: 'time',
      renderCell: (session: ToolboxSession) => new Date(session.sessionTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    },
    {
      header: 'Topic',
      accessor: 'topic'
    },
    {
      header: 'Conductor',
      accessor: 'conductor'
    },
    {
      header: 'Attendees',
      accessor: 'attendeeCount',
      renderCell: (session: ToolboxSession) => {
        const attended = session.attendances?.filter(a => a.wasPresent).length || 0;
        const total = session.attendances?.length || 0;
        const percentage = total > 0 ? Math.round((attended / total) * 100) : 0;
        return (
          <div className="flex items-center space-x-2">
            <span>{attended}/{total}</span>
            <span className={`text-xs px-2 py-1 rounded-full ${
              percentage >= 90 ? 'bg-green-100 text-green-800' :
              percentage >= 80 ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {percentage}%
            </span>
          </div>
        );
      }
    },
    {
      header: 'Actions',
      accessor: 'actions',
      renderCell: (_session: ToolboxSession) => (
        <button className="text-blue-600 hover:text-blue-800">
          View Details
        </button>
      )
    }
  ];

  // Calculate KPI data from GraphQL sessions
  const totalSessions = sessions.length;
  const completedSessions = sessions.length; // All sessions in the list are completed
  const averageAttendance = sessions.length > 0
    ? Math.round(sessions.reduce((acc: number, session: ToolboxSession) => {
        const attended = session.attendances?.filter(a => a.wasPresent).length || 0;
        const total = session.attendances?.length || 0;
        return acc + (total > 0 ? (attended / total) * 100 : 0);
      }, 0) / sessions.length)
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold">Toolbox Talks</h2>
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          />
        </div>
        <button
          onClick={handleStartNewTalk}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
          disabled={!!todaySession}
        >
          <ListChecks className="h-4 w-4" />
          <span>Start New Toolbox Talk</span>
        </button>
      </div>

      {/* Quick stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <KPICard
          title="Total Sessions"
          value={totalSessions}
          icon={<ListChecks className="h-6 w-6 text-blue-500" />}
        />
        <KPICard
          title="Completed Sessions"
          value={completedSessions}
          icon={<CheckCircle className="h-6 w-6 text-green-500" />}
        />
        <KPICard
          title="Average Attendance"
          value={`${averageAttendance}%`}
          change={averageAttendance >= 90 ? 5 : averageAttendance >= 80 ? 2 : -3}
          icon={<Users className="h-6 w-6 text-purple-500" />}
        />
        <KPICard
          title="Today's Status"
          value={todaySession ? "Completed" : "Pending"}
          icon={<Clock className="h-6 w-6 text-orange-500" />}
        />
      </div>

      {/* Toolbox Session History */}
      <div className="bg-white rounded-lg border">
        <div className="p-6">
          <h3 className="text-lg font-medium mb-4">Toolbox Session History</h3>
          <SafetyTable
            data={sessions}
            columns={columns}
            isLoading={sessionsLoading}
            searchable={true}
            searchPlaceholder="Search toolbox sessions..."
          />
        </div>
      </div>

      {/* Toolbox Session Form */}
      {showNewTalkModal && (
        <ToolboxSessionForm
          workers={workers}
          onSave={handleSaveToolboxSession}
          onCancel={() => setShowNewTalkModal(false)}
        />
      )}
    </div>
  );
};

export default ToolboxTalks;
