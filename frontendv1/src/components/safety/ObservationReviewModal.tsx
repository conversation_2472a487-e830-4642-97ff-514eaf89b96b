import React, { useState } from 'react';
import { X} from 'lucide-react';
import { SafetyObservation, ObservationReview } from './types/safety';

interface ObservationReviewModalProps {
  observation: SafetyObservation;
  isOpen: boolean;
  onClose: () => void;
  onSubmitReview: (review: ObservationReview) => void;
  safetyPersonnel: Array<{ id: string; name: string; role: string }>;
}

const ObservationReviewModal: React.FC<ObservationReviewModalProps> = ({
  observation,
  isOpen,
  onClose,
  onSubmitReview,
  safetyPersonnel }) => {
  const [reviewData, setReviewData] = useState({
    priority: observation.priority || 'medium',
    requiresAction: true,
    recommendedActions: [''],
    assignToId: '',
    dueDate: '',
    reviewNotes: '' });

  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const handleAddRecommendedAction = () => {
    setReviewData(prev => ({
      ...prev,
      recommendedActions: [...prev.recommendedActions, '']
    }));
  };

  const handleRemoveRecommendedAction = (index: number) => {
    setReviewData(prev => ({
      ...prev,
      recommendedActions: prev.recommendedActions.filter((_, i) => i !== index)
    }));
  };

  const handleRecommendedActionChange = (index: number, value: string) => {
    setReviewData(prev => ({
      ...prev,
      recommendedActions: prev.recommendedActions.map((action, i) => 
        i === index ? value : action
      )
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!reviewData.reviewNotes.trim()) {
      newErrors.reviewNotes = 'Review notes are required';
    }

    if (reviewData.requiresAction) {
      if (!reviewData.assignToId) {
        newErrors.assignToId = 'Please assign to a team member when action is required';
      }
      if (!reviewData.dueDate) {
        newErrors.dueDate = 'Due date is required when action is required';
      }
      const validActions = reviewData.recommendedActions.filter(action => action.trim());
      if (validActions.length === 0) {
        newErrors.recommendedActions = 'At least one recommended action is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const assignedPerson = safetyPersonnel.find(p => p.id === reviewData.assignToId);
    
    const review: ObservationReview = {
      observationId: observation.id,
      reviewedBy: 'current-user-id', // In real app, get from auth context
      reviewedByName: 'Current', // In real app, get from auth context
      reviewedAt: new Date().toISOString(),
      priority: reviewData.priority as "low" | "medium" | "high" | "critical",
      requiresAction: reviewData.requiresAction,
      recommendedActions: reviewData.recommendedActions.filter(action => action.trim()),
      assignToUserId: reviewData.requiresAction ? reviewData.assignToId : undefined,
      assignToName: assignedPerson?.name,
      dueDate: reviewData.requiresAction ? reviewData.dueDate : undefined,
      reviewNotes: reviewData.reviewNotes };

    onSubmitReview(review);
    onClose();
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-red-500 text-red-700';
      case 'high': return 'border-orange-500 text-orange-700';
      case 'medium': return 'border-yellow-500 text-yellow-700';
      case 'low': return 'border-green-500 text-green-700';
      default: return 'border-gray-500 text-gray-700';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b px-6 py-4 flex justify-between items-center">
          <h3 className="text-lg font-semibold">Review Safety Observation</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-6">
          {/* Observation Summary */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Observation Details</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Date:</span>
                <span className="ml-2">{new Date(observation.dateOfObservation).toLocaleDateString()}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Time:</span>
                <span className="ml-2">{observation.timeOfObservation}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Location:</span>
                <span className="ml-2">{observation.locationOnSite}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Type:</span>
                <span className="ml-2 capitalize">{observation.observationType.replace('-', ' ')}</span>
              </div>
            </div>
            <div className="mt-3">
              <span className="font-medium text-gray-700">Description:</span>
              <p className="mt-1 text-gray-900">{observation.description}</p>
            </div>
            {observation.actionTakenImmediate && (
              <div className="mt-3">
                <span className="font-medium text-gray-700">Immediate Action Taken:</span>
                <p className="mt-1 text-gray-900">{observation.actionTakenImmediate}</p>
              </div>
            )}
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Priority Assessment */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority Level *
              </label>
              <div className="grid grid-cols-4 gap-3">
                {['low', 'medium', 'high', 'critical'].map((priority) => (
                  <label key={priority} className="cursor-pointer">
                    <input
                      type="radio"
                      name="priority"
                      value={priority}
                      checked={reviewData.priority === priority}
                      onChange={(e) => setReviewData(prev => ({ ...prev, priority: e.target.value as "low" | "medium" | "high" | "critical" }))}
                      className="sr-only"
                    />
                    <div className={`border-2 rounded-lg p-3 text-center transition-colors ${
                      reviewData.priority === priority 
                        ? getPriorityColor(priority) + ' bg-opacity-10' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}>
                      <div className="font-medium capitalize">{priority}</div>
                      {priority === 'critical' && <div className="text-xs mt-1">Immediate action required</div>}
                      {priority === 'high' && <div className="text-xs mt-1">Action within 24 hours</div>}
                      {priority === 'medium' && <div className="text-xs mt-1">Action within 1 week</div>}
                      {priority === 'low' && <div className="text-xs mt-1">Action within 1 month</div>}
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Action Required */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Does this observation require follow-up action?
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="radio"
                    name="requiresAction"
                    checked={reviewData.requiresAction === true}
                    onChange={() => setReviewData(prev => ({ ...prev, requiresAction: true }))}
                    className="text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm">Yes, action required</span>
                </label>
                <label className="flex items-center cursor-pointer">
                  <input
                    type="radio"
                    name="requiresAction"
                    checked={reviewData.requiresAction === false}
                    onChange={() => setReviewData(prev => ({ ...prev, requiresAction: false }))}
                    className="text-green-600 focus:ring-green-500"
                  />
                  <span className="ml-2 text-sm">No action needed</span>
                </label>
              </div>
            </div>

            {/* Conditional Action Fields */}
            {reviewData.requiresAction && (
              <>
                {/* Recommended Actions */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Recommended Actions *
                  </label>
                  {reviewData.recommendedActions.map((action, index) => (
                    <div key={index} className="flex items-center space-x-2 mb-2">
                      <input
                        type="text"
                        value={action}
                        onChange={(e) => handleRecommendedActionChange(index, e.target.value)}
                        placeholder="Describe the recommended action..."
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                      />
                      {reviewData.recommendedActions.length > 1 && (
                        <button
                          type="button"
                          onClick={() => handleRemoveRecommendedAction(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={handleAddRecommendedAction}
                    className="text-green-600 hover:text-green-800 text-sm font-medium"
                  >
                    + Add another action
                  </button>
                  {errors.recommendedActions && (
                    <p className="mt-1 text-sm text-red-600">{errors.recommendedActions}</p>
                  )}
                </div>

                {/* Assignment */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Assign To *
                    </label>
                    <select
                      value={reviewData.assignToId}
                      onChange={(e) => setReviewData(prev => ({ ...prev, assignToId: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        errors.assignToId ? 'border-red-300' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select team member...</option>
                      {safetyPersonnel.map(person => (
                        <option key={person.id} value={person.id}>
                          {person.name} - {person.role}
                        </option>
                      ))}
                    </select>
                    {errors.assignToId && (
                      <p className="mt-1 text-sm text-red-600">{errors.assignToId}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Due Date *
                    </label>
                    <input
                      type="date"
                      value={reviewData.dueDate}
                      onChange={(e) => setReviewData(prev => ({ ...prev, dueDate: e.target.value }))}
                      min={new Date().toISOString().split('T')[0]}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        errors.dueDate ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                    {errors.dueDate && (
                      <p className="mt-1 text-sm text-red-600">{errors.dueDate}</p>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Review Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Review Notes *
              </label>
              <textarea
                value={reviewData.reviewNotes}
                onChange={(e) => setReviewData(prev => ({ ...prev, reviewNotes: e.target.value }))}
                placeholder="Provide your assessment and any additional notes..."
                rows={4}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  errors.reviewNotes ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.reviewNotes && (
                <p className="mt-1 text-sm text-red-600">{errors.reviewNotes}</p>
              )}
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Complete Review
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ObservationReviewModal;
