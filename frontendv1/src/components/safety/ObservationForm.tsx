import React, { useState } from 'react';
import { X, Camera, AlertCircle } from 'lucide-react';
import { SafetyObservation, ObservationType, ObservationCategory } from './types/safety';

interface ObservationFormProps {
  siteId: string;
  onSubmit: (observation: Partial<SafetyObservation>) => void;
  onCancel: () => void;
}

const ObservationForm: React.FC<ObservationFormProps> = ({
  siteId,
  onSubmit,
  onCancel }) => {
  const [formData, setFormData] = useState<Partial<SafetyObservation>>({
    siteId,
    dateOfObservation: new Date().toISOString().split('T')[0],
    timeOfObservation: new Date().toTimeString().slice(0, 5),
    isAnonymous: false,
    status: 'open' });
  
  const [photos, setPhotos] = useState<File[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const siteAreas = [
    'Main Entrance',
    'Building A',
    'Building B', 
    'Parking Area',
    'Storage Area',
    'Equipment Yard',
    'Office Area',
    'Break Room',
    'Other'
  ];

  const observationTypes: { value: ObservationType; label: string; description: string }[] = [
    { value: 'safe-behavior', label: 'Safe Behavior', description: 'Positive safety behavior observed' },
    { value: 'safe-condition', label: 'Safe Condition', description: 'Good safety condition noted' },
    { value: 'at-risk-behavior', label: 'At-Risk Behavior', description: 'Unsafe behavior that could lead to injury' },
    { value: 'at-risk-condition', label: 'At-Risk Condition', description: 'Unsafe condition that needs attention' },
  ];

  const categories: { value: ObservationCategory; label: string }[] = [
    { value: 'ppe', label: 'Personal Protective Equipment' },
    { value: 'housekeeping', label: 'Housekeeping' },
    { value: 'equipment-use', label: 'Equipment Use' },
    { value: 'procedure', label: 'Procedures' },
    { value: 'other', label: 'Other' },
  ];

  const handleInputChange = (field: keyof SafetyObservation, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePhoto= (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (photos.length + files.length > 5) {
      setErrors(prev => ({ ...prev, photos: 'Maximum 5 photos allowed' }));
      return;
    }
    setPhotos(prev => [...prev, ...files]);
    setErrors(prev => ({ ...prev, photos: '' }));
  };

  const removePhoto = (index: number) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.observationType) {
      newErrors.observationType = 'Please select what type of observation this is';
    }
    if (!formData.locationOnSite?.trim()) {
      newErrors.locationOnSite = 'Please specify where this was observed';
    }
    if (!formData.description?.trim()) {
      newErrors.description = 'Please describe what you observed';
    }
    if (!formData.category) {
      newErrors.category = 'Please select a category';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // In a real implementation, photos would be uploaded to a service
      const photoUrls = photos.map((_, index) => `photo_${Date.now()}_${index}.jpg`);
      
      const observationData: Partial<SafetyObservation> = {
        ...formData,
        photoUrls: photoUrls.length > 0 ? photoUrls : undefined,
        createdAt: new Date().toISOString() };

      onSubmit(observationData);
    } catch (error) {
      console.error('Error submitting observation:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b px-6 py-4 flex justify-between items-center">
          <h3 className="text-lg font-semibold">Log Safety Observation</h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Observation *
              </label>
              <input
                type="date"
                value={formData.dateOfObservation || ''}
                onChange={(e) => handleInputChange('dateOfObservation', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time of Observation *
              </label>
              <input
                type="time"
                value={formData.timeOfObservation || ''}
                onChange={(e) => handleInputChange('timeOfObservation', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                required
              />
            </div>
          </div>

          {/* Site Area */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Site Area
            </label>
            <select
              value={formData.siteArea || ''}
              onChange={(e) => handleInputChange('siteArea', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">Select site area...</option>
              {siteAreas.map(area => (
                <option key={area} value={area}>{area}</option>
              ))}
            </select>
          </div>

          {/* Observation Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type (What did you observe?) *
            </label>
            <div className="space-y-2">
              {observationTypes.map(type => (
                <label key={type.value} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="observationType"
                    value={type.value}
                    checked={formData.observationType === type.value}
                    onChange={(e) => handleInputChange('observationType', e.target.value)}
                    className="mt-1 text-green-600 focus:ring-green-500"
                  />
                  <div>
                    <div className="font-medium text-gray-900">{type.label}</div>
                    <div className="text-sm text-gray-500">{type.description}</div>
                  </div>
                </label>
              ))}
            </div>
            {errors.observationType && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.observationType}
              </p>
            )}
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Specific Location (Where exactly was this observed?) *
            </label>
            <input
              type="text"
              value={formData.locationOnSite || ''}
              onChange={(e) => handleInputChange('locationOnSite', e.target.value)}
              placeholder="e.g., Near the main crane, Second floor stairwell, Loading dock area..."
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                errors.locationOnSite ? 'border-red-300' : 'border-gray-300'
              }`}
              required
            />
            {errors.locationOnSite && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.locationOnSite}
              </p>
            )}
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category *
            </label>
            <select
              value={formData.category || ''}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                errors.category ? 'border-red-300' : 'border-gray-300'
              }`}
              required
            >
              <option value="">Select category...</option>
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>{cat.label}</option>
              ))}
            </select>
            {errors.category && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.category}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              What (Describe the issue or opportunity to improve safety) *
            </label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Provide a detailed description of what you observed..."
              rows={4}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              required
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.description}
              </p>
            )}
          </div>

          {/* Action Taken */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Action (What action did you take immediately?)
            </label>
            <textarea
              value={formData.actionTakenImmediate || ''}
              onChange={(e) => handleInputChange('actionTakenImmediate', e.target.value)}
              placeholder="Describe any immediate actions you took..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>

          {/* Recommended Actions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              What else should be done?
            </label>
            <textarea
              value={formData.recommendedAction || ''}
              onChange={(e) => handleInputChange('recommendedAction', e.target.value)}
              placeholder="Suggest additional actions or follow-up required..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>

          {/* Photo*/}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Photos (Optional - up to 5 photos)
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center hover:border-gray-400 transition-colors">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handlePhoto}
                className="hidden"
                id="photo-upload"
              />
              <label htmlFor="photo-upload" className="cursor-pointer">
                <Camera className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Click to upload photos</p>
                <p className="text-xs text-gray-500 mt-1">Max 5 photos, 10MB each</p>
              </label>
            </div>
            
            {photos.length > 0 && (
              <div className="mt-3 grid grid-cols-2 gap-2">
                {photos.map((photo, index) => (
                  <div key={index} className="relative bg-gray-100 rounded-md p-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 truncate">{photo.name}</span>
                      <button
                        type="button"
                        onClick={() => removePhoto(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {errors.photos && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.photos}
              </p>
            )}
          </div>

          {/* Reporter Name (Optional) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Your Name (Optional)
            </label>
            <input
              type="text"
              value={formData.reporterName || ''}
              onChange={(e) => handleInputChange('reporterName', e.target.value)}
              placeholder="Leave blank to submit anonymously"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>

          {/* Anonymous Checkbox */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="anonymous"
              checked={formData.isAnonymous || false}
              onChange={(e) => handleInputChange('isAnonymous', e.target.checked)}
              className="text-green-600 focus:ring-green-500"
            />
            <label htmlFor="anonymous" className="text-sm text-gray-700">
              Submit this observation anonymously
            </label>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Observation'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ObservationForm;
