import React, { useState } from 'react';
import { Alert<PERSON>rian<PERSON>, Eye } from 'lucide-react';
import KPICard from '../dashboard/KPICard';
import SafetyTable from './shared/SafetyTable';
import { useSafetyData } from './hooks/useSafetyData';
// import { useSafetyFilters } from './hooks/useSafetyFilters';
import { Incident, IncidentType, IncidentSeverity } from './types/safety';

interface IncidentManagementProps {
	siteId: string;
}

const IncidentTypeBadge: React.FC<{ type: IncidentType }> = ({ type }) => {
	const getTypeConfig = () => {
		switch (type) {
			case "accident-with-injury":
				return {
					label: "Accident w/ Injury",
					className: "bg-red-100 text-red-800",
				};
			case "near-miss":
				return {
					label: "Near Miss",
					className: "bg-yellow-100 text-yellow-800",
				};
			case "property-damage":
				return {
					label: "Property Damage",
					className: "bg-orange-100 text-orange-800",
				};
			case "environmental":
				return {
					label: "Environmental",
					className: "bg-green-100 text-green-800",
				};
			case "first-aid-only":
				return {
					label: "First Aid Only",
					className: "bg-blue-100 text-blue-800",
				};
			default:
				return { label: type, className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getTypeConfig();
	return (
		<span
			className={`px-2 py-1 text-xs font-medium rounded-full ${config.className}`}
		>
			{config.label}
		</span>
	);
};

const SeverityBadge: React.FC<{ severity: IncidentSeverity }> = ({
	severity,
}) => {
	const getSeverityConfig = () => {
		switch (severity) {
			case "critical":
				return { className: "bg-red-100 text-red-800" };
			case "high":
				return { className: "bg-orange-100 text-orange-800" };
			case "medium":
				return { className: "bg-yellow-100 text-yellow-800" };
			case "low":
				return { className: "bg-green-100 text-green-800" };
			default:
				return { className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getSeverityConfig();
	return (
		<span
			className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${config.className}`}
		>
			{severity}
		</span>
	);
};

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
	const getStatusConfig = () => {
		switch (status) {
			case "reported":
				return { className: "bg-blue-100 text-blue-800" };
			case "investigating":
				return { className: "bg-yellow-100 text-yellow-800" };
			case "pending-capa":
				return { className: "bg-orange-100 text-orange-800" };
			case "closed":
				return { className: "bg-green-100 text-green-800" };
			case "draft":
				return { className: "bg-gray-100 text-gray-800" };
			default:
				return { className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getStatusConfig();
	return (
		<span
			className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${config.className}`}
		>
			{status.replace("-", " ")}
		</span>
	);
};

const IncidentManagement: React.FC<IncidentManagementProps> = ({ siteId }) => {
	const [showReportModal, setShowReportModal] = useState(false);
	const [selectedIncident, setSelectedIncident] = useState<Incident | null>(
		null,
	);
	const { data: incidents, isLoading } = useSafetyData(siteId, "incidents");
	// const { filters, updateFilter, clearFilters } = useSafetyFilters();

	const handleReportIncident = () => {
		setShowReportModal(true);
	};

	const handleViewIncident = (incident: Incident) => {
		setSelectedIncident(incident);
	};

	const columns = [
		{
			header: "ID",
			accessor: "id",
			sortable: true,
			renderCell: (incident: Incident) => (
				<span className="font-mono text-xs">{incident.id.slice(-6)}</span>
			),
		},
		{
			header: "Date",
			accessor: "dateOfIncident",
			sortable: true,
			renderCell: (incident: Incident) =>
				new Date(incident.dateOfIncident).toLocaleDateString(),
		},
		{
			header: "Type",
			accessor: "incidentType",
			renderCell: (incident: Incident) => (
				<IncidentTypeBadge type={incident.incidentType} />
			),
		},
		{
			header: "Severity",
			accessor: "severity",
			renderCell: (incident: Incident) => (
				<SeverityBadge severity={incident.severity} />
			),
		},
		{
			header: "Location",
			accessor: "locationOnSite",
		},
		{
			header: "Status",
			accessor: "status",
			renderCell: (incident: Incident) => (
				<StatusBadge status={incident.status} />
			),
		},
		{
			header: "Reported By",
			accessor: "reportedByName",
		},
		{
			header: "Actions",
			accessor: "actions",
			renderCell: (incident: Incident) => (
				<button
					onClick={() => handleViewIncident(incident)}
					className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
				>
					<Eye className="h-4 w-4" />
					<span>View</span>
				</button>
			),
		},
	];

	// Calculate KPI data
	const openIncidents =
		incidents?.filter((i: Incident) => i.status !== "closed").length || 0;
	const highSeverityIncidents =
		incidents?.filter(
			(i: Incident) => i.severity === "high" || i.severity === "critical",
		).length || 0;
	const pendingInvestigation =
		incidents?.filter((i: Incident) => i.status === "reported").length || 0;
	const overdueCAPAs =
		incidents
			?.flatMap((i: Incident) => i.capas || [])
			.filter(
				(c: { dueDate: string | number | Date; status: string; }) => new Date(c.dueDate) < new Date() && c.status !== "completed",
			).length || 0;

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Incident Management</h2>
				<button
					onClick={handleReportIncident}
					className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex items-center space-x-2"
				>
					<AlertTriangle className="h-4 w-4" />
					<span>Report Incident</span>
				</button>
			</div>

      {/* Quick stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <KPICard
          title="Open Incidents"
          value={openIncidents}
          icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
        />
        <KPICard
          title="High Severity"
          value={highSeverityIncidents}
          icon={<AlertTriangle className="h-6 w-6 text-orange-500" />}
        />
        <KPICard
          title="Pending Investigation"
          value={pendingInvestigation}
          icon={<Eye className="h-6 w-6 text-blue-500" />}
        />
        <KPICard
          title="Overdue CAPAs"
          value={overdueCAPAs}
          icon={<AlertTriangle className="h-6 w-6 text-purple-500" />}
        />
      </div>

			{/* Incidents table */}
			<div className="bg-white rounded-lg border">
				<div className="p-6">
					<h3 className="text-lg font-medium mb-4">Incident Log</h3>
					<SafetyTable
						data={incidents || []}
						columns={columns}
						isLoading={isLoading}
						searchable={true}
						searchPlaceholder="Search incidents..."
					/>
				</div>
			</div>

			{/* Modals would go here */}
			{showReportModal && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
						<h3 className="text-lg font-semibold mb-4">Report Incident</h3>
						<p className="text-gray-600 mb-4">
							Incident reporting form will be implemented here.
						</p>
						<div className="flex justify-end space-x-3">
							<button
								onClick={() => setShowReportModal(false)}
								className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
							>
								Cancel
							</button>
							<button
								onClick={() => setShowReportModal(false)}
								className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
							>
								Submit Report
							</button>
						</div>
					</div>
				</div>
			)}

			{selectedIncident && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
						<h3 className="text-lg font-semibold mb-4">Incident Details</h3>
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700">
									Description
								</label>
								<p className="mt-1 text-sm text-gray-900">
									{selectedIncident.description}
								</p>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Date
									</label>
									<p className="mt-1 text-sm text-gray-900">
										{selectedIncident.dateOfIncident}
									</p>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Time
									</label>
									<p className="mt-1 text-sm text-gray-900">
										{selectedIncident.timeOfIncident}
									</p>
								</div>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700">
									Location
								</label>
								<p className="mt-1 text-sm text-gray-900">
									{selectedIncident.locationOnSite}
								</p>
							</div>
						</div>
						<div className="flex justify-end mt-6">
							<button
								onClick={() => setSelectedIncident(null)}
								className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
							>
								Close
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default IncidentManagement;
