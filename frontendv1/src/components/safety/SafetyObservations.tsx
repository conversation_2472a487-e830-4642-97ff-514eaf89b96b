import React, { useState } from 'react';
import { MessageSquare, Eye, ThumbsUp, AlertTriangle, Camera, Search, Plus, UserPlus, CheckCircle} from 'lucide-react';
import KPICard from '../dashboard/KPICard';

import { useSafetyData } from './hooks/useSafetyData';
// import { useSafetyFilters } from './hooks/useSafetyFilters';
import { SafetyObservation, ObservationType, ObservationReview, ObservationAssignment } from './types/safety';
import ObservationForm from './ObservationForm';
import ObservationReviewModal from './ObservationReviewModal';
import ObservationAssignmentModal from './ObservationAssignmentModal';

interface SafetyObservationsProps {
	siteId: string;
}

const ObservationTypeBadge: React.FC<{ type: ObservationType }> = ({
	type }) => {
	const getTypeConfig = () => {
		switch (type) {
			case "safe-behavior":
				return {
					label: "Safe Behavior",
					className: "bg-green-100 text-green-800",
					icon: <ThumbsUp className="h-3 w-3" /> };
			case "safe-condition":
				return {
					label: "Safe Condition",
					className: "bg-blue-100 text-blue-800",
					icon: <ThumbsUp className="h-3 w-3" /> };
			case "at-risk-behavior":
				return {
					label: "At-Risk Behavior",
					className: "bg-orange-100 text-orange-800",
					icon: <AlertTriangle className="h-3 w-3" /> };
			case "at-risk-condition":
				return {
					label: "At-Risk Condition",
					className: "bg-red-100 text-red-800",
					icon: <AlertTriangle className="h-3 w-3" /> };
			default:
				return {
					label: type,
					className: "bg-gray-100 text-gray-800",
					icon: null };
		}
	};

	const config = getTypeConfig();
	return (
		<span
			className={`px-2 py-1 text-xs font-medium rounded-full flex items-center space-x-1 ${config.className}`}
		>
			{config.icon}
			<span>{config.label}</span>
		</span>
	);
};

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
	const getStatusConfig = () => {
		switch (status) {
			case "open":
				return { className: "bg-blue-100 text-blue-800" };
			case "reviewed":
				return { className: "bg-yellow-100 text-yellow-800" };
			case "actioned":
				return { className: "bg-orange-100 text-orange-800" };
			case "closed":
				return { className: "bg-green-100 text-green-800" };
			default:
				return { className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getStatusConfig();
	return (
		<span
			className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${config.className}`}
		>
			{status}
		</span>
	);
};

const SafetyObservations: React.FC<SafetyObservationsProps> = ({ siteId }) => {
	const [showObservationModal, setShowObservationModal] = useState(false);
	const [selectedObservation, setSelectedObservation] =
		useState<SafetyObservation | null>(null);
	const [showReviewModal, setShowReviewModal] = useState(false);
	const [showAssignModal, setShowAssignModal] = useState(false);
	const [observationForReview, setObservationForReview] = useState<SafetyObservation | null>(null);
	const [observationForAssign, setObservationForAssign] = useState<SafetyObservation | null>(null);
	const { data: observations, isLoading } = useSafetyData(
		siteId,
		"observations",
	);
	// const { filters, updateFilter, clearFilters } = useSafetyFilters();

	// Mock safety personnel for assignment
	const safetyPersonnel = [
		{ id: 'safety1', name: 'Sarah Wilson', role: 'Safety Manager' },
		{ id: 'safety2', name: 'Mike Johnson', role: 'Safety Officer' },
		{ id: 'safety3', name: 'Lisa Chen', role: 'Safety Coordinator' },
		{ id: 'safety4', name: 'David Brown', role: 'HSE Specialist' },
	];

	const handleLogObservation = () => {
		setShowObservationModal(true);
	};

	const handleObservationSubmit = (observationData: Partial<SafetyObservation>) => {
		console.log('Submitting observation:', observationData);
		// In a real implementation, this would call an API to save the observation
		setShowObservationModal(false);
		// Refresh data would happen here
	};

	const handleUpdateObservation = (observationId: string, updates: Partial<SafetyObservation>) => {
		console.log('Updating observation:', observationId, updates);
		// In a real implementation, this would call an API to update the observation
		// For now, we'll just log it
	};

	const handleAssignObservation = (assignment: ObservationAssignment) => {
		console.log('Assigning observation:', assignment);
		// In a real implementation, this would call an API to create the assignment
		// Update the observation status and assignment details
		handleUpdateObservation(assignment.observationId, {
			status: 'assigned',
			assignedTo: assignment.assignedToUserId,
			assignedToName: assignment.assignedToName,
			assignedAt: assignment.assignedAt,
			assignedBy: assignment.assignedByUserId,
			priority: assignment.priority });
		setShowAssignModal(false);
		setObservationForAssign(null);
	};

	const handleReviewObservation = (review: ObservationReview) => {
		console.log('Reviewing observation:', review);
		// In a real implementation, this would call an API to save the review
		// Update the observation with review details
		const updates: Partial<SafetyObservation> = {
			status: review.requiresAction ? 'under-review' : 'completed',
			reviewedBy: review.reviewedBy,
			reviewedAt: review.reviewedAt,
			reviewNotes: review.reviewNotes,
			priority: review.priority };

		// If action is required and assignment is made, update assignment details
		if (review.requiresAction && review.assignToUserId) {
			updates.assignedTo = review.assignToUserId;
			updates.assignedToName = review.assignToName;
			updates.assignedAt = new Date().toISOString();
			updates.assignedBy = review.reviewedBy;
			updates.status = 'assigned';
		}

		handleUpdateObservation(review.observationId, updates);
		setShowReviewModal(false);
		setObservationForReview(null);
	};

	const handleQuickAction = (observation: SafetyObservation, action: string) => {
		switch (action) {
			case 'review':
				setObservationForReview(observation);
				setShowReviewModal(true);
				break;
			case 'assign':
				setObservationForAssign(observation);
				setShowAssignModal(true);
				break;
			case 'complete':
				handleUpdateObservation(observation.id, {
					status: 'completed',
					actionCompletedAt: new Date().toISOString(),
					actionCompletedBy: 'current-user' // In real app, get from auth context
				});
				break;
			case 'close':
				handleUpdateObservation(observation.id, {
					status: 'closed',
					updatedAt: new Date().toISOString()
				});
				break;
		}
	};

	const getAvailableActions = (observation: SafetyObservation) => {
		const actions = [];

		switch (observation.status) {
			case 'open':
				actions.push(
					{ key: 'review', label: 'Review', icon: Eye, color: 'blue' },
					{ key: 'assign', label: 'Assign', icon: UserPlus, color: 'purple' }
				);
				break;
			case 'under-review':
				actions.push(
					{ key: 'assign', label: 'Assign', icon: UserPlus, color: 'purple' }
				);
				break;
			case 'assigned':
			case 'in-progress':
				actions.push(
					{ key: 'complete', label: 'Complete', icon: CheckCircle, color: 'green' }
				);
				break;
			case 'completed':
				actions.push(
					{ key: 'close', label: 'Close', icon: CheckCircle, color: 'gray' }
				);
				break;
		}

		return actions;
	};

	const handleViewObservation = (observation: SafetyObservation) => {
		setSelectedObservation(observation);
	};

	const columns = [
		{
			header: "Date/Time",
			accessor: "dateOfObservation",
			sortable: true,
			renderCell: (obs: SafetyObservation) => (
				<div className="text-sm">
					<div className="font-medium">
						{new Date(obs.dateOfObservation).toLocaleDateString()}
					</div>
					<div className="text-gray-500 text-xs">
						{obs.timeOfObservation}
					</div>
				</div>
			) },
		{
			header: "Type & Priority",
			accessor: "observationType",
			renderCell: (obs: SafetyObservation) => (
				<div className="space-y-1">
					<ObservationTypeBadge type={obs.observationType} />
					<div className="text-xs text-gray-500 capitalize">
						{obs.category.replace("-", " ")}
					</div>
					{obs.priority && (
						<span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${
							obs.priority === 'critical' ? 'bg-red-100 text-red-800 border-red-200' :
							obs.priority === 'high' ? 'bg-orange-100 text-orange-800 border-orange-200' :
							obs.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
							'bg-green-100 text-green-800 border-green-200'
						}`}>
							{obs.priority}
						</span>
					)}
				</div>
			) },
		{
			header: "Location",
			accessor: "locationOnSite",
			renderCell: (obs: SafetyObservation) => (
				<div className="text-sm">
					{obs.siteArea && (
						<div className="font-medium text-gray-700 text-xs">
							{obs.siteArea}
						</div>
					)}
					<div className="text-gray-900">
						{obs.locationOnSite}
					</div>
				</div>
			) },
		{
			header: "Description & Actions",
			accessor: "description",
			renderCell: (obs: SafetyObservation) => (
				<div className="max-w-sm space-y-1">
					<div className="text-sm text-gray-900" title={obs.description}>
						<div className="line-clamp-2 overflow-hidden">
							{obs.description.length > 100
								? `${obs.description.substring(0, 100)}...`
								: obs.description}
						</div>
					</div>
					{obs.actionTakenImmediate && (
						<div className="text-xs text-green-700 bg-green-50 px-2 py-1 rounded">
							Action: {obs.actionTakenImmediate.length > 50
								? `${obs.actionTakenImmediate.substring(0, 50)}...`
								: obs.actionTakenImmediate}
						</div>
					)}
					{obs.recommendedAction && (
						<div className="text-xs text-blue-700 bg-blue-50 px-2 py-1 rounded">
							Recommended: {obs.recommendedAction.length > 50
								? `${obs.recommendedAction.substring(0, 50)}...`
								: obs.recommendedAction}
						</div>
					)}
				</div>
			) },
		{
			header: "Reporter & Assignment",
			accessor: "reporterName",
			renderCell: (obs: SafetyObservation) => (
				<div className="text-sm space-y-1">
					<div>
						{obs.isAnonymous ? (
							<span className="text-gray-500 italic">Anonymous</span>
						) : obs.reporterName ? (
							<span className="text-gray-900">{obs.reporterName}</span>
						) : (
							<span className="text-gray-500 italic">Not provided</span>
						)}
					</div>
					{obs.assignedToName && (
						<div className="text-xs text-purple-700 bg-purple-50 px-2 py-1 rounded">
							Assigned to: {obs.assignedToName}
						</div>
					)}
					{obs.photoUrls && obs.photoUrls.length > 0 && (
						<div className="text-xs text-gray-500 flex items-center">
							<Camera className="h-3 w-3 mr-1" />
							{obs.photoUrls.length} photo{obs.photoUrls.length > 1 ? 's' : ''}
						</div>
					)}
				</div>
			) },
		{
			header: "Status",
			accessor: "status",
			renderCell: (obs: SafetyObservation) => (
				<div className="space-y-1">
					<StatusBadge status={obs.status} />
					{obs.reviewedAt && (
						<div className="text-xs text-gray-500">
							Reviewed {new Date(obs.reviewedAt).toLocaleDateString()}
						</div>
					)}
					{obs.assignedAt && (
						<div className="text-xs text-gray-500">
							Assigned {new Date(obs.assignedAt).toLocaleDateString()}
						</div>
					)}
				</div>
			) },
		{
			header: "Actions",
			accessor: "actions",
			renderCell: (obs: SafetyObservation) => {
				const availableActions = getAvailableActions(obs);
				return (
					<div className="flex flex-col space-y-1">
						{/* View button - always available */}
						<button
							onClick={() => handleViewObservation(obs)}
							className="text-blue-600 hover:text-blue-800 flex items-center space-x-1 text-xs"
						>
							<Eye className="h-3 w-3" />
							<span>View</span>
						</button>

						{/* Management action buttons */}
						{availableActions.map((action) => (
							<button
								key={action.key}
								onClick={() => handleQuickAction(obs, action.key)}
								className={`flex items-center space-x-1 text-xs font-medium hover:opacity-80 ${
									action.color === 'blue' ? 'text-blue-600 hover:text-blue-800' :
									action.color === 'purple' ? 'text-purple-600 hover:text-purple-800' :
									action.color === 'green' ? 'text-green-600 hover:text-green-800' :
									'text-gray-600 hover:text-gray-800'
								}`}
							>
								<action.icon className="h-3 w-3" />
								<span>{action.label}</span>
							</button>
						))}
					</div>
				);
			} },
	];

	// Calculate KPI data
	const totalObservations = observations?.length || 0;
	const safeBehaviors =
		observations?.filter((o: SafetyObservation) =>
			o.observationType.includes("safe"),
		).length || 0;
	const atRiskBehaviors =
		observations?.filter((o: SafetyObservation) =>
			o.observationType.includes("at-risk"),
		).length || 0;
	const pendingReview =
		observations?.filter((o: SafetyObservation) => o.status === "open")
			.length || 0;

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Safety Observations</h2>
				<button
					onClick={handleLogObservation}
					className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2"
				>
					<Plus className="h-4 w-4" />
					<span>Log Observation</span>
				</button>
			</div>

				{/* Quick stats */}
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<KPICard
						title="Total Observations"
						value={totalObservations}
						icon={<MessageSquare className="h-6 w-6 text-blue-500" />}
					/>
					<KPICard
						title="Safe Behaviors"
						value={safeBehaviors}
						icon={<ThumbsUp className="h-6 w-6 text-green-500" />}
					/>
					<KPICard
						title="At-Risk Behaviors"
						value={atRiskBehaviors}
						icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
					/>
					<KPICard
						title="Pending Review"
						value={pendingReview}
						icon={<Eye className="h-6 w-6 text-orange-500" />}
					/>
				</div>

			{/* Observations table */}
			<div className="bg-white rounded-lg border">
				<div className="p-6">
					<h3 className="text-lg font-medium mb-4">Observation Log</h3>
					<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
						{/* Search */}
						<div className="p-4 border-b border-gray-200">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
								<input
									type="text"
									placeholder="Search observations..."
									className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
								/>
							</div>
						</div>

						{/* Compact Table */}
						<div className="overflow-x-auto">
							<table className="min-w-full divide-y divide-gray-200">
								<thead className="bg-gray-50">
									<tr>
										{columns.map((column) => (
											<th
												key={column.accessor}
												className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
											>
												{column.header}
											</th>
										))}
									</tr>
								</thead>
								<tbody className="bg-white divide-y divide-gray-200">
									{isLoading ? (
										[...Array(3)].map((_, i) => (
											<tr key={i}>
												{columns.map((column) => (
													<td key={column.accessor} className="px-3 py-3">
														<div className="animate-pulse bg-gray-200 h-4 rounded"></div>
													</td>
												))}
											</tr>
										))
									) : observations && observations.length > 0 ? (
										observations.map((obs: SafetyObservation) => (
											<tr key={obs.id} className="hover:bg-gray-50">
												{columns.map((column) => (
													<td
														key={column.accessor}
														className="px-3 py-3 text-sm align-top"
													>
														{column.renderCell
															? column.renderCell(obs)
															: obs[column.accessor as keyof SafetyObservation]}
													</td>
												))}
											</tr>
										))
									) : (
										<tr>
											<td
												colSpan={columns.length}
												className="px-6 py-12 text-center text-gray-500"
											>
												No observations found
											</td>
										</tr>
									)}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>

			{/* Log observation modal */}
			{showObservationModal && (
				<ObservationForm
					siteId={siteId}
					onSubmit={handleObservationSubmit}
					onCancel={() => setShowObservationModal(false)}
				/>
			)}

			{/* Review Modal */}
			{showReviewModal && observationForReview && (
				<ObservationReviewModal
					observation={observationForReview}
					isOpen={showReviewModal}
					onClose={() => {
						setShowReviewModal(false);
						setObservationForReview(null);
					}}
					onSubmitReview={handleReviewObservation}
					safetyPersonnel={safetyPersonnel}
				/>
			)}

			{/* Assignment Modal */}
			{showAssignModal && observationForAssign && (
				<ObservationAssignmentModal
					observation={observationForAssign}
					isOpen={showAssignModal}
					onClose={() => {
						setShowAssignModal(false);
						setObservationForAssign(null);
					}}
					onSubmitAssignment={handleAssignObservation}
					safetyPersonnel={safetyPersonnel}
				/>
			)}

			{/* View observation modal */}
			{selectedObservation && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
						<h3 className="text-lg font-semibold mb-4">Observation Details</h3>
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700">
									Type
								</label>
								<div className="mt-1">
									<ObservationTypeBadge
										type={selectedObservation.observationType}
									/>
								</div>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700">
									Description
								</label>
								<p className="mt-1 text-sm text-gray-900">
									{selectedObservation.description}
								</p>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Date
									</label>
									<p className="mt-1 text-sm text-gray-900">
										{selectedObservation.dateOfObservation}
									</p>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Time
									</label>
									<p className="mt-1 text-sm text-gray-900">
										{selectedObservation.timeOfObservation}
									</p>
								</div>
							</div>
							{selectedObservation.siteArea && (
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Site Area
									</label>
									<p className="mt-1 text-sm text-gray-900">
										{selectedObservation.siteArea}
									</p>
								</div>
							)}
							<div>
								<label className="block text-sm font-medium text-gray-700">
									Location
								</label>
								<p className="mt-1 text-sm text-gray-900">
									{selectedObservation.locationOnSite}
								</p>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700">
									Category
								</label>
								<p className="mt-1 text-sm text-gray-900 capitalize">
									{selectedObservation.category.replace("-", " ")}
								</p>
							</div>
							{selectedObservation.actionTakenImmediate && (
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Immediate Action Taken
									</label>
									<p className="mt-1 text-sm text-gray-900">
										{selectedObservation.actionTakenImmediate}
									</p>
								</div>
							)}
							{selectedObservation.recommendedAction && (
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Recommended Actions
									</label>
									<p className="mt-1 text-sm text-gray-900">
										{selectedObservation.recommendedAction}
									</p>
								</div>
							)}
							{selectedObservation.photoUrls && selectedObservation.photoUrls.length > 0 && (
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Photos
									</label>
									<div className="mt-2 grid grid-cols-2 gap-2">
										{selectedObservation.photoUrls.map((_, index) => (
											<div key={index} className="bg-gray-100 rounded-md p-2">
												<p className="text-sm text-gray-600">Photo {index + 1}</p>
												{/* In a real implementation, this would show the actual image */}
												<div className="h-20 bg-gray-200 rounded flex items-center justify-center">
													<span className="text-xs text-gray-500">Image Preview</span>
												</div>
											</div>
										))}
									</div>
								</div>
							)}
							{!selectedObservation.isAnonymous && selectedObservation.reporterName && (
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Reported By
									</label>
									<p className="mt-1 text-sm text-gray-900">
										{selectedObservation.reporterName}
									</p>
								</div>
							)}
							{selectedObservation.isAnonymous && (
								<div>
									<label className="block text-sm font-medium text-gray-700">
										Submission Type
									</label>
									<p className="mt-1 text-sm text-gray-900">
										Anonymous Submission
									</p>
								</div>
							)}
						</div>
						<div className="flex justify-end mt-6">
							<button
								onClick={() => setSelectedObservation(null)}
								className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
							>
								Close
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default SafetyObservations;
