import React from 'react';
import { AlertTriangle, ListChe<PERSON>, MessageSquare, Clock, TrendingDown, Activity } from 'lucide-react';
import KPICard from '../dashboard/KPICard';
import { useSafetyData } from './hooks/useSafetyData';

interface SafetyDashboardProps {
	siteId: string;
	onNavigateToTab?: (tabId: string) => void;
}

interface QuickActionCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
}

const QuickActionCard: React.FC<QuickActionCardProps> = ({
  title,
  description,
  icon,
  action
}) => (
  <div
    className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:border-gray-300"
    onClick={action}
  >
    <div className="flex items-start space-x-4">
      <div className="flex-shrink-0">
        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center text-green-600">
          {icon}
        </div>
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="text-lg font-semibold text-gray-900 mb-1">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      <div className="flex-shrink-0">
        <svg
          className="w-5 h-5 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </div>
    </div>
  </div>
);

const RecentActivityItem: React.FC<{ activity: any }> = ({ activity }) => {
	const getActivityIcon = () => {
		switch (activity.type) {
			case "incident":
				return <AlertTriangle className="h-4 w-4 text-red-500" />;
			case "toolbox-talk":
				return <ListChecks className="h-4 w-4 text-blue-500" />;
			case "observation":
				return <MessageSquare className="h-4 w-4 text-green-500" />;
			case "capa":
				return <Clock className="h-4 w-4 text-orange-500" />;
			default:
				return <Activity className="h-4 w-4 text-gray-500" />;
		}
	};

	const formatTime = (timestamp: string) => {
		const date = new Date(timestamp);
		return date.toLocaleString("en-GB", {
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	return (
		<div className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg">
			<div className="flex-shrink-0 mt-1">{getActivityIcon()}</div>
			<div className="flex-1 min-w-0">
				<p className="text-sm font-medium text-gray-900">{activity.title}</p>
				<p className="text-sm text-gray-600">{activity.description}</p>
				<p className="text-xs text-gray-500 mt-1">
					{formatTime(activity.timestamp)}
				</p>
			</div>
		</div>
	);
};

const SafetyDashboard: React.FC<SafetyDashboardProps> = ({
	siteId,
	onNavigateToTab,
}) => {
	const { data: dashboardData, isLoading } = useSafetyData(siteId, "dashboard");

  const quickActions = [
    {
      title: 'Report Incident',
      description: 'Report a new safety incident',
      icon: <AlertTriangle className="h-6 w-6" />,
      action: () => onNavigateToTab?.('incidents')
    },
    {
      title: 'Start Toolbox Talk',
      description: 'Begin daily safety briefing',
      icon: <ListChecks className="h-6 w-6" />,
      action: () => onNavigateToTab?.('toolbox-talks')
    },
    {
      title: 'Log Observation',
      description: 'Record safety observation',
      icon: <MessageSquare className="h-6 w-6" />,
      action: () => onNavigateToTab?.('observations')
    }
  ];

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					{[...Array(4)].map((_, i) => (
						<div
							key={i}
							className="bg-gray-200 animate-pulse h-32 rounded-lg"
						></div>
					))}
				</div>
				<div className="bg-gray-200 animate-pulse h-64 rounded-lg"></div>
			</div>
		);
	}

  return (
    <div className="space-y-6">
      {/* Safety KPI Cards - Matching Time & Permits Design */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <KPICard
          title="Total Incidents (30d)"
          value={dashboardData?.totalIncidents || 0}
          change={dashboardData?.incidentTrend === 'down' ? -5 : dashboardData?.incidentTrend === 'up' ? 8 : 0}
          icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
        />
        <KPICard
          title="Lost Time Incidents"
          value={dashboardData?.lostTimeIncidents || 0}
          icon={<Clock className="h-6 w-6 text-orange-500" />}
        />
        <KPICard
          title="Safety Observations"
          value={dashboardData?.safetyObservations || 24}
          change={12}
          icon={<MessageSquare className="h-6 w-6 text-blue-500" />}
        />
        <KPICard
          title="Overdue Actions"
          value={dashboardData?.overdueActions || 0}
          icon={<TrendingDown className="h-6 w-6 text-purple-500" />}
        />
      </div>

			{/* Quick Actions */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				{quickActions.map((action, index) => (
					<QuickActionCard key={index} {...action} />
				))}
			</div>

			{/* Recent Activity and Upcoming Deadlines */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Recent Activity */}
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
					<div className="p-6 border-b border-gray-200">
						<h3 className="text-lg font-semibold text-gray-900">
							Recent Safety Activity
						</h3>
					</div>
					<div className="p-3">
						{dashboardData?.recentActivities?.length > 0 ? (
							<div className="space-y-1">
								{dashboardData.recentActivities
									.slice(0, 5)
									.map((activity: any) => (
										<RecentActivityItem key={activity.id} activity={activity} />
									))}
							</div>
						) : (
							<p className="text-gray-500 text-center py-8">
								No recent activity
							</p>
						)}
					</div>
				</div>

				{/* Upcoming Deadlines */}
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
					<div className="p-6 border-b border-gray-200">
						<h3 className="text-lg font-semibold text-gray-900">
							Upcoming Deadlines
						</h3>
					</div>
					<div className="p-3">
						{dashboardData?.upcomingDeadlines?.length > 0 ? (
							<div className="space-y-3">
								{dashboardData.upcomingDeadlines.map((deadline: any) => (
									<div
										key={deadline.id}
										className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg"
									>
										<div className="flex-1">
											<p className="text-sm font-medium text-gray-900">
												{deadline.title}
											</p>
											<p className="text-xs text-gray-500">
												Due: {new Date(deadline.dueDate).toLocaleDateString()}
												{deadline.assignedTo && ` • ${deadline.assignedTo}`}
											</p>
										</div>
										<span
											className={`px-2 py-1 text-xs font-medium rounded-full ${
												deadline.priority === "high"
													? "bg-red-100 text-red-800"
													: deadline.priority === "medium"
														? "bg-yellow-100 text-yellow-800"
														: "bg-green-100 text-green-800"
											}`}
										>
											{deadline.priority}
										</span>
									</div>
								))}
							</div>
						) : (
							<p className="text-gray-500 text-center py-8">
								No upcoming deadlines
							</p>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

export default SafetyDashboard;
