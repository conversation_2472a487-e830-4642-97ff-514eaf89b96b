import React, { useState, useRef, useCallback } from 'react';
import { Card, CardContent } from './card';
import { Button } from './button';
import { Camera, Upload, User, RotateCcw, Check, X } from 'lucide-react';
import { cn } from '../../lib/utils';

interface PhotoCaptureProps {
  currentPhoto?: File | string | null;
  onPhotoChange: (photo: File) => void;
  className?: string;
}

const PhotoCapture: React.FC<PhotoCaptureProps> = ({
  currentPhoto,
  onPhotoChange,
  className,
}) => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const startCamera = useCallback(async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user', width: 640, height: 640 }
      });
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      setIsCapturing(true);
    } catch (error) {
      console.error('Error accessing camera:', error);
      alert('Unable to access camera. Please check permissions.');
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsCapturing(false);
    setCapturedPhoto(null);
  }, [stream]);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas size to square
    canvas.width = 400;
    canvas.height = 400;

    // Calculate dimensions to crop to square
    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;
    const minDimension = Math.min(videoWidth, videoHeight);
    const x = (videoWidth - minDimension) / 2;
    const y = (videoHeight - minDimension) / 2;

    // Draw cropped square image
    context.drawImage(video, x, y, minDimension, minDimension, 0, 0, 400, 400);

    const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
    setCapturedPhoto(dataUrl);
  }, []);

  const confirmPhoto = useCallback(() => {
    if (!capturedPhoto || !canvasRef.current) return;

    canvasRef.current.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], 'captured-photo.jpg', { type: 'image/jpeg' });
        onPhotoChange(file);
        stopCamera();
      }
    }, 'image/jpeg', 0.8);
  }, [capturedPhoto, onPhotoChange, stopCamera]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onPhotoChange(file);
    }
  };

  const getPhotoUrl = () => {
    if (typeof currentPhoto === 'string') return currentPhoto;
    if (currentPhoto instanceof File) return URL.createObjectURL(currentPhoto);
    return null;
  };

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Photo Display */}
          <div className="flex justify-center">
            {getPhotoUrl() ? (
              <img
                src={getPhotoUrl()!}
                alt="Worker photo"
                className="w-32 h-32 rounded-full object-cover border-4 border-gray-200"
              />
            ) : (
              <div className="w-32 h-32 rounded-full bg-gray-100 flex items-center justify-center border-4 border-gray-200">
                <User className="w-16 h-16 text-gray-400" />
              </div>
            )}
          </div>

          {/* Camera Modal Content */}
          {isCapturing && (
            <div className="space-y-4 border-t pt-4">
              {!capturedPhoto ? (
                <div className="space-y-4">
                  <div className="relative mx-auto w-64 h-64">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      className="w-full h-full object-cover rounded-lg"
                    />
                    <div className="absolute inset-0 border-2 border-green-500 rounded-lg pointer-events-none" />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={capturePhoto} className="flex-1">
                      <Camera className="w-4 h-4 mr-2" />
                      Capture
                    </Button>
                    <Button variant="outline" onClick={stopCamera}>
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="mx-auto w-64 h-64">
                    <img
                      src={capturedPhoto}
                      alt="Captured"
                      className="w-full h-full object-cover rounded-lg"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={confirmPhoto} className="flex-1">
                      <Check className="w-4 h-4 mr-2" />
                      Use Photo
                    </Button>
                    <Button variant="outline" onClick={() => setCapturedPhoto(null)}>
                      <RotateCcw className="w-4 h-4 mr-2" />
                      Retake
                    </Button>
                    <Button variant="outline" onClick={stopCamera}>
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Upload Controls */}
          {!isCapturing && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="flex-1"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Photo
              </Button>
              <Button
                variant="outline"
                onClick={startCamera}
                className="flex-1"
              >
                <Camera className="w-4 h-4 mr-2" />
                Camera
              </Button>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />
          <canvas ref={canvasRef} className="hidden" />
        </div>
      </CardContent>
    </Card>
  );
};

export { PhotoCapture };
