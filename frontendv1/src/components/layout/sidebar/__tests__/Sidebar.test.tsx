// /**
//  * Comprehensive test suite for the Sidebar component system
//  * Tests functionality, accessibility, and user interactions
//  */

// import React from "react";
// import { render, screen, fireEvent, waitFor } from "@testing-library/react";
// import { <PERSON><PERSON>erRouter } from "react-router-dom";
// import userEvent from "@testing-library/user-event";
// import "@testing-library/jest-dom";

// import Sidebar from "../Sidebar";
// import { SidebarProvider } from "../SidebarProvider";

// // Mock the site context
// jest.mock("../../../../hooks/useSiteContext", () => ({
// 	useSiteContext: () => ({
// 		isSiteLevel: false,
// 		siteId: null,
// 		currentSite: null,
// 	}),
// }));

// // Mock the site context for testing
// jest.mock("../../../../hooks/useSiteContext");

// // Test wrapper component
// const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
// 	<BrowserRouter>{children}</BrowserRouter>
// );

// describe("Sidebar Component", () => {
// 	beforeEach(() => {
// 		// Clear any existing timeouts
// 		jest.clearAllTimers();
// 		jest.useFakeTimers();
// 	});

// 	afterEach(() => {
// 		jest.runOnlyPendingTimers();
// 		jest.useRealTimers();
// 	});

// 	describe("Basic Rendering", () => {
// 		it("renders the sidebar with logo and menu items", () => {
// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			// Check for logo
// 			expect(screen.getByAltText("Workforce Logo")).toBeInTheDocument();

// 			// Check for menu items
// 			expect(screen.getByText("Dashboard")).toBeInTheDocument();
// 			expect(screen.getByText("Settings")).toBeInTheDocument();
// 		});

// 		it("has proper accessibility attributes", () => {
// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			const nav = screen.getByRole("navigation");
// 			expect(nav).toHaveAttribute("aria-label", "Main Navigation");

// 			const menu = screen.getByRole("menu");
// 			expect(menu).toHaveAttribute("aria-orientation", "vertical");
// 		});
// 	});

// 	describe("Menu Item Interactions", () => {
// 		it("expands submenu on hover", async () => {
// 			const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			const settingsItem = screen
// 				.getByText("Settings")
// 				.closest('[role="menuitem"]');
// 			expect(settingsItem).toBeInTheDocument();

// 			// Hover over settings item
// 			await user.hover(settingsItem!);

// 			// Check that submenu appears
// 			await waitFor(() => {
// 				expect(screen.getByText("Settings Menu")).toBeInTheDocument();
// 				expect(screen.getByText("User Management")).toBeInTheDocument();
// 				expect(screen.getByText("Roles & Permissions")).toBeInTheDocument();
// 			});
// 		});

// 		it("collapses submenu when hovering away", async () => {
// 			const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			const settingsItem = screen
// 				.getByText("Settings")
// 				.closest('[role="menuitem"]');

// 			// Hover to expand
// 			await user.hover(settingsItem!);
// 			await waitFor(() => {
// 				expect(screen.getByText("Settings Menu")).toBeInTheDocument();
// 			});

// 			// Hover away
// 			await user.unhover(settingsItem!);

// 			// Advance timers to trigger collapse
// 			jest.advanceTimersByTime(500);

// 			await waitFor(() => {
// 				expect(screen.queryByText("Settings Menu")).not.toBeInTheDocument();
// 			});
// 		});
// 	});

// 	describe("Keyboard Navigation", () => {
// 		it("supports keyboard navigation", async () => {
// 			const user = userEvent.setup();

// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			const settingsItem = screen
// 				.getByText("Settings")
// 				.closest('[role="menuitem"]');

// 			// Focus and press Enter
// 			settingsItem!.focus();
// 			await user.keyboard("{Enter}");

// 			// Check that submenu appears
// 			await waitFor(() => {
// 				expect(screen.getByText("Settings Menu")).toBeInTheDocument();
// 			});
// 		});

// 		it("closes submenu on Escape key", async () => {
// 			const user = userEvent.setup();

// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			const settingsItem = screen
// 				.getByText("Settings")
// 				.closest('[role="menuitem"]');

// 			// Open submenu
// 			await user.hover(settingsItem!);
// 			await waitFor(() => {
// 				expect(screen.getByText("Settings Menu")).toBeInTheDocument();
// 			});

// 			// Press Escape
// 			await user.keyboard("{Escape}");

// 			await waitFor(() => {
// 				expect(screen.queryByText("Settings Menu")).not.toBeInTheDocument();
// 			});
// 		});
// 	});

// 	describe("Click Outside Behavior", () => {
// 		it("closes submenu when clicking outside", async () => {
// 			const user = userEvent.setup();

// 			render(
// 				<TestWrapper>
// 					<div data-testid="outside-area">
// 						<Sidebar />
// 					</div>
// 				</TestWrapper>,
// 			);

// 			const settingsItem = screen
// 				.getByText("Settings")
// 				.closest('[role="menuitem"]');

// 			// Open submenu
// 			await user.hover(settingsItem!);
// 			await waitFor(() => {
// 				expect(screen.getByText("Settings Menu")).toBeInTheDocument();
// 			});

// 			// Click outside
// 			const outsideArea = screen.getByTestId("outside-area");
// 			await user.click(outsideArea);

// 			await waitFor(() => {
// 				expect(screen.queryByText("Settings Menu")).not.toBeInTheDocument();
// 			});
// 		});
// 	});

// 	describe("Accessibility Features", () => {
// 		it("has proper ARIA attributes for menu items", () => {
// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			const settingsItem = screen
// 				.getByText("Settings")
// 				.closest('[role="menuitem"]');

// 			expect(settingsItem).toHaveAttribute("aria-haspopup", "true");
// 			expect(settingsItem).toHaveAttribute("aria-expanded", "false");
// 			expect(settingsItem).toHaveAttribute("tabindex", "0");
// 		});

// 		it("updates aria-expanded when submenu is opened", async () => {
// 			const user = userEvent.setup();

// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			const settingsItem = screen
// 				.getByText("Settings")
// 				.closest('[role="menuitem"]');

// 			// Initially collapsed
// 			expect(settingsItem).toHaveAttribute("aria-expanded", "false");

// 			// Hover to expand
// 			await user.hover(settingsItem!);

// 			await waitFor(() => {
// 				expect(settingsItem).toHaveAttribute("aria-expanded", "true");
// 			});
// 		});

// 		it("has proper focus management", () => {
// 			render(
// 				<TestWrapper>
// 					<Sidebar />
// 				</TestWrapper>,
// 			);

// 			const menuItems = screen.getAllByRole("menuitem");

// 			menuItems.forEach((item) => {
// 				expect(item).toHaveAttribute("tabindex", "0");
// 			});
// 		});
// 	});
// });
