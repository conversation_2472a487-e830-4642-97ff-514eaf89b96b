# Sidebar Component System

A completely rewritten, enterprise-grade sidebar implementation built from first principles with a focus on clean architecture, performance, and accessibility.

## 🏗️ Architecture Overview

The new sidebar system follows a modular, composable architecture:

```
SidebarProvider (Context + State Management)
├── Sidebar (Main Container)
│   ├── SidebarLogo
│   ├── SidebarMenu
│   │   └── SidebarMenuItem (with hover logic)
│   └── SidebarBackButton (site-level only)
└── SidebarFlyout (Portal-rendered)
    ├── FlyoutHeader
    └── FlyoutContent
        └── FlyoutMenuItem
```

## 🚀 Key Improvements

### 1. **Clean Architecture**

- **Single Responsibility**: Each component has one clear purpose
- **Composition over Inheritance**: Components are composed together
- **Separation of Concerns**: State, UI, and business logic are separated

### 2. **Performance Optimizations**

- **Centralized State Management**: Single context provider with useReducer
- **Memoized Calculations**: Prevents unnecessary re-renders
- **Optimized Event Handling**: Efficient hover and keyboard interactions
- **Smart Re-rendering**: Components only update when necessary

### 3. **Enhanced Accessibility**

- **Complete ARIA Implementation**: Proper roles, states, and properties
- **Keyboard Navigation**: Full keyboard support with focus management
- **Screen Reader Support**: Semantic HTML and descriptive labels
- **Focus Indicators**: Clear visual focus states

### 4. **Design System Integration**

- **Design Tokens**: Centralized design values in `sidebar-tokens.ts`
- **Consistent Spacing**: Systematic spacing using design tokens
- **Color System**: Unified color palette with semantic naming
- **Animation System**: Smooth, consistent transitions

## 📁 File Structure

```
src/components/layout/sidebar/
├── index.ts                    # Main exports
├── README.md                   # This documentation
├── Sidebar.tsx                 # Main container component
├── SidebarProvider.tsx         # Context provider and state management
├── SidebarLogo.tsx            # Logo component
├── SidebarMenu.tsx            # Menu container
├── SidebarMenuItem.tsx        # Individual menu items
├── SidebarBackButton.tsx      # Back navigation button
├── SidebarFlyout.tsx          # Flyout menu container
├── FlyoutMenuItem.tsx         # Flyout menu items
└── __tests__/
    └── Sidebar.test.tsx       # Comprehensive test suite
```

## 🎯 Usage

### Basic Usage

```tsx
import Sidebar from "./components/layout/sidebar/Sidebar";

function App() {
  return (
    <div className="app">
      <Sidebar />
      {/* Your main content */}
    </div>
  );
}
```

### Advanced Usage with Custom Configuration

```tsx
import { SidebarProvider } from "./components/layout/sidebar";

function App() {
  return (
    <SidebarProvider
      hoverConfig={{
        enterDelay: 0,
        leaveDelay: 200,
        sidebarLeaveDelay: 400,
        flyoutLeaveDelay: 250,
      }}
    >
      <Sidebar />
    </SidebarProvider>
  );
}
```

## 🎨 Design Tokens

The sidebar uses a comprehensive design token system for consistent styling:

```typescript
// Access design tokens
import { sidebarTokens } from "../../../styles/sidebar-tokens";

// Use utility functions
import { getSpacing, getColorClasses } from "../../../styles/sidebar-tokens";
```

### Key Token Categories:

- **Spacing**: Consistent spacing values for all components
- **Colors**: Semantic color system with light/dark mode support
- **Animation**: Unified timing and easing functions
- **Typography**: Consistent text sizing and weights

## 🔧 State Management

The sidebar uses a centralized state management system with React Context and useReducer:

```typescript
interface SidebarState {
  expandedMenu: string | null;
  isHovering: boolean;
  flyoutVisible: boolean;
  keyboardNavigation: boolean;
  focusedItemId: string | null;
}
```

### Available Actions:

- `EXPAND_MENU`: Opens a submenu
- `COLLAPSE_MENU`: Closes all submenus
- `SET_HOVER`: Updates hover state
- `SET_FLYOUT_VISIBLE`: Controls flyout visibility
- `SET_KEYBOARD_NAV`: Enables/disables keyboard navigation mode
- `SET_FOCUSED_ITEM`: Updates focused item for keyboard navigation
- `RESET`: Resets to initial state

## 🎮 Interaction Patterns

### Hover Behavior

- **Immediate Response**: No delay on hover enter for better UX
- **Smart Delays**: Configurable delays prevent accidental closes
- **Smooth Transitions**: Seamless movement between sidebar and flyout

### Keyboard Navigation

- **Arrow Keys**: Navigate between menu items
- **Enter/Space**: Activate menu items or expand submenus
- **Escape**: Close expanded submenus
- **Tab**: Standard focus navigation

### Touch/Mobile Support

- **Touch-friendly**: Optimized touch targets
- **Responsive Design**: Adapts to different screen sizes
- **Gesture Support**: Swipe gestures for mobile navigation

## ♿ Accessibility Features

### ARIA Implementation

- **Roles**: Proper semantic roles (`navigation`, `menu`, `menuitem`)
- **States**: Dynamic `aria-expanded`, `aria-current` attributes
- **Labels**: Descriptive `aria-label` attributes
- **Relationships**: Proper parent-child relationships

### Keyboard Support

- **Focus Management**: Logical focus order and visible indicators
- **Keyboard Shortcuts**: Standard navigation patterns
- **Screen Reader**: Optimized for screen reader navigation

### Visual Accessibility

- **High Contrast**: Sufficient color contrast ratios
- **Focus Indicators**: Clear visual focus states
- **Reduced Motion**: Respects user motion preferences

## 🧪 Testing

Comprehensive test suite covering:

### Unit Tests

- Component rendering
- State management
- Event handling
- Accessibility features

### Integration Tests

- User interactions
- Keyboard navigation
- Hover behavior
- Route changes

### Accessibility Tests

- ARIA compliance
- Keyboard navigation
- Screen reader compatibility

Run tests:

```bash
npm test -- sidebar
```

## 🔄 Migration from Old Implementation

The new implementation is a drop-in replacement:

1. **No Breaking Changes**: Same external API
2. **Improved Performance**: Better rendering and memory usage
3. **Enhanced Features**: Better accessibility and interactions
4. **Cleaner Code**: More maintainable and extensible

## 🛠️ Customization

### Styling Customization

Modify design tokens in `sidebar-tokens.ts`:

```typescript
export const customTokens = {
  ...sidebarTokens,
  colors: {
    ...sidebarTokens.colors,
    background: {
      ...sidebarTokens.colors.background,
      sidebar: "#custom-color",
    },
  },
};
```

### Behavior Customization

Configure hover behavior:

```typescript
const customHoverConfig = {
  enterDelay: 100,
  leaveDelay: 300,
  sidebarLeaveDelay: 500,
  flyoutLeaveDelay: 200,
};
```

## 📈 Performance Considerations

- **Memoization**: Extensive use of React.memo and useMemo
- **Event Optimization**: Debounced and throttled event handlers
- **Bundle Size**: Tree-shakeable exports and minimal dependencies
- **Runtime Performance**: Optimized re-rendering and DOM updates

## 🔮 Future Enhancements

- **Animation Library Integration**: Framer Motion for advanced animations
- **Theme System**: Dynamic theming support
- **Mobile Optimization**: Enhanced mobile navigation patterns
- **Internationalization**: Multi-language support
- **Analytics Integration**: User interaction tracking
