/**
 * Sidebar Flyout Component
 * Displays submenu items with smooth animations and proper accessibility
 */

import React, { useEffect, useState } from "react";
import { MenuItem, SidebarFlyoutProps } from "../../../types/sidebar";
import { FlyoutMenuItem } from "./FlyoutMenuItem";
import { useSidebar, useSidebarAccessibility } from "./SidebarProvider";
import { useSiteContext } from "../../../hooks/useSiteContext";
import {
	cssClasses,
	zIndexes,
	sidebarTokens,
} from "../../../styles/sidebar-tokens";
import {
	MapPin,
	User,
	Users,
	FileCheck,
	AlertTriangle,
} from "lucide-react";

// Helper function to render site information content
const renderSiteInfoContent = (
	site: {
		id: string;
		name: string;
		healthStatus: "green" | "amber" | "red";
		workersOnSite: number;
		activePermits: number;
		openIncidents: number;
		projectManager: string;
		location: string;
		timeline?: string;
		currentPhase?: string;
		progressPercentage?: number;
	},
	contentVisible: boolean,
) => {
	return (
		<div className="space-y-4">
			{/* Site Header */}
			<div
				className={`
          transition-all
          duration-300
          ease-out
          ${contentVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-[-20px]"}
        `}
			>
				<div className="flex items-start justify-between mb-3">
					<div className="flex-1 min-w-0">
						<h4 className="font-semibold text-gray-900 text-sm truncate">
							{site.name}
						</h4>
						<div className="flex items-center text-xs text-gray-600 mt-1">
							<MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
							<span className="truncate">{site.location}</span>
						</div>
					</div>
				</div>
			</div>

			{/* Project Information */}
			<div
				className={`
          space-y-2
          transition-all
          duration-300
          ease-out
          ${contentVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-[-20px]"}
        `}
				style={{ transitionDelay: contentVisible ? "100ms" : "0ms" }}
			>
				<div className="flex items-center text-xs">
					<User className="h-3 w-3 text-gray-400 mr-2 flex-shrink-0" />
					<span className="text-gray-600 mr-2">PM:</span>
					<span className="font-medium text-gray-900">
						{site.projectManager}
					</span>
				</div>


			</div>

			{/* Site Metrics */}
			<div
				className={`
          border-t border-gray-200 pt-3
          transition-all
          duration-300
          ease-out
          ${contentVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-[-20px]"}
        `}
				style={{ transitionDelay: contentVisible ? "200ms" : "0ms" }}
			>
				<h5 className="text-xs font-medium text-gray-700 mb-2">
					Current Status
				</h5>
				<div className="grid grid-cols-3 gap-2">
					<div className="text-center">
						<div className="flex items-center justify-center mb-1">
							<Users className="h-3 w-3 text-blue-500" />
						</div>
						<div className="text-sm font-semibold text-gray-900">
							{site.workersOnSite}
						</div>
						<div className="text-xs text-gray-500">Workers</div>
					</div>

					<div className="text-center">
						<div className="flex items-center justify-center mb-1">
							<FileCheck className="h-3 w-3 text-green-500" />
						</div>
						<div className="text-sm font-semibold text-gray-900">
							{site.activePermits}
						</div>
						<div className="text-xs text-gray-500">Permits</div>
					</div>

					<div className="text-center">
						<div className="flex items-center justify-center mb-1">
							<AlertTriangle
								className={`h-3 w-3 ${site.openIncidents > 0 ? "text-red-500" : "text-gray-400"}`}
							/>
						</div>
						<div
							className={`text-sm font-semibold ${site.openIncidents > 0 ? "text-red-600" : "text-gray-900"}`}
						>
							{site.openIncidents}
						</div>
						<div className="text-xs text-gray-500">Incidents</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export const SidebarFlyout: React.FC<SidebarFlyoutProps> = ({
	menuItem,
	visible,
	onMouseEnter,
	onMouseLeave,
}) => {
	const { isMenuItemActive } = useSidebar();
	const { getFlyoutProps } = useSidebarAccessibility();
	const { isSiteLevel, currentSite } = useSiteContext();

	// State for content animation
	const [contentVisible, setContentVisible] = useState(false);
	const [currentMenuKey, setCurrentMenuKey] = useState<string | null>(null);

	// Handle content animation when menu changes
	useEffect(() => {
		if (visible && menuItem) {
			const newMenuKey = menuItem.name;

			if (newMenuKey !== currentMenuKey) {
				// New menu - fade out current content, then fade in new content
				setContentVisible(false);

				const timer = setTimeout(() => {
					setCurrentMenuKey(newMenuKey);
					setContentVisible(true);
				}, 150); // Half of the transition duration

				return () => clearTimeout(timer);
			} else {
				// Same menu - just show content
				setContentVisible(true);
			}
		} else {
			// Flyout hidden - reset state
			setContentVisible(false);
			setCurrentMenuKey(null);
		}
	}, [visible, menuItem, currentMenuKey]);

	// Don't render if no menu item or submenu
	if (!menuItem?.submenu || !visible) {
		return null;
	}

	const flyoutProps = getFlyoutProps(menuItem);

	return (
		<div
			className={`
        ${cssClasses.flyout.container}
        fixed
        top-0
        bottom-0
        w-64
        transform
        transition-all
        duration-300
        ease-out
        translate-x-0
        opacity-100
      `}
      style={{
        left: '72px',
        zIndex: zIndexes.flyout,
        backgroundColor: sidebarTokens.colors.background.flyout,
        borderLeft: `1px solid ${sidebarTokens.colors.border.flyout}`,
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      {...flyoutProps}
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div
          className={`
            ${cssClasses.flyout.header}
            p-4
            border-b
            border-gray-200
            flex-shrink-0
            transition-all
            duration-300
            ease-out
            ${contentVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-[-20px]"}
          `}
				>
					<h3 className="font-medium text-gray-800">
						{menuItem.submenu.title}
					</h3>
					{isSiteLevel && currentSite && (
						<p className="text-xs text-gray-500 mt-1">{currentSite.name}</p>
					)}
				</div>

				{/* Scrollable Content */}
				<div
					className={`
            ${cssClasses.flyout.content}
            flex-1
            overflow-y-auto
            p-2
            transition-all
            duration-300
            ease-out
            ${contentVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-[-20px]"}
          `}
				>
					{/* Special handling for Overview menu with site information */}
					{menuItem.name === "Overview" && isSiteLevel && currentSite ? (
						<div className="px-2 py-1">
							{renderSiteInfoContent(currentSite, contentVisible)}

							{/* Divider */}
							<div className="border-t border-gray-200 my-4"></div>

              {/* Regular menu items - sorted to show add actions first */}
              {[...menuItem.submenu.items]
                .sort((a, b) => {
                  // Sort add actions first, then others
                  const aIsAdd = a.action === 'add';
                  const bIsAdd = b.action === 'add';
                  if (aIsAdd && !bIsAdd) return -1;
                  if (!aIsAdd && bIsAdd) return 1;
                  return 0;
                })
                .map((subItem, index) => (
                <div
                  key={subItem.name}
                  className={`
                    transition-all
                    duration-300
                    ease-out
                    ${contentVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-[-20px]"}
                  `}
                  style={{
                    transitionDelay: contentVisible ? `${(index + 3) * 50}ms` : '0ms',
                  }}
                >
                  <FlyoutMenuItem
                    item={subItem}
                    isActive={isMenuItemActive({
                      name: subItem.name,
                      icon: null,
                      path: subItem.path
                    } as MenuItem)}
                  />
                </div>
              ))}
            </div>
          ) : (
            /* Regular menu items for all other menus - sorted to show add actions first */
            [...menuItem.submenu.items]
              .sort((a, b) => {
                // Sort add actions first, then others
                const aIsAdd = a.action === 'add';
                const bIsAdd = b.action === 'add';
                if (aIsAdd && !bIsAdd) return -1;
                if (!aIsAdd && bIsAdd) return 1;
                return 0;
              })
              .map((subItem, index) => (
              <div
                key={subItem.name}
                className={`
                  transition-all
                  duration-300
                  ease-out
                  ${contentVisible ? "opacity-100 translate-x-0" : "opacity-0 translate-x-[-20px]"}
                `}
								style={{
									transitionDelay: contentVisible ? `${index * 50}ms` : "0ms",
								}}
							>
								<FlyoutMenuItem
									item={subItem}
									isActive={isMenuItemActive({
										name: subItem.name,
										icon: null,
										path: subItem.path,
									} as MenuItem)}
								/>
							</div>
						))
					)}
				</div>
			</div>
		</div>
	);
};
