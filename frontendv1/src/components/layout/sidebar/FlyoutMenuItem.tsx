/**
 * Flyout Menu Item Component
 * Individual submenu item with proper styling and accessibility
 */

import React from "react";
import { useLocation } from "react-router-dom";
import { Plus } from "lucide-react";
import { FlyoutMenuItemProps } from "../../../types/sidebar";
import { useSidebarAccessibility } from "./SidebarProvider";
import { isPathActive } from "../../../utils/routeUtils";
import { getFlyoutItemClasses } from "../../../styles/sidebar-tokens";
import { SiteFlyoutMenuItem } from "./SiteFlyoutMenuItem";
import { useHashNavigation } from "../../../hooks/useHashNavigation";

export const FlyoutMenuItem: React.FC<FlyoutMenuItemProps> = ({
	item,
	isActive,
	onClick,
}) => {
	// If this item has site data or is an add action for sites, use the special site component
	if (
		item.siteData ||
		(item.action === "add" && item.path.includes("/sites/"))
	) {
		return (
			<SiteFlyoutMenuItem item={item} isActive={isActive} onClick={onClick} />
		);
	}

	const location = useLocation();
	const { navigateToHash } = useHashNavigation();
	const { getSubmenuItemProps } = useSidebarAccessibility();

	const submenuItemProps = getSubmenuItemProps(item);
	const isCurrentPage = isPathActive(location.pathname, item.path);

	// Determine if this is an "add" action item
	const isAddAction = "action" in item && item.action === "add";

	const handleClick = (e: React.MouseEvent) => {
		e.preventDefault();
		navigateToHash(item.path);
		if (onClick) {
			onClick();
		}
	};

	return (
		<button
			onClick={handleClick}
			aria-current={isCurrentPage ? "page" : undefined}
			className={`
        ${getFlyoutItemClasses(isCurrentPage, isAddAction)}
        ${isCurrentPage ? 'active' : ''}
        ${isAddAction ? 'group' : ''}
        w-full text-left
      `}
      {...submenuItemProps}
    >
      {/* Add icon for "add" actions */}
      {isAddAction && (
        <Plus className="h-4 w-4 mr-2 text-green-500 group-hover:text-green-500 group-hover:scale-110 flex-shrink-0 transition-all duration-300" />
      )}

      {/* Item name */}
      <span className="flex-1 transition-colors duration-300">
        {item.name}
      </span>
    </button>
  );
};
