import React, { useMemo } from 'react';
import { AlertTriangle, Clock, CheckCircle, Calendar, User } from 'lucide-react';
import { WorkerTrainingHistory, Worker } from '../../types';

interface TrainingExpiryTrackerProps {
  workers: Worker[];
  onRenewTraining: (workerId: number, trainingHistoryId: number) => void;
  onViewWorker: (workerId: number) => void;
}

interface ExpiryItem {
  worker: Worker;
  trainingHistory: WorkerTrainingHistory;
  daysUntilExpiry: number;
  status: 'expired' | 'expiring-soon' | 'expiring-later' | 'valid';
}

export const TrainingExpiryTracker: React.FC<TrainingExpiryTrackerProps> = ({
  workers,
  onRenewTraining,
  onViewWorker,
}) => {
  const expiryItems = useMemo(() => {
    const items: ExpiryItem[] = [];
    const today = new Date();

    workers.forEach(worker => {
      worker.trainingHistory.forEach(trainingHistory => {
        if (trainingHistory.expiryDate) {
          const expiryDate = new Date(trainingHistory.expiryDate);
          const timeDiff = expiryDate.getTime() - today.getTime();
          const daysUntilExpiry = Math.ceil(timeDiff / (1000 * 3600 * 24));

          let status: ExpiryItem['status'];
          if (daysUntilExpiry < 0) {
            status = 'expired';
          } else if (daysUntilExpiry <= 30) {
            status = 'expiring-soon';
          } else if (daysUntilExpiry <= 90) {
            status = 'expiring-later';
          } else {
            status = 'valid';
          }

          items.push({
            worker,
            trainingHistory,
            daysUntilExpiry,
            status,
          });
        }
      });
    });

    // Sort by urgency: expired first, then by days until expiry
    return items.sort((a, b) => {
      if (a.status === 'expired' && b.status !== 'expired') return -1;
      if (b.status === 'expired' && a.status !== 'expired') return 1;
      return a.daysUntilExpiry - b.daysUntilExpiry;
    });
  }, [workers]);

  const getStatusBadge = (status: ExpiryItem['status'], daysUntilExpiry: number) => {
    switch (status) {
      case 'expired':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Expired {Math.abs(daysUntilExpiry)} days ago
          </span>
        );
      case 'expiring-soon':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Expires in {daysUntilExpiry} days
          </span>
        );
      case 'expiring-later':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Calendar className="h-3 w-3 mr-1" />
            Expires in {daysUntilExpiry} days
          </span>
        );
      case 'valid':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Valid ({daysUntilExpiry} days)
          </span>
        );
    }
  };

  const getRowClassName = (status: ExpiryItem['status']) => {
    switch (status) {
      case 'expired':
        return 'bg-red-50 hover:bg-red-100';
      case 'expiring-soon':
        return 'bg-yellow-50 hover:bg-yellow-100';
      default:
        return 'hover:bg-gray-50';
    }
  };

  const expiredCount = expiryItems.filter(item => item.status === 'expired').length;
  const expiringSoonCount = expiryItems.filter(item => item.status === 'expiring-soon').length;

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">Training Expiry Tracker</h3>
          <div className="flex items-center space-x-4">
            {expiredCount > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                {expiredCount} Expired
              </span>
            )}
            {expiringSoonCount > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                {expiringSoonCount} Expiring Soon
              </span>
            )}
          </div>
        </div>
      </div>

      {expiryItems.length === 0 ? (
        <div className="p-12 text-center">
          <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">All Trainings Up to Date</h3>
          <p className="text-gray-500">No training certifications require immediate attention.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Worker
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Training
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Completion Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expiry Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {expiryItems.map((item) => (
                <tr key={`${item.worker.id}-${item.trainingHistory.id}`} className={getRowClassName(item.status)}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        {item.worker.photoUrl ? (
                          <img
                            className="h-8 w-8 rounded-full object-cover"
                            src={item.worker.photoUrl}
                            alt={item.worker.name}
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                            <User className="h-4 w-4 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="ml-3">
                        <button
                          onClick={() => onViewWorker(item.worker.id)}
                          className="text-sm font-medium text-gray-900 hover:text-green-600"
                        >
                          {item.worker.name}
                        </button>
                        <div className="text-xs text-gray-500">
                          {item.worker.trades.map(trade => trade.name).join(', ')}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {item.trainingHistory.training?.name || 'Training'}
                    </div>
                    {item.trainingHistory.score && (
                      <div className="text-xs text-gray-500">
                        Score: {item.trainingHistory.score}%
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(item.trainingHistory.completionDate).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.trainingHistory.expiryDate ? 
                      new Date(item.trainingHistory.expiryDate).toLocaleDateString() : 
                      'No expiry'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(item.status, item.daysUntilExpiry)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => onRenewTraining(item.worker.id, item.trainingHistory.id)}
                      className={`${
                        item.status === 'expired' || item.status === 'expiring-soon'
                          ? 'text-red-600 hover:text-red-900'
                          : 'text-green-600 hover:text-green-900'
                      } transition-colors`}
                    >
                      {item.status === 'expired' || item.status === 'expiring-soon' ? 'Renew Now' : 'Schedule Renewal'}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};
