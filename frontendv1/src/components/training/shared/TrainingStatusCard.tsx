import React from "react";

interface TrainingStatusCardProps {
	title: string;
	count?: number;
	percentage?: number;
	status: "good" | "warning" | "danger";
	icon: React.ReactNode;
	onClick?: () => void;
}

const TrainingStatusCard: React.FC<TrainingStatusCardProps> = ({
	title,
	count,
	percentage,
	status,
	icon,
	onClick,
}) => {
	const getStatusColor = () => {
		switch (status) {
			case "good":
				return "text-green-600";
			case "warning":
				return "text-amber-500";
			case "danger":
				return "text-red-600";
			default:
				return "text-gray-600";
		}
	};

	const getBorderColor = () => {
		switch (status) {
			case "good":
				return "border-green-200";
			case "warning":
				return "border-amber-200";
			case "danger":
				return "border-red-200";
			default:
				return "border-gray-200";
		}
	};

	const cardClasses = `
    bg-white p-6 rounded-lg border shadow-sm transition-all duration-200
    ${getBorderColor()}
    ${onClick ? "cursor-pointer hover:shadow-md hover:border-gray-300" : ""}
  `;

	return (
		<div className={cardClasses} onClick={onClick}>
			<div className="flex justify-between items-start">
				<div>
					<p className="text-sm font-medium text-gray-500">{title}</p>
					{percentage !== undefined ? (
						<p className="mt-2 text-2xl font-semibold text-gray-900">
							{percentage}%
						</p>
					) : (
						<p className="mt-2 text-2xl font-semibold text-gray-900">{count}</p>
					)}
				</div>
				<div className={getStatusColor()}>{icon}</div>
			</div>
		</div>
	);
};

export default TrainingStatusCard;
