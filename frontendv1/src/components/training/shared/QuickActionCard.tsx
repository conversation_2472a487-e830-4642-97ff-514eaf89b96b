import React from "react";

interface QuickActionCardProps {
	title: string;
	description: string;
	icon: React.ReactNode;
	onClick: () => void;
}

const QuickActionCard: React.FC<QuickActionCardProps> = ({
	title,
	description,
	icon,
	onClick,
}) => {
	return (
		<div
			className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:border-gray-300"
			onClick={onClick}
		>
			<div className="flex items-start space-x-4">
				<div className="flex-shrink-0">
					<div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center text-green-600">
						{icon}
					</div>
				</div>
				<div className="flex-1 min-w-0">
					<h3 className="text-sm font-medium text-gray-900 mb-1">{title}</h3>
					<p className="text-sm text-gray-500">{description}</p>
				</div>
			</div>
		</div>
	);
};

export default QuickActionCard;
