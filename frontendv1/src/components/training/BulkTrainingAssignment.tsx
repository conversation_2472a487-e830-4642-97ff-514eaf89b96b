import React, { useState, useMemo } from 'react';
import { Users, Award, Calendar, Filter, X } from 'lucide-react';
import { Worker, Training, Trade } from '../../types';

interface BulkTrainingAssignmentProps {
  workers: Worker[];
  trainings: Training[];
  trades: Trade[];
  onAssign: (trainingId: number, workerIds: number[], scheduledDate?: string) => Promise<void>;
  onCancel: () => void;
}

export const BulkTrainingAssignment: React.FC<BulkTrainingAssignmentProps> = ({
  workers,
  trainings,
  trades,
  onAssign,
  onCancel,
}) => {
  const [selectedTraining, setSelectedTraining] = useState<number | null>(null);
  const [selectedTrade, setSelectedTrade] = useState<number | null>(null);
  const [selectedWorkers, setSelectedWorkers] = useState<Set<number>>(new Set());
  const [scheduledDate, setScheduledDate] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter workers by selected trade
  const filteredWorkers = useMemo(() => {
    if (!selectedTrade) return workers;
    return workers.filter(worker => 
      worker.trades.some(trade => trade.id === selectedTrade)
    );
  }, [workers, selectedTrade]);

  const selectedTrainingData = trainings.find(t => t.id === selectedTraining);

  const handleWorkerToggle = (workerId: number) => {
    const newSelected = new Set(selectedWorkers);
    if (newSelected.has(workerId)) {
      newSelected.delete(workerId);
    } else {
      newSelected.add(workerId);
    }
    setSelectedWorkers(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedWorkers.size === filteredWorkers.length) {
      setSelectedWorkers(new Set());
    } else {
      setSelectedWorkers(new Set(filteredWorkers.map(w => w.id)));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedTraining) {
      alert('Please select a training');
      return;
    }

    if (selectedWorkers.size === 0) {
      alert('Please select at least one worker');
      return;
    }

    setIsSubmitting(true);
    try {
      await onAssign(
        selectedTraining,
        Array.from(selectedWorkers),
        scheduledDate || undefined
      );
    } catch (error) {
      console.error('Bulk training assignment failed:', error);
      alert('Failed to assign training. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            Bulk Training Assignment
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Training Selection */}
          <div>
            <label htmlFor="training" className="block text-sm font-medium text-gray-700 mb-2">
              Select Training *
            </label>
            <select
              id="training"
              value={selectedTraining || ''}
              onChange={(e) => setSelectedTraining(e.target.value ? Number(e.target.value) : null)}
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="">Choose a training...</option>
              {trainings.map(training => (
                <option key={training.id} value={training.id}>
                  {training.name} {training.duration && `(${training.duration})`}
                </option>
              ))}
            </select>
            {selectedTrainingData && (
              <div className="mt-2 p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-700">{selectedTrainingData.description}</p>
                {selectedTrainingData.validityPeriodMonths && (
                  <p className="text-xs text-blue-600 mt-1">
                    Valid for {selectedTrainingData.validityPeriodMonths} months
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Scheduled Date */}
          <div>
            <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-2">
              Scheduled Date (Optional)
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="date"
                id="scheduledDate"
                value={scheduledDate}
                onChange={(e) => setScheduledDate(e.target.value)}
                min={new Date().toISOString().split('T')[0]}
                className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Trade Filter */}
          <div>
            <label htmlFor="tradeFilter" className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Trade
            </label>
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                id="tradeFilter"
                value={selectedTrade || ''}
                onChange={(e) => setSelectedTrade(e.target.value ? Number(e.target.value) : null)}
                className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">All trades</option>
                {trades.map(trade => (
                  <option key={trade.id} value={trade.id}>
                    {trade.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Worker Selection */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Select Workers * ({selectedWorkers.size} selected)
              </label>
              <button
                type="button"
                onClick={handleSelectAll}
                className="text-sm text-green-600 hover:text-green-700 font-medium"
              >
                {selectedWorkers.size === filteredWorkers.length ? 'Deselect All' : 'Select All'}
              </button>
            </div>

            <div className="border border-gray-300 rounded-md max-h-64 overflow-y-auto">
              {filteredWorkers.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No workers found for the selected trade</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredWorkers.map(worker => (
                    <label
                      key={worker.id}
                      className="flex items-center p-3 hover:bg-gray-50 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedWorkers.has(worker.id)}
                        onChange={() => handleWorkerToggle(worker.id)}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <div className="ml-3 flex items-center flex-1">
                        <div className="flex-shrink-0 h-8 w-8">
                          {worker.photoUrl ? (
                            <img
                              className="h-8 w-8 rounded-full object-cover"
                              src={worker.photoUrl}
                              alt={worker.name}
                            />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                              <span className="text-xs text-gray-500 font-medium">
                                {worker.name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="text-sm font-medium text-gray-900">
                            {worker.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {worker.trades.map(trade => trade.name).join(', ')} • {worker.company}
                          </div>
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Summary */}
          {selectedTraining && selectedWorkers.size > 0 && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-green-900 mb-2">Assignment Summary</h4>
              <div className="text-sm text-green-700">
                <p>• Training: {selectedTrainingData?.name}</p>
                <p>• Workers: {selectedWorkers.size} selected</p>
                {scheduledDate && (
                  <p>• Scheduled: {new Date(scheduledDate).toLocaleDateString()}</p>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !selectedTraining || selectedWorkers.size === 0}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              <Award className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Assigning...' : `Assign to ${selectedWorkers.size} Workers`}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
