import React, { useState, useEffect } from "react";
import {
  Shield,
  CheckCircle,
  Clock,
  AlertTriangle,
  UserPlus,
  ClipboardCheck,
  CalendarPlus,
  Calendar,
  Users,
  TrendingUp,
  Filter,
} from 'lucide-react';
import TrainingStatusCard from './shared/TrainingStatusCard';
import QuickActionCard from './shared/QuickActionCard';
import TrainingModal from './shared/TrainingModal';
import TrainingAssignmentModal from './TrainingAssignmentModal';
import TrainingCompletionModal from './TrainingCompletionModal';
import TrainingScheduleModal from './TrainingScheduleModal';
import { TrainingStats, UpcomingTraining, ExpiringCertification } from '../../types/training';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';

interface TrainingDashboardProps {
	siteId: string;
	onNavigateToTab: (tabId: string) => void;
}

interface ComplianceByTrade {
  tradeId: number;
  tradeName: string;
  totalWorkers: number;
  compliantWorkers: number;
  complianceRate: number;
}

interface TrainingAssignment {
  trainingId: number;
  workerIds: number[];
  targetCompletionDate?: string;
  notes?: string;
  assignmentType: 'manual' | 'auto' | 'bulk';
}

interface TrainingCompletion {
  workerId: number;
  trainingId: number;
  completionDate: string;
  expiryDate?: string;
  score?: number;
  notes?: string;
  certificateFile?: File;
  trainerName?: string;
  location?: string;
}

// Mock data - replace with actual API calls
const mockTrainingStats: TrainingStats = {
	overallCompliance: 87,
	validTrainings: 245,
	expiringSoon: 18,
	expired: 12,
	totalWorkers: 42,
	activePrograms: 15,
};

const mockUpcomingTrainings: UpcomingTraining[] = [
	{
		id: "1",
		title: "Safety Orientation",
		date: new Date("2024-01-25"),
		time: "09:00 AM",
		location: "Training Room A",
		availableSpots: 5,
		totalSpots: 20,
	},
	{
		id: "2",
		title: "Equipment Operation",
		date: new Date("2024-01-26"),
		time: "02:00 PM",
		location: "Site Workshop",
		availableSpots: 8,
		totalSpots: 15,
	},
	{
		id: "3",
		title: "First Aid Certification",
		date: new Date("2024-01-28"),
		time: "10:00 AM",
		location: "Training Room B",
		availableSpots: 12,
		totalSpots: 25,
	},
];

const mockExpiringCertifications: ExpiringCertification[] = [
	{
		workerId: "w1",
		workerName: "John Kamau",
		trainingName: "Safety Certification",
		expiryDate: new Date("2024-02-15"),
		daysUntilExpiry: 15,
		priority: "high",
	},
	{
		workerId: "w2",
		workerName: "Mary Wanjiku",
		trainingName: "Equipment License",
		expiryDate: new Date("2024-02-20"),
		daysUntilExpiry: 20,
		priority: "medium",
	},
	{
		workerId: "w3",
		workerName: "Peter Ochieng",
		trainingName: "First Aid",
		expiryDate: new Date("2024-02-25"),
		daysUntilExpiry: 25,
		priority: "medium",
	},
];

const TrainingDashboard: React.FC<TrainingDashboardProps> = ({ siteId, onNavigateToTab }) => {
  const { tenantId } = useTenantContext();
  const [stats, setStats] = useState<TrainingStats>(mockTrainingStats);
  const [upcomingTrainings, setUpcomingTrainings] = useState<UpcomingTraining[]>(mockUpcomingTrainings);
  const [expiringCertifications, _setExpiringCertifications] = useState<ExpiringCertification[]>(mockExpiringCertifications);
  const [complianceByTrade, setComplianceByTrade] = useState<ComplianceByTrade[]>([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, _setModalMode] = useState<'assign' | 'record' | 'schedule'>('assign');
  const [assignmentModalOpen, setAssignmentModalOpen] = useState(false);
  const [completionModalOpen, setCompletionModalOpen] = useState(false);
  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);
  // const [selectedTradeFilter, setSelectedTradeFilter] = useState<string>('all');

  // Mock compliance by trade data
  const mockComplianceByTrade: ComplianceByTrade[] = [
    {
      tradeId: 1,
      tradeName: 'Electricians',
      totalWorkers: 12,
      compliantWorkers: 11,
      complianceRate: 92
    },
    {
      tradeId: 2,
      tradeName: 'Plumbers',
      totalWorkers: 8,
      compliantWorkers: 6,
      complianceRate: 75
    },
    {
      tradeId: 3,
      tradeName: 'Carpenters',
      totalWorkers: 15,
      compliantWorkers: 13,
      complianceRate: 87
    },
    {
      tradeId: 4,
      tradeName: 'General Labor',
      totalWorkers: 7,
      compliantWorkers: 7,
      complianceRate: 100
    }
  ];

  useEffect(() => {
    // TODO: Fetch actual data from API
    // fetchTrainingStats(siteId, tenantId);
    // fetchUpcomingTrainings(siteId, tenantId);
    // fetchExpiringCertifications(siteId, tenantId);
    // fetchComplianceByTrade(siteId, tenantId);

    // Mock data loading
    setTimeout(() => {
      setComplianceByTrade(mockComplianceByTrade);
    }, 500);
  }, [siteId, tenantId]);

	const formatDate = (date: Date) => {
		return date.toLocaleDateString("en-US", {
			month: "short",
			day: "numeric",
			year: "numeric",
		});
	};

	// const openAssignTrainingModal = () => {
	// 	setModalMode("assign");
	// 	setModalOpen(true);
	// };

	// const openRecordCompletionModal = () => {
	// 	setModalMode("record");
	// 	setModalOpen(true);
	// };

  const openScheduleTrainingModal = () => {
    setScheduleModalOpen(true);
  };

  const handleTrainingSchedule = (trainingData: any) => {
    console.log('Training scheduled:', trainingData);
    // TODO: Implement actual scheduling logic
    // await scheduleTraining(trainingData);

    // Update upcoming trainings after scheduling
    const newTraining:UpcomingTraining = {
      id: trainingData.id,
      title: trainingData.title,
      date: new Date(trainingData.date),
      time: `${trainingData.startTime} - ${trainingData.endTime}`,
      availableSpots: trainingData.maxParticipants,
      location: "",
      totalSpots: 0
    };

    setUpcomingTrainings(prev => [...prev, newTraining]);
    setScheduleModalOpen(false);
  };

  const handleTrainingAssignment = (assignment: TrainingAssignment) => {
    console.log('Training assignment:', assignment);
    // TODO: Implement actual assignment logic
    // await assignTraining(assignment);

    // Update stats after assignment
    setStats(prev => ({
      ...prev,
      validTrainings: prev.validTrainings + assignment.workerIds.length
    }));
    setAssignmentModalOpen(false);
  };

  const handleTrainingCompletion = (completion: TrainingCompletion) => {
    console.log('Training completion:', completion);
    // TODO: Implement actual completion logic
    // await recordTrainingCompletion(completion);

    // Update stats after completion
    setStats(prev => ({
      ...prev,
      validTrainings: prev.validTrainings + 1,
      overallCompliance: Math.min(100, prev.overallCompliance + 1)
    }));
    setCompletionModalOpen(false);
  };

  // const exportComplianceReport = () => {
  //   // TODO: Implement export functionality
  //   console.log('Exporting compliance report for site:', siteId);
  // };

  const getComplianceColor = (rate: number): string => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  // const getComplianceBgColor = (rate: number): string => {
  //   if (rate >= 90) return 'bg-green-100';
  //   if (rate >= 75) return 'bg-yellow-100';
  //   return 'bg-red-100';
  // };

	return (
		<div className="space-y-6">
			{/* Compliance Summary */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<TrainingStatusCard
					title="Overall Compliance"
					percentage={stats.overallCompliance}
					status="good"
					icon={<Shield className="h-6 w-6" />}
				/>
				<TrainingStatusCard
					title="Valid Trainings"
					count={stats.validTrainings}
					status="good"
					icon={<CheckCircle className="h-6 w-6" />}
				/>
				<TrainingStatusCard
					title="Expiring Soon"
					count={stats.expiringSoon}
					status="warning"
					icon={<Clock className="h-6 w-6" />}
				/>
				<TrainingStatusCard
					title="Expired"
					count={stats.expired}
					status="danger"
					icon={<AlertTriangle className="h-6 w-6" />}
				/>
			</div>



      {/* Enhanced Upcoming Trainings Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Upcoming Trainings</h3>
            <button
              className="text-sm text-green-600 hover:text-green-800"
              onClick={() => onNavigateToTab('calendar')}
            >
              View All →
            </button>
          </div>
          <div className="space-y-4">
            {upcomingTrainings.slice(0, 4).map(training => (
              <div key={training.id} className="border-l-4 border-green-500 pl-4 py-2">
                <h4 className="font-medium text-sm text-gray-900">{training.title}</h4>
                <div className="text-xs text-gray-500 mt-1 flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatDate(training.date)} • {training.time}
                </div>
                <div className="text-xs text-gray-500 flex items-center mt-1">
                  <Users className="h-3 w-3 mr-1" />
                  {training.availableSpots} spots available
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Compliance by Trade - Enhanced */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Compliance by Trade</h3>
            <Filter className="h-4 w-4 text-gray-400" />
          </div>
          <div className="space-y-4">
            {complianceByTrade.slice(0, 4).map(trade => (
              <div key={trade.tradeId} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-900">{trade.tradeName}</span>
                    <span className={`text-sm font-bold ${getComplianceColor(trade.complianceRate)}`}>
                      {trade.complianceRate}%
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                    <span>{trade.compliantWorkers} of {trade.totalWorkers} workers</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        trade.complianceRate >= 90 ? 'bg-green-500' :
                        trade.complianceRate >= 75 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${trade.complianceRate}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <QuickActionCard
          title="Assign Training"
          description="Assign training programs to workers or groups"
          icon={<UserPlus className="h-5 w-5" />}
          onClick={() => setAssignmentModalOpen(true)}
        />
        <QuickActionCard
          title="Record Completion"
          description="Record training completion for workers"
          icon={<ClipboardCheck className="h-5 w-5" />}
          onClick={() => setCompletionModalOpen(true)}
        />
        <QuickActionCard
          title="Schedule Training"
          description="Schedule upcoming training sessions"
          icon={<CalendarPlus className="h-5 w-5" />}
          onClick={openScheduleTrainingModal}
        />
        <QuickActionCard
          title="View Reports"
          description="Generate training compliance reports"
          icon={<TrendingUp className="h-5 w-5" />}
          onClick={() => onNavigateToTab('reports')}
        />
      </div>

      {/* Enhanced Expiring Certifications */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Expiring Certifications</h3>
              <div className="flex items-center space-x-2">
                <button
                  className="text-sm text-green-600 hover:text-green-800"
                  onClick={() => onNavigateToTab('alerts')}
                >
                  View Alerts
                </button>
                <span className="text-gray-300">|</span>
                <button
                  className="text-sm text-green-600 hover:text-green-800"
                  onClick={() => onNavigateToTab('worker-status')}
                >
                  View All
                </button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Worker</th>
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Training</th>
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Expires</th>
                    <th className="text-left text-xs font-medium text-gray-500 uppercase tracking-wider py-2">Priority</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {expiringCertifications.slice(0, 6).map(cert => (
                    <tr key={`${cert.workerId}-${cert.trainingName}`} className="hover:bg-gray-50">
                      <td className="py-3 text-sm text-gray-900 font-medium">{cert.workerName}</td>
                      <td className="py-3 text-sm text-gray-900">{cert.trainingName}</td>
                      <td className="py-3 text-sm text-gray-500">{formatDate(cert.expiryDate)}</td>
                      <td className="py-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          cert.daysUntilExpiry <= 7 ? 'bg-red-100 text-red-800' :
                          cert.daysUntilExpiry <= 30 ? 'bg-amber-100 text-amber-800' : 'bg-blue-100 text-blue-800'
                        }`}>
                          {cert.daysUntilExpiry <= 7 ? 'Critical' :
                           cert.daysUntilExpiry <= 30 ? 'High' : 'Medium'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Training Activity Summary */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Recent Activity</h3>
            <button
              className="text-sm text-green-600 hover:text-green-800"
              onClick={() => onNavigateToTab('audit')}
            >
              View All →
            </button>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Trainings Completed</p>
                  <p className="text-xs text-gray-500">This week</p>
                </div>
              </div>
              <span className="text-lg font-bold text-green-600">12</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">New Assignments</p>
                  <p className="text-xs text-gray-500">This week</p>
                </div>
              </div>
              <span className="text-lg font-bold text-blue-600">8</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-amber-50 rounded-lg">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-amber-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Expiring Soon</p>
                  <p className="text-xs text-gray-500">Next 30 days</p>
                </div>
              </div>
              <span className="text-lg font-bold text-amber-600">{stats.expiringSoon}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Training Modal */}
      <TrainingModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        mode={modalMode}
        siteId={siteId}
      />

      {/* Training Assignment Modal */}
      <TrainingAssignmentModal
        isOpen={assignmentModalOpen}
        onClose={() => setAssignmentModalOpen(false)}
        mode="assign"
        siteId={siteId}
        onAssign={handleTrainingAssignment}
      />

      {/* Training Completion Modal */}
      <TrainingCompletionModal
        isOpen={completionModalOpen}
        onClose={() => setCompletionModalOpen(false)}
        siteId={siteId}
        onComplete={handleTrainingCompletion}
      />

      {/* Training Schedule Modal */}
      <TrainingScheduleModal
        isOpen={scheduleModalOpen}
        onClose={() => setScheduleModalOpen(false)}
        onSchedule={handleTrainingSchedule}
        siteId={siteId}
      />
    </div>
  );
};

export default TrainingDashboard;
