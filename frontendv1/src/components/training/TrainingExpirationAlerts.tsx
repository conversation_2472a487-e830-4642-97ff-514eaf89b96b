import React, { useState, useEffect } from 'react';
import {
  Bell,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  CheckCircle,
  X,
  Settings,
  Filter,
  Download
} from 'lucide-react';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';

interface ExpiringTraining {
  id: number;
  workerId: number;
  workerName: string;
  workerPhoto?: string;
  trainingId: number;
  trainingName: string;
  completionDate: string;
  expiryDate: string;
  daysUntilExpiry: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
  status: 'active' | 'expiring' | 'expired';
  tradeId?: number;
  tradeName?: string;
  siteId?: string;
  siteName?: string;
}

interface AlertSettings {
  criticalDays: number; // Red alerts
  warningDays: number;  // Yellow alerts
  infoDays: number;     // Blue alerts
  enableEmailAlerts: boolean;
  enableSMSAlerts: boolean;
  enablePushNotifications: boolean;
}

interface TrainingExpirationAlertsProps {
  siteId?: string;
  showSettings?: boolean;
}

const TrainingExpirationAlerts: React.FC<TrainingExpirationAlertsProps> = ({
  siteId,
  showSettings = false
}) => {
  const { tenantId } = useTenantContext();
  const [expiringTrainings, setExpiringTrainings] = useState<ExpiringTraining[]>([]);
  const [alertSettings, _setAlertSettings] = useState<AlertSettings>({
    criticalDays: 7,
    warningDays: 30,
    infoDays: 60,
    enableEmailAlerts: true,
    enableSMSAlerts: false,
    enablePushNotifications: true
  });
  const [loading, setLoading] = useState(true);
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filterTrade, setFilterTrade] = useState<string>('all');
  const [_showSettingsModal, setShowSettingsModal] = useState(false);

  // Mock data for demonstration
  const mockExpiringTrainings: ExpiringTraining[] = [
    {
      id: 1,
      workerId: 1,
      workerName: 'David Kamau',
      trainingId: 1,
      trainingName: 'Working at Heights Safety',
      completionDate: '2024-01-15T00:00:00Z',
      expiryDate: '2024-02-15T00:00:00Z',
      daysUntilExpiry: 5,
      priority: 'critical',
      status: 'expiring',
      tradeId: 1,
      tradeName: 'Carpenter',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    },
    {
      id: 2,
      workerId: 2,
      workerName: 'Mary Wanjiku',
      trainingId: 2,
      trainingName: 'First Aid Level 1',
      completionDate: '2023-12-01T00:00:00Z',
      expiryDate: '2024-03-01T00:00:00Z',
      daysUntilExpiry: 20,
      priority: 'high',
      status: 'expiring',
      tradeId: 2,
      tradeName: 'Electrician',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    },
    {
      id: 3,
      workerId: 3,
      workerName: 'Peter Ochieng',
      trainingId: 3,
      trainingName: 'Equipment Operation',
      completionDate: '2023-11-15T00:00:00Z',
      expiryDate: '2024-03-15T00:00:00Z',
      daysUntilExpiry: 35,
      priority: 'medium',
      status: 'expiring',
      tradeId: 3,
      tradeName: 'Plumber',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    },
    {
      id: 4,
      workerId: 4,
      workerName: 'Jane Doe',
      trainingId: 1,
      trainingName: 'Working at Heights Safety',
      completionDate: '2023-12-01T00:00:00Z',
      expiryDate: '2024-01-20T00:00:00Z',
      daysUntilExpiry: -10,
      priority: 'critical',
      status: 'expired',
      tradeId: 1,
      tradeName: 'Carpenter',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    }
  ];

  useEffect(() => {
    fetchExpiringTrainings();
  }, [tenantId, siteId, alertSettings]);

  const fetchExpiringTrainings = async () => {
    setLoading(true);
    try {
      // In real implementation:
      // const result = await getExpiringTrainings({
      //   tenantId,
      //   siteId,
      //   daysAhead: alertSettings.infoDays
      // });

      // Mock implementation
      setTimeout(() => {
        let filtered = mockExpiringTrainings;
        
        if (siteId) {
          filtered = filtered.filter(training => training.siteId === siteId);
        }

        // Calculate priority based on settings
        const updatedTrainings = filtered.map(training => ({
          ...training,
          priority: calculatePriority(training.daysUntilExpiry, alertSettings)
        }));

        setExpiringTrainings(updatedTrainings);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('Error fetching expiring trainings:', error);
      setLoading(false);
    }
  };

  const calculatePriority = (daysUntilExpiry: number, settings: AlertSettings): 'critical' | 'high' | 'medium' | 'low' => {
    if (daysUntilExpiry < 0) return 'critical'; // Expired
    if (daysUntilExpiry <= settings.criticalDays) return 'critical';
    if (daysUntilExpiry <= settings.warningDays) return 'high';
    if (daysUntilExpiry <= settings.infoDays) return 'medium';
    return 'low';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <Clock className="h-4 w-4" />;
      case 'medium': return <Bell className="h-4 w-4" />;
      default: return <CheckCircle className="h-4 w-4" />;
    }
  };

  const handleDismissAlert = (trainingId: number) => {
    setExpiringTrainings(prev => 
      prev.filter(training => training.id !== trainingId)
    );
  };

  const handleRescheduleTraining = (training: ExpiringTraining) => {
    // In real implementation, this would open a reschedule modal
    console.log('Reschedule training:', training);
  };

  const exportAlerts = () => {
    // In real implementation, this would export to CSV/Excel
    console.log('Exporting alerts...');
  };

  const filteredTrainings = expiringTrainings.filter(training => {
    const matchesPriority = filterPriority === 'all' || training.priority === filterPriority;
    const matchesTrade = filterTrade === 'all' || training.tradeName === filterTrade;
    return matchesPriority && matchesTrade;
  });

  const alertCounts = {
    critical: expiringTrainings.filter(t => t.priority === 'critical').length,
    high: expiringTrainings.filter(t => t.priority === 'high').length,
    medium: expiringTrainings.filter(t => t.priority === 'medium').length,
    total: expiringTrainings.length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Training Expiration Alerts</h2>
          <p className="text-sm text-gray-600">
            Monitor and manage expiring training certifications
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {showSettings && (
            <button
              onClick={() => setShowSettingsModal(true)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </button>
          )}
          <button
            onClick={exportAlerts}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Alert Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-red-900">Critical</p>
              <p className="text-2xl font-bold text-red-600">{alertCounts.critical}</p>
            </div>
          </div>
        </div>
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center">
            <Clock className="h-5 w-5 text-orange-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-orange-900">High Priority</p>
              <p className="text-2xl font-bold text-orange-600">{alertCounts.high}</p>
            </div>
          </div>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <Bell className="h-5 w-5 text-yellow-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-yellow-900">Medium Priority</p>
              <p className="text-2xl font-bold text-yellow-600">{alertCounts.medium}</p>
            </div>
          </div>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-blue-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-blue-900">Total Alerts</p>
              <p className="text-2xl font-bold text-blue-600">{alertCounts.total}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            value={filterPriority}
            onChange={(e) => setFilterPriority(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
          >
            <option value="all">All Priorities</option>
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
        <div>
          <select
            value={filterTrade}
            onChange={(e) => setFilterTrade(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
          >
            <option value="all">All Trades</option>
            <option value="Carpenter">Carpenter</option>
            <option value="Electrician">Electrician</option>
            <option value="Plumber">Plumber</option>
          </select>
        </div>
      </div>

      {/* Alerts List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Worker
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Training
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expires
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Days Left
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTrainings.map((training) => (
                <tr key={training.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(training.priority)}`}>
                      {getPriorityIcon(training.priority)}
                      <span className="ml-1 capitalize">{training.priority}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {training.workerName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {training.tradeName}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{training.trainingName}</div>
                    {training.siteName && (
                      <div className="text-sm text-gray-500">{training.siteName}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                      {new Date(training.expiryDate).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${
                      training.daysUntilExpiry < 0 ? 'text-red-600' :
                      training.daysUntilExpiry <= 7 ? 'text-red-600' :
                      training.daysUntilExpiry <= 30 ? 'text-orange-600' : 'text-gray-600'
                    }`}>
                      {training.daysUntilExpiry < 0 
                        ? `${Math.abs(training.daysUntilExpiry)} days overdue`
                        : `${training.daysUntilExpiry} days`
                      }
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleRescheduleTraining(training)}
                        className="text-green-600 hover:text-green-900"
                      >
                        Reschedule
                      </button>
                      <button
                        onClick={() => handleDismissAlert(training.id)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredTrainings.length === 0 && (
          <div className="text-center py-12">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No alerts found</h3>
            <p className="text-gray-500">
              {filterPriority !== 'all' || filterTrade !== 'all'
                ? 'Try adjusting your filters.'
                : 'All training certifications are up to date!'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrainingExpirationAlerts;
