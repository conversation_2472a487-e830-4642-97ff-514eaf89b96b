import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  <PERSON>,
  Clock,
  AlertTriangle,
  CheckCircle,
  GraduationCap
} from 'lucide-react';
import { Training, TrainingStatus } from '../../types';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';

interface TrainingProgramMasterProps {
  onCreateProgram?: () => void;
  onEditProgram?: (program: Training) => void;
}

export interface TrainingProgramFormData {
  name: string;
  description: string;
  validityPeriodMonths: number;
  trainingType: string;
  isMandatory: boolean;
  associatedTrades: number[];
  materials?: File[];
}

const TrainingProgramMaster: React.FC<TrainingProgramMasterProps> = ({
  onCreateProgram,
  onEditProgram
}) => {
  const { tenantId } = useTenantContext();
  const [programs, setPrograms] = useState<Training[]>([]);
  const [filteredPrograms, setFilteredPrograms] = useState<Training[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [_showCreateModal, setShowCreateModal] = useState(false);
  const [_editingProgram, setEditingProgram] = useState<Training | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data for demonstration
  const mockPrograms: Training[] = [
    {
      id: 1,
      tenantId: tenantId || 'tenant-1',
      name: 'Working at Height Safety',
      description: 'Comprehensive safety training for elevated work environments',
      validityPeriodMonths: 12,
      trainingType: 'Safety',
      trainer: 'Safety Officer',
      frequency: 'Annually',
      status: TrainingStatus.Scheduled,
      workers: [],
      trainingHistory: [],
      createdAt: '2025-01-01T00:00:00Z',
      createdBy: 'System'
    },
    {
      id: 2,
      tenantId: tenantId || 'tenant-1',
      name: 'First Aid Level 1',
      description: 'Basic first aid and emergency response procedures',
      validityPeriodMonths: 24,
      trainingType: 'Safety',
      trainer: 'Medical Officer',
      frequency: 'Every 2 years',
      status: TrainingStatus.Scheduled,
      workers: [],
      trainingHistory: [],
      createdAt: '2025-01-01T00:00:00Z',
      createdBy: 'System'
    },
    {
      id: 3,
      tenantId: tenantId || 'tenant-1',
      name: 'Equipment Operation Certification',
      description: 'Heavy machinery operation and safety protocols',
      validityPeriodMonths: 36,
      trainingType: 'Technical',
      trainer: 'Equipment Specialist',
      frequency: 'Every 3 years',
      status: TrainingStatus.Scheduled,
      workers: [],
      trainingHistory: [],
      createdAt: '2025-01-01T00:00:00Z',
      createdBy: 'System'
    }
  ];

  useEffect(() => {
    // Simulate API call
    const fetchPrograms = async () => {
      setLoading(true);
      // In real implementation, this would be:
      // const result = await getTrainingPrograms(tenantId);
      setTimeout(() => {
        setPrograms(mockPrograms);
        setFilteredPrograms(mockPrograms);
        setLoading(false);
      }, 500);
    };

    if (tenantId) {
      fetchPrograms();
    }
  }, [tenantId]);

  useEffect(() => {
    let filtered = programs;

    if (searchTerm) {
      filtered = filtered.filter(program =>
        program.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        program.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (typeFilter) {
      filtered = filtered.filter(program => program.trainingType === typeFilter);
    }

    if (statusFilter) {
      filtered = filtered.filter(program => program.status === statusFilter);
    }

    setFilteredPrograms(filtered);
  }, [programs, searchTerm, typeFilter, statusFilter]);

  const handleCreateProgram = () => {
    setShowCreateModal(true);
    onCreateProgram?.();
  };

  const handleEditProgram = (program: Training) => {
    setEditingProgram(program);
    onEditProgram?.(program);
  };

  const handleDeleteProgram = async (programId: number) => {
    if (window.confirm('Are you sure you want to delete this training program?')) {
      // In real implementation:
      // await deleteTrainingProgram(programId);
      setPrograms(prev => prev.filter(p => p.id !== programId));
    }
  };

  const getStatusIcon = (status: TrainingStatus) => {
    switch (status) {
      case TrainingStatus.Completed:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case TrainingStatus.InProgress:
        return <Clock className="h-4 w-4 text-blue-500" />;
      case TrainingStatus.Expired:
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <GraduationCap className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: TrainingStatus) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    switch (status) {
      case TrainingStatus.Completed:
        return `${baseClasses} bg-green-100 text-green-800`;
      case TrainingStatus.InProgress:
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case TrainingStatus.Expired:
        return `${baseClasses} bg-red-100 text-red-800`;
      case TrainingStatus.Cancelled:
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Master Training Programs</h2>
          <p className="text-sm text-gray-600">
            Manage company-wide training programs and requirements
          </p>
        </div>
        <button
          onClick={handleCreateProgram}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Program
        </button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search training programs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
          >
            <option value="">All Types</option>
            <option value="Safety">Safety</option>
            <option value="Technical">Technical</option>
            <option value="Compliance">Compliance</option>
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
          >
            <option value="">All Status</option>
            <option value={TrainingStatus.Scheduled}>Scheduled</option>
            <option value={TrainingStatus.InProgress}>In Progress</option>
            <option value={TrainingStatus.Completed}>Completed</option>
            <option value={TrainingStatus.Cancelled}>Cancelled</option>
          </select>
        </div>
      </div>

      {/* Programs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPrograms.map((program) => (
          <div key={program.id} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  {getStatusIcon(program.status)}
                  <h3 className="ml-2 text-lg font-medium text-gray-900 truncate">
                    {program.name}
                  </h3>
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEditProgram(program)}
                    className="p-1 text-gray-400 hover:text-gray-600"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteProgram(program.id)}
                    className="p-1 text-gray-400 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                {program.description}
              </p>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Type:</span>
                  <span className="font-medium">{program.trainingType}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Validity:</span>
                  <span className="font-medium">
                    {program.validityPeriodMonths ? `${program.validityPeriodMonths} months` : 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Frequency:</span>
                  <span className="font-medium">{program.frequency || 'One-time'}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className={getStatusBadge(program.status)}>
                  {program.status}
                </span>
                <div className="flex items-center text-sm text-gray-500">
                  <Users className="h-4 w-4 mr-1" />
                  {program.workers?.length || 0} assigned
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredPrograms.length === 0 && (
        <div className="text-center py-12">
          <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No training programs found</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || typeFilter || statusFilter
              ? 'Try adjusting your search criteria.'
              : 'Get started by creating your first training program.'}
          </p>
          {!searchTerm && !typeFilter && !statusFilter && (
            <button
              onClick={handleCreateProgram}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Training Program
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default TrainingProgramMaster;
