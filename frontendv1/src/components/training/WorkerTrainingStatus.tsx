import React, { useState, useEffect } from "react";
import { Search, Download } from "lucide-react";
import ComplianceStatusBadge from "./shared/ComplianceStatusBadge";
import TrainingModal from "./shared/TrainingModal";
import { WorkerTrainingStatus as WorkerTrainingStatusType } from "../../types/training";

interface WorkerTrainingStatusProps {
	siteId: string;
}

// Mock data - replace with actual API calls
const mockWorkers: WorkerTrainingStatusType[] = [
	{
		workerId: "w1",
		name: "<PERSON>",
		photo: undefined,
		nationalId: "12345678",
		primaryTrade: "Electrician",
		requiredTrainings: 5,
		completedTrainings: 4,
		complianceStatus: "expiring",
		nextExpiryDate: new Date("2024-02-15"),
		trainings: [],
	},
	{
		workerId: "w2",
		name: "<PERSON>",
		photo: undefined,
		nationalId: "87654321",
		primaryTrade: "Plumber",
		requiredTrainings: 4,
		completedTrainings: 4,
		complianceStatus: "compliant",
		nextExpiryDate: new Date("2024-06-20"),
		trainings: [],
	},
	{
		workerId: "w3",
		name: "<PERSON>",
		photo: undefined,
		nationalId: "11223344",
		primaryTrade: "Mason",
		requiredTrainings: 3,
		completedTrainings: 2,
		complianceStatus: "incomplete",
		nextExpiryDate: undefined,
		trainings: [],
	},
	{
		workerId: "w4",
		name: "Grace Muthoni",
		photo: undefined,
		nationalId: "44332211",
		primaryTrade: "Carpenter",
		requiredTrainings: 4,
		completedTrainings: 3,
		complianceStatus: "expired",
		nextExpiryDate: new Date("2024-01-10"),
		trainings: [],
	},
];

const mockTrades = [
	{ id: "electrician", name: "Electrician" },
	{ id: "plumber", name: "Plumber" },
	{ id: "mason", name: "Mason" },
	{ id: "carpenter", name: "Carpenter" },
	{ id: "welder", name: "Welder" },
];

const WorkerTrainingStatus: React.FC<WorkerTrainingStatusProps> = ({
	siteId,
}) => {
	const [workers, _setWorkers] =
		useState<WorkerTrainingStatusType[]>(mockWorkers);
	const [filteredWorkers, setFilteredWorkers] =
		useState<WorkerTrainingStatusType[]>(mockWorkers);
	const [searchTerm, setSearchTerm] = useState("");
	const [tradeFilter, setTradeFilter] = useState("");
	const [statusFilter, setStatusFilter] = useState("");
	const [modalOpen, setModalOpen] = useState(false);
	const [modalMode, setModalMode] = useState<"assign" | "record">("assign");
	const [selectedWorkerId, setSelectedWorkerId] = useState<
		string | undefined
	>();

	useEffect(() => {
		// TODO: Fetch actual data from API
		// fetchWorkerTrainingStatus(siteId);
	}, [siteId]);

	useEffect(() => {
		let filtered = workers;

		// Apply search filter
		if (searchTerm) {
			filtered = filtered.filter(
				(worker) =>
					worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
					worker.nationalId.includes(searchTerm),
			);
		}

		// Apply trade filter
		if (tradeFilter) {
			filtered = filtered.filter(
				(worker) =>
					worker.primaryTrade.toLowerCase() === tradeFilter.toLowerCase(),
			);
		}

		// Apply status filter
		if (statusFilter) {
			filtered = filtered.filter(
				(worker) => worker.complianceStatus === statusFilter,
			);
		}

		setFilteredWorkers(filtered);
	}, [workers, searchTerm, tradeFilter, statusFilter]);

	const formatDate = (date: Date) => {
		return date.toLocaleDateString("en-US", {
			month: "short",
			day: "numeric",
			year: "numeric",
		});
	};

	const viewWorkerTrainings = (workerId: string) => {
		// TODO: Implement worker training details view
		console.log("View worker trainings:", workerId);
	};

	const assignTraining = (workerId: string) => {
		setSelectedWorkerId(workerId);
		setModalMode("assign");
		setModalOpen(true);
	};

	const exportData = () => {
		// TODO: Implement export functionality
		console.log("Export worker training data");
	};

	return (
		<div className="space-y-6">
			{/* Search & Filter Controls */}
			<div className="flex flex-col md:flex-row gap-4 justify-between">
				<div className="relative">
					<Search
						className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
						size={18}
					/>
					<input
						type="text"
						placeholder="Search workers..."
						className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full md:w-80 focus:ring-2 focus:ring-green-500 focus:border-green-500"
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
					/>
				</div>
				<div className="flex gap-2">
					<select
						className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500"
						value={tradeFilter}
						onChange={(e) => setTradeFilter(e.target.value)}
					>
						<option value="">All Trades</option>
						{mockTrades.map((trade) => (
							<option key={trade.id} value={trade.name}>
								{trade.name}
							</option>
						))}
					</select>
					<select
						className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500"
						value={statusFilter}
						onChange={(e) => setStatusFilter(e.target.value)}
					>
						<option value="">All Statuses</option>
						<option value="compliant">Compliant</option>
						<option value="expiring">Expiring Soon</option>
						<option value="expired">Expired</option>
						<option value="incomplete">Incomplete</option>
					</select>
					<button
						className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-green-500 focus:border-green-500"
						onClick={exportData}
					>
						<Download className="h-4 w-4 inline mr-1" />
						Export
					</button>
				</div>
			</div>

			{/* Results Summary */}
			<div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
				<p className="text-sm text-gray-600">
					Showing <span className="font-medium">{filteredWorkers.length}</span>{" "}
					of <span className="font-medium">{workers.length}</span> workers
				</p>
			</div>

			{/* Worker Training Table */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th
									scope="col"
									className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									Worker
								</th>
								<th
									scope="col"
									className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									Trade
								</th>
								<th
									scope="col"
									className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									Required Trainings
								</th>
								<th
									scope="col"
									className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									Compliance Status
								</th>
								<th
									scope="col"
									className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									Next Expiry
								</th>
								<th
									scope="col"
									className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
								>
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredWorkers.map((worker) => (
								<tr key={worker.workerId} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<div className="flex-shrink-0 h-10 w-10">
												{worker.photo ? (
													<img
														className="h-10 w-10 rounded-full"
														src={worker.photo}
														alt=""
													/>
												) : (
													<div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-medium">
														{worker.name.charAt(0)}
													</div>
												)}
											</div>
											<div className="ml-4">
												<div className="text-sm font-medium text-gray-900">
													{worker.name}
												</div>
												<div className="text-sm text-gray-500">
													ID: {worker.nationalId}
												</div>
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{worker.primaryTrade}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">
											{worker.requiredTrainings} required
										</div>
										<div className="text-sm text-gray-500">
											{worker.completedTrainings} completed
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<ComplianceStatusBadge status={worker.complianceStatus} />
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{worker.nextExpiryDate
											? formatDate(worker.nextExpiryDate)
											: "N/A"}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
										<button
											onClick={() => viewWorkerTrainings(worker.workerId)}
											className="text-green-600 hover:text-green-900 mr-3"
										>
											View
										</button>
										<button
											onClick={() => assignTraining(worker.workerId)}
											className="text-indigo-600 hover:text-indigo-900"
										>
											Assign
										</button>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>

				{filteredWorkers.length === 0 && (
					<div className="text-center py-12">
						<p className="text-gray-500">
							No workers found matching your criteria.
						</p>
					</div>
				)}
			</div>

			{/* Training Modal */}
			<TrainingModal
				isOpen={modalOpen}
				onClose={() => setModalOpen(false)}
				mode={modalMode}
				workerId={selectedWorkerId}
				siteId={siteId}
			/>
		</div>
	);
};

export default WorkerTrainingStatus;
