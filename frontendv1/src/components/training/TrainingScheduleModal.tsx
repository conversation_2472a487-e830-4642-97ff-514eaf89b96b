import React, { useState, useEffect } from 'react';
import {
  X,
  Calendar,
  Clock,
  MapPin,
  Users,
  User,
} from 'lucide-react';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';

interface TrainingProgram {
  id: string;
  name: string;
  description: string;
  duration: number; // in hours
  maxParticipants: number;
  requiredSkills: string[];
}

interface Instructor {
  id: string;
  name: string;
  email: string;
  specializations: string[];
  availability: string[];
}

interface TrainingScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSchedule: (trainingData: any) => void;
  siteId: string;
}

const TrainingScheduleModal: React.FC<TrainingScheduleModalProps> = ({
  isOpen,
  onClose,
  onSchedule,
  siteId
}) => {
  const { tenantId } = useTenantContext();
  const [formData, setFormData] = useState({
    trainingProgramId: '',
    title: '',
    description: '',
    date: '',
    startTime: '',
    endTime: '',
    location: '',
    instructorId: '',
    maxParticipants: 20,
    notes: '',
    isRecurring: false,
    recurringType: 'weekly',
    recurringEnd: '',
    materials: [''],
    prerequisites: ['']
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [trainingPrograms, setTrainingPrograms] = useState<TrainingProgram[]>([]);
  const [instructors, setInstructors] = useState<Instructor[]>([]);

  // Mock data
  const mockTrainingPrograms: TrainingProgram[] = [
    {
      id: 'tp1',
      name: 'Working at Heights Safety',
      description: 'Comprehensive safety training for elevated work',
      duration: 8,
      maxParticipants: 20,
      requiredSkills: ['Basic Safety Awareness']
    },
    {
      id: 'tp2',
      name: 'First Aid Level 1',
      description: 'Basic first aid and emergency response',
      duration: 6,
      maxParticipants: 15,
      requiredSkills: []
    },
    {
      id: 'tp3',
      name: 'Equipment Operation',
      description: 'Heavy machinery operation and maintenance',
      duration: 4,
      maxParticipants: 10,
      requiredSkills: ['Mechanical Aptitude']
    }
  ];

  const mockInstructors: Instructor[] = [
    {
      id: 'inst1',
      name: 'John Smith',
      email: '<EMAIL>',
      specializations: ['Safety Training', 'Working at Heights'],
      availability: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
    },
    {
      id: 'inst2',
      name: 'Dr. Sarah Johnson',
      email: '<EMAIL>',
      specializations: ['First Aid', 'Medical Training'],
      availability: ['Tuesday', 'Wednesday', 'Thursday']
    },
    {
      id: 'inst3',
      name: 'Mike Wilson',
      email: '<EMAIL>',
      specializations: ['Equipment Operation', 'Technical Training'],
      availability: ['Monday', 'Wednesday', 'Friday']
    }
  ];

  useEffect(() => {
    if (isOpen) {
      setTrainingPrograms(mockTrainingPrograms);
      setInstructors(mockInstructors);
    }
  }, [isOpen]);

  useEffect(() => {
    // Auto-fill data when training program is selected
    if (formData.trainingProgramId) {
      const program = trainingPrograms.find(p => p.id === formData.trainingProgramId);
      if (program) {
        setFormData(prev => ({
          ...prev,
          title: program.name,
          description: program.description,
          maxParticipants: program.maxParticipants,
          endTime: calculateEndTime(prev.startTime, program.duration)
        }));
      }
    }
  }, [formData.trainingProgramId, formData.startTime, trainingPrograms]);

  const calculateEndTime = (startTime: string, duration: number): string => {
    if (!startTime) return '';
    
    const [hours, minutes] = startTime.split(':').map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);
    
    const endDate = new Date(startDate.getTime() + duration * 60 * 60 * 1000);
    
    return endDate.toTimeString().slice(0, 5);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.trainingProgramId) newErrors.trainingProgramId = 'Training program is required';
    if (!formData.title) newErrors.title = 'Title is required';
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.startTime) newErrors.startTime = 'Start time is required';
    if (!formData.endTime) newErrors.endTime = 'End time is required';
    if (!formData.location) newErrors.location = 'Location is required';
    if (!formData.instructorId) newErrors.instructorId = 'Instructor is required';
    if (formData.maxParticipants < 1) newErrors.maxParticipants = 'Must have at least 1 participant';

    // Validate date is not in the past
    if (formData.date) {
      const selectedDate = new Date(formData.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate < today) {
        newErrors.date = 'Date cannot be in the past';
      }
    }

    // Validate time range
    if (formData.startTime && formData.endTime) {
      if (formData.startTime >= formData.endTime) {
        newErrors.endTime = 'End time must be after start time';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const trainingData = {
        ...formData,
        siteId,
        tenantId,
        id: Date.now().toString(),
        status: 'scheduled',
        createdAt: new Date().toISOString(),
        registeredWorkers: []
      };

      onSchedule(trainingData);
      onClose();
      resetForm();
    } catch (error) {
      console.error('Error scheduling training:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      trainingProgramId: '',
      title: '',
      description: '',
      date: '',
      startTime: '',
      endTime: '',
      location: '',
      instructorId: '',
      maxParticipants: 20,
      notes: '',
      isRecurring: false,
      recurringType: 'weekly',
      recurringEnd: '',
      materials: [''],
      prerequisites: ['']
    });
    setErrors({});
  };

  // const addArrayField = (field: 'materials' | 'prerequisites') => {
  //   setFormData(prev => ({
  //     ...prev,
  //     [field]: [...prev[field], '']
  //   }));
  // };

  // const removeArrayField = (field: 'materials' | 'prerequisites', index: number) => {
  //   setFormData(prev => ({
  //     ...prev,
  //     [field]: prev[field].filter((_, i) => i !== index)
  //   }));
  // };

  // const updateArrayField = (field: 'materials' | 'prerequisites', index: number, value: string) => {
  //   setFormData(prev => ({
  //     ...prev,
  //     [field]: prev[field].map((item, i) => i === index ? value : item)
  //   }));
  // };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Schedule Training Session</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Training Program Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Training Program *
              </label>
              <select
                value={formData.trainingProgramId}
                onChange={(e) => setFormData(prev => ({ ...prev, trainingProgramId: e.target.value }))}
                className={`w-full border rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.trainingProgramId ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select a training program</option>
                {trainingPrograms.map(program => (
                  <option key={program.id} value={program.id}>
                    {program.name} ({program.duration}h)
                  </option>
                ))}
              </select>
              {errors.trainingProgramId && (
                <p className="mt-1 text-sm text-red-600">{errors.trainingProgramId}</p>
              )}
            </div>

            {/* Custom Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Session Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className={`w-full border rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.title ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter session title"
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-600">{errors.title}</p>
              )}
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
              placeholder="Enter session description"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="h-4 w-4 inline mr-1" />
                Date *
              </label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                className={`w-full border rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.date ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.date && (
                <p className="mt-1 text-sm text-red-600">{errors.date}</p>
              )}
            </div>

            {/* Start Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="h-4 w-4 inline mr-1" />
                Start Time *
              </label>
              <input
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                className={`w-full border rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.startTime ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.startTime && (
                <p className="mt-1 text-sm text-red-600">{errors.startTime}</p>
              )}
            </div>

            {/* End Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="h-4 w-4 inline mr-1" />
                End Time *
              </label>
              <input
                type="time"
                value={formData.endTime}
                onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                className={`w-full border rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.endTime ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.endTime && (
                <p className="mt-1 text-sm text-red-600">{errors.endTime}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Location */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <MapPin className="h-4 w-4 inline mr-1" />
                Location *
              </label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                className={`w-full border rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.location ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="e.g., Training Room A, Site Workshop"
              />
              {errors.location && (
                <p className="mt-1 text-sm text-red-600">{errors.location}</p>
              )}
            </div>

            {/* Instructor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="h-4 w-4 inline mr-1" />
                Instructor *
              </label>
              <select
                value={formData.instructorId}
                onChange={(e) => setFormData(prev => ({ ...prev, instructorId: e.target.value }))}
                className={`w-full border rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.instructorId ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select an instructor</option>
                {instructors.map(instructor => (
                  <option key={instructor.id} value={instructor.id}>
                    {instructor.name} - {instructor.specializations.join(', ')}
                  </option>
                ))}
              </select>
              {errors.instructorId && (
                <p className="mt-1 text-sm text-red-600">{errors.instructorId}</p>
              )}
            </div>
          </div>

          {/* Max Participants */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Users className="h-4 w-4 inline mr-1" />
                Maximum Participants *
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={formData.maxParticipants}
                onChange={(e) => setFormData(prev => ({ ...prev, maxParticipants: parseInt(e.target.value) || 0 }))}
                className={`w-full border rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500 ${
                  errors.maxParticipants ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.maxParticipants && (
                <p className="mt-1 text-sm text-red-600">{errors.maxParticipants}</p>
              )}
            </div>

            {/* Recurring Training */}
            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.isRecurring}
                  onChange={(e) => setFormData(prev => ({ ...prev, isRecurring: e.target.checked }))}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="text-sm font-medium text-gray-700">Recurring Training</span>
              </label>
              
              {formData.isRecurring && (
                <div className="mt-2 space-y-2">
                  <select
                    value={formData.recurringType}
                    onChange={(e) => setFormData(prev => ({ ...prev, recurringType: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                  </select>
                  <input
                    type="date"
                    value={formData.recurringEnd}
                    onChange={(e) => setFormData(prev => ({ ...prev, recurringEnd: e.target.value }))}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
                    placeholder="End date"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
              placeholder="Any additional information or requirements"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Scheduling...' : 'Schedule Training'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TrainingScheduleModal;
