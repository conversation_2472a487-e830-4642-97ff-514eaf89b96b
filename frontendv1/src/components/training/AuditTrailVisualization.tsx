import React, { useState, useEffect } from 'react';
import {
  User,
  Edit,
  Plus,
  Trash2,
  Eye,
  Download,
  Search,
  FileText,
  Shield
} from 'lucide-react';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';

interface AuditLogEntry {
  id: string;
  entityType: 'worker' | 'training' | 'training_program' | 'assignment' | 'completion';
  entityId: number;
  entityName: string;
  action: 'created' | 'updated' | 'deleted' | 'assigned' | 'completed' | 'viewed';
  fieldChanges?: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  performedBy: string;
  performedByRole: string;
  performedAt: string;
  ipAddress?: string;
  userAgent?: string;
  notes?: string;
  metadata?: {
    siteId?: string;
    siteName?: string;
    workerId?: number;
    workerName?: string;
    trainingId?: number;
    trainingName?: string;
  };
}

interface AuditTrailVisualizationProps {
  entityType?: string;
  entityId?: number;
  siteId?: string;
  showFilters?: boolean;
  maxEntries?: number;
}

const AuditTrailVisualization: React.FC<AuditTrailVisualizationProps> = ({
  entityType,
  entityId,
  siteId,
  showFilters = true,
  // maxEntries = 100
}) => {
  const { tenantId } = useTenantContext();
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState<string>('all');
  const [entityTypeFilter, setEntityTypeFilter] = useState<string>(entityType || 'all');
  const [userFilter, setUserFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: '',
    end: ''
  });

  // Mock audit log data
  const mockAuditLogs: AuditLogEntry[] = [
    {
      id: '1',
      entityType: 'training',
      entityId: 1,
      entityName: 'Working at Heights Safety',
      action: 'assigned',
      performedBy: 'John Smith',
      performedByRole: 'Training Manager',
      performedAt: '2025-01-15T10:30:00Z',
      ipAddress: '*************',
      notes: 'Bulk assignment to electricians',
      metadata: {
        siteId: 'site1',
        siteName: 'Main Construction Site',
        trainingId: 1,
        trainingName: 'Working at Heights Safety'
      }
    },
    {
      id: '2',
      entityType: 'worker',
      entityId: 1,
      entityName: 'David Kamau',
      action: 'updated',
      fieldChanges: [
        {
          field: 'phoneNumber',
          oldValue: '+254 712 345 678',
          newValue: '+254 712 345 679'
        },
        {
          field: 'email',
          oldValue: '<EMAIL>',
          newValue: '<EMAIL>'
        }
      ],
      performedBy: 'Sarah Johnson',
      performedByRole: 'HR Manager',
      performedAt: '2025-01-15T09:15:00Z',
      ipAddress: '*************',
      metadata: {
        siteId: 'site1',
        siteName: 'Main Construction Site',
        workerId: 1,
        workerName: 'David Kamau'
      }
    },
    {
      id: '3',
      entityType: 'completion',
      entityId: 1,
      entityName: 'Training Completion Record',
      action: 'created',
      performedBy: 'Mike Wilson',
      performedByRole: 'Site Supervisor',
      performedAt: '2025-01-14T16:45:00Z',
      ipAddress: '*************',
      notes: 'Certificate uploaded and verified',
      metadata: {
        siteId: 'site1',
        siteName: 'Main Construction Site',
        workerId: 2,
        workerName: 'Mary Wanjiku',
        trainingId: 2,
        trainingName: 'First Aid Level 1'
      }
    },
    {
      id: '4',
      entityType: 'training_program',
      entityId: 3,
      entityName: 'Equipment Operation Training',
      action: 'created',
      performedBy: 'Admin User',
      performedByRole: 'System Administrator',
      performedAt: '2025-01-14T14:20:00Z',
      ipAddress: '*************',
      notes: 'New training program for heavy equipment operators'
    },
    {
      id: '5',
      entityType: 'worker',
      entityId: 3,
      entityName: 'Peter Ochieng',
      action: 'created',
      performedBy: 'Sarah Johnson',
      performedByRole: 'HR Manager',
      performedAt: '2025-01-14T11:30:00Z',
      ipAddress: '*************',
      notes: 'New worker onboarding',
      metadata: {
        siteId: 'site1',
        siteName: 'Main Construction Site',
        workerId: 3,
        workerName: 'Peter Ochieng'
      }
    }
  ];

  useEffect(() => {
    fetchAuditLogs();
  }, [tenantId, entityType, entityId, siteId]);

  const fetchAuditLogs = async () => {
    setLoading(true);
    try {
      // In real implementation:
      // const result = await getAuditLogs({
      //   tenantId,
      //   entityType,
      //   entityId,
      //   siteId,
      //   limit: maxEntries
      // });

      // Mock implementation
      setTimeout(() => {
        let filtered = mockAuditLogs;
        
        if (entityType && entityId) {
          filtered = filtered.filter(log => 
            log.entityType === entityType && log.entityId === entityId
          );
        }
        
        if (siteId) {
          filtered = filtered.filter(log => 
            log.metadata?.siteId === siteId
          );
        }

        setAuditLogs(filtered);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      setLoading(false);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'created': return <Plus className="h-4 w-4 text-green-600" />;
      case 'updated': return <Edit className="h-4 w-4 text-blue-600" />;
      case 'deleted': return <Trash2 className="h-4 w-4 text-red-600" />;
      case 'assigned': return <User className="h-4 w-4 text-purple-600" />;
      case 'completed': return <Shield className="h-4 w-4 text-green-600" />;
      case 'viewed': return <Eye className="h-4 w-4 text-gray-600" />;
      default: return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'created': return 'text-green-600 bg-green-100';
      case 'updated': return 'text-blue-600 bg-blue-100';
      case 'deleted': return 'text-red-600 bg-red-100';
      case 'assigned': return 'text-purple-600 bg-purple-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'viewed': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString()
    };
  };

  const exportAuditLogs = () => {
    // In real implementation, this would export to CSV/Excel
    console.log('Exporting audit logs...');
  };

  const filteredLogs = auditLogs.filter(log => {
    const matchesSearch = 
      log.entityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.performedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.notes?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesAction = actionFilter === 'all' || log.action === actionFilter;
    const matchesEntityType = entityTypeFilter === 'all' || log.entityType === entityTypeFilter;
    const matchesUser = userFilter === 'all' || log.performedBy === userFilter;
    
    let matchesDateRange = true;
    if (dateRange.start && dateRange.end) {
      const logDate = new Date(log.performedAt);
      const startDate = new Date(dateRange.start);
      const endDate = new Date(dateRange.end);
      matchesDateRange = logDate >= startDate && logDate <= endDate;
    }

    return matchesSearch && matchesAction && matchesEntityType && matchesUser && matchesDateRange;
  });

  const uniqueUsers = [...new Set(auditLogs.map(log => log.performedBy))];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Audit Trail</h2>
          <p className="text-sm text-gray-600">
            Complete history of training-related activities and changes
          </p>
        </div>
        <button
          onClick={exportAuditLogs}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50"
        >
          <Download className="h-4 w-4 mr-2" />
          Export
        </button>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Action</label>
              <select
                value={actionFilter}
                onChange={(e) => setActionFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="all">All Actions</option>
                <option value="created">Created</option>
                <option value="updated">Updated</option>
                <option value="deleted">Deleted</option>
                <option value="assigned">Assigned</option>
                <option value="completed">Completed</option>
                <option value="viewed">Viewed</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Entity Type</label>
              <select
                value={entityTypeFilter}
                onChange={(e) => setEntityTypeFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="all">All Types</option>
                <option value="worker">Workers</option>
                <option value="training">Trainings</option>
                <option value="training_program">Training Programs</option>
                <option value="assignment">Assignments</option>
                <option value="completion">Completions</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">User</label>
              <select
                value={userFilter}
                onChange={(e) => setUserFilter(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="all">All Users</option>
                {uniqueUsers.map(user => (
                  <option key={user} value={user}>{user}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
              <div className="flex space-x-1">
                <input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-2 py-2 text-sm focus:ring-green-500 focus:border-green-500"
                />
                <input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-2 py-2 text-sm focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Audit Log Timeline */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="p-6">
          <div className="flow-root">
            <ul className="-mb-8">
              {filteredLogs.map((log, index) => {
                const { date, time } = formatTimestamp(log.performedAt);
                return (
                  <li key={log.id}>
                    <div className="relative pb-8">
                      {index !== filteredLogs.length - 1 && (
                        <span
                          className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                          aria-hidden="true"
                        />
                      )}
                      <div className="relative flex space-x-3">
                        <div>
                          <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${getActionColor(log.action)}`}>
                            {getActionIcon(log.action)}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-gray-900">
                                <span className="font-medium">{log.performedBy}</span>
                                <span className="text-gray-600"> {log.action} </span>
                                <span className="font-medium">{log.entityName}</span>
                              </p>
                              <p className="text-xs text-gray-500">
                                {log.performedByRole} • {log.metadata?.siteName || 'System'}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm text-gray-900">{date}</p>
                              <p className="text-xs text-gray-500">{time}</p>
                            </div>
                          </div>
                          
                          {log.fieldChanges && log.fieldChanges.length > 0 && (
                            <div className="mt-2 text-sm text-gray-600">
                              <p className="font-medium">Changes:</p>
                              <ul className="mt-1 space-y-1">
                                {log.fieldChanges.map((change, changeIndex) => (
                                  <li key={changeIndex} className="text-xs">
                                    <span className="font-medium">{change.field}:</span>
                                    <span className="text-red-600 line-through ml-1">{change.oldValue}</span>
                                    <span className="text-green-600 ml-1">→ {change.newValue}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                          
                          {log.notes && (
                            <div className="mt-2 text-sm text-gray-600">
                              <p className="italic">"{log.notes}"</p>
                            </div>
                          )}
                          
                          {log.metadata && (
                            <div className="mt-2 flex flex-wrap gap-2">
                              {log.metadata.workerName && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                                  <User className="h-3 w-3 mr-1" />
                                  {log.metadata.workerName}
                                </span>
                              )}
                              {log.metadata.trainingName && (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                                  <Shield className="h-3 w-3 mr-1" />
                                  {log.metadata.trainingName}
                                </span>
                              )}
                            </div>
                          )}
                          
                          {log.ipAddress && (
                            <div className="mt-1 text-xs text-gray-400">
                              IP: {log.ipAddress}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>

        {filteredLogs.length === 0 && (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No audit logs found</h3>
            <p className="text-gray-500">
              {searchTerm || actionFilter !== 'all' || entityTypeFilter !== 'all' || userFilter !== 'all'
                ? 'Try adjusting your search criteria.'
                : 'No activities have been recorded yet.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuditTrailVisualization;
