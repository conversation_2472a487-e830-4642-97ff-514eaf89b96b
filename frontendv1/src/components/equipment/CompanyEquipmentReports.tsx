import React from "react";
import { BarChart3, Download, FileText, TrendingUp } from "lucide-react";

export default function CompanyEquipmentReports() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Reports & Exports</h2>
          <p className="text-sm text-gray-600">Generate reports and export equipment data</p>
        </div>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          Generate Report
        </button>
      </div>

      {/* Placeholder Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
            <BarChart3 className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Reports & Analytics</h3>
          <p className="text-gray-600 mb-4">
            Generate comprehensive reports and export equipment data for analysis.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div className="p-4 border border-gray-200 rounded-lg">
              <BarChart3 className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <h4 className="font-medium">Utilization Reports</h4>
              <p className="text-sm text-gray-600">Equipment usage analytics</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <Download className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <h4 className="font-medium">Data Exports</h4>
              <p className="text-sm text-gray-600">Export to CSV, Excel, PDF</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <TrendingUp className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <h4 className="font-medium">Cost Analysis</h4>
              <p className="text-sm text-gray-600">Financial performance metrics</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
