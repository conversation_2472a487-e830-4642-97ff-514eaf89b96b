import { useState, useEffect } from "react";
import {
	Plus,
	Search,
	Filter,
	Edit,
	QrCode,
	MapPin,
	User,
	Calendar,
	AlertTriangle,
	CheckCircle,
	Clock,
	Settings,
	Building,
	ExternalLink,
} from "lucide-react";
import {
	SiteEquipmentInventory,
	EquipmentFilters,
} from "../../types/equipment";
import UniversalSearchFilter from "../shared/UniversalSearchFilter";

interface GeneralEquipmentProps {
	siteId: string;
}

const GeneralEquipment = ({ siteId }: GeneralEquipmentProps) => {
	const [equipment, setEquipment] = useState<SiteEquipmentInventory[]>([]);
	const [filteredEquipment, setFilteredEquipment] = useState<
		SiteEquipmentInventory[]
	>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [filters, setFilters] = useState<EquipmentFilters>({});
	const [isLoading, setIsLoading] = useState(true);
	const [selectedEquipment, setSelectedEquipment] =
		useState<SiteEquipmentInventory | null>(null);
	const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

	// Mock data
	useEffect(() => {
		const mockEquipment: SiteEquipmentInventory[] = [
			{
				id: "eq-001",
				equipmentMaster: {
					id: "em-001",
					name: "Caterpillar Excavator 320D",
					sku: "CAT-EX-320D",
					category: {
						id: "heavy",
						name: "Heavy Machinery",
						description: "Heavy construction equipment",
						parentCategoryId: undefined,
						safetyRequirements: {},
						isPPECategory: false,
					},
					description: "Heavy-duty excavator for construction work",
					defaultCost: 150000,
					expectedLifespanDays: 3650,
					defaultInspectionIntervalDays: 30,
					defaultMaintenanceIntervalDays: 90,
					isPPE: false,
					requiresCertification: true,
					requiredTrainingIds: ["heavy-machinery-cert"],
					safetyStandards: ["ISO 6165"],
					specifications: {
						"Engine Power": "122 kW",
						"Operating Weight": "20,300 kg",
					},
					images: [],
					status: "active",
					createdAt: new Date(),
					updatedAt: new Date(),
				},
				siteId,
				serialNumber: "CAT320D2024001",
				status: {
					id: "in-use",
					name: "In Use",
					isAvailableForAssignment: false,
					color: "blue",
				},
				purchaseDate: new Date("2024-01-15"),
				lastInspectionDate: new Date("2024-12-01"),
				nextInspectionDueDate: new Date("2024-12-31"),
				lastMaintenanceDate: new Date("2024-11-15"),
				nextMaintenanceDueDate: new Date("2025-02-15"),
				currentAssigneeType: "worker",
				currentAssigneeId: "worker-001",
				currentAssigneeName: "John Mwangi",
				locationOnSite: "Zone A - Foundation Area",
				totalUsageHours: 1250,
				acquisitionCost: 150000,
				currentValue: 135000,
				condition: "good",
			},
			{
				id: "eq-002",
				equipmentMaster: {
					id: "em-002",
					name: "Makita Circular Saw",
					sku: "MAK-CS-001",
					category: {
						id: "power-tools",
						name: "Power Tools",
						description: "Portable power tools",
						parentCategoryId: undefined,
						safetyRequirements: {},
						isPPECategory: false,
					},
					description: "Professional circular saw for cutting",
					defaultCost: 250,
					expectedLifespanDays: 1825,
					defaultInspectionIntervalDays: 7,
					defaultMaintenanceIntervalDays: 30,
					isPPE: false,
					requiresCertification: false,
					requiredTrainingIds: [],
					safetyStandards: ["IEC 62841"],
					specifications: { "Blade Size": "7.25 inches", Power: "15 Amp" },
					images: [],
					status: "active",
					createdAt: new Date(),
					updatedAt: new Date(),
				},
				siteId,
				serialNumber: "MAK001CS2024",
				status: {
					id: "available",
					name: "Available",
					isAvailableForAssignment: true,
					color: "green",
				},
				purchaseDate: new Date("2024-03-10"),
				lastInspectionDate: new Date("2024-12-15"),
				nextInspectionDueDate: new Date("2024-12-22"),
				totalUsageHours: 85,
				acquisitionCost: 250,
				currentValue: 220,
				condition: "excellent",
			},
		];

		setEquipment(mockEquipment);
		setFilteredEquipment(mockEquipment);
		setIsLoading(false);
	}, [siteId]);

	// Filter and search logic
	useEffect(() => {
		let filtered = equipment.filter((item) => {
			const matchesSearch =
				item.equipmentMaster.name
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				item.equipmentMaster.sku
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				item.serialNumber?.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesCategory =
				!filters.category ||
				item.equipmentMaster.category.id === filters.category;
			const matchesStatus =
				!filters.status || item.status.id === filters.status;
			const matchesAssignee =
				!filters.assignee ||
				item.currentAssigneeName
					?.toLowerCase()
					.includes(filters.assignee.toLowerCase());

			return (
				matchesSearch && matchesCategory && matchesStatus && matchesAssignee
			);
		});

		setFilteredEquipment(filtered);
	}, [equipment, searchTerm, filters]);

	const getStatusBadge = (status: { name: string; color: string }) => {
		const colorClasses = {
			green: "bg-green-100 text-green-800",
			blue: "bg-blue-100 text-blue-800",
			yellow: "bg-yellow-100 text-yellow-800",
			red: "bg-red-100 text-red-800",
			gray: "bg-gray-100 text-gray-800",
		};

		return (
			<span
				className={`px-2 py-1 text-xs font-medium rounded-full ${colorClasses[status.color as keyof typeof colorClasses] || colorClasses.gray}`}
			>
				{status.name}
			</span>
		);
	};

	const getConditionIcon = (condition: string) => {
		switch (condition) {
			case "excellent":
			case "good":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "fair":
				return <Clock className="h-4 w-4 text-yellow-500" />;
			case "poor":
			case "damaged":
				return <AlertTriangle className="h-4 w-4 text-red-500" />;
			default:
				return <CheckCircle className="h-4 w-4 text-gray-500" />;
		}
	};

	const handleAssign = (equipment: SiteEquipmentInventory) => {
		setSelectedEquipment(equipment);
		setIsAssignModalOpen(true);
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="h-32 bg-gray-200 rounded mb-4"></div>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-16 bg-gray-200 rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">General Equipment</h2>
			</div>

			{/* Search and Filters */}
			<UniversalSearchFilter
				searchQuery={searchTerm}
				onSearchChange={setSearchTerm}
				searchPlaceholder="Search equipment by name, SKU, or serial number..."
				filters={{
					status: {
						label: "Status",
						value: filters.status || "all",
						options: [
							{ id: "available", name: "Available" },
							{ id: "in-use", name: "In Use" },
							{ id: "maintenance", name: "Maintenance" },
							{ id: "damaged", name: "Damaged" }
						],
						onChange: (value) => setFilters({ ...filters, status: value === "all" ? undefined : value })
					},
					category: {
						label: "Category",
						value: filters.category || "all",
						options: [
							{ id: "heavy", name: "Heavy Machinery" },
							{ id: "power-tools", name: "Power Tools" },
							{ id: "hand-tools", name: "Hand Tools" }
						],
						onChange: (value) => setFilters({ ...filters, category: value === "all" ? undefined : value })
					}
				}}
				showViewToggle={false}
				actions={[
					{
						label: "View Company Equipment",
						icon: <Building className="h-4 w-4" />,
						onClick: () => window.open("/company-equipment", "_blank"),
						variant: "outline"
					},
					{
						label: "Scan QR Code",
						icon: <QrCode className="h-4 w-4" />,
						onClick: () => console.log("Scan QR code"),
						variant: "outline"
					},
					{
						label: "Add Equipment",
						icon: <Plus className="h-4 w-4" />,
						onClick: () => console.log("Add equipment"),
						variant: "primary"
					}
				]}
				showActiveFilters={true}
				onClearFilter={(filterKey) => {
					if (filterKey === "status") {
						setFilters({ ...filters, status: undefined });
					} else if (filterKey === "category") {
						setFilters({ ...filters, category: undefined });
					}
				}}
				onClearAllFilters={() => {
					setSearchTerm("");
					setFilters({});
				}}
				resultsCount={filteredEquipment.length}
				totalCount={equipment.length}
			/>

			{/* Equipment List */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Equipment Details
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status & Condition
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Assignment
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Location
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Next Due
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredEquipment.map((item) => (
								<tr key={item.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{item.equipmentMaster.name}
											</div>
											<div className="text-sm text-gray-500">
												SKU: {item.equipmentMaster.sku}
											</div>
											<div className="text-sm text-gray-500">
												Serial: {item.serialNumber}
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center space-x-2">
											{getStatusBadge(item.status)}
											{getConditionIcon(item.condition)}
										</div>
										<div className="text-xs text-gray-500 mt-1">
											{item.condition}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										{item.currentAssigneeName ? (
											<div className="flex items-center">
												<User className="h-4 w-4 text-gray-400 mr-1" />
												<span className="text-sm text-gray-900">
													{item.currentAssigneeName}
												</span>
											</div>
										) : (
											<span className="text-sm text-gray-500">Unassigned</span>
										)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										{item.locationOnSite ? (
											<div className="flex items-center">
												<MapPin className="h-4 w-4 text-gray-400 mr-1" />
												<span className="text-sm text-gray-900">
													{item.locationOnSite}
												</span>
											</div>
										) : (
											<span className="text-sm text-gray-500">
												Not specified
											</span>
										)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">
											{item.nextInspectionDueDate && (
												<div className="flex items-center">
													<Calendar className="h-4 w-4 text-gray-400 mr-1" />
													{item.nextInspectionDueDate.toLocaleDateString()}
												</div>
											)}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<div className="flex space-x-2">
											<button
												onClick={() => window.open(`/company-equipment?search=${item.equipmentMaster.name}`, "_blank")}
												className="text-purple-600 hover:text-purple-900"
												title="View in Company Equipment"
											>
												<ExternalLink className="h-4 w-4" />
											</button>
											<button
												onClick={() => handleAssign(item)}
												className="text-green-600 hover:text-green-900"
												title="Assign Equipment"
											>
												<User className="h-4 w-4" />
											</button>
											<button
												className="text-blue-600 hover:text-blue-900"
												title="Edit Equipment"
											>
												<Edit className="h-4 w-4" />
											</button>
											<button
												className="text-gray-600 hover:text-gray-900"
												title="Equipment Settings"
											>
												<Settings className="h-4 w-4" />
											</button>
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Assignment Modal Placeholder */}
			{isAssignModalOpen && selectedEquipment && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-md">
						<h3 className="text-lg font-semibold mb-4">
							Assign {selectedEquipment.equipmentMaster.name}
						</h3>
						<p className="text-gray-600 mb-4">
							Equipment assignment form will be implemented here.
						</p>
						<div className="flex justify-end space-x-2">
							<button
								onClick={() => {
									setIsAssignModalOpen(false);
									setSelectedEquipment(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
								Assign Equipment
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default GeneralEquipment;
