import React from "react";
import { User<PERSON>heck, MapPin, Calendar, ArrowRight } from "lucide-react";

export default function CompanyEquipmentAssignments() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Equipment Assignments</h2>
          <p className="text-sm text-gray-600">Manage equipment assignments across sites</p>
        </div>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          New Assignment
        </button>
      </div>

      {/* Placeholder Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
            <UserCheck className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Equipment Assignments</h3>
          <p className="text-gray-600 mb-4">
            Track and manage equipment assignments to different sites and operators.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div className="p-4 border border-gray-200 rounded-lg">
              <MapPin className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <h4 className="font-medium">Site Assignments</h4>
              <p className="text-sm text-gray-600">Assign equipment to specific sites</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <Calendar className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <h4 className="font-medium">Schedule Transfers</h4>
              <p className="text-sm text-gray-600">Plan equipment moves between sites</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <ArrowRight className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <h4 className="font-medium">Transfer History</h4>
              <p className="text-sm text-gray-600">View past equipment movements</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
