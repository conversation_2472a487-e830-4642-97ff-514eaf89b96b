import React, { useState, useMemo } from "react";
import { Plus, Search, Calendar, MessageSquare, Send, Building, Package, CheckCircle } from "lucide-react";
import UniversalSearchFilter from "../shared/UniversalSearchFilter";
import { 
  mockCompanyEquipment, 
  equipmentCategories, 
  ownershipTypes,
  CompanyEquipment 
} from "../../data/equipmentMockData";

interface SiteEquipmentRequestProps {
  siteId: string;
}

interface EquipmentRequest {
  equipmentId: string;
  requestedDate: string;
  returnDate?: string;
  reason: string;
  priority: "low" | "medium" | "high";
}

function EquipmentRequestCard({ 
  equipment, 
  onRequest 
}: { 
  equipment: CompanyEquipment; 
  onRequest: (equipment: CompanyEquipment) => void;
}) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "maintenance": return "bg-yellow-100 text-yellow-800";
      case "inactive": return "bg-gray-100 text-gray-800";
      case "retired": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getOwnershipColor = (type: string) => {
    switch (type) {
      case "company": return "bg-blue-100 text-blue-800";
      case "rented": return "bg-purple-100 text-purple-800";
      case "contracted": return "bg-orange-100 text-orange-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-all duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div>
          <span className="font-mono text-sm text-gray-500">{equipment.equipmentNumber}</span>
          <h3 className="text-lg font-semibold mt-1">{equipment.name}</h3>
          <p className="text-sm text-gray-600">{equipment.category} • {equipment.model}</p>
        </div>
        <div className="flex flex-col gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getOwnershipColor(equipment.ownershipType)}`}>
            {equipment.ownershipType}
          </span>
        </div>
      </div>

      {/* Status and Details */}
      <div className="space-y-3 mb-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Status</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(equipment.overallStatus)}`}>
            {equipment.overallStatus}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Current Location</span>
          <span className="text-sm font-medium">{equipment.currentSiteName || "Available"}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Total Hours</span>
          <span className="text-sm font-medium">{equipment.totalHours.toLocaleString()}</span>
        </div>
      </div>

      {/* Actions */}
      <div className="pt-4 border-t border-gray-100">
        <button
          onClick={() => onRequest(equipment)}
          disabled={!equipment.isAvailableForAssignment}
          className={`w-full px-4 py-2 text-sm font-medium rounded-md transition-colors ${
            equipment.isAvailableForAssignment
              ? "bg-blue-600 text-white hover:bg-blue-700"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          }`}
        >
          {equipment.isAvailableForAssignment ? "Request Equipment" : "Not Available"}
        </button>
      </div>
    </div>
  );
}

function RequestModal({ 
  equipment, 
  onClose, 
  onSubmit 
}: { 
  equipment: CompanyEquipment | null; 
  onClose: () => void;
  onSubmit: (request: EquipmentRequest) => void;
}) {
  const [formData, setFormData] = useState<EquipmentRequest>({
    equipmentId: equipment?.id || "",
    requestedDate: new Date().toISOString().split('T')[0],
    returnDate: "",
    reason: "",
    priority: "medium"
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    onClose();
  };

  if (!equipment) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 relative z-[10000]">
        <h2 className="text-lg font-semibold mb-4">Request Equipment</h2>
        
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <h3 className="font-medium">{equipment.name}</h3>
          <p className="text-sm text-gray-600">{equipment.equipmentNumber}</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Requested Date *
            </label>
            <input
              type="date"
              value={formData.requestedDate}
              onChange={(e) => setFormData(prev => ({ ...prev, requestedDate: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Expected Return Date
            </label>
            <input
              type="date"
              value={formData.returnDate}
              onChange={(e) => setFormData(prev => ({ ...prev, returnDate: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Priority
            </label>
            <select
              value={formData.priority}
              onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as "low" | "medium" | "high" }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reason for Request *
            </label>
            <textarea
              value={formData.reason}
              onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows={3}
              placeholder="Describe why you need this equipment..."
              required
            />
          </div>

          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
            >
              <Send className="w-4 h-4 inline mr-2" />
              Submit Request
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function SiteEquipmentRequest({ siteId }: SiteEquipmentRequestProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [ownershipFilter, setOwnershipFilter] = useState("all");
  const [selectedEquipment, setSelectedEquipment] = useState<CompanyEquipment | null>(null);
  const [requests, setRequests] = useState<EquipmentRequest[]>([]);

  // Filter available equipment (not assigned to current site)
  const availableEquipment = useMemo(() => {
    return mockCompanyEquipment.filter((equipment) => {
      const isAvailable = equipment.isAvailableForAssignment || equipment.currentSiteId !== siteId;
      const matchesSearch = 
        equipment.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        equipment.equipmentNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        equipment.model.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesCategory = categoryFilter === "all" || equipment.category === categoryFilter;
      const matchesOwnership = ownershipFilter === "all" || equipment.ownershipType === ownershipFilter;

      return isAvailable && matchesSearch && matchesCategory && matchesOwnership;
    });
  }, [searchQuery, categoryFilter, ownershipFilter, siteId]);

  const handleRequest = (equipment: CompanyEquipment) => {
    setSelectedEquipment(equipment);
  };

  const handleSubmitRequest = (request: EquipmentRequest) => {
    setRequests(prev => [...prev, request]);
    console.log("Equipment request submitted:", request);
    // TODO: Send request to API
  };

  const clearFilter = (filterKey: string) => {
    switch (filterKey) {
      case "category":
        setCategoryFilter("all");
        break;
      case "ownership":
        setOwnershipFilter("all");
        break;
    }
  };

  const clearAllFilters = () => {
    setSearchQuery("");
    setCategoryFilter("all");
    setOwnershipFilter("all");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Request Equipment from Company</h2>
          <p className="text-sm text-gray-600">Browse available company equipment and submit requests</p>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Building className="w-4 h-4" />
          <span>Company Equipment Pool</span>
        </div>
      </div>

      {/* Search and Filters */}
      <UniversalSearchFilter
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search available equipment..."
        filters={{
          category: {
            label: "Category",
            value: categoryFilter,
            options: equipmentCategories.map(cat => ({ id: cat, name: cat })),
            onChange: setCategoryFilter
          },
          ownership: {
            label: "Ownership",
            value: ownershipFilter,
            options: ownershipTypes,
            onChange: setOwnershipFilter
          }
        }}
        showViewToggle={false}
        showActiveFilters={true}
        onClearFilter={clearFilter}
        onClearAllFilters={clearAllFilters}
        resultsCount={availableEquipment.length}
        totalCount={mockCompanyEquipment.length}
      />

      {/* Equipment Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableEquipment.map((equipment) => (
          <EquipmentRequestCard
            key={equipment.id}
            equipment={equipment}
            onRequest={handleRequest}
          />
        ))}
      </div>

      {/* Empty State */}
      {availableEquipment.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
            <Package className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No equipment available</h3>
          <p className="text-gray-600 mb-4">
            No equipment matches your current search criteria.
          </p>
          <button
            onClick={clearAllFilters}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Clear all filters
          </button>
        </div>
      )}

      {/* Recent Requests */}
      {requests.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Requests</h3>
          <div className="space-y-3">
            {requests.slice(-3).map((request, index) => {
              const equipment = mockCompanyEquipment.find(eq => eq.id === request.equipmentId);
              return (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium">{equipment?.name}</h4>
                    <p className="text-sm text-gray-600">Requested for {request.requestedDate}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-green-600">Submitted</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Request Modal */}
      <RequestModal
        equipment={selectedEquipment}
        onClose={() => setSelectedEquipment(null)}
        onSubmit={handleSubmitRequest}
      />
    </div>
  );
}
