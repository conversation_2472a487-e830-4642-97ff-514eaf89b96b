import { useState, useEffect } from "react";
import {
	Search,
	Filter,
	Package,
	AlertTriangle,
	User,
	Calendar,
	TrendingDown,
	TrendingUp,
	ShoppingCart,
	Eye,
} from "lucide-react";
import { SitePPEStock, PPEFilters } from "../../types/equipment";

interface PPEInventoryProps {
	siteId: string;
}

const PPEInventory = ({ siteId }: PPEInventoryProps) => {
	const [ppeStock, setPpeStock] = useState<SitePPEStock[]>([]);
	const [filteredStock, setFilteredStock] = useState<SitePPEStock[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [filters, setFilters] = useState<PPEFilters>({});
	const [isLoading, setIsLoading] = useState(true);
	const [selectedPPE, setSelectedPPE] = useState<SitePPEStock | null>(null);
	const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
	const [isRestockModalOpen, setIsRestockModalOpen] = useState(false);

	// Mock data
	useEffect(() => {
		const mockPPEStock: SitePPEStock[] = [
			{
				id: "ppe-001",
				ppeMaster: {
					id: "ppe-m-001",
					name: "Safety Helmet - Hard Hat",
					sku: "PPE-HH-001",
					category: {
						id: "head-protection",
						name: "Head Protection",
						description: "Head protection equipment",
						parentCategoryId: undefined,
						safetyRequirements: {},
						isPPECategory: true,
					},
					description: "ANSI Z89.1 compliant safety helmet",
					defaultCost: 25,
					expectedLifespanDays: 1095,
					defaultInspectionIntervalDays: 30,
					defaultMaintenanceIntervalDays: 90,
					isPPE: true,
					requiresCertification: false,
					requiredTrainingIds: [],
					safetyStandards: ["ANSI Z89.1", "CSA Z94.1"],
					specifications: { Type: "Class E", Material: "HDPE" },
					images: [],
					status: "active",
					createdAt: new Date(),
					updatedAt: new Date(),
				},
				siteId,
				quantityOnHand: 45,
				quantityAvailable: 38,
				quantityReserved: 7,
				reorderLevel: 20,
				maxStockLevel: 100,
				lastStockUpdate: new Date("2024-12-15"),
				batchNumber: "HH-2024-001",
				expiryDate: new Date("2027-12-31"),
				unitCost: 25,
				// Add fields for lifespan tracking per worker
				assignedDate: new Date("2024-01-15"), // When assigned to current worker
				assignedToWorkerId: "worker-001",
			},
			{
				id: "ppe-002",
				ppeMaster: {
					id: "ppe-m-002",
					name: "Safety Vest - High Visibility",
					sku: "PPE-SV-001",
					category: {
						id: "body-protection",
						name: "Body Protection",
						description: "Body protection equipment",
						parentCategoryId: undefined,
						safetyRequirements: {},
						isPPECategory: true,
					},
					description: "Class 2 high visibility safety vest",
					defaultCost: 15,
					expectedLifespanDays: 365,
					defaultInspectionIntervalDays: 7,
					defaultMaintenanceIntervalDays: 30,
					isPPE: true,
					requiresCertification: false,
					requiredTrainingIds: [],
					safetyStandards: ["ANSI/ISEA 107"],
					specifications: { Class: "2", Color: "Orange" },
					images: [],
					status: "active",
					createdAt: new Date(),
					updatedAt: new Date(),
				},
				siteId,
				quantityOnHand: 12,
				quantityAvailable: 8,
				quantityReserved: 4,
				reorderLevel: 25,
				maxStockLevel: 75,
				lastStockUpdate: new Date("2024-12-10"),
				batchNumber: "SV-2024-002",
				unitCost: 15,
				// Add fields for lifespan tracking per worker
				assignedDate: new Date("2024-11-01"), // Close to end of lifespan
				assignedToWorkerId: "worker-002",
			},
			{
				id: "ppe-003",
				ppeMaster: {
					id: "ppe-m-003",
					name: "Safety Gloves - Cut Resistant",
					sku: "PPE-GL-001",
					category: {
						id: "hand-protection",
						name: "Hand Protection",
						description: "Hand protection equipment",
						parentCategoryId: undefined,
						safetyRequirements: {},
						isPPECategory: true,
					},
					description: "Level A4 cut resistant gloves",
					defaultCost: 8,
					expectedLifespanDays: 90,
					defaultInspectionIntervalDays: 7,
					defaultMaintenanceIntervalDays: 30,
					isPPE: true,
					requiresCertification: false,
					requiredTrainingIds: [],
					safetyStandards: ["EN 388"],
					specifications: { "Cut Level": "A4", Size: "Large" },
					images: [],
					status: "active",
					createdAt: new Date(),
					updatedAt: new Date(),
				},
				siteId,
				quantityOnHand: 85,
				quantityAvailable: 72,
				quantityReserved: 13,
				reorderLevel: 50,
				maxStockLevel: 150,
				lastStockUpdate: new Date("2024-12-12"),
				batchNumber: "GL-2024-003",
				expiryDate: new Date("2025-01-15"), // Close to expiry
				unitCost: 8,
				// Add fields for lifespan tracking per worker
				assignedDate: new Date("2024-10-01"), // Past lifespan
				assignedToWorkerId: "worker-003",
			},
		];

		setPpeStock(mockPPEStock);
		setFilteredStock(mockPPEStock);
		setIsLoading(false);
	}, [siteId]);

	// Filter and search logic
	useEffect(() => {
		let filtered = ppeStock.filter((item) => {
			const matchesSearch =
				item.ppeMaster.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				item.ppeMaster.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
				item.batchNumber?.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesCategory =
				!filters.category || item.ppeMaster.category.id === filters.category;

			let matchesStockLevel = true;
			if (filters.stockLevel) {
				const stockPercentage =
					(item.quantityOnHand / item.maxStockLevel) * 100;
				switch (filters.stockLevel) {
					case "low":
						matchesStockLevel = item.quantityOnHand <= item.reorderLevel;
						break;
					case "normal":
						matchesStockLevel =
							item.quantityOnHand > item.reorderLevel && stockPercentage <= 80;
						break;
					case "high":
						matchesStockLevel = stockPercentage > 80;
						break;
				}
			}

			let matchesStatus = true;
			if (filters.expiryStatus) {
				const ppeStatus = getPPEStatus(item);
				switch (filters.expiryStatus) {
					case "expired":
						matchesStatus = ppeStatus.status === "expired-overdue";
						break;
					case "expiring-soon":
						matchesStatus = ppeStatus.status === "close-to-expiry";
						break;
					case "valid":
						matchesStatus = ppeStatus.status === "valid";
						break;
				}
			}

			return (
				matchesSearch &&
				matchesCategory &&
				matchesStockLevel &&
				matchesStatus
			);
		});

		setFilteredStock(filtered);
	}, [ppeStock, searchTerm, filters]);

	const getStockLevelBadge = (item: SitePPEStock) => {
		if (item.quantityOnHand <= item.reorderLevel) {
			return (
				<span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
					Low Stock
				</span>
			);
		}
		const stockPercentage = (item.quantityOnHand / item.maxStockLevel) * 100;
		if (stockPercentage > 80) {
			return (
				<span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
					Well Stocked
				</span>
			);
		}
		return (
			<span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
				Normal
			</span>
		);
	};

	// Combined status function for expiry and lifespan
	const getPPEStatus = (item: SitePPEStock) => {
		const today = new Date();

		// Calculate expiry status
		let daysToExpiry = null;
		let isExpired = false;
		let isCloseToExpiry = false;

		if (item.expiryDate) {
			daysToExpiry = Math.ceil(
				(item.expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
			);
			isExpired = daysToExpiry < 0;
			isCloseToExpiry = daysToExpiry >= 0 && daysToExpiry <= 90;
		}

		// Calculate lifespan status
		let daysInUse = 0;
		let isOverLifespan = false;
		let isCloseToEndOfLifespan = false;

		if (item.assignedDate) {
			daysInUse = Math.ceil(
				(today.getTime() - item.assignedDate.getTime()) / (1000 * 60 * 60 * 24),
			);
			const lifespanDays = item.ppeMaster.expectedLifespanDays;
			isOverLifespan = daysInUse > lifespanDays;
			isCloseToEndOfLifespan = daysInUse > (lifespanDays * 0.8) && !isOverLifespan;
		}

		// Determine priority status (expiry takes precedence over lifespan)
		if (isExpired || isOverLifespan) {
			return {
				status: "expired-overdue",
				label: isExpired ? "Expired" : "Overdue",
				className: "px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800",
				icon: "🚫"
			};
		}

		if (isCloseToExpiry || isCloseToEndOfLifespan) {
			const reason = isCloseToExpiry ? `Expires in ${daysToExpiry} days` : "Near end of lifespan";
			return {
				status: "close-to-expiry",
				label: reason,
				className: "px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800",
				icon: "⚠️"
			};
		}

		return {
			status: "valid",
			label: "Valid",
			className: "px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800",
			icon: "✅"
		};
	};

	// Get lifespan information for display
	const getLifespanInfo = (item: SitePPEStock) => {
		if (!item.assignedDate) {
			return {
				daysInUse: 0,
				totalLifespan: item.ppeMaster.expectedLifespanDays,
				percentageUsed: 0
			};
		}

		const today = new Date();
		const daysInUse = Math.ceil(
			(today.getTime() - item.assignedDate.getTime()) / (1000 * 60 * 60 * 24),
		);
		const totalLifespan = item.ppeMaster.expectedLifespanDays;
		const percentageUsed = Math.min((daysInUse / totalLifespan) * 100, 100);

		return {
			daysInUse,
			totalLifespan,
			percentageUsed
		};
	};

	const getStockIcon = (item: SitePPEStock) => {
		if (item.quantityOnHand <= item.reorderLevel) {
			return <TrendingDown className="h-4 w-4 text-red-500" />;
		}
		return <TrendingUp className="h-4 w-4 text-green-500" />;
	};

	const handleAssignPPE = (ppe: SitePPEStock) => {
		setSelectedPPE(ppe);
		setIsAssignModalOpen(true);
	};

	const handleRestockPPE = (ppe: SitePPEStock) => {
		setSelectedPPE(ppe);
		setIsRestockModalOpen(true);
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="h-32 bg-gray-200 rounded mb-4"></div>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-16 bg-gray-200 rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">PPE Inventory</h2>
				<div className="flex gap-2">
					<button className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
						<ShoppingCart className="h-4 w-4 mr-2" />
						Restock PPE
					</button>
					<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
						<User className="h-4 w-4 mr-2" />
						Assign PPE
					</button>
				</div>
			</div>

			{/* Summary Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total PPE Items</p>
							<p className="text-2xl font-bold">
								{ppeStock.reduce((sum, item) => sum + item.quantityOnHand, 0)}
							</p>
						</div>
						<Package className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Available</p>
							<p className="text-2xl font-bold text-green-600">
								{ppeStock.reduce(
									(sum, item) => sum + item.quantityAvailable,
									0,
								)}
							</p>
						</div>
						<TrendingUp className="h-8 w-8 text-green-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Low Stock Items</p>
							<p className="text-2xl font-bold text-red-600">
								{
									ppeStock.filter(
										(item) => item.quantityOnHand <= item.reorderLevel,
									).length
								}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-red-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Reserved</p>
							<p className="text-2xl font-bold text-yellow-600">
								{ppeStock.reduce((sum, item) => sum + item.quantityReserved, 0)}
							</p>
						</div>
						<Calendar className="h-8 w-8 text-yellow-500" />
					</div>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4 items-center">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search PPE by name, SKU, or batch number..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-gray-400" />
						<select
							value={filters.stockLevel || ""}
							onChange={(e) =>
								setFilters({
									...filters,
									stockLevel: (e.target.value as any) || undefined,
								})
							}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Stock Levels</option>
							<option value="low">Low Stock</option>
							<option value="normal">Normal</option>
							<option value="high">Well Stocked</option>
						</select>
						<select
							value={filters.category || ""}
							onChange={(e) =>
								setFilters({
									...filters,
									category: e.target.value || undefined,
								})
							}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Categories</option>
							<option value="head-protection">Head Protection</option>
							<option value="body-protection">Body Protection</option>
							<option value="hand-protection">Hand Protection</option>
							<option value="foot-protection">Foot Protection</option>
						</select>
						<select
							value={filters.expiryStatus || ""}
							onChange={(e) =>
								setFilters({
									...filters,
									expiryStatus: (e.target.value as "expired" | "valid" | "expiring-soon") || undefined,
								})
							}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Status</option>
							<option value="valid">Valid</option>
							<option value="expiring-soon">Close to Expiry/End of Lifespan</option>
							<option value="expired">Expired/Overdue</option>
						</select>
					</div>
				</div>
			</div>

			{/* PPE Stock List */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									PPE Item
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									PPE Safety Standard
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Stock Level
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Quantities
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Lifespan
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredStock.map((item) => {
								const ppeStatus = getPPEStatus(item);
								const lifespanInfo = getLifespanInfo(item);

								return (
									<tr key={item.id} className="hover:bg-gray-50">
										<td className="px-6 py-4 whitespace-nowrap">
											<div>
												<div className="text-sm font-medium text-gray-900">
													{item.ppeMaster.name}
												</div>
												<div className="text-sm text-gray-500">
													SKU: {item.ppeMaster.sku}
												</div>
												<div className="text-sm text-gray-500">
													Batch: {item.batchNumber || "N/A"}
												</div>
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="text-sm text-gray-900">
												{item.ppeMaster.safetyStandards.length > 0 ? (
													<div className="space-y-1">
														{item.ppeMaster.safetyStandards.map((standard, index) => (
															<div key={index} className="inline-block">
																<span className="px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800 mr-1">
																	{standard}
																</span>
															</div>
														))}
													</div>
												) : (
													<span className="text-gray-500 text-xs">No standards</span>
												)}
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="flex items-center space-x-2">
												{getStockIcon(item)}
												{getStockLevelBadge(item)}
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="text-sm text-gray-900">
												<div>
													On Hand:{" "}
													<span className="font-medium">
														{item.quantityOnHand}
													</span>
												</div>
												<div>
													Available:{" "}
													<span className="font-medium text-green-600">
														{item.quantityAvailable}
													</span>
												</div>
												<div>
													Reserved:{" "}
													<span className="font-medium text-yellow-600">
														{item.quantityReserved}
													</span>
												</div>
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="text-sm text-gray-900">
												<div className="mb-1">
													<span className="font-medium">
														{lifespanInfo.daysInUse} / {lifespanInfo.totalLifespan} days
													</span>
												</div>
												<div className="w-full bg-gray-200 rounded-full h-2">
													<div
														className={`h-2 rounded-full ${
															lifespanInfo.percentageUsed > 80
																? 'bg-red-500'
																: lifespanInfo.percentageUsed > 60
																	? 'bg-yellow-500'
																	: 'bg-green-500'
														}`}
														style={{ width: `${Math.min(lifespanInfo.percentageUsed, 100)}%` }}
													></div>
												</div>
												<div className="text-xs text-gray-500 mt-1">
													{lifespanInfo.percentageUsed.toFixed(0)}% used
												</div>
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="flex items-center space-x-2">
												<span className="text-sm">{ppeStatus.icon}</span>
												<span className={ppeStatus.className}>
													{ppeStatus.label}
												</span>
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
											<div className="flex space-x-2">
												<button
													onClick={() => handleAssignPPE(item)}
													className="text-green-600 hover:text-green-900"
													title="Assign PPE"
												>
													<User className="h-4 w-4" />
												</button>
												<button
													onClick={() => handleRestockPPE(item)}
													className="text-blue-600 hover:text-blue-900"
													title="Restock PPE"
												>
													<ShoppingCart className="h-4 w-4" />
												</button>
												<button
													className="text-gray-600 hover:text-gray-900"
													title="View Details"
												>
													<Eye className="h-4 w-4" />
												</button>
											</div>
										</td>
									</tr>
								);
							})}
						</tbody>
					</table>
				</div>
			</div>

			{/* Assignment Modal Placeholder */}
			{isAssignModalOpen && selectedPPE && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-md">
						<h3 className="text-lg font-semibold mb-4">
							Assign {selectedPPE.ppeMaster.name}
						</h3>
						<p className="text-gray-600 mb-4">
							PPE assignment form will be implemented here.
						</p>
						<div className="flex justify-end space-x-2">
							<button
								onClick={() => {
									setIsAssignModalOpen(false);
									setSelectedPPE(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
								Assign PPE
							</button>
						</div>
					</div>
				</div>
			)}

			{/* Restock Modal Placeholder */}
			{isRestockModalOpen && selectedPPE && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-md">
						<h3 className="text-lg font-semibold mb-4">
							Restock {selectedPPE.ppeMaster.name}
						</h3>
						<p className="text-gray-600 mb-4">
							PPE restock form will be implemented here.
						</p>
						<div className="flex justify-end space-x-2">
							<button
								onClick={() => {
									setIsRestockModalOpen(false);
									setSelectedPPE(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
								Add Stock
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default PPEInventory;
