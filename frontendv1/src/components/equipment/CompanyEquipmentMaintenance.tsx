import React from "react";
import { Wrench, Calendar, AlertTriangle, CheckCircle } from "lucide-react";

export default function CompanyEquipmentMaintenance() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Maintenance & Inspections</h2>
          <p className="text-sm text-gray-600">Manage maintenance schedules and inspection requirements</p>
        </div>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          Schedule Maintenance
        </button>
      </div>

      {/* Placeholder Content */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
            <Wrench className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Maintenance & Inspections</h3>
          <p className="text-gray-600 mb-4">
            Keep track of maintenance schedules, inspections, and compliance requirements.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
            <div className="p-4 border border-gray-200 rounded-lg">
              <Calendar className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <h4 className="font-medium">Scheduled</h4>
              <p className="text-sm text-gray-600">Upcoming maintenance</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <Wrench className="w-6 h-6 text-yellow-600 mx-auto mb-2" />
              <h4 className="font-medium">In Progress</h4>
              <p className="text-sm text-gray-600">Currently being serviced</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-red-600 mx-auto mb-2" />
              <h4 className="font-medium">Overdue</h4>
              <p className="text-sm text-gray-600">Requires immediate attention</p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <h4 className="font-medium">Completed</h4>
              <p className="text-sm text-gray-600">Recently finished</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
