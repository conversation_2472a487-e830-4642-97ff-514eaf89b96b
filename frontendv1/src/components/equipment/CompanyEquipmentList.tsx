import React, { useState, useMemo, useEffect } from "react";
import { Eye, Edit, UserCheck, MoreHorizontal, Download, Settings } from "lucide-react";
import UniversalSearchFilter from "../shared/UniversalSearchFilter";
import { 
  mockCompanyEquipment, 
  equipmentCategories, 
  equipmentStatuses, 
  complianceStatuses, 
  ownershipTypes, 
  equipmentSites,
  CompanyEquipment 
} from "../../data/equipmentMockData";

interface EquipmentCardProps {
  equipment: CompanyEquipment;
  variant?: "card" | "row";
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  onAssign?: (id: string) => void;
}

function EquipmentCard({ equipment, variant = "card", onView, onEdit, onAssign }: EquipmentCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "maintenance": return "bg-yellow-100 text-yellow-800";
      case "inactive": return "bg-gray-100 text-gray-800";
      case "retired": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getComplianceColor = (status: string) => {
    switch (status) {
      case "compliant": return "bg-green-100 text-green-800";
      case "warning": return "bg-yellow-100 text-yellow-800";
      case "critical": return "bg-orange-100 text-orange-800";
      case "overdue": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getOwnershipColor = (type: string) => {
    switch (type) {
      case "company": return "bg-blue-100 text-blue-800";
      case "rented": return "bg-purple-100 text-purple-800";
      case "contracted": return "bg-orange-100 text-orange-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  if (variant === "row") {
    return (
      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors">
        <div className="flex items-center gap-4 flex-1">
          <div className="flex flex-col">
            <span className="font-mono text-sm text-gray-500">{equipment.equipmentNumber}</span>
            <h3 className="font-semibold">{equipment.name}</h3>
            <p className="text-sm text-gray-600">{equipment.category} • {equipment.model}</p>
          </div>
          
          <div className="flex flex-col items-center">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getOwnershipColor(equipment.ownershipType)}`}>
              {equipment.ownershipType}
            </span>
          </div>
          
          <div className="flex flex-col">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(equipment.overallStatus)}`}>
              {equipment.overallStatus}
            </span>
          </div>
          
          <div className="flex flex-col">
            <span className="text-sm text-gray-600">Site</span>
            <span className="text-sm font-medium">{equipment.currentSiteName || "Unassigned"}</span>
          </div>
          
          <div className="flex flex-col">
            <span className="text-sm text-gray-600">Hours</span>
            <span className="text-sm font-medium">{equipment.totalHours.toLocaleString()}</span>
          </div>
          
          <div className="flex flex-col">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getComplianceColor(equipment.complianceStatus)}`}>
              {equipment.complianceStatus}
            </span>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => onView?.(equipment.id)}
            className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
            title="View Details"
          >
            <Eye className="w-4 h-4" />
          </button>
          <button
            onClick={() => onEdit?.(equipment.id)}
            className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
            title="Edit Equipment"
          >
            <Edit className="w-4 h-4" />
          </button>
          <button
            onClick={() => onAssign?.(equipment.id)}
            className="p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors"
            title="Assign to Site"
          >
            <UserCheck className="w-4 h-4" />
          </button>
          <button className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-md transition-colors">
            <MoreHorizontal className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm hover:shadow-md transition-all duration-200">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div>
          <span className="font-mono text-sm text-gray-500">{equipment.equipmentNumber}</span>
          <h3 className="text-lg font-semibold mt-1">{equipment.name}</h3>
          <p className="text-sm text-gray-600">{equipment.category} • {equipment.model}</p>
        </div>
        <div className="flex flex-col gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getOwnershipColor(equipment.ownershipType)}`}>
            {equipment.ownershipType}
          </span>
        </div>
      </div>

      {/* Status and Details */}
      <div className="space-y-3 mb-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Status</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(equipment.overallStatus)}`}>
            {equipment.overallStatus}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Current Site</span>
          <span className="text-sm font-medium">{equipment.currentSiteName || "Unassigned"}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Total Hours</span>
          <span className="text-sm font-medium">{equipment.totalHours.toLocaleString()}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Compliance</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getComplianceColor(equipment.complianceStatus)}`}>
            {equipment.complianceStatus}
          </span>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2 pt-4 border-t border-gray-100">
        <button
          onClick={() => onView?.(equipment.id)}
          className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-md transition-colors"
        >
          View Details
        </button>
        <button
          onClick={() => onAssign?.(equipment.id)}
          className="flex-1 px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
        >
          Assign
        </button>
      </div>
    </div>
  );
}

interface CompanyEquipmentListProps {
  initialSearchQuery?: string;
}

export default function CompanyEquipmentList({ initialSearchQuery = "" }: CompanyEquipmentListProps) {
  const [searchQuery, setSearchQuery] = useState(initialSearchQuery);
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [ownershipFilter, setOwnershipFilter] = useState("all");
  const [siteFilter, setSiteFilter] = useState("all");
  const [complianceFilter, setComplianceFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");

  // Update search query when initial search query changes
  useEffect(() => {
    if (initialSearchQuery) {
      setSearchQuery(initialSearchQuery);
    }
  }, [initialSearchQuery]);

  // Filter equipment based on search and filters
  const filteredEquipment = useMemo(() => {
    return mockCompanyEquipment.filter((equipment) => {
      const matchesSearch =
        equipment.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        equipment.equipmentNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        equipment.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
        equipment.manufacturer.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesStatus = statusFilter === "all" || equipment.overallStatus === statusFilter;
      const matchesCategory = categoryFilter === "all" || equipment.category === categoryFilter;
      const matchesOwnership = ownershipFilter === "all" || equipment.ownershipType === ownershipFilter;
      const matchesSite = siteFilter === "all" || equipment.currentSiteId === siteFilter;
      const matchesCompliance = complianceFilter === "all" || equipment.complianceStatus === complianceFilter;

      return matchesSearch && matchesStatus && matchesCategory && matchesOwnership && matchesSite && matchesCompliance;
    });
  }, [searchQuery, statusFilter, categoryFilter, ownershipFilter, siteFilter, complianceFilter]);

  const handleView = (id: string) => {
    console.log("View equipment:", id);
    // TODO: Navigate to equipment detail view
  };

  const handleEdit = (id: string) => {
    console.log("Edit equipment:", id);
    // TODO: Open edit modal or navigate to edit page
  };

  const handleAssign = (id: string) => {
    console.log("Assign equipment:", id);
    // TODO: Open assignment modal
  };

  const handleExport = () => {
    console.log("Export equipment data");
    // TODO: Implement export functionality
  };

  const handleBulkActions = () => {
    console.log("Open bulk actions");
    // TODO: Implement bulk actions
  };

  const clearFilter = (filterKey: string) => {
    switch (filterKey) {
      case "status":
        setStatusFilter("all");
        break;
      case "category":
        setCategoryFilter("all");
        break;
      case "ownership":
        setOwnershipFilter("all");
        break;
      case "site":
        setSiteFilter("all");
        break;
      case "compliance":
        setComplianceFilter("all");
        break;
    }
  };

  const clearAllFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    setCategoryFilter("all");
    setOwnershipFilter("all");
    setSiteFilter("all");
    setComplianceFilter("all");
  };

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <UniversalSearchFilter
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search equipment name, number, model, or manufacturer..."
        filters={{
          status: {
            label: "Status",
            value: statusFilter,
            options: equipmentStatuses,
            onChange: setStatusFilter
          },
          category: {
            label: "Category",
            value: categoryFilter,
            options: equipmentCategories.map(cat => ({ id: cat, name: cat })),
            onChange: setCategoryFilter
          },
          ownership: {
            label: "Ownership",
            value: ownershipFilter,
            options: ownershipTypes,
            onChange: setOwnershipFilter
          },
          site: {
            label: "Site",
            value: siteFilter,
            options: equipmentSites,
            onChange: setSiteFilter
          },
          compliance: {
            label: "Compliance",
            value: complianceFilter,
            options: complianceStatuses,
            onChange: setComplianceFilter
          }
        }}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        showViewToggle={true}
        actions={[
          {
            label: "Export",
            icon: <Download className="w-4 h-4" />,
            onClick: handleExport,
            variant: "outline"
          },
          {
            label: "Bulk Actions",
            icon: <Settings className="w-4 h-4" />,
            onClick: handleBulkActions,
            variant: "outline"
          }
        ]}
        showActiveFilters={true}
        onClearFilter={clearFilter}
        onClearAllFilters={clearAllFilters}
        resultsCount={filteredEquipment.length}
        totalCount={mockCompanyEquipment.length}
      />

      {/* Equipment List/Grid */}
      {viewMode === "list" ? (
        <div className="space-y-3">
          {filteredEquipment.map((equipment) => (
            <EquipmentCard
              key={equipment.id}
              equipment={equipment}
              variant="row"
              onView={handleView}
              onEdit={handleEdit}
              onAssign={handleAssign}
            />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredEquipment.map((equipment) => (
            <EquipmentCard
              key={equipment.id}
              equipment={equipment}
              variant="card"
              onView={handleView}
              onEdit={handleEdit}
              onAssign={handleAssign}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {filteredEquipment.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
            <Eye className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No equipment found</h3>
          <p className="text-gray-600 mb-4">
            Try adjusting your search criteria or filters to find equipment.
          </p>
          <button
            onClick={clearAllFilters}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Clear all filters
          </button>
        </div>
      )}
    </div>
  );
}
