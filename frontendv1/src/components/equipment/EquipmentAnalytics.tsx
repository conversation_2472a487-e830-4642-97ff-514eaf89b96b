import { useState, useEffect } from "react";
import {
	BarChart3,
	TrendingUp,
	TrendingDown,
	DollarSign,
	Clock,
	Wrench,
	AlertTriangle,
	Download,
} from "lucide-react";
import { UtilizationData } from "../../types/equipment";

interface EquipmentAnalyticsProps {
	siteId: string;
}

const EquipmentAnalytics = ({ siteId }: EquipmentAnalyticsProps) => {
	const [timeRange, setTimeRange] = useState("30d");
	const [isLoading, setIsLoading] = useState(true);
	const [analyticsData, setAnalyticsData] = useState({
		utilizationTrends: [] as UtilizationData[],
		costAnalysis: {
			totalMaintenanceCost: 45230,
			totalOperatingCost: 125600,
			costPerHour: 12.5,
			costTrend: 8.2,
		},
		equipmentPerformance: {
			averageUtilization: 78,
			topPerformers: [
				{ name: "Excavator CAT-320D", utilization: 92, hours: 245 },
				{ name: "Crane TC-5013", utilization: 85, hours: 198 },
				{ name: "Concrete Mixer CM-350", utilization: 76, hours: 156 },
			],
			underPerformers: [
				{ name: "Compactor CP-132", utilization: 45, hours: 89 },
				{ name: "Generator GN-250", utilization: 38, hours: 67 },
			],
		},
		maintenanceMetrics: {
			scheduledCompliance: 85,
			averageDowntime: 4.2,
			mtbf: 156, // Mean Time Between Failures (hours)
			mttr: 6.8, // Mean Time To Repair (hours)
		},
		costBreakdown: [
			{ category: "Labor", amount: 18500, percentage: 41 },
			{ category: "Parts", amount: 15200, percentage: 34 },
			{ category: "Fuel", amount: 8900, percentage: 20 },
			{ category: "Other", amount: 2630, percentage: 5 },
		],
	});

	useEffect(() => {
		// Simulate API call
		const fetchAnalyticsData = async () => {
			setIsLoading(true);
			// Mock data loading based on time range
			setTimeout(() => {
				// Generate mock utilization data
				const mockUtilizationData: UtilizationData[] = [];
				const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;

				for (let i = days - 1; i >= 0; i--) {
					const date = new Date();
					date.setDate(date.getDate() - i);
					mockUtilizationData.push({
						date: date.toISOString().split("T")[0],
						utilization: Math.floor(Math.random() * 40) + 60, // 60-100%
						equipmentCount: Math.floor(Math.random() * 10) + 40, // 40-50 equipment
					});
				}

				setAnalyticsData((prev) => ({
					...prev,
					utilizationTrends: mockUtilizationData,
				}));
				setIsLoading(false);
			}, 1000);
		};

		fetchAnalyticsData();
	}, [siteId, timeRange]);

	const getUtilizationColor = (utilization: number) => {
		if (utilization >= 80) return "text-green-600";
		if (utilization >= 60) return "text-yellow-600";
		return "text-red-600";
	};

	const getTrendIcon = (trend: number) => {
		if (trend > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
		if (trend < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
		return <div className="h-4 w-4" />;
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
						{[...Array(4)].map((_, i) => (
							<div key={i} className="h-24 bg-gray-200 rounded"></div>
						))}
					</div>
					<div className="h-64 bg-gray-200 rounded mb-6"></div>
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="h-48 bg-gray-200 rounded"></div>
						<div className="h-48 bg-gray-200 rounded"></div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">Equipment Analytics</h2>
				<div className="flex gap-2">
					<select
						value={timeRange}
						onChange={(e) => setTimeRange(e.target.value)}
						className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
					>
						<option value="7d">Last 7 Days</option>
						<option value="30d">Last 30 Days</option>
						<option value="90d">Last 90 Days</option>
					</select>
					<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
						<Download className="h-4 w-4 mr-2" />
						Export Report
					</button>
				</div>
			</div>

			{/* Key Metrics */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Average Utilization</p>
							<p
								className={`text-2xl font-bold ${getUtilizationColor(analyticsData.equipmentPerformance.averageUtilization)}`}
							>
								{analyticsData.equipmentPerformance.averageUtilization}%
							</p>
						</div>
						<BarChart3 className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Operating Cost</p>
							<p className="text-2xl font-bold">
								$
								{analyticsData.costAnalysis.totalOperatingCost.toLocaleString()}
							</p>
							<div className="flex items-center text-sm">
								{getTrendIcon(analyticsData.costAnalysis.costTrend)}
								<span className="ml-1 text-gray-500">
									{analyticsData.costAnalysis.costTrend}% vs last period
								</span>
							</div>
						</div>
						<DollarSign className="h-8 w-8 text-green-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Average Downtime</p>
							<p className="text-2xl font-bold">
								{analyticsData.maintenanceMetrics.averageDowntime}h
							</p>
						</div>
						<Clock className="h-8 w-8 text-yellow-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Maintenance Compliance</p>
							<p className="text-2xl font-bold text-green-600">
								{analyticsData.maintenanceMetrics.scheduledCompliance}%
							</p>
						</div>
						<Wrench className="h-8 w-8 text-blue-500" />
					</div>
				</div>
			</div>

			{/* Utilization Trend Chart */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold mb-4 flex items-center">
					<TrendingUp className="h-5 w-5 mr-2" />
					Equipment Utilization Trend
				</h3>
				<div className="h-64 flex items-end justify-between space-x-1">
					{analyticsData.utilizationTrends.map((data, index) => (
						<div key={index} className="flex flex-col items-center flex-1">
							<div
								className="bg-green-500 rounded-t w-full min-h-[20px] transition-all duration-300 hover:bg-green-600"
								style={{ height: `${(data.utilization / 100) * 200}px` }}
								title={`${data.date}: ${data.utilization}% utilization`}
							></div>
							<span className="text-xs text-gray-500 mt-2 transform rotate-45 origin-left">
								{new Date(data.date).toLocaleDateString("en-US", {
									month: "short",
									day: "numeric",
								})}
							</span>
						</div>
					))}
				</div>
			</div>

			{/* Performance Analysis */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Top Performers */}
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold mb-4 flex items-center">
						<TrendingUp className="h-5 w-5 mr-2 text-green-500" />
						Top Performers
					</h3>
					<div className="space-y-3">
						{analyticsData.equipmentPerformance.topPerformers.map(
							(equipment, index) => (
								<div
									key={index}
									className="flex items-center justify-between p-3 bg-green-50 rounded-lg"
								>
									<div>
										<div className="font-medium text-gray-900">
											{equipment.name}
										</div>
										<div className="text-sm text-gray-500">
											{equipment.hours} hours
										</div>
									</div>
									<div className="text-right">
										<div className="font-bold text-green-600">
											{equipment.utilization}%
										</div>
										<div className="text-xs text-gray-500">utilization</div>
									</div>
								</div>
							),
						)}
					</div>
				</div>

				{/* Under Performers */}
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold mb-4 flex items-center">
						<TrendingDown className="h-5 w-5 mr-2 text-red-500" />
						Needs Attention
					</h3>
					<div className="space-y-3">
						{analyticsData.equipmentPerformance.underPerformers.map(
							(equipment, index) => (
								<div
									key={index}
									className="flex items-center justify-between p-3 bg-red-50 rounded-lg"
								>
									<div>
										<div className="font-medium text-gray-900">
											{equipment.name}
										</div>
										<div className="text-sm text-gray-500">
											{equipment.hours} hours
										</div>
									</div>
									<div className="text-right">
										<div className="font-bold text-red-600">
											{equipment.utilization}%
										</div>
										<div className="text-xs text-gray-500">utilization</div>
									</div>
								</div>
							),
						)}
					</div>
				</div>
			</div>

			{/* Cost Analysis */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Cost Breakdown */}
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold mb-4 flex items-center">
						<DollarSign className="h-5 w-5 mr-2" />
						Cost Breakdown
					</h3>
					<div className="space-y-3">
						{analyticsData.costBreakdown.map((item, index) => (
							<div key={index} className="flex items-center justify-between">
								<div className="flex items-center">
									<div
										className="w-4 h-4 rounded-full mr-3"
										style={{
											backgroundColor: [
												"#10B981",
												"#F59E0B",
												"#EF4444",
												"#6B7280",
											][index],
										}}
									></div>
									<span className="text-sm font-medium text-gray-900">
										{item.category}
									</span>
								</div>
								<div className="text-right">
									<div className="text-sm font-bold">
										${item.amount.toLocaleString()}
									</div>
									<div className="text-xs text-gray-500">
										{item.percentage}%
									</div>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* Maintenance Metrics */}
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold mb-4 flex items-center">
						<Wrench className="h-5 w-5 mr-2" />
						Maintenance Metrics
					</h3>
					<div className="space-y-4">
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">
								Scheduled Compliance
							</span>
							<div className="flex items-center">
								<div className="w-24 bg-gray-200 rounded-full h-2 mr-2">
									<div
										className="bg-green-600 h-2 rounded-full"
										style={{
											width: `${analyticsData.maintenanceMetrics.scheduledCompliance}%`,
										}}
									></div>
								</div>
								<span className="text-sm font-medium">
									{analyticsData.maintenanceMetrics.scheduledCompliance}%
								</span>
							</div>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">
								Mean Time Between Failures
							</span>
							<span className="text-sm font-medium">
								{analyticsData.maintenanceMetrics.mtbf}h
							</span>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">Mean Time To Repair</span>
							<span className="text-sm font-medium">
								{analyticsData.maintenanceMetrics.mttr}h
							</span>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">
								Cost per Operating Hour
							</span>
							<span className="text-sm font-medium">
								${analyticsData.costAnalysis.costPerHour}
							</span>
						</div>
					</div>
				</div>
			</div>

			{/* Recommendations */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold mb-4 flex items-center">
					<AlertTriangle className="h-5 w-5 mr-2 text-yellow-500" />
					Recommendations
				</h3>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
						<h4 className="font-medium text-yellow-800 mb-2">
							Optimize Underutilized Equipment
						</h4>
						<p className="text-sm text-yellow-700">
							Consider redistributing or scheduling maintenance for equipment
							with utilization below 50%.
						</p>
					</div>
					<div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
						<h4 className="font-medium text-blue-800 mb-2">
							Preventive Maintenance
						</h4>
						<p className="text-sm text-blue-700">
							Increase preventive maintenance frequency for high-utilization
							equipment to reduce unexpected downtime.
						</p>
					</div>
					<div className="p-4 bg-green-50 rounded-lg border border-green-200">
						<h4 className="font-medium text-green-800 mb-2">
							Cost Optimization
						</h4>
						<p className="text-sm text-green-700">
							Review parts procurement strategy to reduce maintenance costs by
							10-15%.
						</p>
					</div>
					<div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
						<h4 className="font-medium text-purple-800 mb-2">
							Training Opportunity
						</h4>
						<p className="text-sm text-purple-700">
							Provide additional training for operators of underperforming
							equipment.
						</p>
					</div>
				</div>
			</div>
		</div>
	);
};

export default EquipmentAnalytics;
