import React from "react";
import { Search, Filter, Grid, List, Download, MoreHorizontal } from "lucide-react";

export interface FilterOption {
  id: string;
  name: string;
  color?: string;
}

export interface SearchFilterProps {
  // Search functionality
  searchQuery: string;
  onSearchChange: (query: string) => void;
  searchPlaceholder?: string;
  
  // Filter options
  filters: {
    [key: string]: {
      label: string;
      value: string;
      options: FilterOption[];
      onChange: (value: string) => void;
    };
  };
  
  // View mode toggle
  viewMode?: "list" | "grid";
  onViewModeChange?: (mode: "list" | "grid") => void;
  showViewToggle?: boolean;
  
  // Action buttons
  actions?: {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    variant?: "primary" | "secondary" | "outline";
  }[];
  
  // Active filters display
  showActiveFilters?: boolean;
  onClearFilter?: (filterKey: string) => void;
  onClearAllFilters?: () => void;
  
  // Results count
  resultsCount?: number;
  totalCount?: number;
  
  // Styling
  className?: string;
}

export function UniversalSearchFilter({
  searchQuery,
  onSearchChange,
  searchPlaceholder = "Search...",
  filters,
  viewMode = "list",
  onViewModeChange,
  showViewToggle = true,
  actions = [],
  showActiveFilters = true,
  onClearFilter,
  onClearAllFilters,
  resultsCount,
  totalCount,
  className = ""
}: SearchFilterProps) {
  const hasActiveFilters = Object.values(filters).some(filter => filter.value !== "all" && filter.value !== "");
  const hasActiveSearch = searchQuery.trim() !== "";

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Results Summary */}
      {(resultsCount !== undefined || totalCount !== undefined) && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {resultsCount !== undefined && totalCount !== undefined ? (
              <>Showing {resultsCount} of {totalCount} items</>
            ) : resultsCount !== undefined ? (
              <>{resultsCount} items</>
            ) : (
              <>{totalCount} total items</>
            )}
          </div>
          
          {/* Action Buttons */}
          {actions.length > 0 && (
            <div className="flex items-center gap-2">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className={`
                    inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${action.variant === "primary" 
                      ? "bg-blue-600 text-white hover:bg-blue-700" 
                      : action.variant === "secondary"
                      ? "bg-gray-600 text-white hover:bg-gray-700"
                      : "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                    }
                  `}
                >
                  {action.icon}
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search Input */}
        <div className="relative flex-1 min-w-0">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
          />
        </div>

        {/* Filter Controls */}
        <div className="flex items-center gap-2 flex-wrap lg:flex-nowrap">
          {/* Filter Dropdowns */}
          {Object.entries(filters).map(([key, filter]) => (
            <select
              key={key}
              value={filter.value}
              onChange={(e) => filter.onChange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:ring-blue-500 focus:border-blue-500 min-w-[120px] flex-shrink-0"
            >
              <option value="all">All {filter.label}</option>
              {filter.options.map((option) => (
                <option key={option.id} value={option.id}>
                  {option.name}
                </option>
              ))}
            </select>
          ))}

          {/* View Mode Toggle */}
          {showViewToggle && onViewModeChange && (
            <div className="flex items-center border border-gray-300 rounded-md">
              <button
                onClick={() => onViewModeChange("list")}
                className={`p-2 ${
                  viewMode === "list" 
                    ? "bg-blue-600 text-white" 
                    : "bg-white text-gray-600 hover:bg-gray-50"
                }`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => onViewModeChange("grid")}
                className={`p-2 ${
                  viewMode === "grid" 
                    ? "bg-blue-600 text-white" 
                    : "bg-white text-gray-600 hover:bg-gray-50"
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Active Filters */}
      {showActiveFilters && (hasActiveFilters || hasActiveSearch) && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-gray-500">Active filters:</span>
          
          {/* Search Filter Badge */}
          {hasActiveSearch && (
            <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md">
              Search: "{searchQuery}"
              <button
                onClick={() => onSearchChange("")}
                className="ml-1 hover:text-blue-600"
              >
                ×
              </button>
            </span>
          )}
          
          {/* Filter Badges */}
          {Object.entries(filters).map(([key, filter]) => {
            if (filter.value === "all" || filter.value === "") return null;
            
            const selectedOption = filter.options.find(opt => opt.id === filter.value);
            if (!selectedOption) return null;
            
            return (
              <span
                key={key}
                className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-md"
              >
                {filter.label}: {selectedOption.name}
                {onClearFilter && (
                  <button
                    onClick={() => onClearFilter(key)}
                    className="ml-1 hover:text-gray-600"
                  >
                    ×
                  </button>
                )}
              </span>
            );
          })}
          
          {/* Clear All Button */}
          {onClearAllFilters && (
            <button
              onClick={onClearAllFilters}
              className="text-xs text-blue-600 hover:text-blue-800 underline"
            >
              Clear all
            </button>
          )}
        </div>
      )}
    </div>
  );
}

export default UniversalSearchFilter;
