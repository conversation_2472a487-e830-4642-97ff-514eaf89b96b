import React, { useState } from 'react';
import {
  <PERSON><PERSON>eft,
  ChevronRight,
  Clock,
  Building,
  Zap,
  Wrench,
  HardHat,
  Hammer
} from 'lucide-react';
import { TaskCategory } from '../../types/tasks';

// Task Categories with Icons and Colors for Mobile-First UI
interface TaskCategoryInfo {
  id: TaskCategory;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  templates: TaskTemplateInfo[];
}

interface TaskTemplateInfo {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface BulkTaskItem {
  id: string;
  category: TaskCategory;
  categoryName: string;
  template: TaskTemplateInfo;
  workDescription: string;
  location: string;
  plannedDate: string;
  plannedTime: string;
  estimatedDuration: number;
  urgency: 'normal' | 'urgent';
  dependencies?: string[];
  notes?: string;
}

// Predefined Task Categories and Templates for Site Engineers
const TASK_CATEGORIES: TaskCategoryInfo[] = [
  {
    id: 'excavation',
    name: 'Excavation',
    description: 'Digging, trenching, and earthwork',
    icon: Building,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 border-orange-200',
    templates: [
      {
        id: 'exc-001',
        name: 'Standard Excavation',
        description: 'General excavation work',
        estimatedDuration: 8,
        riskLevel: 'medium'
      },
      {
        id: 'exc-002',
        name: 'Deep Excavation',
        description: 'Deep excavation requiring shoring',
        estimatedDuration: 12,
        riskLevel: 'high'
      },
      {
        id: 'exc-003',
        name: 'Utility Excavation',
        description: 'Excavation near utilities',
        estimatedDuration: 6,
        riskLevel: 'high'
      },
      {
        id: 'exc-004',
        name: 'Foundation Excavation',
        description: 'Foundation preparation work',
        estimatedDuration: 10,
        riskLevel: 'medium'
      }
    ]
  },
  {
    id: 'electrical-installation',
    name: 'Electrical',
    description: 'Electrical installation and wiring',
    icon: Zap,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 border-yellow-200',
    templates: [
      {
        id: 'elec-001',
        name: 'Panel Installation',
        description: 'Electrical panel installation',
        estimatedDuration: 6,
        riskLevel: 'high'
      },
      {
        id: 'elec-002',
        name: 'Conduit Installation',
        description: 'Electrical conduit routing',
        estimatedDuration: 4,
        riskLevel: 'medium'
      },
      {
        id: 'elec-003',
        name: 'Lighting Installation',
        description: 'Interior and exterior lighting',
        estimatedDuration: 3,
        riskLevel: 'low'
      },
      {
        id: 'elec-004',
        name: 'Power Outlet Installation',
        description: 'Electrical outlets and switches',
        estimatedDuration: 3,
        riskLevel: 'medium'
      }
    ]
  },
  {
    id: 'construction',
    name: 'Construction',
    description: 'General construction work',
    icon: Hammer,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 border-blue-200',
    templates: [
      {
        id: 'const-001',
        name: 'Concrete Pour',
        description: 'Concrete pouring and finishing',
        estimatedDuration: 8,
        riskLevel: 'medium'
      },
      {
        id: 'const-002',
        name: 'Steel Erection',
        description: 'Structural steel installation',
        estimatedDuration: 10,
        riskLevel: 'high'
      },
      {
        id: 'const-003',
        name: 'Formwork Installation',
        description: 'Concrete formwork setup',
        estimatedDuration: 6,
        riskLevel: 'medium'
      },
      {
        id: 'const-004',
        name: 'Masonry Work',
        description: 'Brick and block laying',
        estimatedDuration: 8,
        riskLevel: 'low'
      }
    ]
  },
  {
    id: 'maintenance',
    name: 'Maintenance',
    description: 'Equipment and facility maintenance',
    icon: Wrench,
    color: 'text-green-600',
    bgColor: 'bg-green-50 border-green-200',
    templates: [
      {
        id: 'maint-001',
        name: 'Equipment Maintenance',
        description: 'Routine equipment servicing',
        estimatedDuration: 4,
        riskLevel: 'low'
      },
      {
        id: 'maint-002',
        name: 'Plumbing Repair',
        description: 'Plumbing system repairs',
        estimatedDuration: 3,
        riskLevel: 'low'
      },
      {
        id: 'maint-003',
        name: 'HVAC Maintenance',
        description: 'Heating and cooling system work',
        estimatedDuration: 5,
        riskLevel: 'medium'
      }
    ]
  },
  {
    id: 'safety',
    name: 'Safety',
    description: 'Safety inspections and audits',
    icon: HardHat,
    color: 'text-red-600',
    bgColor: 'bg-red-50 border-red-200',
    templates: [
      {
        id: 'safety-001',
        name: 'Safety Inspection',
        description: 'General safety inspection',
        estimatedDuration: 2,
        riskLevel: 'low'
      },
      {
        id: 'safety-002',
        name: 'Equipment Safety Check',
        description: 'Equipment safety verification',
        estimatedDuration: 3,
        riskLevel: 'low'
      },
      {
        id: 'safety-003',
        name: 'Site Safety Audit',
        description: 'Comprehensive site safety audit',
        estimatedDuration: 4,
        riskLevel: 'low'
      }
    ]
  }
];

// Common locations for quick selection
const COMMON_LOCATIONS = [
  "Main Building - Ground Floor",
  "Main Building - First Floor", 
  "Office Block - Ground Floor",
  "Office Block - First Floor",
  "Electrical Room",
  "Generator Room",
  "Parking Area",
  "Main Entrance",
  "Site Compound",
  "Storage Area",
  "Workshop",
  "Security Gate"
];

interface TaskCreationTabProps {
  tasks: BulkTaskItem[];
  setTasks: React.Dispatch<React.SetStateAction<BulkTaskItem[]>>;
  siteId?: string;
}

const TaskCreationTab: React.FC<TaskCreationTabProps> = ({ setTasks }) => {
  const [currentStep, setCurrentStep] = useState<'category' | 'template' | 'details'>('category');
  const [selectedCategory, setSelectedCategory] = useState<TaskCategoryInfo | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<TaskTemplateInfo | null>(null);

  // Current task being created
  const [workDescription, setWorkDescription] = useState('');
  const [location, setLocation] = useState('');
  const [plannedDate, setPlannedDate] = useState('');
  const [plannedTime, setPlannedTime] = useState('08:00');
  const [urgency, setUrgency] = useState<'normal' | 'urgent'>('normal');
  const [notes, setNotes] = useState('');

  // Get tomorrow's date as minimum date (tasks can't be scheduled for today)
  const getTomorrowDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  // Initialize with tomorrow's date
  React.useEffect(() => {
    setPlannedDate(getTomorrowDate());
  }, []);

  const handleCategorySelect = (category: TaskCategoryInfo) => {
    setSelectedCategory(category);
    setCurrentStep('template');
  };

  const handleTemplateSelect = (template: TaskTemplateInfo) => {
    setSelectedTemplate(template);
    setCurrentStep('details');
  };

  const handleAddTask = () => {
    if (!selectedCategory || !selectedTemplate || !workDescription.trim() || !location.trim() || !plannedDate) {
      return;
    }

    const newTask: BulkTaskItem = {
      id: `task-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      category: selectedCategory.id,
      categoryName: selectedCategory.name,
      template: selectedTemplate,
      workDescription: workDescription.trim(),
      location: location.trim(),
      plannedDate,
      plannedTime,
      estimatedDuration: selectedTemplate.estimatedDuration,
      urgency,
      notes: notes.trim() || undefined
    };

    setTasks(prev => [...prev, newTask]);

    // Reset form for next task
    setWorkDescription('');
    setLocation('');
    setNotes('');
    setUrgency('normal');
    setCurrentStep('category');
    setSelectedCategory(null);
    setSelectedTemplate(null);
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <>
      {/* Category Selection */}
      {currentStep === 'category' && (
        <div className="space-y-4">
          <div className="text-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Select Task Category</h2>
            <p className="text-gray-600">Choose the type of work you need to plan</p>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {TASK_CATEGORIES.map((category) => {
              const IconComponent = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => handleCategorySelect(category)}
                  className={`p-5 border-2 rounded-xl text-left transition-all hover:shadow-lg hover:scale-[1.02] ${category.bgColor} border-current`}
                >
                  <div className="flex items-center space-x-4">
                    <div className={`p-3 rounded-xl ${category.color} bg-white shadow-sm`}>
                      <IconComponent className="h-7 w-7" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 text-lg">{category.name}</h3>
                      <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                      <p className="text-xs text-gray-500 mt-2 flex items-center">
                        <span className="inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-700">
                          {category.templates.length} template{category.templates.length !== 1 ? 's' : ''}
                        </span>
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      <ChevronRight className="h-6 w-6 text-gray-400" />
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Template Selection */}
      {currentStep === 'template' && selectedCategory && (
        <div className="space-y-4">
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => setCurrentStep('category')}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </button>
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900">{selectedCategory.name}</h2>
              <p className="text-sm text-gray-600">Select specific task type</p>
            </div>
            <div className="w-16"></div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {selectedCategory.templates.map((template) => (
              <button
                key={template.id}
                onClick={() => handleTemplateSelect(template)}
                className="p-5 border-2 border-gray-200 rounded-xl text-left hover:border-blue-400 hover:bg-blue-50 hover:shadow-lg hover:scale-[1.02] transition-all"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 text-lg">{template.name}</h3>
                    <p className="text-sm text-gray-600 mt-2">{template.description}</p>
                    <div className="flex items-center space-x-3 mt-3">
                      <div className="flex items-center space-x-1 text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        <Clock className="h-4 w-4" />
                        <span>{template.estimatedDuration}h</span>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskLevelColor(template.riskLevel)}`}>
                        {template.riskLevel} risk
                      </span>
                    </div>
                  </div>
                  <div className="flex-shrink-0 ml-4">
                    <ChevronRight className="h-6 w-6 text-gray-400" />
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Task Details */}
      {currentStep === 'details' && selectedCategory && selectedTemplate && (
        <div className="space-y-6">
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => setCurrentStep('template')}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </button>
            <div className="text-center">
              <h2 className="text-lg font-semibold text-gray-900">{selectedTemplate.name}</h2>
              <p className="text-sm text-gray-600">{selectedCategory.name}</p>
            </div>
            <div className="w-16"></div>
          </div>

          {/* Task Details Form */}
          <div className="space-y-4">
            {/* Work Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Describe the work <span className="text-red-500">*</span>
              </label>
              <textarea
                value={workDescription}
                onChange={(e) => setWorkDescription(e.target.value)}
                placeholder="Describe what needs to be done..."
                rows={3}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
              />
            </div>

            {/* Location Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Location <span className="text-red-500">*</span>
              </label>
              <select
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
              >
                <option value="">Select location...</option>
                {COMMON_LOCATIONS.map((loc) => (
                  <option key={loc} value={loc}>{loc}</option>
                ))}
              </select>
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Planned Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  value={plannedDate}
                  onChange={(e) => setPlannedDate(e.target.value)}
                  min={getTomorrowDate()}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start Time
                </label>
                <input
                  type="time"
                  value={plannedTime}
                  onChange={(e) => setPlannedTime(e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base"
                />
              </div>
            </div>

            {/* Urgency */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Urgency Level
              </label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() => setUrgency('normal')}
                  className={`p-3 border rounded-lg text-left transition-all ${
                    urgency === 'normal'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <div className="font-medium">Normal</div>
                  <div className="text-sm text-gray-600">Standard priority</div>
                </button>
                <button
                  type="button"
                  onClick={() => setUrgency('urgent')}
                  className={`p-3 border rounded-lg text-left transition-all ${
                    urgency === 'urgent'
                      ? 'border-orange-500 bg-orange-50 text-orange-700'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <div className="font-medium">Urgent</div>
                  <div className="text-sm text-gray-600">High priority</div>
                </button>
              </div>
            </div>

            {/* Additional Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Notes
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Any additional information or special requirements..."
                rows={3}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base resize-none"
              />
            </div>

            {/* Task Summary */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Task Summary</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <div><span className="font-medium">Category:</span> {selectedCategory.name}</div>
                <div><span className="font-medium">Template:</span> {selectedTemplate.name}</div>
                <div><span className="font-medium">Estimated Duration:</span> {selectedTemplate.estimatedDuration} hours</div>
                <div><span className="font-medium">Risk Level:</span>
                  <span className={`ml-1 px-2 py-1 rounded text-xs ${getRiskLevelColor(selectedTemplate.riskLevel)}`}>
                    {selectedTemplate.riskLevel}
                  </span>
                </div>
              </div>
            </div>

            {/* Add Task Button */}
            <button
              onClick={handleAddTask}
              disabled={!workDescription.trim() || !location.trim() || !plannedDate}
              className="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
            >
              <span>Add Task to List</span>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default TaskCreationTab;
