import React from 'react';
import {
  Plus,
  Trash2,
  Calendar,
  Clock,
  MapPin,
  AlertTriangle,
  List
} from 'lucide-react';
import { TaskCategory } from '../../types/tasks';

interface TaskTemplateInfo {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface BulkTaskItem {
  id: string;
  category: TaskCategory;
  categoryName: string;
  template: TaskTemplateInfo;
  workDescription: string;
  location: string;
  plannedDate: string;
  plannedTime: string;
  estimatedDuration: number;
  urgency: 'normal' | 'urgent';
  dependencies?: string[];
  notes?: string;
}

interface TaskReviewTabProps {
  tasks: BulkTaskItem[];
  setTasks: React.Dispatch<React.SetStateAction<BulkTaskItem[]>>;
  onAddMore: () => void;
}

const TaskReviewTab: React.FC<TaskReviewTabProps> = ({ tasks, setTasks, onAddMore }) => {
  const handleRemoveTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    return urgency === 'urgent' 
      ? 'text-orange-600 bg-orange-50 border-orange-200' 
      : 'text-blue-600 bg-blue-50 border-blue-200';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  if (tasks.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <List className="h-16 w-16 mx-auto" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Tasks Created Yet</h3>
        <p className="text-gray-600 mb-6">Create your first task to see it here for review</p>
        <button
          onClick={onAddMore}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create First Task
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Tasks to Submit ({tasks.length})
        </h3>
        <button
          onClick={onAddMore}
          className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 font-medium"
        >
          <Plus className="h-4 w-4" />
          <span>Add Another</span>
        </button>
      </div>

      <div className="space-y-4">
        {tasks.map((task, index) => (
          <div key={task.id} className="bg-white border border-gray-200 rounded-xl p-5 hover:shadow-md transition-shadow">
            {/* Task Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 text-sm font-medium rounded-full">
                    {index + 1}
                  </span>
                  <h4 className="font-semibold text-gray-900">{task.template.name}</h4>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(task.urgency)}`}>
                    {task.urgency}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{task.categoryName}</p>
              </div>
              <button
                onClick={() => handleRemoveTask(task.id)}
                className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Remove task"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>

            {/* Task Details */}
            <div className="space-y-3">
              {/* Work Description */}
              <div>
                <p className="text-sm font-medium text-gray-700 mb-1">Work Description:</p>
                <p className="text-sm text-gray-600 bg-gray-50 rounded-lg p-3">{task.workDescription}</p>
              </div>

              {/* Location and Schedule */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span>{task.location}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span>{formatDate(task.plannedDate)} at {task.plannedTime}</span>
                </div>
              </div>

              {/* Duration and Risk */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1 text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  <Clock className="h-3 w-3" />
                  <span>{task.estimatedDuration}h</span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(task.template.riskLevel)}`}>
                  {task.template.riskLevel} risk
                </span>
              </div>

              {/* Additional Notes */}
              {task.notes && (
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Additional Notes:</p>
                  <p className="text-sm text-gray-600 bg-yellow-50 border border-yellow-200 rounded-lg p-3">{task.notes}</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Summary Card */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-5 mt-6">
        <h4 className="font-semibold text-blue-900 mb-3 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2" />
          Submission Summary
        </h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-blue-700 font-medium">Total Tasks:</span>
            <span className="ml-2 text-blue-900">{tasks.length}</span>
          </div>
          <div>
            <span className="text-blue-700 font-medium">Urgent Tasks:</span>
            <span className="ml-2 text-blue-900">{tasks.filter(t => t.urgency === 'urgent').length}</span>
          </div>
          <div>
            <span className="text-blue-700 font-medium">Total Duration:</span>
            <span className="ml-2 text-blue-900">{tasks.reduce((sum, task) => sum + task.estimatedDuration, 0)}h</span>
          </div>
          <div>
            <span className="text-blue-700 font-medium">High Risk Tasks:</span>
            <span className="ml-2 text-blue-900">{tasks.filter(t => t.template.riskLevel === 'high' || t.template.riskLevel === 'critical').length}</span>
          </div>
        </div>
        <div className="mt-4 pt-3 border-t border-blue-200">
          <p className="text-sm text-blue-700">
            <strong>Next Step:</strong> Tasks will be sent to the HSE team for safety review and documentation. 
            You'll receive notifications when tasks are approved and permits are ready.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TaskReviewTab;
