import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Search,
  Users,
  Shield,
  HardHat,
  Plus,
  FileText,
  Shovel,
  Flame
} from 'lucide-react';

interface TaskTemplatesViewProps {
  siteId: string;
}

const TaskTemplatesView: React.FC<TaskTemplatesViewProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const { siteId: paramSiteId } = useParams<{ siteId: string }>();
  const currentSiteId = siteId || paramSiteId;

  const [searchTerm, setSearchTerm] = useState('');

  // Define the specific permit types for work activities
  const permitTypes = [
    {
      id: 'general-ptw',
      name: 'General Permit to Work',
      description: 'Standard permit for general work activities requiring safety authorization',
      icon: <FileText className="h-5 w-5" />,
      route: '/ptw/form',
      color: 'bg-blue-100 text-blue-800 border-blue-200'
    },
    {
      id: 'excavation-ptw',
      name: 'Excavation Permit to Work',
      description: 'Permit for excavation and digging activities with ground disturbance',
      icon: <Shovel className="h-5 w-5" />,
      route: '/excavation/form',
      color: 'bg-orange-100 text-orange-800 border-orange-200'
    },
    {
      id: 'hot-work-ptw',
      name: 'Hot Work Permit to Work',
      description: 'Permit for welding, cutting, grinding and other hot work activities',
      icon: <Flame className="h-5 w-5" />,
      route: '/hot-work/form',
      color: 'bg-red-100 text-red-800 border-red-200'
    },
    {
      id: 'work-at-height',
      name: 'Work at Height Permit',
      description: 'Permit for work activities performed at elevated heights',
      icon: <Users className="h-5 w-5" />,
      route: '/work-at-height/form',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200'
    },
    {
      id: 'confined-space',
      name: 'Confined Space Entry Permit',
      description: 'Permit for entry into confined spaces with restricted access',
      icon: <HardHat className="h-5 w-5" />,
      route: '/confined-space/form',
      color: 'bg-purple-100 text-purple-800 border-purple-200'
    }
  ];

  const filteredPermits = permitTypes.filter(permit => {
    const matchesSearch = permit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permit.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });



  const handlePermitClick = (permit: typeof permitTypes[0]) => {
    const route = currentSiteId
      ? `/sites/${currentSiteId}${permit.route}`
      : permit.route;
    navigate(route);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Work Activities - Permit Types</h2>
          <p className="text-gray-600 mt-1">
            Select a permit type to create a new work permit
          </p>
        </div>
        <div className="text-sm text-gray-500">
          {filteredPermits.length} of {permitTypes.length} permit types
        </div>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search permit types..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Permit Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPermits.map((permit) => (
          <div
            key={permit.id}
            onClick={() => handlePermitClick(permit)}
            className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all cursor-pointer hover:border-blue-300"
          >
            {/* Header */}
            <div className="flex items-start space-x-4 mb-4">
              <div className={`p-3 rounded-lg ${permit.color}`}>
                {permit.icon}
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-lg mb-1">{permit.name}</h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {permit.description}
                </p>
              </div>
            </div>

            {/* Action Button */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Click to create permit</span>
                <div className="flex items-center text-blue-600">
                  <Plus className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">Create</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredPermits.length === 0 && (
        <div className="text-center py-12">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No permit types found
          </h3>
          <p className="text-gray-500">
            {searchTerm
              ? "Try adjusting your search to see more results."
              : "No permit types are currently available."}
          </p>
        </div>
      )}

      {/* Info Box */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Shield className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900 mb-1">About Work Activity Permits</h4>
            <p className="text-sm text-blue-800">
              These permit types ensure proper safety authorization for different work activities.
              Each permit type has specific safety requirements, risk assessments, and approval processes
              tailored to the nature of the work being performed.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskTemplatesView;
