import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Clock,
  AlertTriangle,
  FileText,
  Shield
} from 'lucide-react';
import { SiteTask } from '../../types/tasks';
import TaskRequestOverview from './request/TaskRequestOverview';
import HazardManagement from './request/HazardManagement';
import ControlMeasureManagement from './request/ControlMeasureManagement';
import DocumentManagement from './request/DocumentManagement';
import ApprovalActions from './request/ApprovalActions';

interface TaskRequestWorkflowProps {
  task: SiteTask;
  siteId: string;
  onTaskUpdate: (task: SiteTask) => void;
}

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  status: 'completed' | 'current' | 'pending';
  icon: React.ReactNode;
  component: React.ReactNode;
}

const TaskRequestWorkflow: React.FC<TaskRequestWorkflowProps> = ({
  task,
  siteId: _siteId,
  onTaskUpdate
}) => {
  const [activeStep, setActiveStep] = useState('overview');
  const [taskData, setTaskData] = useState(task);

  const handleTaskUpdate = (updatedTask: SiteTask) => {
    setTaskData(updatedTask);
    onTaskUpdate(updatedTask);
  };

  // Determine workflow progress based on task data
  const getStepStatus = (stepId: string): 'completed' | 'current' | 'pending' => {
    switch (stepId) {
      case 'overview':
        return 'completed'; // Always completed as it's just viewing
      case 'hazards':
        return taskData.hazards.length > 0 ? 'completed' :
               activeStep === 'hazards' ? 'current' : 'pending';
      case 'controls':
        return taskData.controlMeasures.length > 0 ? 'completed' :
               activeStep === 'controls' ? 'current' : 'pending';
      case 'documents':
        return taskData.attachedDocuments.length > 0 ? 'completed' :
               activeStep === 'documents' ? 'current' : 'pending';
      case 'approval':
        return taskData.status === 'approved' || taskData.status === 'rejected' ? 'completed' :
               activeStep === 'approval' ? 'current' : 'pending';
      default:
        return 'pending';
    }
  };

  const workflowSteps: WorkflowStep[] = [
    {
      id: 'overview',
      title: 'Task Overview',
      description: 'Review task details and requirements',
      status: getStepStatus('overview'),
      icon: <FileText className="h-5 w-5" />,
      component: <TaskRequestOverview task={taskData} />
    },
    {
      id: 'hazards',
      title: 'Hazard Assessment',
      description: 'Identify and assess potential hazards',
      status: getStepStatus('hazards'),
      icon: <AlertTriangle className="h-5 w-5" />,
      component: <HazardManagement task={taskData} onTaskUpdate={handleTaskUpdate} />
    },
    {
      id: 'controls',
      title: 'Control Measures',
      description: 'Define safety control measures',
      status: getStepStatus('controls'),
      icon: <Shield className="h-5 w-5" />,
      component: <ControlMeasureManagement task={taskData} onTaskUpdate={handleTaskUpdate} />
    },
    {
      id: 'documents',
      title: 'Documentation',
      description: 'Attach required documents and certificates',
      status: getStepStatus('documents'),
      icon: <FileText className="h-5 w-5" />,
      component: <DocumentManagement task={taskData} onTaskUpdate={handleTaskUpdate} />
    },
    {
      id: 'approval',
      title: 'Final Approval',
      description: 'Review and approve/disapprove the task',
      status: getStepStatus('approval'),
      icon: <CheckCircle className="h-5 w-5" />,
      component: <ApprovalActions task={taskData} onTaskUpdate={handleTaskUpdate} />
    }
  ];

  const currentStepIndex = workflowSteps.findIndex(step => step.id === activeStep);
  const currentStep = workflowSteps[currentStepIndex];

  const canProceedToNext = () => {
    const current = workflowSteps[currentStepIndex];
    return current.status === 'completed' && currentStepIndex < workflowSteps.length - 1;
  };

  const canGoToPrevious = () => {
    return currentStepIndex > 0;
  };

  const handleNext = () => {
    if (canProceedToNext()) {
      setActiveStep(workflowSteps[currentStepIndex + 1].id);
    }
  };

  const handlePrevious = () => {
    if (canGoToPrevious()) {
      setActiveStep(workflowSteps[currentStepIndex - 1].id);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'current':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'current':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-300" />;
    }
  };

  return (
    <div className="h-full flex overflow-hidden">
      {/* Left Sidebar - Workflow Steps */}
      <div className="w-80 border-r border-gray-200 bg-[#fafaf8] flex flex-col">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Request Workflow</h2>
          <p className="text-sm text-gray-600">
            Complete each step to process this task request
          </p>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-2">
            {workflowSteps.map((step) => (
              <button
                key={step.id}
                onClick={() => setActiveStep(step.id)}
                className={`w-full text-left p-4 rounded-lg border transition-all duration-200 ${
                  activeStep === step.id
                    ? 'bg-blue-50 border-blue-200 shadow-sm'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full border-2 flex items-center justify-center ${
                    step.status === 'completed' ? 'bg-green-100 border-green-200' :
                    step.status === 'current' ? 'bg-blue-100 border-blue-200' :
                    'bg-gray-100 border-gray-200'
                  }`}>
                    {getStatusIcon(step.status)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className={`text-sm font-medium ${
                        activeStep === step.id ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {step.title}
                      </h3>
                      <span className={`px-2 py-0.5 text-xs rounded-full border ${getStatusColor(step.status)}`}>
                        {step.status}
                      </span>
                    </div>
                    <p className="text-xs text-gray-600">{step.description}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="p-4 border-t border-gray-200 bg-white">
          <div className="flex justify-between space-x-3">
            <button
              onClick={handlePrevious}
              disabled={!canGoToPrevious()}
              className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Previous
            </button>
            <button
              onClick={handleNext}
              disabled={!canProceedToNext()}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Next
            </button>
          </div>
        </div>
      </div>

      {/* Right Content Area */}
      <div className="flex-1 bg-white overflow-hidden">
        <div className="h-full overflow-y-auto">
          {currentStep.component}
        </div>
      </div>
    </div>
  );
};

export default TaskRequestWorkflow;
