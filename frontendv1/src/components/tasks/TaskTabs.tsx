import React, { useRef, useState, useEffect } from 'react';
import { X, FileText, Shield, AlertTriangle, History, User, Calendar, MapPin, ChevronLeft, ChevronRight } from 'lucide-react';
import { SiteTask } from '../../types/tasks';
import TaskDetailsTab from './tabs/TaskDetailsTab';
import DocumentTab from './tabs/DocumentTab';
import HazardsTab from './tabs/HazardsTab';
import ControlMeasuresTab from './tabs/ControlMeasuresTab';
import AuditTrailTab from './tabs/AuditTrailTab';
import RequesterTab from './tabs/RequesterTab';
import ScheduleTab from './tabs/ScheduleTab';
import LocationTab from './tabs/LocationTab';

interface Tab {
  id: string;
  title: string;
  type: 'details' | 'document' | 'hazards' | 'control-measures' | 'audit-trail' | 'requester' | 'schedule' | 'location';
  data?: any;
}

interface TaskTabsProps {
  tabs: Tab[];
  activeTabId: string | null;
  onTabChange: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  task: SiteTask;
}

const TaskTabs: React.FC<TaskTabsProps> = ({
  tabs,
  activeTabId,
  onTabChange,
  onTabClose,
  task
}) => {
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // Check if scrolling is needed and update scroll button states
  const updateScrollState = () => {
    if (!tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const tabsWrapper = container.querySelector('div');

    if (!tabsWrapper) return;

    const hasOverflow = tabsWrapper.scrollWidth > container.clientWidth;
    const isAtStart = container.scrollLeft <= 0;
    const isAtEnd = container.scrollLeft >= container.scrollWidth - container.clientWidth - 1;

    setShowScrollButtons(hasOverflow);
    setCanScrollLeft(hasOverflow && !isAtStart);
    setCanScrollRight(hasOverflow && !isAtEnd);
  };

  // Update scroll state on mount and when tabs change
  useEffect(() => {
    updateScrollState();

    const container = tabsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', updateScrollState);
      return () => container.removeEventListener('scroll', updateScrollState);
    }
  }, [tabs]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => updateScrollState();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Scroll functions
  const scrollLeft = () => {
    if (!tabsContainerRef.current) return;
    const container = tabsContainerRef.current;
    const scrollAmount = Math.min(200, container.clientWidth / 2);
    container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
  };

  const scrollRight = () => {
    if (!tabsContainerRef.current) return;
    const container = tabsContainerRef.current;
    const scrollAmount = Math.min(200, container.clientWidth / 2);
    container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  };

  // Scroll active tab into view
  const scrollTabIntoView = (tabId: string) => {
    if (!tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const tabElement = container.querySelector(`[data-tab-id="${tabId}"]`) as HTMLElement;

    if (tabElement) {
      const containerRect = container.getBoundingClientRect();
      const tabRect = tabElement.getBoundingClientRect();

      if (tabRect.left < containerRect.left) {
        // Tab is to the left of visible area
        container.scrollBy({
          left: tabRect.left - containerRect.left - 20,
          behavior: 'smooth'
        });
      } else if (tabRect.right > containerRect.right) {
        // Tab is to the right of visible area
        container.scrollBy({
          left: tabRect.right - containerRect.right + 20,
          behavior: 'smooth'
        });
      }
    }
  };

  // Scroll to active tab when it changes
  useEffect(() => {
    if (activeTabId) {
      scrollTabIntoView(activeTabId);
    }
  }, [activeTabId]);
  const getTabIcon = (type: string) => {
    switch (type) {
      case 'details':
        return <FileText className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      case 'hazards':
        return <AlertTriangle className="h-4 w-4" />;
      case 'control-measures':
        return <Shield className="h-4 w-4" />;
      case 'audit-trail':
        return <History className="h-4 w-4" />;
      case 'requester':
        return <User className="h-4 w-4" />;
      case 'schedule':
        return <Calendar className="h-4 w-4" />;
      case 'location':
        return <MapPin className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const renderTabContent = (tab: Tab) => {
    switch (tab.type) {
      case 'details':
        return <TaskDetailsTab task={task} />;
      case 'document':
        return <DocumentTab document={tab.data?.document} />;
      case 'hazards':
        return <HazardsTab hazards={tab.data?.hazards || task.hazards} />;
      case 'control-measures':
        return <ControlMeasuresTab controlMeasures={tab.data?.controlMeasures || task.controlMeasures} />;
      case 'audit-trail':
        return <AuditTrailTab task={task} />;
      case 'requester':
        return <RequesterTab task={task} />;
      case 'schedule':
        return <ScheduleTab task={task} />;
      case 'location':
        return <LocationTab task={task} />;
      default:
        return (
          <div className="p-6">
            <div className="text-center text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>Content not available</p>
            </div>
          </div>
        );
    }
  };

  if (tabs.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-gray-500">
          <FileText className="h-16 w-16 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium mb-2">No tabs open</h3>
          <p>Select an item from the explorer to view its details</p>
        </div>
      </div>
    );
  }

  const activeTab = tabs.find(tab => tab.id === activeTabId);

  return (
    <div className="h-full flex flex-col min-w-0">
      {/* Tab Bar */}
      <div className="flex items-center border-b border-gray-200 bg-[#f8f9fa] relative min-w-0">
        {/* Left Scroll Button */}
        {showScrollButtons && (
          <button
            onClick={scrollLeft}
            disabled={!canScrollLeft}
            className={`flex-shrink-0 w-10 h-10 flex items-center justify-center border-r border-gray-200 transition-colors ${
              canScrollLeft
                ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                : 'text-gray-300 cursor-not-allowed'
            }`}
            title="Scroll left"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
        )}

        {/* Scrollable Tabs Container */}
        <div
          ref={tabsContainerRef}
          className="flex-1 min-w-0 overflow-x-auto scrollbar-hide"
          style={{
            scrollbarWidth: 'none', // Firefox
            msOverflowStyle: 'none', // IE/Edge
          }}
        >
          <div className="flex min-w-max">
            {tabs.map((tab) => (
              <div
                key={tab.id}
                data-tab-id={tab.id}
                className={`flex items-center space-x-2 px-4 py-3 border-r border-gray-200 cursor-pointer flex-shrink-0 transition-colors ${
                  activeTabId === tab.id
                    ? 'bg-white border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
                onClick={() => onTabChange(tab.id)}
                style={{ minWidth: 'max-content' }}
              >
                <div className="flex-shrink-0">
                  {getTabIcon(tab.type)}
                </div>
                <span className="text-sm font-medium whitespace-nowrap max-w-[200px] truncate">
                  {tab.title}
                </span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onTabClose(tab.id);
                  }}
                  className="flex-shrink-0 p-1 hover:bg-gray-200 rounded transition-colors"
                  title="Close tab"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Right Scroll Button */}
        {showScrollButtons && (
          <button
            onClick={scrollRight}
            disabled={!canScrollRight}
            className={`flex-shrink-0 w-10 h-10 flex items-center justify-center border-l border-gray-200 transition-colors ${
              canScrollRight
                ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                : 'text-gray-300 cursor-not-allowed'
            }`}
            title="Scroll right"
          >
            <ChevronRight className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto bg-[#fafaf8] min-w-0">
        {activeTab && renderTabContent(activeTab)}
      </div>
    </div>
  );
};

export default TaskTabs;
