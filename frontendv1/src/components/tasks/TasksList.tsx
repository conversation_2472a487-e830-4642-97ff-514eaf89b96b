import React, { useState, useEffect } from "react";
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Plus,
  Eye,
  Edit,
  Clock,
  Users,
  MapPin,
  Calendar,
  Shield,
  ClipboardList
} from 'lucide-react';
import { Task, TaskFilters } from '../../types/tasks';
import TaskStatusBadge from './shared/TaskStatusBadge';
import TaskPriorityBadge from './shared/TaskPriorityBadge';
import PermitStatusBadge from '../permits/shared/PermitStatusBadge';
import TaskCreationModal from './TaskCreationModal';
import { PermitStatus } from "../../types/permits";

interface TasksListProps {
	siteId: string;
}

// Mock data - replace with actual API calls
const mockTasks: Task[] = [
	{
		id: "task-1",
		taskNumber: "TSK-2024-001",
		title: "Concrete Pouring - Foundation Level 1",
		description: "Pour concrete for foundation at Level 1, Zone A",
		category: "construction",
		location: "Zone A - Level 1",
		siteId: "site-1",
		plannedStartDate: new Date("2024-01-15T08:00:00"),
		plannedEndDate: new Date("2024-01-15T16:00:00"),
		actualStartDate: new Date("2024-01-15T08:15:00"),
		estimatedDuration: 8,
		actualDuration: 7.5,
		status: "in-progress",
		priority: "high",
		progressPercentage: 65,
		createdBy: "supervisor-1",
		createdByName: "John Smith",
		assignedSupervisor: "supervisor-1",
		assignedSupervisorName: "John Smith",
		assignedWorkers: [
			{
				workerId: "worker-1",
				workerName: "Mike Johnson",
				primaryTrade: "Concrete Worker",
				role: "worker",
				assignedAt: new Date("2024-01-15T07:00:00"),
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				estimatedHours: 8,
			},
			{
				workerId: "worker-2",
				workerName: "David Wilson",
				primaryTrade: "Concrete Worker",
				role: "lead-worker",
				assignedAt: new Date("2024-01-15T07:00:00"),
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				estimatedHours: 8,
			},
		],
		dependencies: [],
		requiresPermit: false,
		riskLevel: "medium",
		safetyRequirements: [],
		requiredPPE: [],
		requiredTrainings: [],
		requiredCertifications: [],
		ramsDocuments: [],
		attachments: [],
		qualityChecks: [],
		complianceRequirements: [],
		createdAt: new Date("2024-01-14T16:00:00"),
		updatedAt: new Date("2024-01-15T08:15:00"),
		history: [],
		tags: [],
		customFields: {},
	},
	{
		id: "task-2",
		taskNumber: "TSK-2024-002",
		title: "Electrical Wiring - Floor 2 Offices",
		description: "Install electrical wiring for Floor 2 office spaces",
		category: "electrical",
		location: "Zone B - Floor 2",
		siteId: "site-1",
		plannedStartDate: new Date("2024-01-16T09:00:00"),
		plannedEndDate: new Date("2024-01-16T17:00:00"),
		estimatedDuration: 8,
		status: "permit-pending",
		priority: "medium",
		progressPercentage: 0,
		createdBy: "supervisor-2",
		createdByName: "Sarah Johnson",
		assignedSupervisor: "supervisor-2",
		assignedSupervisorName: "Sarah Johnson",
		assignedWorkers: [
			{
				workerId: "worker-3",
				workerName: "Robert Brown",
				primaryTrade: "Electrician",
				role: "worker",
				assignedAt: new Date("2024-01-15T14:00:00"),
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				estimatedHours: 8,
			},
		],
		dependencies: [],
		requiresPermit: true,
		permitStatus: "pending-approval",
		permitTypes: ["electrical-work"],
		riskLevel: "high",
		safetyRequirements: [],
		requiredPPE: [],
		requiredTrainings: [],
		requiredCertifications: [],
		ramsDocuments: [],
		attachments: [],
		qualityChecks: [],
		complianceRequirements: [],
		createdAt: new Date("2024-01-15T14:00:00"),
		updatedAt: new Date("2024-01-15T14:00:00"),
		history: [],
		tags: [],
		customFields: {},
	},
	{
		id: "task-3",
		taskNumber: "TSK-2024-003",
		title: "HVAC Installation - Basement Level",
		description: "Install HVAC system in basement level",
		category: "hvac",
		location: "Zone C - Basement",
		siteId: "site-1",
		plannedStartDate: new Date("2024-01-17T08:00:00"),
		plannedEndDate: new Date("2024-01-18T16:00:00"),
		estimatedDuration: 16,
		status: "todo",
		priority: "low",
		progressPercentage: 0,
		createdBy: "supervisor-3",
		createdByName: "Mike Davis",
		assignedSupervisor: "supervisor-3",
		assignedSupervisorName: "Mike Davis",
		assignedWorkers: [],
		dependencies: [],
		requiresPermit: false,
		riskLevel: "low",
		safetyRequirements: [],
		requiredPPE: [],
		requiredTrainings: [],
		requiredCertifications: [],
		ramsDocuments: [],
		attachments: [],
		qualityChecks: [],
		complianceRequirements: [],
		createdAt: new Date("2024-01-15T10:00:00"),
		updatedAt: new Date("2024-01-15T10:00:00"),
		history: [],
		tags: [],
		customFields: {},
	},
];

const TasksList: React.FC<TasksListProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [tasks, setTasks] = useState<Task[]>(mockTasks);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>(mockTasks);
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [filters, setFilters] = useState<TaskFilters>({
    search: '',
    status: 'all',
    priority: 'all',
    category: 'all',
    assignedWorker: '',
    assignedSupervisor: '',
    dateRange: {},
    location: '',
    requiresPermit: 'all',
    permitStatus: '',
    riskLevel: '',
    tags: []
  });

  useEffect(() => {
    // Fetch tasks for the site
    console.log(`Fetching tasks for site ${siteId}`);
  }, [siteId]);

  const handleCreateTask = async (taskData: Partial<Task>) => {
    try {
      // Here you would call the actual API to create the task
      console.log('Creating task:', taskData);

      // For now, just add to local state
      const newTask: Task = {
        id: `task-${Date.now()}`,
        taskNumber: `TSK-${new Date().getFullYear()}-${String(tasks.length + 1).padStart(3, '0')}`,
        createdBy: 'current-user',
        createdByName: 'Current User',
        createdAt: new Date(),
        updatedAt: new Date(),
        ...taskData
      } as Task;

      setTasks(prev => [newTask, ...prev]);

      // Show success message or redirect
      console.log('Task created successfully');
    } catch (error) {
      console.error('Error creating task:', error);
      // Handle error (show toast, etc.)
    }
  };

	useEffect(() => {
		// Apply filters
		let filtered = tasks;

		if (filters.search) {
			filtered = filtered.filter(
				(task) =>
					task.title.toLowerCase().includes(filters.search.toLowerCase()) ||
					task.taskNumber
						.toLowerCase()
						.includes(filters.search.toLowerCase()) ||
					task.description.toLowerCase().includes(filters.search.toLowerCase()),
			);
		}

		if (filters.status !== "all") {
			filtered = filtered.filter((task) => task.status === filters.status);
		}

		if (filters.priority !== "all") {
			filtered = filtered.filter((task) => task.priority === filters.priority);
		}

		if (filters.category !== "all") {
			filtered = filtered.filter((task) => task.category === filters.category);
		}

		if (filters.location) {
			filtered = filtered.filter((task) =>
				task.location.toLowerCase().includes(filters.location.toLowerCase()),
			);
		}

		if (filters.requiresPermit !== "all") {
			filtered = filtered.filter(
				(task) => task.requiresPermit === filters.requiresPermit,
			);
		}

		setFilteredTasks(filtered);
	}, [tasks, filters]);

	const isOverdue = (task: Task) => {
		return new Date() > task.plannedEndDate && task.status !== "completed";
	};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Task List</h2>
        <button
          onClick={() => setIsTaskModalOpen(true)}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create New Task
        </button>
      </div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search tasks..."
							value={filters.search}
							onChange={(e) =>
								setFilters({ ...filters, search: e.target.value })
							}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>

					<select
						value={filters.status}
						onChange={(e) =>
							setFilters({ ...filters, status: e.target.value as any })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					>
						<option value="all">All Statuses</option>
						<option value="todo">To Do</option>
						<option value="permit-pending">Permit Pending</option>
						<option value="permit-approved">Permit Approved</option>
						<option value="in-progress">In Progress</option>
						<option value="blocked">Blocked</option>
						<option value="completed">Completed</option>
					</select>

					<select
						value={filters.priority}
						onChange={(e) =>
							setFilters({ ...filters, priority: e.target.value as any })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					>
						<option value="all">All Priorities</option>
						<option value="critical">Critical</option>
						<option value="high">High</option>
						<option value="medium">Medium</option>
						<option value="low">Low</option>
					</select>

					<select
						value={filters.category}
						onChange={(e) =>
							setFilters({ ...filters, category: e.target.value as any })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					>
						<option value="all">All Categories</option>
						<option value="construction">Construction</option>
						<option value="electrical">Electrical</option>
						<option value="plumbing">Plumbing</option>
						<option value="hvac">HVAC</option>
						<option value="safety">Safety</option>
						<option value="inspection">Inspection</option>
					</select>
				</div>
			</div>

      {/* Tasks List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="divide-y divide-gray-200">
          {filteredTasks.map((task) => (
            <div key={task.id} className="p-6 hover:bg-gray-50">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-medium text-gray-900">{task.title}</h3>
                    <TaskStatusBadge status={task.status} size="sm" />
                    <TaskPriorityBadge priority={task.priority} size="sm" />
                    {task.requiresPermit && task.permitStatus && (
                      <PermitStatusBadge status={task.permitStatus as PermitStatus} size="sm" />
                    )}
                    {isOverdue(task) && (
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                        OVERDUE
                      </span>
                    )}
                  </div>

									<p className="text-sm text-gray-600 mb-3">
										{task.description}
									</p>

									<div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-500">
										<div className="flex items-center">
											<MapPin className="h-4 w-4 mr-1" />
											{task.location}
										</div>
										<div className="flex items-center">
											<Users className="h-4 w-4 mr-1" />
											{task.assignedWorkers.length} worker(s) assigned
										</div>
										<div className="flex items-center">
											<Clock className="h-4 w-4 mr-1" />
											{task.estimatedDuration}h estimated
										</div>
										<div className="flex items-center">
											<Calendar className="h-4 w-4 mr-1" />
											Due: {task.plannedEndDate.toLocaleDateString()}
										</div>
									</div>



									<div className="mt-3 flex items-center space-x-4 text-xs text-gray-400">
										<span>
											{task.category} • {task.taskNumber}
										</span>
										<span>Requested by: {task.createdByName}</span>
										{task.requiresPermit && (
											<span className="flex items-center text-amber-600">
												<Shield className="h-3 w-3 mr-1" />
												Permit Required
											</span>
										)}
									</div>
								</div>

								<div className="flex space-x-2 ml-4">
									<button
										onClick={() => navigate(`/sites/${siteId}/tasks/${task.id}`)}
										className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md"
										title="View Details"
									>
										<Eye className="h-4 w-4" />
									</button>
									<button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md">
										<Edit className="h-4 w-4" />
									</button>
								</div>
							</div>
						</div>
					))}
				</div>

        {filteredTasks.length === 0 && (
          <div className="p-12 text-center">
            <ClipboardList className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
            <p className="text-gray-500">
              {filters.search || filters.status !== 'all' || filters.priority !== 'all' || filters.category !== 'all' || filters.location
                ? 'Try adjusting your filters to see more results.'
                : 'Create your first task to get started.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Task Creation Modal */}
      <TaskCreationModal
        isOpen={isTaskModalOpen}
        onClose={() => setIsTaskModalOpen(false)}
        onSubmit={handleCreateTask}
        siteId={siteId}
      />
    </div>
  );
};

export default TasksList;
