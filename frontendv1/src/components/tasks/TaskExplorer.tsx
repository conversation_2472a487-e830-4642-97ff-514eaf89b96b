import React, { useState } from 'react';
import {
  ChevronDown,
  ChevronRight,
  FileText,
  Shield,
  AlertTriangle,
  History,
  File,
  Image,
  Video,
  User,
  Calendar,
  MapPin,
  Wrench
} from 'lucide-react';
import { SiteTask } from '../../types/tasks';

interface TaskExplorerProps {
  task: SiteTask;
  onItemSelect: (itemId: string, itemType: string, itemData?: any) => void;
  selectedItem: string | null;
}

interface ExplorerItem {
  id: string;
  name: string;
  type: 'folder' | 'file' | 'link';
  icon: React.ReactNode;
  children?: ExplorerItem[];
  data?: any;
  badge?: string;
  badgeColor?: string;
}

const TaskExplorer: React.FC<TaskExplorerProps> = ({ task, onItemSelect, selectedItem }) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set(['task-root', 'documents', 'linked-items']));

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <File className="h-4 w-4 text-red-500" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <Image className="h-4 w-4 text-blue-500" />;
      case 'mp4':
      case 'avi':
      case 'mov':
        return <Video className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };



  // Build the explorer tree structure
  const explorerItems: ExplorerItem[] = [
    {
      id: 'task-overview',
      name: 'Task Overview',
      type: 'file',
      icon: <FileText className="h-4 w-4 text-blue-500" />,
      data: { type: 'details', task }
    },
    {
      id: 'risk-assessment',
      name: 'Risk Assessment',
      type: 'folder',
      icon: <AlertTriangle className="h-4 w-4 text-orange-500" />,
      badge: `${task.hazards.length + task.controlMeasures.length}`,
      badgeColor: 'bg-orange-100 text-orange-800',
      children: [
        {
          id: 'hazards',
          name: 'Hazards',
          type: 'file' as const,
          icon: <AlertTriangle className="h-4 w-4 text-red-500" />,
          badge: task.hazards.length.toString(),
          badgeColor: 'bg-red-100 text-red-800',
          data: { type: 'hazards', hazards: task.hazards }
        },
        {
          id: 'control-measures',
          name: 'Control Measures',
          type: 'file' as const,
          icon: <Shield className="h-4 w-4 text-blue-500" />,
          badge: task.controlMeasures.length.toString(),
          badgeColor: 'bg-blue-100 text-blue-800',
          data: { type: 'control-measures', controlMeasures: task.controlMeasures }
        }
      ]
    },
    {
      id: 'documents',
      name: 'Documents',
      type: 'folder',
      icon: <FileText className="h-4 w-4 text-gray-600" />,
      badge: task.attachedDocuments.length.toString(),
      badgeColor: 'bg-gray-100 text-gray-800',
      children: task.attachedDocuments.map(doc => ({
        id: `doc-${doc.id}`,
        name: doc.name,
        type: 'file' as const,
        icon: getFileIcon(doc.name),
        data: { type: 'document', document: doc }
      }))
    },
    {
      id: 'task-info',
      name: 'Task Information',
      type: 'folder',
      icon: <Wrench className="h-4 w-4 text-purple-500" />,
      children: [
        {
          id: 'requester',
          name: 'Requester Details',
          type: 'file' as const,
          icon: <User className="h-4 w-4 text-green-500" />,
          data: { type: 'requester', task }
        },
        {
          id: 'schedule',
          name: 'Schedule',
          type: 'file' as const,
          icon: <Calendar className="h-4 w-4 text-blue-500" />,
          data: { type: 'schedule', task }
        },
        {
          id: 'location',
          name: 'Location Details',
          type: 'file' as const,
          icon: <MapPin className="h-4 w-4 text-red-500" />,
          data: { type: 'location', task }
        }
      ]
    },
    {
      id: 'audit-trail',
      name: 'Audit Trail',
      type: 'file',
      icon: <History className="h-4 w-4 text-gray-500" />,
      data: { type: 'audit-trail', task }
    }
  ];

  const renderItem = (item: ExplorerItem, level: number = 0) => {
    const isExpanded = expandedItems.has(item.id);
    const isSelected = selectedItem === item.id;
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id}>
        <div
          className={`flex items-center space-x-2 px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 ${
            isSelected ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-500' : 'text-gray-700'
          }`}
          style={{ paddingLeft: `${12 + level * 16}px` }}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.id);
            } else {
              onItemSelect(item.id, item.data?.type || 'file', item.data);
            }
          }}
        >
          {hasChildren && (
            <button className="p-0.5 hover:bg-gray-200 rounded">
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </button>
          )}
          {!hasChildren && <div className="w-4" />}

          <div className="flex-shrink-0">
            {item.icon}
          </div>

          <span className="flex-1 truncate">{item.name}</span>

          {item.badge && (
            <span className={`px-2 py-0.5 text-xs rounded-full ${item.badgeColor}`}>
              {item.badge}
            </span>
          )}
        </div>

        {hasChildren && isExpanded && item.children && (
          <div>
            {item.children.map(child => renderItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="flex-shrink-0">
            <div className={`w-3 h-3 rounded-full ${
              task.status === 'approved' ? 'bg-green-500' :
              task.status === 'in-progress' ? 'bg-blue-500' :
              task.status === 'completed' ? 'bg-gray-500' :
              'bg-yellow-500'
            }`} />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {task.name}
            </h3>
            <p className="text-xs text-gray-500 truncate">
              {task.category.replace('-', ' ').toUpperCase()} • {task.location}
            </p>
          </div>
        </div>
      </div>

      {/* Explorer Tree */}
      <div className="flex-1 overflow-y-auto">
        <div className="py-2">
          {explorerItems.map(item => renderItem(item))}
        </div>
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200">
        <div className="text-xs text-gray-500">
          <div>Created: {task.createdAt.toLocaleDateString()}</div>
          <div>Updated: {task.updatedAt.toLocaleDateString()}</div>
          <div>Status: <span className="capitalize">{task.status.replace('-', ' ')}</span></div>
        </div>
      </div>
    </div>
  );
};

export default TaskExplorer;
