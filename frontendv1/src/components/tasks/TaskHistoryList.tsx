import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  CheckCircle,
  XCircle,
  MapPin,
  User,
  Clock,
  History
} from 'lucide-react';
import { Task } from '../../types/tasks';
import TaskStatusBadge from './shared/TaskStatusBadge';
import TaskPriorityBadge from './shared/TaskPriorityBadge';

interface TaskHistoryListProps {
  siteId: string;
}

const TaskHistoryList: React.FC<TaskHistoryListProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [historyTasks, setHistoryTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');

  useEffect(() => {
    fetchHistoryTasks();
  }, [siteId]);

  useEffect(() => {
    filterTasks();
  }, [historyTasks, searchTerm, statusFilter, dateFilter]);

  const filterTasks = () => {
    let filtered = historyTasks.filter(task =>
      task.status === 'completed' ||
      task.status === 'cancelled' ||
      task.status === 'blocked'
    );

    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.assignedSupervisorName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(task => task.status === statusFilter);
    }

    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          filtered = filtered.filter(task =>
            task.updatedAt >= filterDate
          );
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          filtered = filtered.filter(task =>
            task.updatedAt >= filterDate
          );
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          filtered = filtered.filter(task =>
            task.updatedAt >= filterDate
          );
          break;
      }
    }

    // Sort by most recent first
    filtered.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
    setFilteredTasks(filtered);
  };

  const fetchHistoryTasks = async () => {
    // Mock data - replace with actual API call
    const mockHistoryTasks: Task[] = [
      {
        id: "task-hist-1",
        taskNumber: "TSK-2024-090",
        title: "Plumbing Rough-in - Building A",
        description: "Complete plumbing rough-in for Building A floors 1-2",
        category: "plumbing",
        location: "Building A - Floors 1-2",
        siteId: siteId,
        plannedStartDate: new Date('2024-01-15T08:00:00'),
        plannedEndDate: new Date('2024-01-17T16:00:00'),
        actualStartDate: new Date('2024-01-15T08:30:00'),
        actualEndDate: new Date('2024-01-17T15:45:00'),
        estimatedDuration: 20,
        actualDuration: 19.25,
        status: "completed",
        priority: "medium",
        progressPercentage: 100,
        createdBy: "engineer-1",
        createdByName: "Site Engineer",
        assignedSupervisor: "supervisor-2",
        assignedSupervisorName: "Mike Johnson",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: false,
        riskLevel: "low",
        safetyRequirements: [],
        requiredPPE: [],
        requiredTrainings: [],
        requiredCertifications: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        createdAt: new Date('2024-01-12T10:00:00'),
        updatedAt: new Date('2024-01-17T15:45:00'),
        history: [],
        tags: ["plumbing", "rough-in"],
        customFields: {}
      },
      {
        id: "task-hist-2",
        taskNumber: "TSK-2024-088",
        title: "Site Cleanup - Zone B",
        description: "Clean up construction debris in Zone B",
        category: "maintenance",
        location: "Zone B",
        siteId: siteId,
        plannedStartDate: new Date('2024-01-16T14:00:00'),
        plannedEndDate: new Date('2024-01-16T18:00:00'),
        estimatedDuration: 4,
        status: "cancelled",
        priority: "low",
        progressPercentage: 0,
        createdBy: "supervisor-1",
        createdByName: "John Smith",
        assignedSupervisor: "supervisor-1",
        assignedSupervisorName: "John Smith",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: false,
        riskLevel: "low",
        safetyRequirements: [],
        requiredPPE: [],
        requiredTrainings: [],
        requiredCertifications: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        createdAt: new Date('2024-01-16T13:00:00'),
        updatedAt: new Date('2024-01-16T14:30:00'),
        history: [],
        tags: ["cleanup", "maintenance"],
        customFields: {}
      }
    ];

    setHistoryTasks(mockHistoryTasks);
    setLoading(false);
  };

  const handleTaskClick = (taskId: string) => {
    navigate(`/sites/${siteId}/tasks/${taskId}`);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'blocked':
        return <Clock className="h-4 w-4 text-orange-500" />;
      default:
        return <History className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDuration = (planned: number, actual?: number) => {
    if (actual) {
      const variance = actual - planned;
      const varianceText = variance > 0 ? `+${variance}h` : `${variance}h`;
      return `${actual}h (${varianceText})`;
    }
    return `${planned}h planned`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading task history...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Task History</h2>
          <p className="text-sm text-gray-600">
            Completed, cancelled, and blocked tasks
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            {filteredTasks.length} task{filteredTasks.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search tasks..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="blocked">Blocked</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Time Period
            </label>
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
            </select>
          </div>
        </div>
      </div>

      {/* History Tasks List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        {filteredTasks.length === 0 ? (
          <div className="p-12 text-center">
            <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No task history found</h3>
            <p className="text-gray-500">
              {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'No tasks have been completed yet.'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredTasks.map((task) => (
              <div
                key={task.id}
                className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleTaskClick(task.id)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      {getStatusIcon(task.status)}
                      <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600">
                        {task.title}
                      </h3>
                      <TaskStatusBadge status={task.status} size="sm" />
                      <TaskPriorityBadge priority={task.priority} size="sm" />
                    </div>

                    <p className="text-sm text-gray-600 mb-3">{task.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{task.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">Requested by {task.createdByName}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">
                          Duration: {formatDuration(task.estimatedDuration, task.actualDuration)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="text-right ml-4">
                    <div className="text-sm text-gray-500 mb-1">
                      {task.status === 'completed' ? 'Completed' :
                       task.status === 'cancelled' ? 'Cancelled' : 'Blocked'}
                    </div>
                    <div className="text-xs text-gray-400 mb-1">
                      {task.updatedAt.toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-400">
                      {task.taskNumber}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskHistoryList;
