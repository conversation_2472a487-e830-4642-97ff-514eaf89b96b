import React, { useState, useEffect } from "react";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	TrendingUp,
	Download,
	Printer,
	CheckCircle,
} from "lucide-react";
// import { TaskReportData } from "../../types/tasks";
import {
	<PERSON><PERSON><PERSON>,
	<PERSON>,
	<PERSON>Axis,
	<PERSON>Axis,
	CartesianGrid,
	Tooltip,
	ResponsiveContainer,
	<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Cell,
	LineChart,
	Line,
} from "recharts";

interface TasksReportsProps {
	siteId: string;
}

// Mock report data
const mockTasksByStatus = [
	{ name: "To Do", count: 8, percentage: 19 },
	{ name: "In Progress", count: 12, percentage: 29 },
	{ name: "Permit Pending", count: 3, percentage: 7 },
	{ name: "Completed", count: 18, percentage: 43 },
	{ name: "Blocked", count: 1, percentage: 2 },
];

const mockTasksByCategory = [
	{ name: "Construction", count: 15, percentage: 36 },
	{ name: "Electrical", count: 8, percentage: 19 },
	{ name: "Plumbing", count: 6, percentage: 14 },
	{ name: "HVAC", count: 5, percentage: 12 },
	{ name: "Safety", count: 4, percentage: 10 },
	{ name: "Other", count: 4, percentage: 9 },
];

const mockWeeklyProgress = [
	{ week: "Week 1", planned: 12, completed: 10, efficiency: 83 },
	{ week: "Week 2", planned: 15, completed: 14, efficiency: 93 },
	{ week: "Week 3", planned: 18, completed: 16, efficiency: 89 },
	{ week: "Week 4", planned: 14, completed: 13, efficiency: 93 },
];

const mockProductivityTrends = [
	{ month: "Oct", productivity: 85, onTime: 78 },
	{ month: "Nov", productivity: 88, onTime: 82 },
	{ month: "Dec", productivity: 92, onTime: 87 },
	{ month: "Jan", productivity: 89, onTime: 85 },
];

// Permit-related mock data
const mockPermitsByStatus = [
  { name: 'Active', count: 8, percentage: 33 },
  { name: 'Pending Approval', count: 3, percentage: 13 },
  { name: 'Expired', count: 2, percentage: 8 },
  { name: 'Closed', count: 11, percentage: 46 }
];

const mockPermitsByType = [
  { name: 'Hot Work', count: 6, percentage: 25 },
  { name: 'Confined Space', count: 4, percentage: 17 },
  { name: 'Working at Height', count: 5, percentage: 21 },
  { name: 'Electrical Work', count: 3, percentage: 13 },
  { name: 'Excavation', count: 3, percentage: 13 },
  { name: 'Other', count: 3, percentage: 13 }
];

const COLORS = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#6B7280'];

const TasksReports: React.FC<TasksReportsProps> = ({ siteId }) => {
	const [reportType, setReportType] = useState("overview");
	const [dateRange, setDateRange] = useState("last-30");
	// const [reportData, setReportData] = useState<TaskReportData | null>(null);

	useEffect(() => {
		// Fetch report data based on selected parameters
		console.log(`Fetching task reports for site ${siteId}`);
	}, [siteId, reportType, dateRange]);

	const generateReport = () => {
		// Generate custom report based on selected parameters
		console.log("Generating custom task report...");
	};

	const exportReport = (format: "pdf" | "excel") => {
		console.log(`Exporting task report as ${format}`);
	};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Tasks & Permits Reports</h2>
        <div className="flex space-x-2">
          <button 
            onClick={() => exportReport('pdf')}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <Printer className="h-4 w-4 mr-1" />
            Print
          </button>
          <button 
            onClick={() => exportReport('excel')}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </button>
        </div>
      </div>

      {/* Report Templates */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h3 className="text-lg font-medium mb-4">Quick Reports</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
            <div className="flex items-center mb-2">
              <PieChart className="h-5 w-5 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-900">Task Distribution</h4>
            </div>
            <p className="text-sm text-gray-500">Breakdown of tasks by status and category</p>
          </button>

					<button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
						<div className="flex items-center mb-2">
							<TrendingUp className="h-5 w-5 text-green-600 mr-2" />
							<h4 className="font-medium text-gray-900">Productivity Trends</h4>
						</div>
						<p className="text-sm text-gray-500">
							Weekly and monthly productivity analysis
						</p>
					</button>

          <button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
            <div className="flex items-center mb-2">
              <BarChart3 className="h-5 w-5 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-900">Performance Metrics</h4>
            </div>
            <p className="text-sm text-gray-500">Completion rates and efficiency metrics</p>
          </button>

          <button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
            <div className="flex items-center mb-2">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-900">Permit Analysis</h4>
            </div>
            <p className="text-sm text-gray-500">Permit approval times and compliance</p>
          </button>
        </div>
      </div>

			{/* Custom Report Builder */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Custom Report</h3>
				<div className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Report Type
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
								value={reportType}
								onChange={(e) => setReportType(e.target.value)}
							>
								<option value="overview">Overview Report</option>
								<option value="productivity">Productivity Report</option>
								<option value="delays">Delays & Bottlenecks</option>
								<option value="worker-performance">Worker Performance</option>
							</select>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Date Range
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
								value={dateRange}
								onChange={(e) => setDateRange(e.target.value)}
							>
								<option value="last-7">Last 7 Days</option>
								<option value="last-30">Last 30 Days</option>
								<option value="last-90">Last 90 Days</option>
								<option value="year-to-date">Year to Date</option>
								<option value="custom">Custom Range</option>
							</select>
						</div>
						<div className="flex items-end">
							<button
								onClick={generateReport}
								className="w-full px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
							>
								Generate Report
							</button>
						</div>
					</div>
				</div>
			</div>

      {/* Report Visualizations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tasks by Status */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Tasks by Status</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <RechartsPieChart data={mockTasksByStatus} cx="50%" cy="50%" outerRadius={80} dataKey="count">
                  {mockTasksByStatus.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </RechartsPieChart>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-2">
            {mockTasksByStatus.map((item, index) => (
              <div key={item.name} className="flex items-center text-sm">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                ></div>
                <span className="text-gray-600">{item.name}: {item.count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Tasks by Category */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Tasks by Category</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={mockTasksByCategory}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Permits by Status */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Permits by Status</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <RechartsPieChart data={mockPermitsByStatus} cx="50%" cy="50%" outerRadius={80} dataKey="count">
                  {mockPermitsByStatus.map((_entry, index) => (
                    <Cell key={`permit-cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </RechartsPieChart>
                <Tooltip />
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-2">
            {mockPermitsByStatus.map((item, index) => (
              <div key={item.name} className="flex items-center text-sm">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                ></div>
                <span className="text-gray-600">{item.name}: {item.count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Permits by Type */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Permits by Type</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={mockPermitsByType}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#F59E0B" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

			{/* Weekly Progress */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">
					Weekly Progress & Efficiency
				</h3>
				<div className="h-64">
					<ResponsiveContainer width="100%" height="100%">
						<BarChart data={mockWeeklyProgress}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="week" />
							<YAxis />
							<Tooltip />
							<Bar dataKey="planned" fill="#93C5FD" name="Planned" />
							<Bar dataKey="completed" fill="#10B981" name="Completed" />
						</BarChart>
					</ResponsiveContainer>
				</div>
			</div>

			{/* Productivity Trends */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Productivity Trends</h3>
				<div className="h-64">
					<ResponsiveContainer width="100%" height="100%">
						<LineChart data={mockProductivityTrends}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="month" />
							<YAxis />
							<Tooltip />
							<Line
								type="monotone"
								dataKey="productivity"
								stroke="#10B981"
								name="Productivity %"
							/>
							<Line
								type="monotone"
								dataKey="onTime"
								stroke="#3B82F6"
								name="On-Time %"
							/>
						</LineChart>
					</ResponsiveContainer>
				</div>
			</div>

      {/* Key Performance Indicators */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h3 className="text-lg font-medium mb-4">Key Performance Indicators</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">87.5%</div>
            <div className="text-sm text-gray-600">On-Time Completion</div>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">18.5h</div>
            <div className="text-sm text-gray-600">Avg. Task Duration</div>
          </div>
          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">92.3%</div>
            <div className="text-sm text-gray-600">Productivity Score</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">42</div>
            <div className="text-sm text-gray-600">Total Tasks (30d)</div>
          </div>
          <div className="text-center p-4 bg-amber-50 rounded-lg">
            <div className="text-2xl font-bold text-amber-600">2.5h</div>
            <div className="text-sm text-gray-600">Avg. Permit Approval</div>
          </div>
          <div className="text-center p-4 bg-emerald-50 rounded-lg">
            <div className="text-2xl font-bold text-emerald-600">98.5%</div>
            <div className="text-sm text-gray-600">Permit Compliance</div>
          </div>
        </div>
      </div>

			{/* Task Summary Table */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<div className="flex justify-between items-center mb-4">
					<h3 className="text-lg font-medium">Task Summary</h3>
					<div className="flex space-x-2">
						<button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
							<Printer className="h-4 w-4 inline mr-1" />
							Print
						</button>
						<button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
							<Download className="h-4 w-4 inline mr-1" />
							Export
						</button>
					</div>
				</div>

				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Category
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Total Tasks
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Completed
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									In Progress
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Completion Rate
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{mockTasksByCategory.map((category) => (
								<tr key={category.name}>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
										{category.name}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{category.count}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{Math.floor(category.count * 0.7)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{Math.floor(category.count * 0.3)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{Math.floor(70 + Math.random() * 25)}%
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
};

export default TasksReports;
