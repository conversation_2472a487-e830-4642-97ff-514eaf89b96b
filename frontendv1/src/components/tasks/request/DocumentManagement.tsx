import React, { useState } from 'react';
import {
  FileText,
  Upload,
  Download,
  Eye,
  Trash2,
  CheckCircle,
  AlertCircle,
  Plus,
  X,
  Calendar,
  User
} from 'lucide-react';
import { SiteTask, TaskDocument } from '../../../types/tasks';

interface DocumentManagementProps {
  task: SiteTask;
  onTaskUpdate: (task: SiteTask) => void;
}

const DocumentManagement: React.FC<DocumentManagementProps> = ({ task, onTaskUpdate }) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newDocument, setNewDocument] = useState<Partial<TaskDocument>>({
    name: '',
    type: 'other',
    version: '1.0',
    isRequired: false
  });

  const documentTypes = [
    { value: 'rams', label: 'RAMS (Risk Assessment Method Statement)', required: true },
    { value: 'permit', label: 'Work Permit', required: true },
    { value: 'certificate', label: 'Equipment Certificate', required: false },
    { value: 'competency', label: 'Operator Competency', required: false },
    { value: 'procedure', label: 'Work Procedure', required: false },
    { value: 'drawing', label: 'Technical Drawing', required: false },
    { value: 'specification', label: 'Material Specification', required: false },
    { value: 'other', label: 'Other Document', required: false }
  ];

  const handleAddDocument = () => {
    if (!newDocument.name?.trim()) return;

    const document: TaskDocument = {
      id: `doc${Date.now()}`,
      name: newDocument.name,
      type: newDocument.type || 'other',
      url: `/documents/${newDocument.name.toLowerCase().replace(/\s+/g, '-')}.pdf`,
      version: newDocument.version || '1.0',
      uploadedBy: 'current-user',
      uploadedAt: new Date(),
      isRequired: newDocument.isRequired || false
    };

    const updatedTask = {
      ...task,
      attachedDocuments: [...task.attachedDocuments, document]
    };

    onTaskUpdate(updatedTask);
    setNewDocument({
      name: '',
      type: 'other',
      version: '1.0',
      isRequired: false
    });
    setShowAddForm(false);
  };

  const handleDeleteDocument = (documentId: string) => {
    const updatedDocuments = task.attachedDocuments.filter(doc => doc.id !== documentId);
    const updatedTask = {
      ...task,
      attachedDocuments: updatedDocuments
    };
    onTaskUpdate(updatedTask);
  };

  const handleToggleRequired = (documentId: string) => {
    const updatedDocuments = task.attachedDocuments.map(doc => {
      if (doc.id === documentId) {
        return { ...doc, isRequired: !doc.isRequired };
      }
      return doc;
    });

    const updatedTask = {
      ...task,
      attachedDocuments: updatedDocuments
    };
    onTaskUpdate(updatedTask);
  };

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'rams':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'permit':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'certificate':
        return <FileText className="h-5 w-5 text-blue-600" />;
      case 'competency':
        return <User className="h-5 w-5 text-purple-600" />;
      case 'procedure':
        return <FileText className="h-5 w-5 text-orange-600" />;
      case 'drawing':
        return <FileText className="h-5 w-5 text-indigo-600" />;
      case 'specification':
        return <FileText className="h-5 w-5 text-teal-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const getDocumentTypeColor = (type: string) => {
    switch (type) {
      case 'rams':
        return 'bg-red-100 text-red-800';
      case 'permit':
        return 'bg-green-100 text-green-800';
      case 'certificate':
        return 'bg-blue-100 text-blue-800';
      case 'competency':
        return 'bg-purple-100 text-purple-800';
      case 'procedure':
        return 'bg-orange-100 text-orange-800';
      case 'drawing':
        return 'bg-indigo-100 text-indigo-800';
      case 'specification':
        return 'bg-teal-100 text-teal-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Check for required documents
  const requiredDocTypes = documentTypes.filter(type => type.required).map(type => type.value);
  const attachedDocTypes = task.attachedDocuments.map(doc => doc.type);
  const missingRequiredDocs = requiredDocTypes.filter(type => !attachedDocTypes.includes(type as any));

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Document Management</h2>
            <p className="text-sm text-gray-600">
              Attach and verify all required documents for this task. Ensure all certificates and permits are current.
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Add Document</span>
          </button>
        </div>

        {/* Required Documents Status */}
        {missingRequiredDocs.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-red-900 mb-1">Missing Required Documents</h3>
                <div className="text-sm text-red-700">
                  <p className="mb-2">The following required documents are missing:</p>
                  <ul className="list-disc list-inside space-y-1">
                    {missingRequiredDocs.map(type => {
                      const docType = documentTypes.find(dt => dt.value === type);
                      return (
                        <li key={type}>{docType?.label || type}</li>
                      );
                    })}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Document Requirements Guide */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-3">Document Requirements</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Required Documents:</h4>
              <ul className="space-y-1 text-blue-700">
                {documentTypes.filter(type => type.required).map(type => (
                  <li key={type.value} className="flex items-center space-x-2">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    <span>{type.label}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Optional Documents:</h4>
              <ul className="space-y-1 text-blue-700">
                {documentTypes.filter(type => !type.required).slice(0, 4).map(type => (
                  <li key={type.value} className="flex items-center space-x-2">
                    <FileText className="h-3 w-3 text-blue-600" />
                    <span>{type.label}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Add Document Form */}
      {showAddForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Add Document</h3>
            <button
              onClick={() => setShowAddForm(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Document Name *
              </label>
              <input
                type="text"
                value={newDocument.name || ''}
                onChange={(e) => setNewDocument({ ...newDocument, name: e.target.value })}
                placeholder="Enter document name..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Document Type
                </label>
                <select
                  value={newDocument.type || 'other'}
                  onChange={(e) => {
                    const selectedType = documentTypes.find(type => type.value === e.target.value);
                    setNewDocument({
                      ...newDocument,
                      type: e.target.value as any,
                      isRequired: selectedType?.required || false
                    });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  {documentTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label} {type.required ? '(Required)' : ''}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Version
                </label>
                <input
                  type="text"
                  value={newDocument.version || '1.0'}
                  onChange={(e) => setNewDocument({ ...newDocument, version: e.target.value })}
                  placeholder="e.g., 1.0, 2.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>

            <div>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={newDocument.isRequired || false}
                  onChange={(e) => setNewDocument({ ...newDocument, isRequired: e.target.checked })}
                  className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                />
                <span className="text-sm text-gray-700">Mark as required document</span>
              </label>
            </div>

            {/* File Upload Simulation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Upload File
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 mb-2">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500">
                  PDF, DOC, DOCX up to 10MB
                </p>
                <button
                  type="button"
                  className="mt-2 px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Choose File
                </button>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddDocument}
                disabled={!newDocument.name?.trim()}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Add Document
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Documents List */}
      <div className="space-y-4">
        {task.attachedDocuments.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">No documents attached yet</p>
            <p className="text-sm text-gray-400">
              Add required documents to proceed with the approval process
            </p>
          </div>
        ) : (
          task.attachedDocuments.map((document) => (
            <div key={document.id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    {getDocumentIcon(document.type)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {document.name}
                      </h3>
                      <span className={`px-2 py-1 text-xs rounded-full font-medium ${getDocumentTypeColor(document.type)}`}>
                        {documentTypes.find(t => t.value === document.type)?.label || document.type}
                      </span>
                      {document.isRequired && (
                        <span className="px-2 py-1 text-xs rounded-full font-medium bg-red-100 text-red-800">
                          REQUIRED
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <FileText className="h-3 w-3" />
                        <span>Version {document.version}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <User className="h-3 w-3" />
                        <span>{document.uploadedBy}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{document.uploadedAt.toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span className="text-green-600">Verified</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  <button
                    onClick={() => window.open(document.url, '_blank')}
                    className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    title="View document"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => window.open(document.url, '_blank')}
                    className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                    title="Download document"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleToggleRequired(document.id)}
                    className={`p-2 transition-colors ${
                      document.isRequired
                        ? 'text-red-600 hover:text-red-700'
                        : 'text-gray-400 hover:text-red-600'
                    }`}
                    title={document.isRequired ? 'Mark as optional' : 'Mark as required'}
                  >
                    <AlertCircle className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteDocument(document.id)}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    title="Delete document"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      <div className="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Document Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Total Documents:</span>
            <span className="ml-2 font-medium text-gray-900">{task.attachedDocuments.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Required:</span>
            <span className="ml-2 font-medium text-gray-900">
              {task.attachedDocuments.filter(doc => doc.isRequired).length}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Missing Required:</span>
            <span className={`ml-2 font-medium ${missingRequiredDocs.length > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {missingRequiredDocs.length}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Status:</span>
            <span className={`ml-2 font-medium ${missingRequiredDocs.length === 0 ? 'text-green-600' : 'text-red-600'}`}>
              {missingRequiredDocs.length === 0 ? 'Complete' : 'Incomplete'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentManagement;
