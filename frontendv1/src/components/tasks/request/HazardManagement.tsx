import React, { useState } from 'react';
import {
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  X
} from 'lucide-react';
import { SiteTask, Hazard } from '../../../types/tasks';

interface HazardManagementProps {
  task: SiteTask;
  onTaskUpdate: (task: SiteTask) => void;
}

const HazardManagement: React.FC<HazardManagementProps> = ({ task, onTaskUpdate }) => {
  const [editingHazard, setEditingHazard] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newHazard, setNewHazard] = useState<Partial<Hazard>>({
    description: '',
    riskLevel: 'medium',
    likelihood: 2,
    severity: 2,
    riskScore: 4,
    controlMeasures: []
  });

  const riskLevels = [
    { value: 'low', label: 'Low', color: 'text-green-600 bg-green-100' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600 bg-yellow-100' },
    { value: 'high', label: 'High', color: 'text-orange-600 bg-orange-100' },
    { value: 'critical', label: 'Critical', color: 'text-red-600 bg-red-100' }
  ];

  const calculateRiskScore = (likelihood: number, severity: number) => {
    return likelihood * severity;
  };

  const getRiskLevel = (score: number): 'low' | 'medium' | 'high' | 'critical' => {
    if (score <= 4) return 'low';
    if (score <= 9) return 'medium';
    if (score <= 16) return 'high';
    return 'critical';
  };

  const handleAddHazard = () => {
    if (!newHazard.description?.trim()) return;

    const riskScore = calculateRiskScore(newHazard.likelihood || 2, newHazard.severity || 2);
    const riskLevel = getRiskLevel(riskScore);

    const hazard: Hazard = {
      id: `h${Date.now()}`,
      description: newHazard.description,
      riskLevel,
      likelihood: newHazard.likelihood || 2,
      severity: newHazard.severity || 2,
      riskScore,
      controlMeasures: []
    };

    const updatedTask = {
      ...task,
      hazards: [...task.hazards, hazard]
    };

    onTaskUpdate(updatedTask);
    setNewHazard({
      description: '',
      riskLevel: 'medium',
      likelihood: 2,
      severity: 2,
      riskScore: 4,
      controlMeasures: []
    });
    setShowAddForm(false);
  };

  const handleUpdateHazard = (hazardId: string, updatedHazard: Partial<Hazard>) => {
    const updatedHazards = task.hazards.map(hazard => {
      if (hazard.id === hazardId) {
        const riskScore = calculateRiskScore(
          updatedHazard.likelihood || hazard.likelihood,
          updatedHazard.severity || hazard.severity
        );
        const riskLevel = getRiskLevel(riskScore);

        return {
          ...hazard,
          ...updatedHazard,
          riskScore,
          riskLevel
        };
      }
      return hazard;
    });

    const updatedTask = {
      ...task,
      hazards: updatedHazards
    };

    onTaskUpdate(updatedTask);
    setEditingHazard(null);
  };

  const handleDeleteHazard = (hazardId: string) => {
    const updatedHazards = task.hazards.filter(hazard => hazard.id !== hazardId);
    const updatedTask = {
      ...task,
      hazards: updatedHazards
    };
    onTaskUpdate(updatedTask);
  };

  const getRiskLevelStyle = (riskLevel: string) => {
    const level = riskLevels.find(l => l.value === riskLevel);
    return level ? level.color : 'text-gray-600 bg-gray-100';
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Hazard Assessment</h2>
            <p className="text-sm text-gray-600">
              Identify and assess potential hazards for this task. Each hazard will be evaluated based on likelihood and severity.
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Add Hazard</span>
          </button>
        </div>

        {/* Risk Matrix Guide */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Risk Assessment Guide</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
            <div>
              <span className="font-medium">Likelihood (1-5):</span>
              <div className="mt-1 space-y-1">
                <div>1 - Very Unlikely</div>
                <div>2 - Unlikely</div>
                <div>3 - Possible</div>
                <div>4 - Likely</div>
                <div>5 - Very Likely</div>
              </div>
            </div>
            <div>
              <span className="font-medium">Severity (1-5):</span>
              <div className="mt-1 space-y-1">
                <div>1 - Negligible</div>
                <div>2 - Minor</div>
                <div>3 - Moderate</div>
                <div>4 - Major</div>
                <div>5 - Catastrophic</div>
              </div>
            </div>
            <div>
              <span className="font-medium">Risk Score:</span>
              <div className="mt-1 space-y-1">
                <div>1-4: Low</div>
                <div>5-9: Medium</div>
                <div>10-16: High</div>
                <div>17-25: Critical</div>
              </div>
            </div>
            <div>
              <span className="font-medium">Actions:</span>
              <div className="mt-1 space-y-1">
                <div className="text-green-600">Low: Monitor</div>
                <div className="text-yellow-600">Medium: Control</div>
                <div className="text-orange-600">High: Mitigate</div>
                <div className="text-red-600">Critical: Stop Work</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Hazard Form */}
      {showAddForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Add New Hazard</h3>
            <button
              onClick={() => setShowAddForm(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Hazard Description *
              </label>
              <textarea
                value={newHazard.description || ''}
                onChange={(e) => setNewHazard({ ...newHazard, description: e.target.value })}
                placeholder="Describe the potential hazard..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Likelihood (1-5)
                </label>
                <select
                  value={newHazard.likelihood || 2}
                  onChange={(e) => {
                    const likelihood = parseInt(e.target.value);
                    const riskScore = calculateRiskScore(likelihood, newHazard.severity || 2);
                    setNewHazard({
                      ...newHazard,
                      likelihood,
                      riskScore,
                      riskLevel: getRiskLevel(riskScore)
                    });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value={1}>1 - Very Unlikely</option>
                  <option value={2}>2 - Unlikely</option>
                  <option value={3}>3 - Possible</option>
                  <option value={4}>4 - Likely</option>
                  <option value={5}>5 - Very Likely</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Severity (1-5)
                </label>
                <select
                  value={newHazard.severity || 2}
                  onChange={(e) => {
                    const severity = parseInt(e.target.value);
                    const riskScore = calculateRiskScore(newHazard.likelihood || 2, severity);
                    setNewHazard({
                      ...newHazard,
                      severity,
                      riskScore,
                      riskLevel: getRiskLevel(riskScore)
                    });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value={1}>1 - Negligible</option>
                  <option value={2}>2 - Minor</option>
                  <option value={3}>3 - Moderate</option>
                  <option value={4}>4 - Major</option>
                  <option value={5}>5 - Catastrophic</option>
                </select>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Risk Assessment:</span>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">
                    Score: {newHazard.riskScore || 4}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full font-medium ${getRiskLevelStyle(newHazard.riskLevel || 'medium')}`}>
                    {(newHazard.riskLevel || 'medium').toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddHazard}
                disabled={!newHazard.description?.trim()}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Add Hazard
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Hazards List */}
      <div className="space-y-4">
        {task.hazards.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
            <AlertTriangle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">No hazards identified yet</p>
            <p className="text-sm text-gray-400">
              Click "Add Hazard" to start the risk assessment process
            </p>
          </div>
        ) : (
          task.hazards.map((hazard) => (
            <HazardCard
              key={hazard.id}
              hazard={hazard}
              isEditing={editingHazard === hazard.id}
              onEdit={() => setEditingHazard(hazard.id)}
              onSave={(updatedHazard) => handleUpdateHazard(hazard.id, updatedHazard)}
              onCancel={() => setEditingHazard(null)}
              onDelete={() => handleDeleteHazard(hazard.id)}
              getRiskLevelStyle={getRiskLevelStyle}
              calculateRiskScore={calculateRiskScore}
              getRiskLevel={getRiskLevel}
            />
          ))
        )}
      </div>
    </div>
  );
};

// Separate component for hazard cards to keep the main component clean
interface HazardCardProps {
  hazard: Hazard;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (hazard: Partial<Hazard>) => void;
  onCancel: () => void;
  onDelete: () => void;
  getRiskLevelStyle: (riskLevel: string) => string;
  calculateRiskScore: (likelihood: number, severity: number) => number;
  getRiskLevel: (score: number) => 'low' | 'medium' | 'high' | 'critical';
}

const HazardCard: React.FC<HazardCardProps> = ({
  hazard,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  onDelete,
  getRiskLevelStyle,
  calculateRiskScore,
  getRiskLevel
}) => {
  const [editData, setEditData] = useState(hazard);

  const handleSave = () => {
    onSave(editData);
  };

  if (isEditing) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Hazard Description
            </label>
            <textarea
              value={editData.description}
              onChange={(e) => setEditData({ ...editData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Likelihood (1-5)
              </label>
              <select
                value={editData.likelihood}
                onChange={(e) => {
                  const likelihood = parseInt(e.target.value);
                  const riskScore = calculateRiskScore(likelihood, editData.severity);
                  setEditData({
                    ...editData,
                    likelihood,
                    riskScore,
                    riskLevel: getRiskLevel(riskScore)
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value={1}>1 - Very Unlikely</option>
                <option value={2}>2 - Unlikely</option>
                <option value={3}>3 - Possible</option>
                <option value={4}>4 - Likely</option>
                <option value={5}>5 - Very Likely</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Severity (1-5)
              </label>
              <select
                value={editData.severity}
                onChange={(e) => {
                  const severity = parseInt(e.target.value);
                  const riskScore = calculateRiskScore(editData.likelihood, severity);
                  setEditData({
                    ...editData,
                    severity,
                    riskScore,
                    riskLevel: getRiskLevel(riskScore)
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value={1}>1 - Negligible</option>
                <option value={2}>2 - Minor</option>
                <option value={3}>3 - Moderate</option>
                <option value={4}>4 - Major</option>
                <option value={5}>5 - Catastrophic</option>
              </select>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Updated Risk Assessment:</span>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  Score: {editData.riskScore}
                </span>
                <span className={`px-2 py-1 text-xs rounded-full font-medium ${getRiskLevelStyle(editData.riskLevel)}`}>
                  {editData.riskLevel.toUpperCase()}
                </span>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-3">
            <AlertTriangle className={`h-5 w-5 ${
              hazard.riskLevel === 'critical' ? 'text-red-500' :
              hazard.riskLevel === 'high' ? 'text-orange-500' :
              hazard.riskLevel === 'medium' ? 'text-yellow-500' :
              'text-green-500'
            }`} />
            <span className={`px-2 py-1 text-xs rounded-full font-medium ${getRiskLevelStyle(hazard.riskLevel)}`}>
              {hazard.riskLevel.toUpperCase()}
            </span>
            <span className="text-sm text-gray-500">
              Score: {hazard.riskScore} (L:{hazard.likelihood} × S:{hazard.severity})
            </span>
          </div>

          <p className="text-gray-900 mb-3">{hazard.description}</p>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Likelihood:</span>
              <span className="ml-2 font-medium">{hazard.likelihood}/5</span>
            </div>
            <div>
              <span className="text-gray-600">Severity:</span>
              <span className="ml-2 font-medium">{hazard.severity}/5</span>
            </div>
          </div>
        </div>

        <div className="flex space-x-2 ml-4">
          <button
            onClick={onEdit}
            className="p-2 text-gray-400 hover:text-orange-600 transition-colors"
            title="Edit hazard"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={onDelete}
            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
            title="Delete hazard"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default HazardManagement;
