import React, { useState } from 'react';
import {
  Shield,
  Plus,
  Edit,
  Trash2,
  X,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Circle,
  Wrench,
  Users,
} from 'lucide-react';
import { SiteTask, ControlMeasure } from '../../../types/tasks';

interface ControlMeasureManagementProps {
  task: SiteTask;
  onTaskUpdate: (task: SiteTask) => void;
}

const ControlMeasureManagement: React.FC<ControlMeasureManagementProps> = ({ task, onTaskUpdate }) => {
  const [editingMeasure, setEditingMeasure] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newMeasure, setNewMeasure] = useState<Partial<ControlMeasure>>({
    description: '',
    type: 'administrative',
    effectiveness: 3,
    implementationCost: 'medium',
    trainingRequired: false,
    equipmentRequired: []
  });

  const measureTypes = [
    { value: 'elimination', label: 'Elimination', description: 'Remove the hazard completely' },
    { value: 'substitution', label: 'Substitution', description: 'Replace with something safer' },
    { value: 'engineering', label: 'Engineering Controls', description: 'Physical safeguards' },
    { value: 'administrative', label: 'Administrative Controls', description: 'Policies and procedures' },
    { value: 'ppe', label: 'Personal Protective Equipment', description: 'Individual protection' }
  ];

  const costLevels = [
    { value: 'low', label: 'Low', color: 'text-green-600 bg-green-100' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600 bg-yellow-100' },
    { value: 'high', label: 'High', color: 'text-red-600 bg-red-100' }
  ];

  const handleAddMeasure = () => {
    if (!newMeasure.description?.trim()) return;

    const measure: ControlMeasure = {
      id: `cm${Date.now()}`,
      description: newMeasure.description,
      type: newMeasure.type || 'administrative',
      effectiveness: newMeasure.effectiveness || 3,
      implementationCost: newMeasure.implementationCost || 'medium',
      trainingRequired: newMeasure.trainingRequired || false,
      equipmentRequired: newMeasure.equipmentRequired || []
    };

    const updatedTask = {
      ...task,
      controlMeasures: [...task.controlMeasures, measure]
    };

    onTaskUpdate(updatedTask);
    setNewMeasure({
      description: '',
      type: 'administrative',
      effectiveness: 3,
      implementationCost: 'medium',
      trainingRequired: false,
      equipmentRequired: []
    });
    setShowAddForm(false);
  };

  const handleUpdateMeasure = (measureId: string, updatedMeasure: Partial<ControlMeasure>) => {
    const updatedMeasures = task.controlMeasures.map(measure => {
      if (measure.id === measureId) {
        return { ...measure, ...updatedMeasure };
      }
      return measure;
    });

    const updatedTask = {
      ...task,
      controlMeasures: updatedMeasures
    };

    onTaskUpdate(updatedTask);
    setEditingMeasure(null);
  };

  const handleDeleteMeasure = (measureId: string) => {
    const updatedMeasures = task.controlMeasures.filter(measure => measure.id !== measureId);
    const updatedTask = {
      ...task,
      controlMeasures: updatedMeasures
    };
    onTaskUpdate(updatedTask);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'elimination':
        return <X className="h-4 w-4 text-red-600" />;
      case 'substitution':
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'engineering':
        return <Wrench className="h-4 w-4 text-purple-600" />;
      case 'administrative':
        return <Users className="h-4 w-4 text-orange-600" />;
      case 'ppe':
        return <Shield className="h-4 w-4 text-green-600" />;
      default:
        return <Shield className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'elimination':
        return 'bg-red-100 text-red-800';
      case 'substitution':
        return 'bg-blue-100 text-blue-800';
      case 'engineering':
        return 'bg-purple-100 text-purple-800';
      case 'administrative':
        return 'bg-orange-100 text-orange-800';
      case 'ppe':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCostColor = (cost: string) => {
    const level = costLevels.find(l => l.value === cost);
    return level ? level.color : 'text-gray-600 bg-gray-100';
  };

  const getEffectivenessColor = (effectiveness: number) => {
    if (effectiveness >= 4) return 'text-green-600';
    if (effectiveness >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };



  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Control Measures</h2>
            <p className="text-sm text-gray-600">
              Define safety control measures to mitigate identified hazards. Follow the hierarchy of controls for maximum effectiveness.
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Add Control Measure</span>
          </button>
        </div>

        {/* Hierarchy of Controls Guide */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-3">Hierarchy of Controls (Most to Least Effective)</h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-3 text-xs">
            {measureTypes.map((type, index) => (
              <div key={type.value} className="flex items-start space-x-2">
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-white flex items-center justify-center text-blue-600 font-bold">
                  {index + 1}
                </div>
                <div>
                  <div className="font-medium text-blue-900">{type.label}</div>
                  <div className="text-blue-700">{type.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Hazards Summary */}
        {task.hazards.length > 0 && (
          <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-yellow-900 mb-2">Identified Hazards Requiring Control</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {task.hazards.map((hazard) => (
                <div key={hazard.id} className="flex items-center space-x-2 text-sm">
                  <AlertTriangle className={`h-4 w-4 ${
                    hazard.riskLevel === 'critical' ? 'text-red-500' :
                    hazard.riskLevel === 'high' ? 'text-orange-500' :
                    hazard.riskLevel === 'medium' ? 'text-yellow-500' :
                    'text-green-500'
                  }`} />
                  <span className="text-yellow-800">{hazard.description}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Add Control Measure Form */}
      {showAddForm && (
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Add Control Measure</h3>
            <button
              onClick={() => setShowAddForm(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Control Measure Description *
              </label>
              <textarea
                value={newMeasure.description || ''}
                onChange={(e) => setNewMeasure({ ...newMeasure, description: e.target.value })}
                placeholder="Describe the control measure in detail..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Control Type
                </label>
                <select
                  value={newMeasure.type || 'administrative'}
                  onChange={(e) => setNewMeasure({ ...newMeasure, type: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {measureTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Effectiveness (1-5)
                </label>
                <select
                  value={newMeasure.effectiveness || 3}
                  onChange={(e) => setNewMeasure({ ...newMeasure, effectiveness: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={1}>1 - Very Low</option>
                  <option value={2}>2 - Low</option>
                  <option value={3}>3 - Medium</option>
                  <option value={4}>4 - High</option>
                  <option value={5}>5 - Very High</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Implementation Cost
                </label>
                <select
                  value={newMeasure.implementationCost || 'medium'}
                  onChange={(e) => setNewMeasure({ ...newMeasure, implementationCost: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="low">Low Cost</option>
                  <option value="medium">Medium Cost</option>
                  <option value="high">High Cost</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Training Required
                </label>
                <select
                  value={newMeasure.trainingRequired ? 'yes' : 'no'}
                  onChange={(e) => setNewMeasure({ ...newMeasure, trainingRequired: e.target.value === 'yes' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="no">No Training Required</option>
                  <option value="yes">Training Required</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Equipment Required (Optional)
              </label>
              <input
                type="text"
                value={newMeasure.equipmentRequired?.join(', ') || ''}
                onChange={(e) => setNewMeasure({
                  ...newMeasure,
                  equipmentRequired: e.target.value.split(',').map(item => item.trim()).filter(item => item)
                })}
                placeholder="Enter equipment names separated by commas"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddMeasure}
                disabled={!newMeasure.description?.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Add Control Measure
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Control Measures List */}
      <div className="space-y-4">
        {task.controlMeasures.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
            <Shield className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">No control measures defined yet</p>
            <p className="text-sm text-gray-400">
              Add control measures to mitigate the identified hazards
            </p>
          </div>
        ) : (
          task.controlMeasures.map((measure) => (
            <ControlMeasureCard
              key={measure.id}
              measure={measure}
              isEditing={editingMeasure === measure.id}
              onEdit={() => setEditingMeasure(measure.id)}
              onSave={(updatedMeasure) => handleUpdateMeasure(measure.id, updatedMeasure)}
              onCancel={() => setEditingMeasure(null)}
              onDelete={() => handleDeleteMeasure(measure.id)}
              getTypeIcon={getTypeIcon}
              getTypeColor={getTypeColor}
              getCostColor={getCostColor}
              getEffectivenessColor={getEffectivenessColor}
              measureTypes={measureTypes}
            />
          ))
        )}
      </div>
    </div>
  );
};

// Separate component for control measure cards
interface ControlMeasureCardProps {
  measure: ControlMeasure;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (measure: Partial<ControlMeasure>) => void;
  onCancel: () => void;
  onDelete: () => void;
  getTypeIcon: (type: string) => React.ReactNode;
  getTypeColor: (type: string) => string;
  getCostColor: (cost: string) => string;
  getEffectivenessColor: (effectiveness: number) => string;
  measureTypes: Array<{ value: string; label: string; description: string }>;
}

const ControlMeasureCard: React.FC<ControlMeasureCardProps> = ({
  measure,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  onDelete,
  getTypeIcon,
  getTypeColor,
  getCostColor,
  getEffectivenessColor,
  measureTypes
}) => {
  const [editData, setEditData] = useState(measure);

  const handleSave = () => {
    onSave(editData);
  };

  if (isEditing) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Control Measure Description
            </label>
            <textarea
              value={editData.description}
              onChange={(e) => setEditData({ ...editData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Control Type
              </label>
              <select
                value={editData.type}
                onChange={(e) => setEditData({ ...editData, type: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {measureTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Effectiveness (1-5)
              </label>
              <select
                value={editData.effectiveness}
                onChange={(e) => setEditData({ ...editData, effectiveness: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={1}>1 - Very Low</option>
                <option value={2}>2 - Low</option>
                <option value={3}>3 - Medium</option>
                <option value={4}>4 - High</option>
                <option value={5}>5 - Very High</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Implementation Cost
              </label>
              <select
                value={editData.implementationCost}
                onChange={(e) => setEditData({ ...editData, implementationCost: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="low">Low Cost</option>
                <option value="medium">Medium Cost</option>
                <option value="high">High Cost</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Training Required
              </label>
              <select
                value={editData.trainingRequired ? 'yes' : 'no'}
                onChange={(e) => setEditData({ ...editData, trainingRequired: e.target.value === 'yes' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="no">No Training Required</option>
                <option value="yes">Training Required</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Equipment Required
            </label>
            <input
              type="text"
              value={editData.equipmentRequired?.join(', ') || ''}
              onChange={(e) => setEditData({
                ...editData,
                equipmentRequired: e.target.value.split(',').map(item => item.trim()).filter(item => item)
              })}
              placeholder="Enter equipment names separated by commas"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Save Changes
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-3">
            {getTypeIcon(measure.type)}
            <span className={`px-2 py-1 text-xs rounded-full font-medium ${getTypeColor(measure.type)}`}>
              {measureTypes.find(t => t.value === measure.type)?.label || measure.type}
            </span>
            <span className={`px-2 py-1 text-xs rounded-full font-medium ${getCostColor(measure.implementationCost)}`}>
              {measure.implementationCost.toUpperCase()} COST
            </span>
            <span className={`text-sm font-medium ${getEffectivenessColor(measure.effectiveness)}`}>
              Effectiveness: {measure.effectiveness}/5
            </span>
          </div>

          <p className="text-gray-900 mb-3">{measure.description}</p>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Training Required:</span>
              <span className={`ml-2 font-medium ${measure.trainingRequired ? 'text-orange-600' : 'text-green-600'}`}>
                {measure.trainingRequired ? 'Yes' : 'No'}
              </span>
            </div>
            {measure.equipmentRequired && measure.equipmentRequired.length > 0 && (
              <div>
                <span className="text-gray-600">Equipment:</span>
                <span className="ml-2 font-medium">{measure.equipmentRequired.join(', ')}</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex space-x-2 ml-4">
          <button
            onClick={onEdit}
            className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
            title="Edit control measure"
          >
            <Edit className="h-4 w-4" />
          </button>
          <button
            onClick={onDelete}
            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
            title="Delete control measure"
          >
            <Trash2 className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ControlMeasureManagement;
