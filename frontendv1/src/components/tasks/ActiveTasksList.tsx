import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Play,
  Calendar,
  MapPin,
  User,
  Clock,
  CheckCircle
} from 'lucide-react';
import { Task } from '../../types/tasks';
import TaskStatusBadge from './shared/TaskStatusBadge';
import TaskPriorityBadge from './shared/TaskPriorityBadge';

interface ActiveTasksListProps {
  siteId: string;
}

const ActiveTasksList: React.FC<ActiveTasksListProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [activeTasks, setActiveTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    fetchActiveTasks();
  }, [siteId]);

  useEffect(() => {
    filterTasks();
  }, [activeTasks, searchTerm, statusFilter]);

  const filterTasks = () => {
    let filtered = activeTasks.filter(task =>
      task.status === 'approved' ||
      task.status === 'permit-approved' ||
      task.status === 'in-progress'
    );

    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.assignedSupervisorName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(task => task.status === statusFilter);
    }

    setFilteredTasks(filtered);
  };

  const fetchActiveTasks = async () => {
    // Mock data - replace with actual API call
    const mockActiveTasks: Task[] = [
      {
        id: "task-active-1",
        taskNumber: "TSK-2024-095",
        title: "Steel Frame Installation - Building B",
        description: "Install steel frame structure for Building B",
        category: "construction",
        location: "Building B - Level 2",
        siteId: siteId,
        plannedStartDate: new Date('2024-01-20T08:00:00'),
        plannedEndDate: new Date('2024-01-22T16:00:00'),
        actualStartDate: new Date('2024-01-20T08:15:00'),
        estimatedDuration: 24,
        actualDuration: 16,
        status: "in-progress",
        priority: "high",
        progressPercentage: 65,
        createdBy: "engineer-1",
        createdByName: "Site Engineer",
        assignedSupervisor: "supervisor-1",
        assignedSupervisorName: "John Smith",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: false,
        riskLevel: "medium",
        safetyRequirements: [],
        requiredPPE: [],
        requiredTrainings: [],
        requiredCertifications: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        createdAt: new Date('2024-01-18T10:00:00'),
        updatedAt: new Date('2024-01-20T14:30:00'),
        history: [],
        tags: ["steel", "structure"],
        customFields: {}
      },
      {
        id: "task-active-2",
        taskNumber: "TSK-2024-096",
        title: "HVAC Ductwork Installation",
        description: "Install HVAC ductwork for floors 1-3",
        category: "hvac",
        location: "Building A - Floors 1-3",
        siteId: siteId,
        plannedStartDate: new Date('2024-01-21T09:00:00'),
        plannedEndDate: new Date('2024-01-23T17:00:00'),
        estimatedDuration: 20,
        status: "permit-approved",
        priority: "medium",
        progressPercentage: 0,
        createdBy: "engineer-2",
        createdByName: "HVAC Engineer",
        assignedSupervisor: "supervisor-3",
        assignedSupervisorName: "Sarah Wilson",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: true,
        riskLevel: "low",
        safetyRequirements: [],
        requiredPPE: [],
        requiredTrainings: [],
        requiredCertifications: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        createdAt: new Date('2024-01-19T11:00:00'),
        updatedAt: new Date('2024-01-21T09:00:00'),
        history: [],
        tags: ["hvac", "ductwork"],
        customFields: {}
      }
    ];

    setActiveTasks(mockActiveTasks);
    setLoading(false);
  };

  const handleTaskClick = (taskId: string) => {
    navigate(`/sites/${siteId}/tasks/${taskId}`);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in-progress':
        return <Play className="h-4 w-4 text-blue-500" />;
      case 'permit-approved':
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'in-progress':
        return 'In Progress';
      case 'permit-approved':
      case 'approved':
        return 'Ready to Start';
      default:
        return status.replace('-', ' ').toUpperCase();
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading active tasks...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Active Tasks</h2>
          <p className="text-sm text-gray-600">
            Approved and in-progress tasks
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            {filteredTasks.length} active task{filteredTasks.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search tasks..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="permit-approved">Ready to Start</option>
              <option value="in-progress">In Progress</option>
            </select>
          </div>
        </div>
      </div>

      {/* Active Tasks List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        {filteredTasks.length === 0 ? (
          <div className="p-12 text-center">
            <Play className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No active tasks found</h3>
            <p className="text-gray-500">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'No tasks are currently active.'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredTasks.map((task) => (
              <div
                key={task.id}
                className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleTaskClick(task.id)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      {getStatusIcon(task.status)}
                      <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600">
                        {task.title}
                      </h3>
                      <TaskStatusBadge status={task.status} size="sm" />
                      <TaskPriorityBadge priority={task.priority} size="sm" />
                    </div>

                    <p className="text-sm text-gray-600 mb-3">{task.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{task.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">Requested by {task.createdByName}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">
                          Due: {task.plannedEndDate.toLocaleDateString()}
                        </span>
                      </div>
                    </div>


                  </div>

                  <div className="text-right ml-4">
                    <div className="text-sm font-medium text-gray-900 mb-1">
                      {getStatusText(task.status)}
                    </div>
                    <div className="text-xs text-gray-400">
                      {task.taskNumber}
                    </div>
                    {task.actualStartDate && (
                      <div className="text-xs text-gray-500 mt-1">
                        Started: {task.actualStartDate.toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ActiveTasksList;
