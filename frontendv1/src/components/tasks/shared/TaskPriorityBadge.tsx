import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ArrowD<PERSON> } from "lucide-react";
import { TaskPriority } from "../../../types/tasks";

interface TaskPriorityBadgeProps {
	priority: TaskPriority;
	size?: "sm" | "md" | "lg";
	showIcon?: boolean;
}

const TaskPriorityBadge: React.FC<TaskPriorityBadgeProps> = ({
	priority,
	size = "md",
	showIcon = true,
}) => {
	const getPriorityConfig = (priority: TaskPriority) => {
		switch (priority) {
			case "critical":
				return {
					icon: <AlertTriangle className="h-4 w-4" />,
					label: "Critical",
					classes: "bg-red-100 text-red-800 border-red-200",
				};
			case "high":
				return {
					icon: <ArrowUp className="h-4 w-4" />,
					label: "High",
					classes: "bg-orange-100 text-orange-800 border-orange-200",
				};
			case "medium":
				return {
					icon: <Minus className="h-4 w-4" />,
					label: "Medium",
					classes: "bg-yellow-100 text-yellow-800 border-yellow-200",
				};
			case "low":
				return {
					icon: <ArrowDown className="h-4 w-4" />,
					label: "Low",
					classes: "bg-green-100 text-green-800 border-green-200",
				};
			default:
				return {
					icon: <Minus className="h-4 w-4" />,
					label: priority,
					classes: "bg-gray-100 text-gray-800 border-gray-200",
				};
		}
	};

	const getSizeClasses = (size: string) => {
		switch (size) {
			case "sm":
				return "px-2 py-1 text-xs";
			case "lg":
				return "px-3 py-2 text-sm";
			default:
				return "px-2.5 py-1.5 text-xs";
		}
	};

	const config = getPriorityConfig(priority);
	const sizeClasses = getSizeClasses(size);

	return (
		<span
			className={`
      inline-flex items-center font-medium rounded-full border
      ${config.classes}
      ${sizeClasses}
    `}
		>
			{showIcon && <span className="mr-1">{config.icon}</span>}
			{config.label}
		</span>
	);
};

export default TaskPriorityBadge;
