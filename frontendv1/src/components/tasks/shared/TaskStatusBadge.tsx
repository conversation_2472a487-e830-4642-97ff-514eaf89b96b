import React from "react";
import {
	Clock,
	CheckCircle,
	XCircle,
	Shield,
	Play,
} from "lucide-react";
import { TaskStatus } from "../../../types/tasks";

interface TaskStatusBadgeProps {
	status: TaskStatus;
	size?: "sm" | "md" | "lg";
	showIcon?: boolean;
}

const TaskStatusBadge: React.FC<TaskStatusBadgeProps> = ({
	status,
	size = "md",
	showIcon = true,
}) => {
	const getStatusConfig = (status: TaskStatus) => {
		switch (status) {
			case "todo":
				return {
					icon: <Clock className="h-4 w-4" />,
					label: "To Do",
					classes: "bg-gray-100 text-gray-800 border-gray-200",
				};
			case "permit-pending":
				return {
					icon: <Shield className="h-4 w-4" />,
					label: "Permit Pending",
					classes: "bg-amber-100 text-amber-800 border-amber-200",
				};
			case "permit-approved":
				return {
					icon: <CheckCircle className="h-4 w-4" />,
					label: "Permit Approved",
					classes: "bg-green-100 text-green-800 border-green-200",
				};
			case "in-progress":
				return {
					icon: <Play className="h-4 w-4" />,
					label: "In Progress",
					classes: "bg-blue-100 text-blue-800 border-blue-200",
				};
			case "blocked":
				return {
					icon: <XCircle className="h-4 w-4" />,
					label: "Blocked",
					classes: "bg-red-100 text-red-800 border-red-200",
				};
			case "completed":
				return {
					icon: <CheckCircle className="h-4 w-4" />,
					label: "Completed",
					classes: "bg-green-100 text-green-800 border-green-200",
				};
			case "cancelled":
				return {
					icon: <XCircle className="h-4 w-4" />,
					label: "Cancelled",
					classes: "bg-red-100 text-red-800 border-red-200",
				};
			default:
				return {
					icon: <Clock className="h-4 w-4" />,
					label: status,
					classes: "bg-gray-100 text-gray-800 border-gray-200",
				};
		}
	};

	const getSizeClasses = (size: string) => {
		switch (size) {
			case "sm":
				return "px-2 py-1 text-xs";
			case "lg":
				return "px-3 py-2 text-sm";
			default:
				return "px-2.5 py-1.5 text-xs";
		}
	};

	const config = getStatusConfig(status);
	const sizeClasses = getSizeClasses(size);

	return (
		<span
			className={`
      inline-flex items-center font-medium rounded-full border
      ${config.classes}
      ${sizeClasses}
    `}
		>
			{showIcon && <span className="mr-1">{config.icon}</span>}
			{config.label}
		</span>
	);
};

export default TaskStatusBadge;
