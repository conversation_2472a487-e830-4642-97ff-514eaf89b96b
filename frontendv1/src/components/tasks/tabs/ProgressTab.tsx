import React, { useState } from 'react';
import {
  Play,
  CheckCircle,
  Clock,
  Calendar,
  TrendingUp,
  AlertCircle,
  Users,
  Camera,
  Plus
} from 'lucide-react';
import { SiteTask } from '../../../types/tasks';

interface ProgressTabProps {
  task: SiteTask;
}

interface StatusUpdate {
  id: string;
  timestamp: Date;
  description: string;
  updatedBy: string;
  photos?: string[];
  notes?: string;
}

const ProgressTab: React.FC<ProgressTabProps> = ({ task }) => {
  const [newDescription, setNewDescription] = useState('');
  const [newNotes, setNewNotes] = useState('');

  // Mock status updates - replace with actual API data
  const statusUpdates: StatusUpdate[] = [
    {
      id: '1',
      timestamp: new Date('2024-01-20T10:30:00'),
      description: 'Started excavation work',
      updatedBy: '<PERSON>',
      notes: 'Weather conditions good, all safety measures in place'
    },
    {
      id: '2',
      timestamp: new Date('2024-01-20T14:15:00'),
      description: 'Excavation work progressing well',
      updatedBy: '<PERSON>',
      notes: 'Found underground cable, adjusted digging path accordingly'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600';
      case 'in-progress':
        return 'text-blue-600';
      case 'completed':
        return 'text-gray-600';
      case 'blocked':
        return 'text-red-600';
      default:
        return 'text-yellow-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'in-progress':
        return <Play className="h-5 w-5 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-gray-500" />;
      case 'blocked':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-yellow-500" />;
    }
  };

  const handleStatusUpdate = () => {
    // Here you would call the API to add status update
    console.log('Adding status update:', {
      description: newDescription,
      notes: newNotes
    });

    // Reset form
    setNewDescription('');
    setNewNotes('');
  };

  const calculateTimeRemaining = () => {
    const now = new Date();
    const endTime = task.plannedEndDate;
    const diffMs = endTime.getTime() - now.getTime();

    if (diffMs <= 0) return 'Overdue';

    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m remaining`;
    }
    return `${diffMinutes}m remaining`;
  };

  const calculateEstimatedCompletion = () => {
    if (task.status === 'opened' || task.status === 'requested') return 'Not started';
    if (task.status === 'completed') return 'Completed';

    // For in-progress tasks, estimate based on planned end date
    const now = new Date();
    const plannedEnd = task.plannedEndDate;

    if (now >= plannedEnd) return 'Should be completed';

    return plannedEnd.toLocaleDateString() + ' ' + plannedEnd.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="p-6">
      {/* Progress Overview */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Task Progress</h2>
          <div className="flex items-center space-x-2">
            {getStatusIcon(task.status)}
            <span className={`font-medium ${getStatusColor(task.status)}`}>
              {task.status.replace('-', ' ').toUpperCase()}
            </span>
          </div>
        </div>

        {/* Task Status */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Current Status</span>
            <span className="text-sm font-medium text-gray-900 capitalize">
              {task.status.replace('-', ' ')}
            </span>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">Time Remaining</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {calculateTimeRemaining()}
            </p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium text-gray-700">Estimated Completion</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {calculateEstimatedCompletion()}
            </p>
          </div>

          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="h-5 w-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-700">Workers Assigned</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {task.assignedWorkers.length} workers
            </p>
          </div>
        </div>
      </div>

      {/* Status Update Form */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Status Update</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status Description
            </label>
            <input
              type="text"
              value={newDescription}
              onChange={(e) => setNewDescription(e.target.value)}
              placeholder="Brief description of current status..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes (Optional)
            </label>
            <textarea
              value={newNotes}
              onChange={(e) => setNewNotes(e.target.value)}
              placeholder="Additional notes, observations, or issues..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="flex items-center space-x-4">
            <button
              onClick={handleStatusUpdate}
              disabled={!newDescription.trim()}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Add Update</span>
            </button>

            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
              <Camera className="h-4 w-4" />
              <span>Add Photos</span>
            </button>
          </div>
        </div>
      </div>

      {/* Status History */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Status History</h3>

        <div className="space-y-4">
          {statusUpdates.map((update, index) => (
            <div key={update.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <Clock className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      {update.description}
                    </h4>
                    <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                      <span className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{update.timestamp.toLocaleDateString()}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{update.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Users className="h-3 w-3" />
                        <span>{update.updatedBy}</span>
                      </span>
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-sm text-gray-500">
                    {update.timestamp.toLocaleDateString()}
                  </div>
                  {index === 0 && (
                    <span className="text-xs text-blue-600 font-medium">Latest</span>
                  )}
                </div>
              </div>

              {update.notes && (
                <div className="mt-3 p-3 bg-white rounded border border-gray-200">
                  <p className="text-sm text-gray-700">{update.notes}</p>
                </div>
              )}

              {update.photos && update.photos.length > 0 && (
                <div className="mt-3">
                  <div className="flex space-x-2">
                    {update.photos.map((photo, photoIndex) => (
                      <img
                        key={photoIndex}
                        src={photo}
                        alt={`Progress photo ${photoIndex + 1}`}
                        className="w-16 h-16 object-cover rounded border border-gray-200"
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProgressTab;
