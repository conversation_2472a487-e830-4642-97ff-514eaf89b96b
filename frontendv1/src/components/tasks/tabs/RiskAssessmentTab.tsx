import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  Shield,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Hazard, ControlMeasure } from '../../../types/tasks';

interface RiskAssessmentTabProps {
  hazards: Hazard[];
  controlMeasures: ControlMeasure[];
}

const RiskAssessmentTab: React.FC<RiskAssessmentTabProps> = ({ hazards, controlMeasures }) => {
  const [activeView, setActiveView] = useState<'matrix' | 'hazards' | 'controls'>('matrix');

  const getRiskColor = (riskScore: number) => {
    if (riskScore >= 15) return 'bg-red-500';
    if (riskScore >= 10) return 'bg-orange-500';
    if (riskScore >= 5) return 'bg-yellow-500';
    return 'bg-green-500';
  };



  const getControlTypeColor = (type: string) => {
    switch (type) {
      case 'elimination':
        return 'bg-green-100 text-green-800';
      case 'substitution':
        return 'bg-blue-100 text-blue-800';
      case 'engineering':
        return 'bg-purple-100 text-purple-800';
      case 'administrative':
        return 'bg-yellow-100 text-yellow-800';
      case 'ppe':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getEffectivenessStars = (effectiveness: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span
        key={i}
        className={`text-lg ${i < effectiveness ? 'text-yellow-400' : 'text-gray-300'}`}
      >
        ★
      </span>
    ));
  };

  const renderRiskMatrix = () => {
    const matrix = Array.from({ length: 5 }, () => Array.from({ length: 5 }, () => [] as Hazard[]));

    hazards.forEach(hazard => {
      const row = 5 - hazard.severity; // Invert for display (5 = top row)
      const col = hazard.likelihood - 1;
      if (row >= 0 && row < 5 && col >= 0 && col < 5) {
        matrix[row][col].push(hazard);
      }
    });

    return (
      <div className="p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Risk Assessment Matrix</h3>
          <p className="text-sm text-gray-600">
            Visual representation of identified hazards plotted by likelihood and severity
          </p>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead>
              <tr>
                <th className="w-20 h-12 bg-gray-50 border-b border-r border-gray-200"></th>
                <th className="h-12 bg-gray-50 border-b border-r border-gray-200 text-xs font-medium text-gray-700">
                  Very Unlikely<br/>(1)
                </th>
                <th className="h-12 bg-gray-50 border-b border-r border-gray-200 text-xs font-medium text-gray-700">
                  Unlikely<br/>(2)
                </th>
                <th className="h-12 bg-gray-50 border-b border-r border-gray-200 text-xs font-medium text-gray-700">
                  Possible<br/>(3)
                </th>
                <th className="h-12 bg-gray-50 border-b border-r border-gray-200 text-xs font-medium text-gray-700">
                  Likely<br/>(4)
                </th>
                <th className="h-12 bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700">
                  Very Likely<br/>(5)
                </th>
              </tr>
            </thead>
            <tbody>
              {['Catastrophic (5)', 'Major (4)', 'Moderate (3)', 'Minor (2)', 'Negligible (1)'].map((severity, rowIndex) => (
                <tr key={rowIndex}>
                  <td className="w-20 h-16 bg-gray-50 border-r border-b border-gray-200 text-xs font-medium text-gray-700 text-center">
                    {severity}
                  </td>
                  {matrix[rowIndex].map((cell, colIndex) => {
                    const riskScore = (5 - rowIndex) * (colIndex + 1);
                    return (
                      <td
                        key={colIndex}
                        className={`h-16 border-r border-b border-gray-200 p-1 ${getRiskColor(riskScore)} bg-opacity-20`}
                      >
                        <div className="h-full flex flex-col justify-center items-center">
                          <div className="text-xs font-medium text-gray-700 mb-1">
                            {riskScore}
                          </div>
                          {cell.map((hazard, hazardIndex) => (
                            <div
                              key={hazardIndex}
                              className="w-2 h-2 bg-red-600 rounded-full mb-0.5"
                              title={hazard.description}
                            />
                          ))}
                        </div>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-4 flex items-center space-x-6 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-green-500 bg-opacity-20 border border-green-500 rounded"></div>
            <span className="text-gray-600">Low Risk (1-4)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-yellow-500 bg-opacity-20 border border-yellow-500 rounded"></div>
            <span className="text-gray-600">Medium Risk (5-9)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-orange-500 bg-opacity-20 border border-orange-500 rounded"></div>
            <span className="text-gray-600">High Risk (10-14)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 bg-opacity-20 border border-red-500 rounded"></div>
            <span className="text-gray-600">Critical Risk (15-25)</span>
          </div>
        </div>
      </div>
    );
  };

  const renderHazardsList = () => (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Identified Hazards</h3>
          <p className="text-sm text-gray-600">
            {hazards.length} hazards identified for this task
          </p>
        </div>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          <Plus className="h-4 w-4" />
          <span>Add Hazard</span>
        </button>
      </div>

      <div className="space-y-4">
        {hazards.map((hazard) => (
          <div key={hazard.id} className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <AlertTriangle className={`h-5 w-5 ${
                    hazard.riskLevel === 'critical' ? 'text-red-500' :
                    hazard.riskLevel === 'high' ? 'text-orange-500' :
                    hazard.riskLevel === 'medium' ? 'text-yellow-500' :
                    'text-green-500'
                  }`} />
                  <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                    hazard.riskLevel === 'critical' ? 'bg-red-100 text-red-800' :
                    hazard.riskLevel === 'high' ? 'bg-orange-100 text-orange-800' :
                    hazard.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {hazard.riskLevel.toUpperCase()}
                  </span>
                  <span className="text-sm text-gray-500">
                    Risk Score: {hazard.riskScore}
                  </span>
                </div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  {hazard.description}
                </h4>
              </div>
              <div className="flex space-x-2">
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded">
                  <Edit className="h-4 w-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Likelihood
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    {Array.from({ length: 5 }, (_, i) => (
                      <div
                        key={i}
                        className={`w-3 h-3 rounded-full ${
                          i < hazard.likelihood ? 'bg-blue-500' : 'bg-gray-200'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">({hazard.likelihood}/5)</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Severity
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    {Array.from({ length: 5 }, (_, i) => (
                      <div
                        key={i}
                        className={`w-3 h-3 rounded-full ${
                          i < hazard.severity ? 'bg-red-500' : 'bg-gray-200'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">({hazard.severity}/5)</span>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Associated Control Measures
              </label>
              <div className="flex flex-wrap gap-2">
                {hazard.controlMeasures.map((controlId) => {
                  const control = controlMeasures.find(c => c.id === controlId);
                  return control ? (
                    <span
                      key={controlId}
                      className={`px-2 py-1 text-xs rounded-full ${getControlTypeColor(control.type)}`}
                    >
                      {control.description}
                    </span>
                  ) : null;
                })}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderControlsList = () => (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Control Measures</h3>
          <p className="text-sm text-gray-600">
            {controlMeasures.length} control measures defined for this task
          </p>
        </div>
        <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
          <Plus className="h-4 w-4" />
          <span>Add Control</span>
        </button>
      </div>

      <div className="space-y-4">
        {controlMeasures.map((control) => (
          <div key={control.id} className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span className={`px-2 py-1 text-xs rounded-full font-medium ${getControlTypeColor(control.type)}`}>
                    {control.type.replace('-', ' ').toUpperCase()}
                  </span>
                  <span className="text-sm text-gray-500">
                    Cost: {control.implementationCost}
                  </span>
                </div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  {control.description}
                </h4>
              </div>
              <div className="flex space-x-2">
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded">
                  <Edit className="h-4 w-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Effectiveness
                </label>
                <div className="flex items-center space-x-2">
                  <div className="flex">
                    {getEffectivenessStars(control.effectiveness)}
                  </div>
                  <span className="text-sm text-gray-600">({control.effectiveness}/5)</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Training Required
                </label>
                <div className="flex items-center space-x-2">
                  {control.trainingRequired ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-gray-400" />
                  )}
                  <span className="text-sm text-gray-600">
                    {control.trainingRequired ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Equipment Required
                </label>
                <span className="text-sm text-gray-600">
                  {control.equipmentRequired.length > 0
                    ? `${control.equipmentRequired.length} items`
                    : 'None'
                  }
                </span>
              </div>
            </div>

            {control.equipmentRequired.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Required Equipment
                </label>
                <div className="flex flex-wrap gap-2">
                  {control.equipmentRequired.map((equipment, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"
                    >
                      {equipment}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 bg-gray-50">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'matrix', label: 'Risk Matrix', icon: <AlertTriangle className="h-4 w-4" /> },
            { id: 'hazards', label: 'Hazards', icon: <AlertTriangle className="h-4 w-4" /> },
            { id: 'controls', label: 'Controls', icon: <Shield className="h-4 w-4" /> }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as any)}
              className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm ${
                activeView === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-auto">
        {activeView === 'matrix' && renderRiskMatrix()}
        {activeView === 'hazards' && renderHazardsList()}
        {activeView === 'controls' && renderControlsList()}
      </div>
    </div>
  );
};

export default RiskAssessmentTab;
