import React from 'react';
import { Calendar, Clock, Play, Pause, CheckCircle, AlertTriangle } from 'lucide-react';
import { SiteTask } from '../../../types/tasks';

interface ScheduleTabProps {
  task: SiteTask;
}

const ScheduleTab: React.FC<ScheduleTabProps> = ({ task }) => {
  const formatDateTime = (date: Date) => {
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-100';
      case 'in-progress':
        return 'text-blue-600 bg-blue-100';
      case 'completed':
        return 'text-gray-600 bg-gray-100';
      case 'permit-pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'in-progress':
        return <Play className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'permit-pending':
        return <Pause className="h-4 w-4" />;
      case 'rejected':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const plannedStart = formatDateTime(task.plannedStartDate);
  const plannedEnd = formatDateTime(task.plannedEndDate);

  const isOverdue = new Date() > task.plannedStartDate && task.status === 'permit-pending';
  const daysUntilStart = Math.ceil((task.plannedStartDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Task Schedule</h2>
        <p className="text-sm text-gray-600">
          Planned timing and duration for this task
        </p>
      </div>

      {/* Schedule Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Planned Start */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <Play className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Planned Start</h3>
              <p className="text-sm text-gray-600">When work is scheduled to begin</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">{plannedStart.date}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">{plannedStart.time}</span>
            </div>
          </div>

          {isOverdue && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-800 font-medium">Overdue</span>
              </div>
            </div>
          )}

          {!isOverdue && daysUntilStart > 0 && (
            <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
              <div className="text-sm text-blue-800">
                Starts in {daysUntilStart} day{daysUntilStart !== 1 ? 's' : ''}
              </div>
            </div>
          )}
        </div>

        {/* Planned End */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-red-100 rounded-lg">
              <CheckCircle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Planned End</h3>
              <p className="text-sm text-gray-600">When work is scheduled to complete</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">{plannedEnd.date}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">{plannedEnd.time}</span>
            </div>
          </div>

          <div className="mt-3 p-2 bg-gray-50 border border-gray-200 rounded">
            <div className="text-sm text-gray-700">
              Duration: {task.estimatedDuration} hours
            </div>
          </div>
        </div>
      </div>

      {/* Current Status */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Current Status</h3>
        
        <div className="flex items-center space-x-3 mb-4">
          <div className={`p-2 rounded-lg ${getStatusColor(task.status)}`}>
            {getStatusIcon(task.status)}
          </div>
          <div>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(task.status)}`}>
              {task.status.replace('-', ' ').toUpperCase()}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-sm font-medium text-gray-700">Progress</div>
            <div className="text-2xl font-bold text-gray-900">{task.progressPercentage}%</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700">Priority</div>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              task.priority === 'critical' ? 'bg-red-100 text-red-800' :
              task.priority === 'high' ? 'bg-orange-100 text-orange-800' :
              task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {task.priority.toUpperCase()}
            </span>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700">Risk Level</div>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              task.riskLevel === 'critical' ? 'bg-red-100 text-red-800' :
              task.riskLevel === 'high' ? 'bg-orange-100 text-orange-800' :
              task.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {task.riskLevel.toUpperCase()}
            </span>
          </div>
        </div>
      </div>

      {/* Timeline */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Task Timeline</h3>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-3 h-3 bg-blue-500 rounded-full mt-1.5"></div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">Task Created</div>
              <div className="text-sm text-gray-600">
                {task.createdAt.toLocaleDateString()} at {task.createdAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div className="text-xs text-gray-500">by {task.createdByName}</div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className={`flex-shrink-0 w-3 h-3 rounded-full mt-1.5 ${
              task.status === 'permit-pending' ? 'bg-yellow-500' : 'bg-green-500'
            }`}></div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">
                {task.status === 'permit-pending' ? 'Pending Approval' : 'Approved'}
              </div>
              <div className="text-sm text-gray-600">
                {task.updatedAt.toLocaleDateString()} at {task.updatedAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div className="text-xs text-gray-500">
                {task.status === 'permit-pending' ? 'Awaiting HSE approval' : 'Ready to start'}
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className={`flex-shrink-0 w-3 h-3 rounded-full mt-1.5 ${
              task.status === 'approved' || task.status === 'in-progress' || task.status === 'completed' 
                ? 'bg-green-500' : 'bg-gray-300'
            }`}></div>
            <div className="flex-1">
              <div className={`text-sm font-medium ${
                task.status === 'approved' || task.status === 'in-progress' || task.status === 'completed'
                  ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Planned Start
              </div>
              <div className="text-sm text-gray-600">
                {plannedStart.date} at {plannedStart.time}
              </div>
              <div className="text-xs text-gray-500">
                {task.status === 'approved' || task.status === 'in-progress' || task.status === 'completed'
                  ? 'Work can begin' : 'Pending approval'}
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className={`flex-shrink-0 w-3 h-3 rounded-full mt-1.5 ${
              task.status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
            }`}></div>
            <div className="flex-1">
              <div className={`text-sm font-medium ${
                task.status === 'completed' ? 'text-gray-900' : 'text-gray-500'
              }`}>
                Planned End
              </div>
              <div className="text-sm text-gray-600">
                {plannedEnd.date} at {plannedEnd.time}
              </div>
              <div className="text-xs text-gray-500">
                {task.status === 'completed' ? 'Task completed' : 'Estimated completion'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleTab;
