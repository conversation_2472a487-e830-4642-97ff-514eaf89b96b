import React from 'react';
import { AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react';
import { Hazard } from '../../../types/tasks';

interface HazardsTabProps {
  hazards: Hazard[];
}

const HazardsTab: React.FC<HazardsTabProps> = ({ hazards }) => {
  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-100 border-green-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <TrendingUp className="h-4 w-4" />;
      case 'medium':
        return <AlertTriangle className="h-4 w-4" />;
      case 'low':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getLikelihoodText = (likelihood: number) => {
    switch (likelihood) {
      case 1: return 'Very Unlikely';
      case 2: return 'Unlikely';
      case 3: return 'Possible';
      case 4: return 'Likely';
      case 5: return 'Very Likely';
      default: return 'Unknown';
    }
  };

  const getSeverityText = (severity: number) => {
    switch (severity) {
      case 1: return 'Negligible';
      case 2: return 'Minor';
      case 3: return 'Moderate';
      case 4: return 'Major';
      case 5: return 'Catastrophic';
      default: return 'Unknown';
    }
  };

  if (hazards.length === 0) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <AlertTriangle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Hazards Identified</h3>
          <p className="text-gray-500">
            No hazards have been identified for this task yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Identified Hazards</h2>
        <p className="text-sm text-gray-600">
          {hazards.length} hazard{hazards.length !== 1 ? 's' : ''} identified for this task
        </p>
      </div>

      <div className="space-y-4">
        {hazards.map((hazard, index) => (
          <div key={hazard.id} className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg border ${getRiskLevelColor(hazard.riskLevel)}`}>
                  {getRiskIcon(hazard.riskLevel)}
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    Hazard #{index + 1}
                  </h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskLevelColor(hazard.riskLevel)}`}>
                    {hazard.riskLevel.toUpperCase()} RISK
                  </span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{hazard.riskScore}</div>
                <div className="text-xs text-gray-500">Risk Score</div>
              </div>
            </div>

            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Description</h4>
              <p className="text-gray-900">{hazard.description}</p>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Likelihood</h4>
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((level) => (
                      <div
                        key={level}
                        className={`w-3 h-3 rounded-full ${
                          level <= hazard.likelihood ? 'bg-orange-500' : 'bg-gray-200'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {hazard.likelihood}/5 - {getLikelihoodText(hazard.likelihood)}
                  </span>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Severity</h4>
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((level) => (
                      <div
                        key={level}
                        className={`w-3 h-3 rounded-full ${
                          level <= hazard.severity ? 'bg-red-500' : 'bg-gray-200'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {hazard.severity}/5 - {getSeverityText(hazard.severity)}
                  </span>
                </div>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Risk Calculation:</span>
                <span className="font-medium text-gray-900">
                  {hazard.likelihood} × {hazard.severity} = {hazard.riskScore}
                </span>
              </div>
            </div>

            {hazard.controlMeasures && hazard.controlMeasures.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Linked Control Measures</h4>
                <div className="flex flex-wrap gap-2">
                  {hazard.controlMeasures.map((measureId) => (
                    <span
                      key={measureId}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      Control #{measureId}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary */}
      <div className="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Risk Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {['critical', 'high', 'medium', 'low'].map((level) => {
            const count = hazards.filter(h => h.riskLevel === level).length;
            return (
              <div key={level} className="text-center">
                <div className={`text-2xl font-bold ${
                  level === 'critical' ? 'text-red-600' :
                  level === 'high' ? 'text-orange-600' :
                  level === 'medium' ? 'text-yellow-600' :
                  'text-green-600'
                }`}>
                  {count}
                </div>
                <div className="text-xs text-gray-600 capitalize">{level} Risk</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default HazardsTab;
