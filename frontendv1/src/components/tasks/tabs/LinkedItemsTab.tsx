import React, { useState } from 'react';
import {
  ExternalLink,
  Plus,
  Trash2,
  CheckCircle,
  Clock,
  Users,
  Wrench,
  FileText,
  Calendar,
  MapPin
} from 'lucide-react';
import { SiteTask } from '../../../types/tasks';

interface LinkedItemsTabProps {
  task: SiteTask;
}

interface LinkedPermit {
  id: string;
  permitNumber: string;
  title: string;
  type: string;
  status: 'active' | 'pending' | 'expired' | 'closed';
  expiryDate: Date;
  location: string;
}

interface LinkedWorker {
  id: string;
  name: string;
  role: string;
  trade: string;
  isOnSite: boolean;
  assignedHours: number;
}

interface LinkedEquipment {
  id: string;
  name: string;
  type: string;
  status: 'available' | 'in-use' | 'maintenance';
  certificateExpiry: Date;
  operatorRequired: boolean;
}

const LinkedItemsTab: React.FC<LinkedItemsTabProps> = ({ task: _task }) => {
  const [activeSection, setActiveSection] = useState<'permits' | 'workers' | 'equipment'>('permits');

  // Mock data - replace with actual API calls
  const linkedPermits: LinkedPermit[] = [
    {
      id: 'SP-2024-045',
      permitNumber: 'SP-2024-045',
      title: 'Hot Work Permit - Excavation Area',
      type: 'Hot Work',
      status: 'active',
      expiryDate: new Date('2024-01-20T18:00:00'),
      location: 'Zone A - North Wall'
    }
  ];

  const linkedWorkers: LinkedWorker[] = [
    {
      id: 'worker-1',
      name: 'John Smith',
      role: 'Excavator Operator',
      trade: 'Heavy Equipment',
      isOnSite: true,
      assignedHours: 8
    },
    {
      id: 'worker-2',
      name: 'Mike Johnson',
      role: 'Safety Observer',
      trade: 'Safety',
      isOnSite: true,
      assignedHours: 8
    }
  ];

  const linkedEquipment: LinkedEquipment[] = [
    {
      id: 'eq-1',
      name: 'Excavator XC-200',
      type: 'Heavy Equipment',
      status: 'available',
      certificateExpiry: new Date('2024-06-15'),
      operatorRequired: true
    },
    {
      id: 'eq-2',
      name: 'Shoring Equipment Set A',
      type: 'Safety Equipment',
      status: 'available',
      certificateExpiry: new Date('2024-03-20'),
      operatorRequired: false
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'pending':
      case 'in-use':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
      case 'maintenance':
        return 'bg-red-100 text-red-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };



  const renderPermitsSection = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Linked Permits</h3>
        <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          <Plus className="h-4 w-4" />
          <span>Link Permit</span>
        </button>
      </div>

      {linkedPermits.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">No permits linked to this task</p>
        </div>
      ) : (
        <div className="space-y-3">
          {linkedPermits.map((permit) => (
            <div key={permit.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <FileText className="h-5 w-5 text-blue-500" />
                    <h4 className="text-lg font-medium text-gray-900">
                      {permit.title}
                    </h4>
                    <span className={`px-2 py-1 text-xs rounded-full font-medium ${getStatusColor(permit.status)}`}>
                      {permit.status.toUpperCase()}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Permit Number:</span>
                      <span className="ml-2 font-medium text-gray-900">{permit.permitNumber}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Type:</span>
                      <span className="ml-2 font-medium text-gray-900">{permit.type}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600">Expires:</span>
                      <span className="ml-1 font-medium text-gray-900">
                        {permit.expiryDate.toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-1 mt-2 text-sm">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">{permit.location}</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded">
                    <ExternalLink className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderWorkersSection = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Assigned Workers</h3>
        <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
          <Plus className="h-4 w-4" />
          <span>Assign Worker</span>
        </button>
      </div>

      {linkedWorkers.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">No workers assigned to this task</p>
        </div>
      ) : (
        <div className="space-y-3">
          {linkedWorkers.map((worker) => (
            <div key={worker.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    <Users className="h-6 w-6 text-gray-500" />
                  </div>
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">{worker.name}</h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{worker.role}</span>
                      <span>•</span>
                      <span>{worker.trade}</span>
                      <span>•</span>
                      <span>{worker.assignedHours}h assigned</span>
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      {worker.isOnSite ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm text-green-600 font-medium">On Site</span>
                        </>
                      ) : (
                        <>
                          <Clock className="h-4 w-4 text-gray-500" />
                          <span className="text-sm text-gray-600">Off Site</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded">
                    <ExternalLink className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderEquipmentSection = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Assigned Equipment</h3>
        <button className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
          <Plus className="h-4 w-4" />
          <span>Assign Equipment</span>
        </button>
      </div>

      {linkedEquipment.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <Wrench className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">No equipment assigned to this task</p>
        </div>
      ) : (
        <div className="space-y-3">
          {linkedEquipment.map((equipment) => (
            <div key={equipment.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    <Wrench className="h-6 w-6 text-gray-500" />
                  </div>
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">{equipment.name}</h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{equipment.type}</span>
                      <span>•</span>
                      <span className={`px-2 py-0.5 text-xs rounded-full font-medium ${getStatusColor(equipment.status)}`}>
                        {equipment.status.replace('-', ' ').toUpperCase()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 mt-1 text-sm">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-gray-600">
                          Cert expires: {equipment.certificateExpiry.toLocaleDateString()}
                        </span>
                      </div>
                      {equipment.operatorRequired && (
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4 text-orange-500" />
                          <span className="text-orange-600 font-medium">Operator Required</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded">
                    <ExternalLink className="h-4 w-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded">
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="p-6">
      {/* Section Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          {[
            { id: 'permits', label: 'Permits', icon: <FileText className="h-4 w-4" />, count: linkedPermits.length },
            { id: 'workers', label: 'Workers', icon: <Users className="h-4 w-4" />, count: linkedWorkers.length },
            { id: 'equipment', label: 'Equipment', icon: <Wrench className="h-4 w-4" />, count: linkedEquipment.length }
          ].map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id as any)}
              className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm ${
                activeSection === section.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {section.icon}
              <span>{section.label}</span>
              <span className="bg-gray-100 text-gray-600 px-2 py-0.5 text-xs rounded-full">
                {section.count}
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* Section Content */}
      {activeSection === 'permits' && renderPermitsSection()}
      {activeSection === 'workers' && renderWorkersSection()}
      {activeSection === 'equipment' && renderEquipmentSection()}
    </div>
  );
};

export default LinkedItemsTab;
