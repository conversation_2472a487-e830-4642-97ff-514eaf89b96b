import React, { useState, useRef } from 'react';
import { X, Upload, CheckCircle, AlertTriangle, Camera } from 'lucide-react';
import { FaceRegistrationResult } from '../../types/time';

interface FaceRegistrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  workerId?: string;
  workerName?: string;
  onRegistrationComplete: (result: FaceRegistrationResult) => void;
}

const FaceRegistrationModal: React.FC<FaceRegistrationModalProps> = ({
  isOpen,
  onClose,
  workerId,
  workerName,
  onRegistrationComplete
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isRegistering, setIsRegistering] = useState(false);
  const [result, setResult] = useState<FaceRegistrationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size must be less than 5MB');
        return;
      }

      setSelectedFile(file);
      setError(null);

      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      const fakeEvent = {
        target: { files: [file] }
      } as unknown as React.ChangeEvent<HTMLInputElement>;
      handleFileSelect(fakeEvent);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleRegister = async () => {
    if (!selectedFile || !workerId) return;

    setIsRegistering(true);
    setError(null);

    try {
      // Simulate API call to register face
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock registration result
      const registrationResult: FaceRegistrationResult = {
        success: true,
        hikvisionPersonId: `hik_person_${workerId}_${Date.now()}`,
        message: 'Face registered successfully with Hikvision system'
      };

      setResult(registrationResult);
      onRegistrationComplete(registrationResult);
    } catch (err) {
      setError('Failed to register face. Please try again.');
    } finally {
      setIsRegistering(false);
    }
  };

  const handleClose = () => {
    if (!isRegistering) {
      setSelectedFile(null);
      setPreviewUrl(null);
      setResult(null);
      setError(null);
      onClose();
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setResult(null);
    setError(null);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Register Face</h3>
            <p className="text-sm text-gray-500 mt-1">
              {workerName ? `Register face for ${workerName}` : 'Register worker face with Hikvision'}
            </p>
          </div>
          <button
            onClick={handleClose}
            disabled={isRegistering}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {!result && (
            <>
              {/* File Upload Area */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />

                {previewUrl ? (
                  <div className="space-y-4">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-32 h-32 object-cover rounded-lg mx-auto border-2 border-gray-200"
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{selectedFile?.name}</p>
                      <p className="text-xs text-gray-500">
                        {selectedFile && (selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        resetForm();
                      }}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      Choose different photo
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Camera className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Upload worker photo</p>
                      <p className="text-xs text-gray-500 mt-1">
                        Drag and drop or click to select
                      </p>
                    </div>
                    <div className="flex items-center justify-center">
                      <Upload className="h-5 w-5 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-600">JPG, PNG up to 5MB</span>
                    </div>
                  </div>
                )}
              </div>

              {error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                    <span className="text-sm text-red-600">{error}</span>
                  </div>
                </div>
              )}

              {/* Registration Guidelines */}
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-sm font-medium text-blue-800 mb-2">Photo Guidelines</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• Clear, front-facing photo of the worker</li>
                  <li>• Good lighting with no shadows on face</li>
                  <li>• No sunglasses, hats, or face coverings</li>
                  <li>• High resolution for better recognition accuracy</li>
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={handleClose}
                  disabled={isRegistering}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleRegister}
                  disabled={!selectedFile || isRegistering}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {isRegistering ? 'Registering...' : 'Register Face'}
                </button>
              </div>
            </>
          )}

          {result && (
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Registration Successful</h4>
              <p className="text-gray-500 mb-4">{result.message}</p>
              
              {result.hikvisionPersonId && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-6">
                  <p className="text-sm text-green-800">
                    <strong>Person ID:</strong> {result.hikvisionPersonId}
                  </p>
                </div>
              )}

              <button
                onClick={handleClose}
                className="px-6 py-2 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors"
              >
                Done
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FaceRegistrationModal;
