import React from 'react';
import { <PERSON>, Clock, CheckCircle, XCircle, <PERSON>ert<PERSON>riangle, User, Shield, Eye } from 'lucide-react';
import { TimeLog } from '../../types';
import { formatHours, formatDuration } from '../../utils/timeUtils';

interface TimeLogTableProps {
	timeLogs: TimeLog[];
	onEdit: (timeLog: TimeLog) => void;
	loading?: boolean;
}

const TimeLogTable: React.FC<TimeLogTableProps> = ({
	timeLogs,
	onEdit,
	loading = false,
}) => {
	const getStatusBadge = (status: TimeLog["status"]) => {
		const statusConfig = {
			"on-site": {
				icon: CheckCircle,
				className: "bg-green-100 text-green-800",
				label: "On-site",
			},
			late: {
				icon: AlertTriangle,
				className: "bg-yellow-100 text-yellow-800",
				label: "Late",
			},
			absent: {
				icon: XCircle,
				className: "bg-red-100 text-red-800",
				label: "Absent",
			},
			"off-site": {
				icon: Clock,
				className: "bg-gray-100 text-gray-800",
				label: "Off-site",
			},
		};

		const config = statusConfig[status];
		const Icon = config.icon;

		return (
			<span
				className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}
			>
				<Icon className="h-3 w-3 mr-1" />
				{config.label}
			</span>
		);
	};

  const getToolboxTalkBadge = (attended: boolean) => {
    return attended ? (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <CheckCircle className="h-3 w-3 mr-1" />
        Yes
      </span>
    ) : (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
        <XCircle className="h-3 w-3 mr-1" />
        No
      </span>
    );
  };

  const getVerificationBadge = (isVerified?: boolean) => {
    if (isVerified) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <Shield className="h-3 w-3 mr-1" />
          Face Recognition
        </span>
      );
    }

    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        <Eye className="h-3 w-3 mr-1" />
        Manual Entry
      </span>
    );
  };

	if (loading) {
		return (
			<div className="bg-white rounded-lg border border-gray-200 p-8">
				<div className="animate-pulse">
					<div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-4 bg-gray-200 rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	if (timeLogs.length === 0) {
		return (
			<div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
				<Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
				<h3 className="text-lg font-medium text-gray-900 mb-2">
					No Time Logs Found
				</h3>
				<p className="text-gray-500">
					No time logs match your current filters. Try adjusting your search
					criteria.
				</p>
			</div>
		);
	}

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Worker
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Trade
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Clock In
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Clock Out
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Break
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total Hours
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Overtime
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Toolbox Talk
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Verification
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {timeLogs.map((timeLog) => (
              <tr key={timeLog.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      {timeLog.workerPhoto ? (
                        <img
                          className="h-10 w-10 rounded-full object-cover"
                          src={timeLog.workerPhoto}
                          alt={timeLog.workerName}
                        />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <User className="h-5 w-5 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {timeLog.workerName}
                        {timeLog.isManuallyEdited && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            Edited
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">ID: {timeLog.workerId}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timeLog.workerTrade}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timeLog.clockIn || '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timeLog.clockOut || '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timeLog.breakDuration ? formatDuration(timeLog.breakDuration) : '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timeLog.totalHours ? formatHours(timeLog.totalHours) : '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {timeLog.overtime ? formatHours(timeLog.overtime) : '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(timeLog.status)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getToolboxTalkBadge(timeLog.toolboxTalkAttended)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getVerificationBadge(timeLog.isVerifiedByHikvision)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => onEdit(timeLog)}
                    className="text-green-600 hover:text-green-900 transition-colors"
                    title="Edit time log"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TimeLogTable;
