import React, { useState } from 'react';
import { <PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>cle, <PERSON><PERSON><PERSON><PERSON>gle, Shield } from 'lucide-react';
import { HikvisionSyncResult } from '../../types/time';

interface SyncAttendanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  siteId: string;
  selectedDate: string;
  onSyncComplete: (result: HikvisionSyncResult) => void;
}

const SyncAttendanceModal: React.FC<SyncAttendanceModalProps> = ({
  isOpen,
  onClose,
  siteId:_siteId,
  selectedDate,
  onSyncComplete
}) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncResult, setSyncResult] = useState<HikvisionSyncResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSync = async () => {
    setIsSyncing(true);
    setError(null);
    setSyncResult(null);

    try {
      // Simulate API call to sync with Hikvision
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock sync result - in real app this would come from the GraphQL mutation
      const result: HikvisionSyncResult = {
        success: true,
        syncedCount: 5,
        message: 'Successfully synced attendance data from Hikvision terminals',
        attendanceRecords: [
          {
            workerId: 'W001',
            checkInTime: '07:55:00',
            checkOutTime: '17:05:00',
            isVerified: true,
            terminalId: 'term1'
          },
          {
            workerId: 'W002',
            checkInTime: '08:15:00',
            isVerified: true,
            terminalId: 'term2'
          },
          {
            workerId: 'W003',
            checkInTime: '07:45:00',
            isVerified: true,
            terminalId: 'term1'
          },
          {
            workerId: 'W005',
            checkInTime: '08:00:00',
            checkOutTime: '17:00:00',
            isVerified: true,
            terminalId: 'term2'
          },
          {
            workerId: 'W006',
            checkInTime: '07:50:00',
            isVerified: true,
            terminalId: 'term1'
          }
        ]
      };

      setSyncResult(result);
      onSyncComplete(result);
    } catch (err) {
      setError('Failed to sync attendance data. Please check terminal connectivity and try again.');
    } finally {
      setIsSyncing(false);
    }
  };

  const handleClose = () => {
    if (!isSyncing) {
      setSyncResult(null);
      setError(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Sync Hikvision Attendance</h3>
            <p className="text-sm text-gray-500 mt-1">
              Sync attendance data from face recognition terminals for {selectedDate}
            </p>
          </div>
          <button
            onClick={handleClose}
            disabled={isSyncing}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {!syncResult && !error && !isSyncing && (
            <div className="text-center">
              <Shield className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Ready to Sync</h4>
              <p className="text-gray-500 mb-6">
                This will fetch the latest attendance data from all connected Hikvision terminals
                for the selected date. Any new check-ins or check-outs will be added to the system.
              </p>
              <button
                onClick={handleSync}
                className="flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors mx-auto"
              >
                <RefreshCw className="h-5 w-5 mr-2" />
                Start Sync
              </button>
            </div>
          )}

          {isSyncing && (
            <div className="text-center">
              <RefreshCw className="h-12 w-12 text-blue-600 mx-auto mb-4 animate-spin" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Syncing...</h4>
              <p className="text-gray-500">
                Connecting to Hikvision terminals and fetching attendance data...
              </p>
            </div>
          )}

          {error && (
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Sync Failed</h4>
              <p className="text-red-600 mb-6">{error}</p>
              <div className="flex space-x-3 justify-center">
                <button
                  onClick={handleSync}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </button>
                <button
                  onClick={handleClose}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md font-medium hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}

          {syncResult && (
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Sync Completed</h4>
              <p className="text-gray-500 mb-6">{syncResult.message}</p>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-center space-x-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-800">{syncResult.syncedCount}</div>
                    <div className="text-sm text-green-600">Records Synced</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-800">{syncResult.attendanceRecords.length}</div>
                    <div className="text-sm text-green-600">Verified Entries</div>
                  </div>
                </div>
              </div>

              {syncResult.attendanceRecords.length > 0 && (
                <div className="text-left">
                  <h5 className="font-medium text-gray-900 mb-3">Synced Records:</h5>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {syncResult.attendanceRecords.map((record, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center space-x-2">
                          <Shield className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium">Worker {record.workerId}</span>
                        </div>
                        <div className="text-sm text-gray-500">
                          {record.checkInTime} {record.checkOutTime && `- ${record.checkOutTime}`}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <button
                onClick={handleClose}
                className="mt-6 px-6 py-2 bg-green-600 text-white rounded-md font-medium hover:bg-green-700 transition-colors"
              >
                Done
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SyncAttendanceModal;
