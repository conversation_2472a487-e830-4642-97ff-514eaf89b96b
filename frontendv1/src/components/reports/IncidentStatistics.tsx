import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts';
import {
  AlertTriangle,
  Shield,
  Clock,
  RefreshCw,
  Download
} from 'lucide-react';
import { mockSites } from '../../data/mockTenantData';

interface IncidentData {
  id: string;
  siteId: string;
  siteName: string;
  date: string;
  type: 'accident-with-injury' | 'near-miss' | 'property-damage' | 'environmental';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  status: 'reported' | 'investigating' | 'closed';
}

interface IncidentStats {
  totalIncidents: number;
  openIncidents: number;
  closedIncidents: number;
  highSeverityIncidents: number;
  incidentsByType: { type: string; count: number; color: string }[];
  incidentsBySeverity: { severity: string; count: number; color: string }[];
  incidentsByMonth: { month: string; count: number }[];
  incidentsBySite: { siteName: string; count: number }[];
}

const IncidentStatistics: React.FC = () => {
  const [_incidentData, setIncidentData] = useState<IncidentData[]>([]);
  const [stats, setStats] = useState<IncidentStats | null>(null);
  const [timeRange, setTimeRange] = useState<'30d' | '90d' | '1y'>('90d');
  const [selectedView, setSelectedView] = useState<'overview' | 'trends' | 'by-site'>('overview');

  useEffect(() => {
    // Generate mock incident data
    const mockIncidents: IncidentData[] = [];
    const incidentTypes = ['accident-with-injury', 'near-miss', 'property-damage', 'environmental'] as const;
    const severities = ['low', 'medium', 'high', 'critical'] as const;
    const statuses = ['reported', 'investigating', 'closed'] as const;

    // Generate incidents for the last 6 months
    for (let i = 0; i < 45; i++) {
      const randomSite = mockSites[Math.floor(Math.random() * mockSites.length)];
      const randomDate = new Date();
      randomDate.setDate(randomDate.getDate() - Math.floor(Math.random() * 180));

      mockIncidents.push({
        id: `incident-${i + 1}`,
        siteId: randomSite.id,
        siteName: randomSite.name,
        date: randomDate.toISOString().split('T')[0],
        type: incidentTypes[Math.floor(Math.random() * incidentTypes.length)],
        severity: severities[Math.floor(Math.random() * severities.length)],
        description: `Incident ${i + 1} description`,
        status: statuses[Math.floor(Math.random() * statuses.length)]
      });
    }

    setIncidentData(mockIncidents);

    // Calculate statistics
    const totalIncidents = mockIncidents.length;
    const openIncidents = mockIncidents.filter(i => i.status !== 'closed').length;
    const closedIncidents = mockIncidents.filter(i => i.status === 'closed').length;
    const highSeverityIncidents = mockIncidents.filter(i => i.severity === 'high' || i.severity === 'critical').length;

    // Group by type
    const typeGroups = mockIncidents.reduce((acc, incident) => {
      acc[incident.type] = (acc[incident.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const incidentsByType = Object.entries(typeGroups).map(([type, count]) => ({
      type: type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      count,
      color: type === 'accident-with-injury' ? '#EF4444' : 
             type === 'near-miss' ? '#F59E0B' :
             type === 'property-damage' ? '#8B5CF6' : '#10B981'
    }));

    // Group by severity
    const severityGroups = mockIncidents.reduce((acc, incident) => {
      acc[incident.severity] = (acc[incident.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const incidentsBySeverity = Object.entries(severityGroups).map(([severity, count]) => ({
      severity: severity.charAt(0).toUpperCase() + severity.slice(1),
      count,
      color: severity === 'critical' ? '#DC2626' :
             severity === 'high' ? '#EF4444' :
             severity === 'medium' ? '#F59E0B' : '#10B981'
    }));

    // Group by month
    const monthGroups = mockIncidents.reduce((acc, incident) => {
      const month = new Date(incident.date).toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const incidentsByMonth = Object.entries(monthGroups)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([month, count]) => ({ month, count }));

    // Group by site
    const siteGroups = mockIncidents.reduce((acc, incident) => {
      acc[incident.siteName] = (acc[incident.siteName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const incidentsBySite = Object.entries(siteGroups)
      .sort(([, a], [, b]) => b - a)
      .map(([siteName, count]) => ({ siteName: siteName.split(' ')[0], count }));

    setStats({
      totalIncidents,
      openIncidents,
      closedIncidents,
      highSeverityIncidents,
      incidentsByType,
      incidentsBySeverity,
      incidentsByMonth,
      incidentsBySite
    });
  }, [timeRange]);

  if (!stats) {
    return <div>Loading...</div>;
  }

  // const COLORS = ['#EF4444', '#F59E0B', '#8B5CF6', '#10B981', '#3B82F6', '#EC4899'];

  const handleRefresh = () => {
    console.log('Refreshing incident statistics...');
  };

  const handleExport = () => {
    console.log('Exporting incident statistics...');
  };

  return (
    <div className="space-y-6">
      {/* Controls and Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex space-x-4">
            <select
              value={selectedView}
              onChange={(e) => setSelectedView(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="overview">Overview</option>
              <option value="trends">Trends</option>
              <option value="by-site">By Site</option>
            </select>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleRefresh}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
            <button
              onClick={handleExport}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Incidents</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalIncidents}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-gray-500" />
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Open Incidents</p>
              <p className="text-2xl font-semibold text-orange-600">{stats.openIncidents}</p>
            </div>
            <Clock className="h-8 w-8 text-orange-500" />
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">High Severity</p>
              <p className="text-2xl font-semibold text-red-600">{stats.highSeverityIncidents}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Resolution Rate</p>
              <p className="text-2xl font-semibold text-green-600">
                {Math.round((stats.closedIncidents / stats.totalIncidents) * 100)}%
              </p>
            </div>
            <Shield className="h-8 w-8 text-green-500" />
          </div>
        </div>
      </div>

      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Incidents by Type */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Incidents by Type</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={stats.incidentsByType}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ type, percent }) => `${type} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {stats.incidentsByType.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Incidents by Severity */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Incidents by Severity</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={stats.incidentsBySeverity}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="severity" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                    {stats.incidentsBySeverity.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}

      {selectedView === 'trends' && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Incident Trends Over Time</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={stats.incidentsByMonth}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="count" 
                  stroke="#EF4444" 
                  fill="#FEE2E2" 
                  strokeWidth={2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {selectedView === 'by-site' && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Incidents by Site</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={stats.incidentsBySite}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="siteName" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#EF4444" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}
    </div>
  );
};

export default IncidentStatistics;
