import React, { useState, useEffect } from 'react';
import {
  Search,
  Clock, Calendar,
  MapPin, Shield,
  ClipboardList,
  Filter
} from 'lucide-react';
import { Task } from '../../types/tasks';
interface TaskApprovalHistoryProps {
  siteId: string;
}
interface TaskApprovalRecord {
  task: Task;
  submittedBy: string; submittedAt: Date;
  reviewedBy: string; reviewedAt: Date;
  status: 'approved' | 'disapproved';
  reason?: string;
}

const TaskApprovalHistory: React.FC<TaskApprovalHistoryProps> = ({ siteId }) => {
  const [approvalHistory, setApprovalHistory] = useState<TaskApprovalRecord[]>([]);
  const [filteredHistory, setFilteredHistory] = useState<TaskApprovalRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'approved' | 'disapproved'>('all');

  useEffect(() => {
    fetchApprovalHistory();
  }, [siteId]);

  useEffect(() => {
    // Apply filters
    let filtered = [...approvalHistory];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.task.taskNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.submittedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.reviewedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.reason && item.reason.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(item => item.status === statusFilter);
    }

    setFilteredHistory(filtered);
  }, [approvalHistory, searchTerm, statusFilter]);

  const fetchApprovalHistory = async () => {
    // In a real app, this would be an API call
    // For now, using mock data
    const mockHistory: TaskApprovalRecord[] = [
      {
        task: {
          id: "task-history-1",
          taskNumber: "TSK-2024-095",
          title: "Site Preparation - Zone A",
          description: "Clear and prepare site for foundation work in Zone A",

          location: "Zone A",
          siteId: "site-1",
          plannedStartDate: new Date(new Date().setDate(new Date().getDate() - 5)),
          plannedEndDate: new Date(new Date().setDate(new Date().getDate() - 4)),
          estimatedDuration: 8,
          status: "completed",
          priority: "high",
          progressPercentage: 100,
          createdBy: "supervisor-1",
          createdByName: "John Smith",
          assignedSupervisor: "supervisor-1",
          assignedSupervisorName: "John Smith",

          dependencies: [],
          requiresPermit: false,
          riskLevel: "medium",
          safetyRequirements: ["Site survey", "Utility marking"],
          requiredPPE: ["hard-hat", "safety-vest", "safety-boots"],
          requiredTrainings: ["safety-orientation", "heavy-machinery"],
          requiredCertifications: [],
          ramsDocuments: [],
          attachments: [],
          qualityChecks: [],
          complianceRequirements: [],
          history: [],
          tags: ["site-prep", "excavation"],
          customFields: {},
          createdAt: new Date(new Date().setDate(new Date().getDate() - 7)),
          updatedAt: new Date(new Date().setDate(new Date().getDate() - 4)),
          category: 'plumbing',
          assignedWorkers: []
        },
        submittedBy: "John Smith",
        submittedAt: new Date(new Date().setDate(new Date().getDate() - 4)),
        reviewedBy: "Michael Johnson",
        reviewedAt: new Date(new Date().setDate(new Date().getDate() - 4)),
        status: "approved"
      },
      {
        task: {
          id: "task-history-2",
          taskNumber: "TSK-2024-096",
          title: "Plumbing Installation - Building B",
          description: "Install main water supply lines in Building B basement",
          category: "plumbing",
          location: "Building B - Basement",
          siteId: "site-1",
          plannedStartDate: new Date(new Date().setDate(new Date().getDate() - 3)),
          plannedEndDate: new Date(new Date().setDate(new Date().getDate() - 2)),
          estimatedDuration: 16,
          status: "cancelled",
          priority: "medium",
          progressPercentage: 0,
          createdBy: "supervisor-3",
          createdByName: "Robert Williams",
          assignedSupervisor: "supervisor-3",
          assignedSupervisorName: "Robert Williams",


          requiresPermit: true,
          permitTypes: ["plumbing-work"],
          riskLevel: "low",
          safetyRequirements: ["Water supply shutdown procedure"],
          requiredPPE: ["hard-hat", "safety-vest", "gloves"],
          requiredTrainings: ["plumbing-safety"],
          requiredCertifications: ["plumber-license"],
          ramsDocuments: [],
          attachments: [],
          qualityChecks: [],
          complianceRequirements: [],
          history: [],
          tags: ["plumbing", "installation"],
          customFields: {},
          createdAt: new Date(new Date().setDate(new Date().getDate() - 6)),
          updatedAt: new Date(new Date().setDate(new Date().getDate() - 2)),
          assignedWorkers: [],
          dependencies: []
        },
        submittedBy: "Robert Williams",
        submittedAt: new Date(new Date().setDate(new Date().getDate() - 3)),
        reviewedBy: "Michael Johnson",
        reviewedAt: new Date(new Date().setDate(new Date().getDate() - 3)),
        status: "disapproved",
        reason: "Incorrect pipe specifications in the task description. Please update with the correct specifications according to the latest building code requirements."
      },
      {
        task: {
          id: "task-history-3",
          taskNumber: "TSK-2024-097",
          title: "HVAC Ductwork - Floor 2",
          description: "Install HVAC ductwork in Floor 2 ceiling space",
          category: "hvac",
          location: "Building A - Floor 2",
          siteId: "site-1",
          plannedStartDate: new Date(new Date().setDate(new Date().getDate() - 2)),
          plannedEndDate: new Date(new Date().setDate(new Date().getDate() - 1)),
          estimatedDuration: 12,
          status: "completed",
          priority: "medium",
          progressPercentage: 100,
          createdBy: "supervisor-2",
          createdByName: "Sarah Johnson",
          assignedSupervisor: "supervisor-2",
          assignedSupervisorName: "Sarah Johnson",

          dependencies: [],
          requiresPermit: false,
          riskLevel: "low",
          safetyRequirements: ["Working at height procedure"],
          requiredPPE: ["hard-hat", "safety-vest", "gloves"],
          requiredTrainings: ["working-at-height"],
          requiredCertifications: ["hvac-technician"],
          ramsDocuments: [],
          attachments: [],
          qualityChecks: [],
          complianceRequirements: [],
          history: [],
          tags: ["hvac", "installation"],
          customFields: {},
          createdAt: new Date(new Date().setDate(new Date().getDate() - 5)),
          updatedAt: new Date(new Date().setDate(new Date().getDate() - 1)),
          assignedWorkers: []
        },
        submittedBy: "Sarah Johnson",
        submittedAt: new Date(new Date().setDate(new Date().getDate() - 2)),
        reviewedBy: "Michael Johnson",
        reviewedAt: new Date(new Date().setDate(new Date().getDate() - 2)),
        status: "approved"
      }
    ];

    setApprovalHistory(mockHistory);
    setFilteredHistory(mockHistory);
    setLoading(false);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Task Approval History</h2>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="w-full md:w-64">
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'approved' | 'disapproved')}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 appearance-none"
              >
                <option value="all">All Statuses</option>
                <option value="approved">Approved Only</option>
                <option value="disapproved">Disapproved Only</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* History List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        {loading ? (
          <div className="text-center py-10">Loading history...</div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredHistory.length === 0 ? (
              <div className="p-12 text-center">
                <ClipboardList className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No approval history found</h3>
                <p className="text-gray-500">
                  {searchTerm || statusFilter !== 'all' ? 'Try adjusting your filters to see more results.' : 'No tasks have been reviewed yet.'}
                </p>
              </div>
            ) : (
              filteredHistory.map((item) => (
                <div key={item.task.id} className="p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="text-base font-medium text-gray-900">{item.task.title}</h3>
                        <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${item.task.priority === 'high' ? 'bg-red-100 text-red-800' :
                            item.task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                          }`}>
                          {item.task.priority.toUpperCase()}
                        </span>
                        <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${item.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                          {item.status === 'approved' ? 'APPROVED' : 'DISAPPROVED'}
                        </span>
                      </div>

                      <p className="text-xs text-gray-600 mb-2">
                        {item.task.description}
                      </p>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-500 mb-2">
                        <div className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {item.task.location}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {item.task.estimatedDuration}h
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(item.task.plannedStartDate)}
                        </div>
                        <div className="flex items-center">
                          <Shield className="h-3 w-3 mr-1" />
                          {item.task.riskLevel} risk
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-500 mb-2">
                        <div>
                          <span className="font-medium">Submitted:</span> {item.submittedBy}, {formatDate(item.submittedAt)} at {formatTime(item.submittedAt)}
                        </div>
                        <div>
                          <span className="font-medium">Reviewed:</span> {item.reviewedBy}, {formatDate(item.reviewedAt)} at {formatTime(item.reviewedAt)}
                        </div>
                      </div>

                      {/* Removed the approved icon and text completely */}

                      {item.status === 'disapproved' && item.reason && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-100 rounded-md">
                          <p className="text-xs text-red-700">{item.reason}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskApprovalHistory;








