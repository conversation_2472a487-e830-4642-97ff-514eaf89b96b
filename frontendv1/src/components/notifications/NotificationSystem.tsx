import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import {
  Bell,
  Clock,
  User,
  Calendar,
  AlertTriangle,
  CheckCircle} from 'lucide-react';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';

interface Notification {
  id: string;
  type: 'training_expiring' | 'training_expired' | 'training_assigned' | 'training_completed' | 'system' | 'reminder';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  actionUrl?: string;
  actionLabel?: string;
  metadata?: {
    workerId?: number;
    workerName?: string;
    trainingId?: number;
    trainingName?: string;
    siteId?: string;
    siteName?: string;
    daysUntilExpiry?: number;
  };
}

interface NotificationSystemProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  maxNotifications?: number;
  autoHideDelay?: number;
  enableSound?: boolean;
}

const NotificationSystem: React.FC<NotificationSystemProps> = ({
  position:_position = 'top-right',
  maxNotifications = 5,
  autoHideDelay = 5000,
  enableSound = true
}) => {
  const { tenantId } = useTenantContext();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  // Mock notifications for demonstration
  const mockNotifications: Notification[] = [
    {
      id: '1',
      type: 'training_expiring',
      title: 'Training Expiring Soon',
      message: 'David Kamau\'s Working at Heights certification expires in 5 days',
      timestamp: new Date().toISOString(),
      isRead: false,
      priority: 'high',
      actionUrl: '/sites/site1/training#worker-status',
      actionLabel: 'View Details',
      metadata: {
        workerId: 1,
        workerName: 'David Kamau',
        trainingId: 1,
        trainingName: 'Working at Heights',
        daysUntilExpiry: 5
      }
    },
    {
      id: '2',
      type: 'training_assigned',
      title: 'Training Assigned',
      message: 'First Aid Level 1 has been assigned to 5 workers',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      isRead: false,
      priority: 'medium',
      actionUrl: '/sites/site1/training#dashboard',
      actionLabel: 'View Assignment',
      metadata: {
        trainingId: 2,
        trainingName: 'First Aid Level 1'
      }
    },
    {
      id: '3',
      type: 'training_completed',
      title: 'Training Completed',
      message: 'Mary Wanjiku completed Equipment Operation training',
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      isRead: true,
      priority: 'low',
      actionUrl: '/sites/site1/workers/2',
      actionLabel: 'View Worker',
      metadata: {
        workerId: 2,
        workerName: 'Mary Wanjiku',
        trainingId: 3,
        trainingName: 'Equipment Operation'
      }
    }
  ];

  useEffect(() => {
    // Initialize with mock notifications
    setNotifications(mockNotifications);
    setUnreadCount(mockNotifications.filter(n => !n.isRead).length);

    // Set up real-time notification polling/websocket
    const interval = setInterval(() => {
      checkForNewNotifications();
    }, 30000); //every 30 seconds

    return () => clearInterval(interval);
  }, [tenantId]);

  const checkForNewNotifications = useCallback(async () => {
    try {
      // In real implementation, this would fetch from API or receive via WebSocket
      // const newNotifications = await getNotifications({ tenantId, since: last});
      
      // Mock: Randomly generate new notifications
      if (Math.random() > 0.8) {
        const newNotification: Notification = {
          id: Date.now().toString(),
          type: 'training_expiring',
          title: 'New Training Alert',
          message: 'A worker\'s training is expiring soon',
          timestamp: new Date().toISOString(),
          isRead: false,
          priority: 'medium'
        };
        
        addNotification(newNotification);
      }
    } catch (error) {
      console.error('Error checking for notifications:', error);
    }
  }, [tenantId]);

  const addNotification = (notification: Notification) => {
    setNotifications(prev => {
      const updated = [notification, ...prev].slice(0, maxNotifications);
      return updated;
    });
    
    setUnreadCount(prev => prev + 1);
    
    // Play notification sound
    if (enableSound && notification.priority !== 'low') {
      playNotificationSound(notification.priority);
    }
    
    // Auto-hide for low priority notifications
    if (notification.priority === 'low' && autoHideDelay > 0) {
      setTimeout(() => {
        markAsRead(notification.id);
      }, autoHideDelay);
    }
  };

  const playNotificationSound = (priority: string) => {
    // In real implementation, this would play appropriate sound based on priority
    try {
      const audio = new Audio();
      switch (priority) {
        case 'critical':
          audio.src = '/sounds/critical-alert.mp3';
          break;
        case 'high':
          audio.src = '/sounds/high-alert.mp3';
          break;
        default:
          audio.src = '/sounds/notification.mp3';
      }
      audio.play().catch(() => {
        // Ignore audio play errors (user interaction required)
      });
    } catch (error) {
      console.error('Error playing notification sound:', error);
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, isRead: true } : n
      )
    );

    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const getNotificationIcon = (type: Notification['type']) => {
    const iconSize = "h-5 w-5";
    switch (type) {
      case 'training_expiring':
        return <Clock className={iconSize} />;
      case 'training_expired':
        return <AlertTriangle className={iconSize} />;
      case 'training_assigned':
        return <User className={iconSize} />;
      case 'training_completed':
        return <CheckCircle className={iconSize} />;
      case 'reminder':
        return <Calendar className={iconSize} />;
      default:
        return <Bell className={iconSize} />;
    }
  };

  const getPriorityStyles = (priority: string) => {
    switch (priority) {
      case 'critical':
        return {
          icon: 'text-red-600 bg-red-100',
          border: 'border-l-red-500',
          badge: 'bg-red-100 text-red-800 border-red-200'
        };
      case 'high':
        return {
          icon: 'text-orange-600 bg-orange-100',
          border: 'border-l-orange-500',
          badge: 'bg-orange-100 text-orange-800 border-orange-200'
        };
      case 'medium':
        return {
          icon: 'text-yellow-600 bg-yellow-100',
          border: 'border-l-yellow-500',
          badge: 'bg-yellow-100 text-yellow-800 border-yellow-200'
        };
      case 'low':
        return {
          icon: 'text-blue-600 bg-blue-100',
          border: 'border-l-blue-500',
          badge: 'bg-blue-100 text-blue-800 border-blue-200'
        };
      default:
        return {
          icon: 'text-gray-600 bg-gray-100',
          border: 'border-l-gray-500',
          badge: 'bg-gray-100 text-gray-800 border-gray-200'
        };
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'critical': return 'Critical';
      case 'high': return 'High';
      case 'medium': return 'Medium';
      case 'low': return 'Low';
      default: return 'Normal';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };



  // const getPositionClasses = () => {
  //   switch (position) {
  //     case 'top-left': return 'top-4 left-4';
  //     case 'top-right': return 'top-4 right-4';
  //     case 'bottom-left': return 'bottom-4 left-4';
  //     case 'bottom-right': return 'bottom-4 right-4';
  //     default: return 'top-4 right-4';
  //   }
  // };

  return (
    <>
      {/* Notification Bell */}
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="relative inline-flex items-center justify-center p-2 rounded-lg text-gray-600 hover:text-green-600 hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          title="Notifications"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium shadow-sm">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </button>

        {/* Notification Dropdown - Simplified */}
        {isOpen && (
          <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden">
            {/* Header */}
            <div className="px-4 py-3 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-gray-900">Notifications</h3>
                <div className="flex items-center space-x-2">
                  {unreadCount > 0 && (
                    <span className="text-xs text-gray-500">{unreadCount} new</span>
                  )}
                  <Link
                    to="/notifications"
                    onClick={() => setIsOpen(false)}
                    className="text-xs text-green-600 hover:text-green-700 font-medium"
                  >
                    View all
                  </Link>
                </div>
              </div>
            </div>

            {/* Notifications List - Simplified */}
            <div className="max-h-80 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-6 text-center">
                  <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No notifications</p>
                </div>
              ) : (
                notifications.slice(0, 4).map((notification) => {
                  const priorityStyles = getPriorityStyles(notification.priority);
                  return (
                    <div
                      key={notification.id}
                      className={`p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer ${
                        !notification.isRead ? 'bg-blue-50/30' : 'bg-white'
                      }`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start space-x-3">
                        {/* Icon */}
                        <div className={`flex-shrink-0 p-1.5 rounded-lg ${priorityStyles.icon}`}>
                          {getNotificationIcon(notification.type)}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="text-sm font-medium text-gray-900 truncate">
                                {notification.title}
                              </h4>
                              <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                {notification.message}
                              </p>
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-xs text-gray-500">
                                  {formatTimestamp(notification.timestamp)}
                                </span>
                                {notification.priority === 'high' || notification.priority === 'critical' ? (
                                  <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${priorityStyles.badge}`}>
                                    {getPriorityLabel(notification.priority)}
                                  </span>
                                ) : null}
                              </div>
                            </div>
                            {!notification.isRead && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}

              {/* View All Footer */}
              {notifications.length > 4 && (
                <div className="p-3 bg-gray-50 border-t border-gray-100">
                  <Link
                    to="/notifications"
                    onClick={() => setIsOpen(false)}
                    className="block text-center text-sm text-green-600 hover:text-green-700 font-medium"
                  >
                    View all {notifications.length} notifications
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
};

export default NotificationSystem;
