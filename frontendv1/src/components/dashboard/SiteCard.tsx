import { Link } from "react-router-dom";
import { SiteInfo } from "../../types";
import {
	AlertTriangle,
	Calendar,
	ChevronRight,
	FileCheck,
	MapPin,
	Users,
} from "lucide-react";

interface SiteCardProps {
	site: SiteInfo;
}

const SiteCard = ({ site }: SiteCardProps) => {
	const healthColorMap = {
		green: "border-green-500",
		amber: "border-amber-500",
		red: "border-red-500",
	};

	return (
		<div
			className={`bg-[#f3f2ee] rounded-lg overflow-hidden hover:shadow-sm transition-shadow duration-200 border-t-4 ${healthColorMap[site.healthStatus]}`}
		>
			<div className="p-5">
				<div className="flex flex-col space-y-3">
					{/* Header with title */}
					<div>
						<h3 className="font-semibold text-gray-800 text-lg">{site.name}</h3>
						<div className="flex items-center text-xs text-gray-500 mt-1">
							<MapPin className="h-3 w-3 mr-1" />
							<span>{site.location}</span>
						</div>

						{/* PM and Timeline moved below project name */}
						<div className="mt-2">
							<div className="text-xs text-gray-500 font-medium">
								PM: {site.projectManager}
							</div>
							{site.timeline && (
								<div className="text-xs text-gray-500 mt-1 flex items-center">
									<Calendar className="h-3 w-3 mr-1" />
									<span>{site.timeline}</span>
								</div>
							)}
						</div>
					</div>

					{/* Divider */}
					<div className="border-t border-gray-200 my-2"></div>

					{/* Stats section */}
					<div className="flex justify-between items-center mt-2 bg-white/50 p-3 rounded-md">
						<div className="flex items-center space-x-1">
							<Users className="h-4 w-4 text-gray-500" />
							<div className="flex flex-col">
								<span className="text-sm font-medium">
									{site.workersOnSite}
								</span>
								<span className="text-xs text-gray-500">Workers</span>
							</div>
						</div>

						<div className="flex items-center space-x-1">
							<FileCheck className="h-4 w-4 text-gray-500" />
							<div className="flex flex-col">
								<span className="text-sm font-medium">
									{site.activePermits}
								</span>
								<span className="text-xs text-gray-500">Permits</span>
							</div>
						</div>

						<div className="flex items-center space-x-1">
							<AlertTriangle className="h-4 w-4 text-gray-500" />
							<div className="flex flex-col">
								<span className="text-sm font-medium">
									{site.openIncidents}
								</span>
								<span className="text-xs text-gray-500">Incidents</span>
							</div>
						</div>
					</div>

					{/* Footer */}
					<div className="flex justify-end mt-1">
						<Link
							to={`/sites/${site.id}/dashboard`}
							className="flex items-center text-xs font-medium text-green-500 hover:text-green-600"
						>
							View Details <ChevronRight className="h-3 w-3 ml-0.5" />
						</Link>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SiteCard;
