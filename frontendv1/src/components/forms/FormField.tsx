import React from "react";
import {
	FormField as FormFieldType,
	FormValidationError,
} from "../../types/forms";
import { Calendar, Clock, Upload, MapPin, Star, Check, X } from "lucide-react";

interface FormFieldProps {
	field: FormFieldType;
	value: any;
	onChange: (fieldId: string, value: any) => void;
	error?: FormValidationError;
	disabled?: boolean;
}

const FormField: React.FC<FormFieldProps> = ({
	field,
	value,
	onChange,
	error,
	disabled = false,
}) => {
	const handleChange = (newValue: any) => {
		onChange(field.id, newValue);
	};

	const getFieldClasses = () => {
		const baseClasses =
			"w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors";
		const errorClasses = error ? "border-red-500" : "border-gray-300";
		const disabledClasses = disabled ? "bg-gray-100 cursor-not-allowed" : "";

		return `${baseClasses} ${errorClasses} ${disabledClasses}`;
	};

	const renderField = () => {
		switch (field.type) {
			case "text":
			case "email":
			case "phone":
				return (
					<input
						type={field.type}
						value={value || ""}
						onChange={(e) => handleChange(e.target.value)}
						placeholder={field.placeholder}
						className={getFieldClasses()}
						disabled={disabled}
						required={field.required}
					/>
				);

			case "textarea":
				return (
					<textarea
						value={value || ""}
						onChange={(e) => handleChange(e.target.value)}
						placeholder={field.placeholder}
						rows={4}
						className={getFieldClasses()}
						disabled={disabled}
						required={field.required}
					/>
				);

			case "number":
				return (
					<input
						type="number"
						value={value || ""}
						onChange={(e) =>
							handleChange(e.target.value ? Number(e.target.value) : "")
						}
						placeholder={field.placeholder}
						min={field.validation?.min}
						max={field.validation?.max}
						className={getFieldClasses()}
						disabled={disabled}
						required={field.required}
					/>
				);

			case "date":
				return (
					<div className="relative">
						<input
							type="date"
							value={value || ""}
							onChange={(e) => handleChange(e.target.value)}
							className={getFieldClasses()}
							disabled={disabled}
							required={field.required}
						/>
						<Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				);

			case "time":
				return (
					<div className="relative">
						<input
							type="time"
							value={value || ""}
							onChange={(e) => handleChange(e.target.value)}
							className={getFieldClasses()}
							disabled={disabled}
							required={field.required}
						/>
						<Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				);

			case "datetime":
				return (
					<input
						type="datetime-local"
						value={value || ""}
						onChange={(e) => handleChange(e.target.value)}
						className={getFieldClasses()}
						disabled={disabled}
						required={field.required}
					/>
				);

			case "select":
				return (
					<select
						value={value || ""}
						onChange={(e) => handleChange(e.target.value)}
						className={getFieldClasses()}
						disabled={disabled}
						required={field.required}
					>
						<option value="">Select an option...</option>
						{field.options?.map((option) => (
							<option key={option} value={option}>
								{option}
							</option>
						))}
					</select>
				);

			case "multiselect":
				return (
					<div className="space-y-2">
						{field.options?.map((option) => (
							<label key={option} className="flex items-center space-x-2">
								<input
									type="checkbox"
									checked={Array.isArray(value) && value.includes(option)}
									onChange={(e) => {
										const currentValues = Array.isArray(value) ? value : [];
										if (e.target.checked) {
											handleChange([...currentValues, option]);
										} else {
											handleChange(currentValues.filter((v) => v !== option));
										}
									}}
									className="rounded border-gray-300 text-green-600 focus:ring-green-500"
									disabled={disabled}
								/>
								<span className="text-sm text-gray-700">{option}</span>
							</label>
						))}
					</div>
				);

			case "radio":
				return (
					<div className="space-y-2">
						{field.options?.map((option) => (
							<label key={option} className="flex items-center space-x-2">
								<input
									type="radio"
									name={field.id}
									value={option}
									checked={value === option}
									onChange={(e) => handleChange(e.target.value)}
									className="border-gray-300 text-green-600 focus:ring-green-500"
									disabled={disabled}
									required={field.required}
								/>
								<span className="text-sm text-gray-700">{option}</span>
							</label>
						))}
					</div>
				);

			case "checkbox":
				return (
					<label className="flex items-center space-x-2">
						<input
							type="checkbox"
							checked={value === true}
							onChange={(e) => handleChange(e.target.checked)}
							className="rounded border-gray-300 text-green-600 focus:ring-green-500"
							disabled={disabled}
							required={field.required}
						/>
						<span className="text-sm text-gray-700">{field.label}</span>
					</label>
				);

			case "yesno":
				return (
					<div className="flex space-x-4">
						<label className="flex items-center space-x-2">
							<input
								type="radio"
								name={field.id}
								value="yes"
								checked={value === "yes"}
								onChange={(e) => handleChange(e.target.value)}
								className="border-gray-300 text-green-600 focus:ring-green-500"
								disabled={disabled}
								required={field.required}
							/>
							<Check className="h-4 w-4 text-green-600" />
							<span className="text-sm text-gray-700">Yes</span>
						</label>
						<label className="flex items-center space-x-2">
							<input
								type="radio"
								name={field.id}
								value="no"
								checked={value === "no"}
								onChange={(e) => handleChange(e.target.value)}
								className="border-gray-300 text-red-600 focus:ring-red-500"
								disabled={disabled}
								required={field.required}
							/>
							<X className="h-4 w-4 text-red-600" />
							<span className="text-sm text-gray-700">No</span>
						</label>
					</div>
				);

			case "rating":
				const maxRating = field.validation?.max || 5;
				return (
					<div className="flex space-x-1">
						{Array.from({ length: maxRating }, (_, i) => i + 1).map(
							(rating) => (
								<button
									key={rating}
									type="button"
									onClick={() => handleChange(rating)}
									className={`p-1 ${value >= rating ? "text-yellow-400" : "text-gray-300"} hover:text-yellow-400 transition-colors`}
									disabled={disabled}
								>
									<Star className="h-6 w-6 fill-current" />
								</button>
							),
						)}
					</div>
				);

			case "file":
			case "photo":
				return (
					<div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center hover:border-gray-400 transition-colors">
						<input
							type="file"
							onChange={(e) => {
								const file = e.target.files?.[0];
								if (file) {
									handleChange(file);
								}
							}}
							accept={
								field.type === "photo"
									? "image/*"
									: field.validation?.fileTypes?.join(",")
							}
							className="hidden"
							id={`file-${field.id}`}
							disabled={disabled}
							required={field.required}
						/>
						<label htmlFor={`file-${field.id}`} className="cursor-pointer">
							<Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
							<p className="text-sm text-gray-600">
								{field.type === "photo"
									? "Click to upload photo"
									: "Click to upload file"}
							</p>
							{field.validation?.maxFileSize && (
								<p className="text-xs text-gray-500 mt-1">
									Max size: {field.validation.maxFileSize}MB
								</p>
							)}
						</label>
						{value && (
							<p className="text-sm text-green-600 mt-2">
								File selected: {value.name || "File uploaded"}
							</p>
						)}
					</div>
				);

			case "signature":
				return (
					<div className="border border-gray-300 rounded-md p-4 bg-gray-50">
						<p className="text-sm text-gray-600 mb-2">
							Digital signature required
						</p>
						<button
							type="button"
							onClick={() => {
								// In a real implementation, this would open a signature pad
								handleChange("signature_placeholder");
							}}
							className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
							disabled={disabled}
						>
							{value ? "Update Signature" : "Add Signature"}
						</button>
						{value && (
							<p className="text-sm text-green-600 mt-2">
								✓ Signature captured
							</p>
						)}
					</div>
				);

			case "location":
				return (
					<div className="space-y-2">
						<button
							type="button"
							onClick={() => {
								if (navigator.geolocation) {
									navigator.geolocation.getCurrentPosition(
										(position) => {
											handleChange({
												latitude: position.coords.latitude,
												longitude: position.coords.longitude,
												accuracy: position.coords.accuracy,
											});
										},
										(error) => {
											console.error("Error getting location:", error);
										},
									);
								}
							}}
							className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
							disabled={disabled}
						>
							<MapPin className="h-4 w-4" />
							<span>{value ? "Update Location" : "Capture Location"}</span>
						</button>
						{value && (
							<p className="text-sm text-green-600">
								✓ Location captured: {value.latitude?.toFixed(6)},{" "}
								{value.longitude?.toFixed(6)}
							</p>
						)}
					</div>
				);

			default:
				return (
					<div className="text-red-500 text-sm">
						Unsupported field type: {field.type}
					</div>
				);
		}
	};

	return (
		<div className="space-y-2">
			{field.type !== "checkbox" && (
				<label className="block text-sm font-medium text-gray-700">
					{field.label}
					{field.required && <span className="text-red-500 ml-1">*</span>}
				</label>
			)}

			{field.description && (
				<p className="text-sm text-gray-500">{field.description}</p>
			)}

			{renderField()}

			{error && <p className="text-red-500 text-sm">{error.message}</p>}
		</div>
	);
};

export default FormField;
