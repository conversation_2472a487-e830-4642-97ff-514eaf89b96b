import React from 'react';
import {
  User,
  GraduationCap,
  Award,
  Clock,
  AlertTriangle,
  DollarSign,
  Heart,
  FileText,
  Image,
  Calendar,
  Shield,
  CheckCircle,
  XCircle,
  Users,
  FolderOpen,
  Activity
} from 'lucide-react';
import { Worker } from '../../types';
import { CompanyWorker } from '../../data/workers';
import { ExplorerItem } from '../shared/VSCodeInterface';

interface WorkerExplorerProps {
  worker: Worker | CompanyWorker;
}

const createWorkerExplorerItems = (worker: Worker | CompanyWorker): ExplorerItem[] => {
  const getComplianceIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending_training':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'expired':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
    }
  };

  const getTrainingStatusBadge = (trainings: any[]) => {
    const completed = trainings?.filter(t => t.status === 'completed').length || 0;
    const total = trainings?.length || 0;
    return `${completed}/${total}`;
  };

  const getCertificationStatusBadge = (certifications: any[]) => {
    const valid = certifications?.filter(c => c.status === 'valid').length || 0;
    const total = certifications?.length || 0;
    return `${valid}/${total}`;
  };

  // Build the explorer tree structure for workers
  const explorerItems: ExplorerItem[] = [
    // Primary Information
    {
      id: 'worker-about',
      name: 'About',
      type: 'file',
      icon: <User className="h-4 w-4 text-blue-600" />,
      data: { type: 'about', worker }
    },

    // Training Information
    {
      id: 'worker-trainings',
      name: 'Trainings',
      type: 'folder',
      icon: <GraduationCap className="h-4 w-4 text-purple-600" />,
      badge: getTrainingStatusBadge(worker.trainings),
      badgeColor: 'bg-purple-100 text-purple-800',
      children: [
        {
          id: 'training-records',
          name: 'Training Records',
          type: 'file',
          icon: <FileText className="h-4 w-4 text-purple-500" />,
          data: { type: 'training-records', worker }
        },
        {
          id: 'training-history',
          name: 'Training History',
          type: 'file',
          icon: <Calendar className="h-4 w-4 text-purple-500" />,
          data: { type: 'training-history', worker }
        },
        ...(worker.trainings?.map(training => ({
          id: `training-${training.id}`,
          name: training.name,
          type: 'file' as const,
          icon: <GraduationCap className="h-4 w-4 text-purple-400" />,
          data: { type: 'training-detail', training }
        })) || [])
      ]
    },

    // Certifications
    {
      id: 'worker-certificates',
      name: 'Certificates',
      type: 'folder',
      icon: <Award className="h-4 w-4 text-yellow-600" />,
      badge: getCertificationStatusBadge(worker.certifications),
      badgeColor: 'bg-yellow-100 text-yellow-800',
      children: [
        {
          id: 'certificates-overview',
          name: 'Certificates Overview',
          type: 'file',
          icon: <Award className="h-4 w-4 text-yellow-500" />,
          data: { type: 'certificates-overview', worker }
        },
        ...(worker.certifications?.map(cert => ({
          id: `cert-${cert.id}`,
          name: cert.name,
          type: 'file' as const,
          icon: cert.status === 'valid' 
            ? <CheckCircle className="h-4 w-4 text-green-500" />
            : cert.status === 'expiring'
            ? <Clock className="h-4 w-4 text-yellow-500" />
            : <XCircle className="h-4 w-4 text-red-500" />,
          badge: cert.status,
          badgeColor: cert.status === 'valid' 
            ? 'bg-green-100 text-green-800'
            : cert.status === 'expiring'
            ? 'bg-yellow-100 text-yellow-800'
            : 'bg-red-100 text-red-800',
          data: { type: 'certificate-detail', certificate: cert }
        })) || [])
      ]
    },

    // Time & Attendance
    {
      id: 'worker-time',
      name: 'Time & Attendance',
      type: 'folder',
      icon: <Clock className="h-4 w-4 text-green-600" />,
      badge: `${worker.manHours || 0}h`,
      badgeColor: 'bg-green-100 text-green-800',
      children: [
        {
          id: 'time-overview',
          name: 'Time Overview',
          type: 'file',
          icon: <Activity className="h-4 w-4 text-green-500" />,
          data: { type: 'time-overview', worker }
        },
        {
          id: 'attendance-records',
          name: 'Attendance Records',
          type: 'file',
          icon: <Calendar className="h-4 w-4 text-green-500" />,
          data: { type: 'attendance-records', worker }
        },
        {
          id: 'time-logs',
          name: 'Time Logs',
          type: 'file',
          icon: <Clock className="h-4 w-4 text-green-500" />,
          data: { type: 'time-logs', worker }
        }
      ]
    },

    // Incidents
    {
      id: 'worker-incidents',
      name: 'Incidents',
      type: 'folder',
      icon: <AlertTriangle className="h-4 w-4 text-red-600" />,
      badge: worker.incidents?.length.toString() || '0',
      badgeColor: worker.incidents?.length ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800',
      children: [
        {
          id: 'incidents-overview',
          name: 'Incidents Overview',
          type: 'file',
          icon: <Shield className="h-4 w-4 text-red-500" />,
          data: { type: 'incidents-overview', worker }
        },
        ...(worker.incidents?.map(incident => ({
          id: `incident-${incident.id}`,
          name: incident.title,
          type: 'file' as const,
          icon: <AlertTriangle className="h-4 w-4 text-red-400" />,
          badge: incident.status,
          badgeColor: incident.status === 'resolved' 
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800',
          data: { type: 'incident-detail', incident }
        })) || [])
      ]
    },

    // Payroll
    {
      id: 'worker-payroll',
      name: 'Payroll',
      type: 'folder',
      icon: <DollarSign className="h-4 w-4 text-blue-600" />,
      children: [
        {
          id: 'payroll-overview',
          name: 'Payroll Overview',
          type: 'file',
          icon: <DollarSign className="h-4 w-4 text-blue-500" />,
          data: { type: 'payroll-overview', worker }
        },
        {
          id: 'pay-history',
          name: 'Pay History',
          type: 'file',
          icon: <Calendar className="h-4 w-4 text-blue-500" />,
          data: { type: 'pay-history', worker }
        },
        {
          id: 'benefits',
          name: 'Benefits',
          type: 'file',
          icon: <Shield className="h-4 w-4 text-blue-500" />,
          data: { type: 'benefits', worker }
        }
      ]
    },

    // Medical
    {
      id: 'worker-medical',
      name: 'Medical',
      type: 'folder',
      icon: <Heart className="h-4 w-4 text-pink-600" />,
      children: [
        {
          id: 'medical-overview',
          name: 'Medical Overview',
          type: 'file',
          icon: <Heart className="h-4 w-4 text-pink-500" />,
          data: { type: 'medical-overview', worker }
        },
        {
          id: 'medical-records',
          name: 'Medical Records',
          type: 'file',
          icon: <FileText className="h-4 w-4 text-pink-500" />,
          data: { type: 'medical-records', worker }
        },
        {
          id: 'health-checks',
          name: 'Health Checks',
          type: 'file',
          icon: <CheckCircle className="h-4 w-4 text-pink-500" />,
          data: { type: 'health-checks', worker }
        }
      ]
    }
  ];

  return explorerItems;
};

export default createWorkerExplorerItems;
