import React, { useRef, useState } from "react";
import { toast } from "react-toastify";
import {
  User,
  Building,
  Phone,
  Mail,
  Calendar,
  Save,
  X,
  AlertCircle,
  IdCard,
  Plus,
  File as FileIcon,
} from "lucide-react";
import { mockTrades, mockSkills, mockTrainings } from "../../data/mockData";
import { Gender } from "../../types/graphql";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Button } from "../ui/button";
import { Select } from "../ui/select";

import { PhotoCapture } from "../ui/photo-capture";
import { DocumentUpload } from "../ui/document-upload";

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

interface CreateWorkerFormProps {
  onSuccess?: (worker: any) => void;
  onCancel?: () => void;
  useDummyData?: boolean;
}

interface DocumentFileInput {
  file: File;
  name: string;
  isPublic: boolean;
}

interface FormData {
  name: string;
  company: string;
  nationalId: string;
  gender: Gender;
  phoneNumber: string;
  dateOfBirth: string;
  tradeIds: number[];
  skillIds: number[];
  mpesaNumber: string;
  email: string;
  inductionDate: string;
  medicalCheckDate: string;
  profilePicture: File | null;
  signature?: File | null;
  documents?: DocumentFileInput[];
}

interface FormErrors {
  [key: string]: string;
}

const CreateWorkerForm: React.FC<CreateWorkerFormProps> = ({
  onSuccess,
  onCancel,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    company: "",
    nationalId: "",
    gender: "MALE",
    phoneNumber: "",
    dateOfBirth: "",
    tradeIds: [],
    skillIds: [],
    mpesaNumber: "",
    email: "",
    inductionDate: "",
    medicalCheckDate: "",
    signature: null,
    documents: [],
    profilePicture: null,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  // --- STATE for training selection and document upload ---
  const [selectedTrainings, setSelectedTrainings] = useState<number[]>([]); // training ids
  const [trainingDocuments, setTrainingDocuments] = useState<
    Record<number, DocumentFileInput[]>
  >({}); // {trainingId: [docs]}
  const [generalDocuments, setGeneralDocuments] = useState<DocumentFileInput[]>(
    []
  );

  // Use mock data instead of GraphQL
  const trainings = mockTrainings;
  const trades = mockTrades;
  const skills = mockSkills;

  const isLoadingDropdownData = false;
  const hasDropdownErrors = false;

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    // Required field validations
    !formData.name.trim()
      ? (newErrors.name = "Name is required")
      : delete newErrors.name;
    !formData.company.trim()
      ? (newErrors.company = "Company is required")
      : delete newErrors.company;
    !formData.nationalId.trim()
      ? (newErrors.nationalId = "National ID is required")
      : delete newErrors.nationalId;
    !formData.gender
      ? (newErrors.gender = "Gender is required")
      : delete newErrors.gender;
    !formData.phoneNumber.trim()
      ? (newErrors.phoneNumber = "Phone number is required")
      : delete newErrors.phoneNumber;
    !formData.mpesaNumber.trim()
      ? (newErrors.mpesaNumber = "Mpesa number is required")
      : delete newErrors.mpesaNumber;
    !formData.email.trim()
      ? (newErrors.email = "Email is required")
      : delete newErrors.email;
    !formData.inductionDate.trim()
      ? (newErrors.inductionDate = "Induction date is required")
      : delete newErrors.inductionDate;
    !formData.medicalCheckDate.trim()
      ? (newErrors.medicalCheckDate = "Medical check date is required")
      : delete newErrors.medicalCheckDate;
    !formData.dateOfBirth.trim()
      ? (newErrors.dateOfBirth = "Date of birth is required")
      : delete newErrors.dateOfBirth;

    // Format validations
    if (formData.nationalId && !/^\d{8,12}$/.test(formData.nationalId)) {
      newErrors.nationalId = "National ID must be 8-12 digits";
    } else if (formData.nationalId.trim()) {
      delete newErrors.nationalId;
    }

    if (
      formData.phoneNumber &&
      !/^[+]?[\d\s-()]{10,15}$/.test(formData.phoneNumber)
    ) {
      newErrors.phoneNumber = "Please enter a valid phone number";
    } else if (formData.phoneNumber.trim()) {
      delete newErrors.phoneNumber;
    }
    // Validate mpesa number
    if (
      formData.mpesaNumber &&
      !/^[+]?[\d\s-()]{10,15}$/.test(formData.mpesaNumber)
    ) {
      newErrors.mpesaNumber = "Please enter a valid mpesa number";
    } else if (formData.mpesaNumber.trim()) {
      delete newErrors.mpesaNumber;
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    } else if (formData.email.trim()) {
      delete newErrors.email;
    }

    // Date validations
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();

      if (age < 18 || age > 80) {
        newErrors.dateOfBirth = "Age must be between 18 and 80 years";
      } else if (formData.dateOfBirth.trim()) {
        delete newErrors.dateOfBirth;
      }
    }

    if (
      formData.inductionDate &&
      new Date(formData.inductionDate) > new Date()
    ) {
      newErrors.inductionDate = "Induction date cannot be in the future";
    } else if (formData.inductionDate.trim()) {
      delete newErrors.inductionDate;
    }

    if (
      formData.medicalCheckDate &&
      new Date(formData.medicalCheckDate) > new Date()
    ) {
      newErrors.medicalCheckDate = "Medical check date cannot be in the future";
    } else if (formData.medicalCheckDate.trim()) {
      delete newErrors.medicalCheckDate;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleMultiSelectChange = (
    field: "tradeIds" | "skillIds",
    value: number
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter((id) => id !== value)
        : [...prev[field], value],
    }));
  };








  // Helper function to clear the form
  function clearForm() {
    setFormData({
      name: "",
      company: "",
      nationalId: "",
      gender: "MALE",
      phoneNumber: "",
      dateOfBirth: "",
      tradeIds: [],
      skillIds: [],
      mpesaNumber: "",
      email: "",
      inductionDate: "",
      medicalCheckDate: "",
      signature: null,
      documents: [],
      profilePicture: null,
    });
    setErrors({});
    setPhotoFile(null);
    setPhotoPreview(null);
    setSelectedTrainings([]);
    setTrainingDocuments({});
    setGeneralDocuments([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;
    if (!photoFile) {
      toast.error("Please select a photo");
      return;
    }

    setIsSubmitting(true);

    // Simulate API call with mock data
    try {
      // Create mock worker object
      const newWorker = {
        id: Date.now(), // Simple ID generation
        name: formData.name,
        company: formData.company,
        nationalId: formData.nationalId,
        gender: formData.gender,
        phoneNumber: formData.phoneNumber,
        dateOfBirth: formData.dateOfBirth,
        email: formData.email,
        inductionDate: formData.inductionDate,
        medicalCheckDate: formData.medicalCheckDate,
        trades: formData.tradeIds.map(id => trades.find(t => t.id === id)).filter(Boolean),
        skills: formData.skillIds.map(id => skills.find(s => s.id === id)).filter(Boolean),
        mpesaNumber: formData.mpesaNumber,
        profilePictureUrl: URL.createObjectURL(photoFile),
        status: 'active',
        createdAt: new Date().toISOString(),
      };

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success toast
      toast.success(`Worker "${newWorker.name}" created successfully!`);
      clearForm();
      onSuccess?.(newWorker);
    } catch (error) {
      console.error('Error creating worker:', error);
      toast.error('Failed to create worker. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };


  const handleTrainingCheckbox = (trainingId: number) => {
    setSelectedTrainings((prev) =>
      prev.includes(trainingId)
        ? prev.filter((id) => id !== trainingId)
        : [...prev, trainingId]
    );
  };

  const handleTrainingDocumentChange = (
    trainingId: number,
    file: File,
    name: string
  ) => {
    if (!file || !name) return;
    if (file.size > MAX_FILE_SIZE) {
      toast.error(
        `File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`
      );
      return;
    }
    setTrainingDocuments((prev) => {
      const docs = prev[trainingId] || [];
      return {
        ...prev,
        [trainingId]: [...docs, { file, name, isPublic: true }],
      };
    });
  };

  const handleRemoveTrainingDocument = (trainingId: number, idx: number) => {
    setTrainingDocuments((prev) => {
      const docs = prev[trainingId] || [];
      return {
        ...prev,
        [trainingId]: docs.filter((_, i) => i !== idx),
      };
    });
  };

  const handleGeneralDocumentChange = (file: File, name: string) => {
    if (!file || !name) return;
    if (file.size > MAX_FILE_SIZE) {
      toast.error(
        `File size must be less than ${MAX_FILE_SIZE / 1024 / 1024}MB`
      );
      return;
    }
    setGeneralDocuments((prev) => [...prev, { file, name, isPublic: true }]);
  };

  const handleRemoveGeneralDocument = (idx: number) => {
    setGeneralDocuments((prev) => prev.filter((_, i) => i !== idx));
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-8 max-w-6xl mx-auto">
        {/* Photo Upload Section */}
        <PhotoCapture
          currentPhoto={photoFile || photoPreview}
          onPhotoChange={(file) => {
            setPhotoFile(file);
            setPhotoPreview(URL.createObjectURL(file));
            setFormData(prev => ({ ...prev, profilePicture: file }));
          }}
        />

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  Full Name *
                </Label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className={errors.name ? "border-red-500" : ""}
                  placeholder="Enter full name"
                />
                {errors.name && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="company" className="flex items-center gap-1">
                  <Building className="h-4 w-4" />
                  Company *
                </Label>
                <Input
                  id="company"
                  type="text"
                  value={formData.company}
                  onChange={(e) => handleInputChange("company", e.target.value)}
                  className={errors.company ? "border-red-500" : ""}
                  placeholder="Enter company name"
                />
                {errors.company && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.company}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="nationalId" className="flex items-center gap-1">
                  <IdCard className="h-4 w-4" />
                  National ID *
                </Label>
                <Input
                  id="nationalId"
                  type="text"
                  value={formData.nationalId}
                  onChange={(e) => handleInputChange("nationalId", e.target.value)}
                  className={errors.nationalId ? "border-red-500" : ""}
                  placeholder="Enter national ID"
                />
                {errors.nationalId && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.nationalId}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender">Gender *</Label>
                <Select
                  value={formData.gender}
                  onChange={(e) => handleInputChange("gender", e.target.value)}
                  className={errors.gender ? "border-red-500" : ""}
                >
                  <option value="MALE">Male</option>
                  <option value="FEMALE">Female</option>
                </Select>
                {errors.gender && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.gender}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="phoneNumber" className="flex items-center gap-1">
                  <Phone className="h-4 w-4" />
                  Phone Number *
                </Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                  className={errors.phoneNumber ? "border-red-500" : ""}
                  placeholder="+254 712 345 678"
                />
                {errors.phoneNumber && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.phoneNumber}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-1">
                  <Mail className="h-4 w-4" />
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className={errors.email ? "border-red-500" : ""}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.email}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="mpesaNumber" className="flex items-center gap-1">
                  <Phone className="h-4 w-4" />
                  M-Pesa Number
                </Label>
                <Input
                  id="mpesaNumber"
                  type="tel"
                  value={formData.mpesaNumber}
                  onChange={(e) => handleInputChange("mpesaNumber", e.target.value)}
                  className={errors.mpesaNumber ? "border-red-500" : ""}
                  placeholder="+254 712 345 678"
                />
                {errors.mpesaNumber && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.mpesaNumber}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Date Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Important Dates
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="dateOfBirth" className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Date of Birth
                </Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                  className={errors.dateOfBirth ? "border-red-500" : ""}
                />
                {errors.dateOfBirth && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.dateOfBirth}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="inductionDate">Induction Date</Label>
                <Input
                  id="inductionDate"
                  type="date"
                  value={formData.inductionDate}
                  onChange={(e) => handleInputChange("inductionDate", e.target.value)}
                  className={errors.inductionDate ? "border-red-500" : ""}
                />
                {errors.inductionDate && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.inductionDate}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="medicalCheckDate">Medical Check Date</Label>
                <Input
                  id="medicalCheckDate"
                  type="date"
                  value={formData.medicalCheckDate}
                  onChange={(e) => handleInputChange("medicalCheckDate", e.target.value)}
                  className={errors.medicalCheckDate ? "border-red-500" : ""}
                />
                {errors.medicalCheckDate && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.medicalCheckDate}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error message for dropdown loading failures */}
        {hasDropdownErrors && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Failed to load dropdown data
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>
                    Unable to load skills, trades, or trainings. Please refresh
                    the page and try again.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Training Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileIcon className="h-5 w-5" />
              Training & Certifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border border-gray-200 rounded-md p-4 max-h-60 overflow-y-auto bg-gray-50">
              {isLoadingDropdownData ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                  <span className="ml-2 text-sm text-gray-500">
                    Loading trainings...
                  </span>
                </div>
              ) : (
                trainings.map((training: { id: number; name: string }) => (
                  <div key={training.id} className="mb-4 last:mb-0">
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedTrainings.includes(training.id)}
                        onChange={() => handleTrainingCheckbox(training.id)}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <span className="text-sm font-medium">{training.name}</span>
                    </label>
                    {selectedTrainings.includes(training.id) && (
                      <div className="ml-7 mt-3 space-y-3 p-3 bg-white rounded-md border border-gray-200">
                        <p className="text-sm text-gray-600">
                          Upload documents for {training.name}, if any. E.g certificates
                        </p>
                        {/* List uploaded docs for this training */}
                        {(trainingDocuments[training.id] || []).map(
                          (doc, idx) => (
                            <div
                              key={idx}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded-md border"
                            >
                              <div className="flex items-center space-x-2">
                                <FileIcon className="h-4 w-4 text-gray-500" />
                                <span className="text-sm text-gray-700">{doc.file.name}</span>
                                <span className="text-xs text-gray-500">({doc.name})</span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveTrainingDocument(training.id, idx)}
                                className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          )
                        )}
                        {/* Upload new doc for this training */}
                        <TrainingDocUploader
                          trainingId={training.id}
                          onAdd={handleTrainingDocumentChange}
                        />
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Skills and Trades */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Skills</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border border-gray-200 rounded-md p-3 max-h-40 overflow-y-auto bg-gray-50">
                {isLoadingDropdownData ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                    <span className="ml-2 text-sm text-gray-500">
                      Loading skills...
                    </span>
                  </div>
                ) : skills.length === 0 ? (
                  <div className="text-gray-500 text-sm">No skills available</div>
                ) : (
                  skills.map((skill: { id: number; name: string }) => (
                    <label
                      key={skill.id}
                      className="flex items-center space-x-3 mb-2 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={formData.skillIds.includes(skill.id)}
                        onChange={() =>
                          handleMultiSelectChange("skillIds", skill.id)
                        }
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <span className="text-sm">{skill.name}</span>
                    </label>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Trades</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border border-gray-200 rounded-md p-3 max-h-40 overflow-y-auto bg-gray-50">
                {isLoadingDropdownData ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                    <span className="ml-2 text-sm text-gray-500">
                      Loading trades...
                    </span>
                  </div>
                ) : (
                  trades.map((trade: { id: number; name: string }) => (
                    <label
                      key={trade.id}
                      className="flex items-center space-x-3 mb-2 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={formData.tradeIds.includes(trade.id)}
                        onChange={() =>
                          handleMultiSelectChange("tradeIds", trade.id)
                        }
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <span className="text-sm">{trade.name}</span>
                    </label>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* General Documents Section */}
        <DocumentUpload
          documents={generalDocuments}
          onDocumentAdd={handleGeneralDocumentChange}
          onDocumentRemove={handleRemoveGeneralDocument}
          title="Additional Documents"
        />

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            disabled={isSubmitting}
            className="min-w-[140px]"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Worker
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CreateWorkerForm;

// --- COMPONENTS for uploading docs ---
export const TrainingDocUploader: React.FC<{
  trainingId: number;
  onAdd: (trainingId: number, file: File, name: string) => void;
}> = ({ trainingId, onAdd }) => {
  const [file, setFile] = useState<File | null>(null);
  const [name, setName] = useState("");
  const [formError, setFormError] = useState<FormErrors>({});
  const fileInput = useRef<HTMLInputElement>(null);
  return (
    <div className="flex items-center space-x-2">
      <div className="flex flex-col space-y-1">
        <input
          type="file"
          ref={fileInput}
          onChange={(e) => setFile(e.target.files?.[0] || null)}
          className="text-xs"
        />
        {formError.file && (
          <p className="text-xs text-red-500">{formError.file}</p>
        )}
      </div>
      <div className="flex flex-col space-y-1">
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Document name"
          className="text-xs border rounded px-1 py-0.5"
        />
        {formError.name && (
          <p className="text-xs text-red-500">{formError.name}</p>
        )}
      </div>
      <button
        type="submit"
        className="text-xs text-green-600 flex items-center border border-green-600 rounded px-2 py-1"
        onClick={(e) => {
          e.preventDefault();
          if (!file) {
            setFormError({ file: "Please select a file" });
            return;
          }
          if (!name) {
            setFormError({ name: "Please enter a name for the document" });
            return;
          }
          if (file && name) {
            onAdd(trainingId, file, name);
            setFile(null);
            setName("");
            setFormError({});
            fileInput.current!.value = "";
          }
        }}
      >
        <Plus className="h-4 w-4 mr-1" />
        Add
      </button>
    </div>
  );
};

export const GeneralDocUploader: React.FC<{
  onAdd: (file: File, name: string) => void;
}> = ({ onAdd }) => {
  const [file, setFile] = useState<File | null>(null);
  const [name, setName] = useState("");
  const fileInput = useRef<HTMLInputElement>(null);
  const [formError, setFormError] = useState<FormErrors>({});
  return (
    <div className="flex items-center space-x-2">
      <div className="flex flex-col space-y-1">
        <input
          type="file"
          ref={fileInput}
          onChange={(e) => setFile(e.target.files?.[0] || null)}
          className="text-xs"
        />
        {formError.file && (
          <p className="text-xs text-red-500">{formError.file}</p>
        )}
      </div>
      <div className="flex flex-col space-y-1">
        <input
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Document name"
          className="text-xs border rounded px-1 py-0.5"
        />
        {formError.name && (
          <p className="text-xs text-red-500">{formError.name}</p>
        )}
      </div>
      <button
        type="submit"
        className="text-xs text-green-600 flex items-center border border-green-600 rounded px-2 py-1"
        onClick={(e) => {
          e.preventDefault();
          if (!file) {
            setFormError({ file: "Please select a file" });
            return;
          }
          if (!name) {
            setFormError({ name: "Please enter a name for the document" });
            return;
          }
          if (file && name) {
            onAdd(file, name);
            setFile(null);
            setName("");
            setFormError({});
            fileInput.current!.value = "";
          }
        }}
      >
        <Plus className="h-4 w-4 mr-1" />
        Add
      </button>
    </div>
  );
};
