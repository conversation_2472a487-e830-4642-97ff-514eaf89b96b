import React, { useState } from 'react';
import { FileText, AlertTriangle, LayoutDashboard, GraduationCap, Award, Clock, AlertTriangle as AlertIcon, Wrench } from 'lucide-react';
import { VSCodeTab } from '../shared/VSCodeInterface';
import { Worker, TimeLog } from '../../types';
import { CompanyWorker } from '../../data/workers';
import PayrollTab from '../worker/PayrollTab';
// import { FILE_BASE_URL } from '../../utils/constants';
import { PhotoUpload } from '../workers/PhotoUpload';
import { AuditTrail } from '../common/AuditTrail';
import TabContainer, { Tab } from '../data/shared/TabContainer';

interface WorkerTabsProps {
  worker: Worker | CompanyWorker;
  timeLogs?: TimeLog[];
  siteId?: string;
  onPhotoUpload?: (file: File) => Promise<string>;
  onPhotoDelete?: () => Promise<boolean>;
}
// Child component that can safely use hooks
export const WorkerTabContent: React.FC<WorkerTabsProps & { tab: VSCodeTab }> = ({ worker, timeLogs = [], siteId, onPhotoUpload, onPhotoDelete, tab }) => {
  const [activeAboutTab, setActiveAboutTab] = useState('overview');

  const isAssignedToSite = (worker.siteAssignments || []).some(sa => (sa as any).siteId?.toString() === (siteId || ''))
    || (worker.siteAssignments || []).length > 0;

  const complianceSummary = () => {
    // Check if this is a CompanyWorker with enhanced compliance status
    const companyWorker = worker as CompanyWorker;
    if (companyWorker.complianceStatus) {
      switch (companyWorker.complianceStatus) {
        case 'compliant':
          return { label: 'Compliant', color: 'text-green-700 bg-green-50 border-green-200' };
        case 'pending_training':
          return { label: 'Pending Training', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' };
        case 'non_compliant':
          return { label: 'Non-Compliant', color: 'text-red-700 bg-red-50 border-red-200' };
        case 'expired':
          return { label: 'Expired', color: 'text-red-700 bg-red-50 border-red-200' };
        default:
          return { label: 'Unknown', color: 'text-gray-700 bg-gray-50 border-gray-200' };
      }
    }

    // Fallback to legacy compliance calculation
    const totalTrainings = worker.trainings?.length || 0;
    const completed = worker.trainingHistory?.filter((t: any) => t.status === 'Completed').length || 0;
    const expired = worker.trainingHistory?.filter((t: any) => t.status === 'Expired').length || 0;
    if (expired > 0) return { label: 'Expired', color: 'text-red-700 bg-red-50 border-red-200' };
    if (completed < totalTrainings) return { label: 'Pending', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' };
    return { label: 'Compliant', color: 'text-green-700 bg-green-50 border-green-200' };
  };

  const renderAbout = () => {
    return (
      <div className="p-6 space-y-8">
        {/* Summary Header - Flush with page, no cards */}
        <div className="w-full">
          <div className="flex items-start gap-6">
            <PhotoUpload
              workerId={worker?.id || 0}
              currentPhotoUrl={(worker as any)?.profilePictureUrl}
              onPhotoUpload={onPhotoUpload}
              onPhotoDelete={onPhotoDelete}
              mode="overlay"
            />
            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1">
                <div className="text-xl font-semibold text-gray-900">{worker?.name}</div>
                <div className="text-sm font-semibold text-green-600">{(worker.trades && worker.trades[0]?.name) || 'N/A'}</div>
                <div className="text-xs text-gray-500">{worker.company || 'N/A'}</div>
              </div>
              <div className="text-sm text-gray-700 space-y-1">
                <div>Employee ID: {(worker as CompanyWorker).employeeNumber || worker.nationalId || 'N/A'}</div>
                <div>Phone: {worker.phoneNumber || 'N/A'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Flush Form Fields - No cards, just clean form layout */}
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Phone</label>
              <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.phoneNumber || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Email</label>
              <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.email || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Gender</label>
              <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.gender || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Date of Birth</label>
              <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.dateOfBirth ? new Date(worker.dateOfBirth).toLocaleDateString() : 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Trades</label>
              <div className="flex flex-wrap gap-2 border-b border-gray-200 pb-1">
                {(worker.trades || []).map((trade: any) => (
                  <span key={trade.id} className="px-2 py-0.5 rounded bg-blue-100 text-blue-700 text-xs">{trade.name}</span>
                ))}
                {(worker.trades || []).length === 0 && <span className="text-sm text-gray-500">No trades</span>}
              </div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Skills</label>
              <div className="flex flex-wrap gap-2 border-b border-gray-200 pb-1">
                {(worker.skills || []).map((skill: any) => (
                  <span key={skill.id} className="px-2 py-0.5 rounded bg-green-100 text-green-700 text-xs">{skill.name}</span>
                ))}
                {(worker.skills || []).length === 0 && <span className="text-sm text-gray-500">No skills</span>}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Man Hours</label>
              <div className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-1">{worker.manHours?.toLocaleString() || '0'}</div>
            </div>
            {worker.inductionDate && (
              <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Induction Date</label>
                <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{new Date(worker.inductionDate).toLocaleDateString()}</div>
              </div>
            )}
            {worker.medicalCheckDate && (
              <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Medical Check</label>
                <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{new Date(worker.medicalCheckDate).toLocaleDateString()}</div>
              </div>
            )}
          </div>
        </div>

        {/* Embedded Tab Navigation for Worker Functions */}
        <div className="pt-4">
          <TabContainer
            tabs={[
              { 
                id: 'overview', 
                label: 'Overview', 
                icon: <LayoutDashboard className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Current Status</label>
                        <div className="text-sm text-gray-900">{isAssignedToSite ? 'Active on Site' : 'Available for Assignment'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Compliance Status</label>
                        <div className="text-sm text-gray-900">{complianceSummary().label}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Last Active</label>
                        <div className="text-sm text-gray-900">{worker.updatedAt ? new Date(worker.updatedAt).toLocaleDateString() : 'N/A'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Site Assignments</label>
                        <div className="text-sm text-gray-900">{(worker.siteAssignments || []).length} sites</div>
                      </div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'trainings', 
                label: 'Trainings', 
                icon: <GraduationCap className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Trainings</label>
                        <div className="text-lg font-semibold text-gray-900">{worker.trainings?.length || 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Completed</label>
                        <div className="text-lg font-semibold text-green-600">{worker.trainingHistory?.filter((t: any) => t.status === 'Completed').length || 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Expired</label>
                        <div className="text-lg font-semibold text-red-600">{worker.trainingHistory?.filter((t: any) => t.status === 'Expired').length || 0}</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Recent Training</label>
                      <div className="text-sm text-gray-900">
                        {worker.trainingHistory && worker.trainingHistory.length > 0 
                          ? worker.trainingHistory[0].training?.name || 'N/A'
                          : 'No training history'
                        }
                      </div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'certificates', 
                label: 'Certificates', 
                icon: <Award className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Certificates</label>
                        <div className="text-lg font-semibold text-gray-900">{worker.certifications?.length || 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Valid</label>
                        <div className="text-lg font-semibold text-green-600">{worker.certifications?.filter((c: any) => c.status === 'valid').length || 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Expiring Soon</label>
                        <div className="text-lg font-semibold text-yellow-600">{worker.certifications?.filter((c: any) => c.status === 'expiring').length || 0}</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Primary Certification</label>
                      <div className="text-sm text-gray-900">
                        {worker.certifications && worker.certifications.length > 0 
                          ? worker.certifications[0].name
                          : 'No certifications'
                        }
                      </div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'time', 
                label: 'Time', 
                icon: <Clock className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Hours</label>
                        <div className="text-lg font-semibold text-gray-900">{worker.manHours?.toLocaleString() || '0'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">This Month</label>
                        <div className="text-lg font-semibold text-gray-900">{timeLogs.filter(log => new Date(log.date).getMonth() === new Date().getMonth()).reduce((sum, log) => sum + (log.totalHours || 0), 0)} hrs</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Average Daily</label>
                        <div className="text-lg font-semibold text-gray-900">{timeLogs.length > 0 ? (timeLogs.reduce((sum, log) => sum + (log.totalHours || 0), 0) / timeLogs.length).toFixed(1) : '0'} hrs</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Last Clock In</label>
                      <div className="text-sm text-gray-900">
                        {timeLogs.length > 0 
                          ? `${timeLogs[0].date} at ${timeLogs[0].clockIn}`
                          : 'No time records'
                        }
                      </div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'incidents', 
                label: 'Incidents', 
                icon: <AlertIcon className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Incidents</label>
                        <div className="text-lg font-semibold text-gray-900">{Array.isArray((worker as any).incidents) ? (worker as any).incidents.length : 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">This Year</label>
                        <div className="text-lg font-semibold text-gray-900">0</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Last Incident</label>
                        <div className="text-lg font-semibold text-gray-900">None</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Safety Record</label>
                      <div className="text-sm text-gray-900">Clean safety record</div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'tools', 
                label: 'Tools', 
                icon: <Wrench className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Assigned Tools</label>
                        <div className="text-lg font-semibold text-gray-900">0</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Tool Certifications</label>
                        <div className="text-lg font-semibold text-gray-900">0</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Tool Access Level</label>
                      <div className="text-sm text-gray-900">Standard access</div>
                    </div>
                  </div>
                ) 
              },
            ] as Tab[]}
            activeTab={activeAboutTab}
            onTabChange={setActiveAboutTab}
          />
        </div>

        {/* Audit Trail at bottom */}
        <div className="pt-4 border-t border-gray-200">
          <AuditTrail
            entity={{
              createdAt: (worker as any)?.createdAt,
              createdBy: (worker as any)?.createdBy,
              updatedAt: (worker as any)?.updatedAt,
              updatedBy: (worker as any)?.updatedBy,
            }}
            entityType="Worker"
            entityId={(worker?.id as any) || 0}
          />
        </div>
      </div>
    );
  };

  const renderTrainingRecords = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Trainings</h3>
          <button className="text-green-500 hover:text-green-600 text-sm font-medium">+ Record Training</button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Training</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(worker.trainingHistory || []).map((t: any) => (
                <tr key={t.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{t.training?.name || 'Training'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(t.completionDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{t.expiryDate ? new Date(t.expiryDate).toLocaleDateString() : 'No expiry'}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      t.status === 'Completed' ? 'bg-green-100 text-green-800' :
                      t.status === 'Expired' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {t.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-green-500 hover:text-green-600">View</button>
                    <span className="px-2 text-gray-300">|</span>
                    <button className="text-blue-500 hover:text-blue-600">Renew</button>
                  </td>
                </tr>
              ))}
              {(!worker.trainingHistory || worker.trainingHistory.length === 0) && (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <FileText className="h-8 w-8 text-gray-400 mb-2" />
                      <p>No training history recorded yet</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderCertificates = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Certifications</h3>
          <button className="text-green-500 hover:text-green-600 text-sm font-medium">+ Add Certification</button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Certification</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(worker.certifications || []).map((c: any) => (
                <tr key={c.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{c.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(c.issueDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(c.expiryDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      c.status === 'valid' ? 'bg-green-100 text-green-800' :
                      c.status === 'expiring' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {c.status === 'valid' ? 'Valid' : c.status === 'expiring' ? 'Expiring Soon' : 'Expired'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-green-500 hover:text-green-600">View</button>
                    <span className="px-2 text-gray-300">|</span>
                    <button className="text-blue-500 hover:text-blue-600">Renew</button>
                  </td>
                </tr>
              ))}
              {(!worker.certifications || worker.certifications.length === 0) && (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <FileText className="h-8 w-8 text-gray-400 mb-2" />
                      <p>No certifications recorded yet</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderTimeLogs = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Time Logs</h3>
          <div className="flex items-center space-x-3">
            <select className="border border-gray-300 rounded-md text-sm p-2">
              <option value="current-month">Current Month</option>
              <option value="last-month">Last Month</option>
              <option value="custom">Custom Range</option>
            </select>
            <button className="text-green-500 hover:text-green-600 text-sm font-medium">Request Manual Entry</button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock In</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock Out</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Break</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Hours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OT Hours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toolbox Talk</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {timeLogs.map((log) => (
                <tr key={log.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.clockIn}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.clockOut || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.breakDuration} min</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.totalHours} hrs</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.overtime || 0} hrs</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      log.status === 'on-site' ? 'bg-green-100 text-green-800' :
                      log.status === 'late' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {log.status === 'on-site' ? 'On Time' : log.status === 'late' ? 'Late' : 'Absent'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.toolboxTalkAttended ? (
                    <span className="text-green-500">Attended</span>
                  ) : (
                    <span className="text-red-500">Missed</span>
                  )}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderIncidents = () => {
    const hasIncidents = Array.isArray((worker as any).incidents) && (worker as any).incidents.length > 0;
    if (!hasIncidents) {
      return (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6 m-6">
          <div className="text-center py-10">
            <AlertTriangle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Incidents Reported</h3>
            <p className="text-gray-500 max-w-md mx-auto mb-6">This worker has no recorded incidents on this site.</p>
            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-500 hover:bg-green-600">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Report New Incident
            </button>
          </div>
        </div>
      );
    }
    return (
      <div className="p-6">{/* Placeholder for incidents list */}</div>
    );
  };

  const renderPayroll = () => {
    return (
      <div className="p-2">
        <PayrollTab workerId={(worker.id || 0).toString()} workerName={worker.name} />
      </div>
    );
  };

  const renderMedical = () => {
    return (
      <div className="p-6">
        <div className="text-gray-500">Medical records not implemented in mock.</div>
      </div>
    );
  };

  switch (tab.data?.type || tab.type) {
      case 'about':
        return renderAbout();
      case 'training-records':
      case 'training-history':
        return renderTrainingRecords();
      case 'certificates-overview':
      case 'certificate-detail':
        return renderCertificates();
      case 'time-logs':
      case 'time-overview':
      case 'attendance-records':
        return renderTimeLogs();
      case 'incidents-overview':
      case 'incident-detail':
        return renderIncidents();
      case 'payroll-overview':
      case 'pay-history':
      case 'benefits':
        return renderPayroll();
      case 'medical-overview':
      case 'medical-records':
      case 'health-checks':
        return renderMedical();
      default:
        return (
          <div className="p-6">
            <div className="text-center text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">Content Not Available</h3>
              <p>The selected content type is not yet implemented.</p>
            </div>
          </div>
        );
  }
};

// Factory that returns a pure renderer to be used by VSCodeInterface
const WorkerTabs = (props: WorkerTabsProps) => {
  const renderTabContent = (tab: VSCodeTab) => {
    return <WorkerTabContent {...props} tab={tab} />;
  };
  return { renderTabContent };
};

export default WorkerTabs;



import React, { useState } from 'react';
import { FileText, AlertTriangle, Phone as PhoneIcon, LayoutDashboard, GraduationCap, Award, Clock, AlertTriangle as AlertIcon, Wrench } from 'lucide-react';
import { VSCodeTab } from '../shared/VSCodeInterface';
import { Worker, TimeLog } from '../../types';
import { CompanyWorker } from '../../data/workers';
import PayrollTab from '../worker/PayrollTab';
// import { FILE_BASE_URL } from '../../utils/constants';
import { PhotoUpload } from '../workers/PhotoUpload';
import { AuditTrail } from '../common/AuditTrail';
import TabContainer, { Tab } from '../data/shared/TabContainer';

interface WorkerTabsProps {
  worker: Worker | CompanyWorker;
  timeLogs?: TimeLog[];
  siteId?: string;
  onPhotoUpload?: (file: File) => Promise<string>;
  onPhotoDelete?: () => Promise<boolean>;
}
// Child component that can safely use hooks
export const WorkerTabContent: React.FC<WorkerTabsProps & { tab: VSCodeTab }> = ({ worker, timeLogs = [], siteId, onPhotoUpload, onPhotoDelete, tab }) => {
  const [activeAboutTab, setActiveAboutTab] = useState('overview');

  const isAssignedToSite = (worker.siteAssignments || []).some(sa => (sa as any).siteId?.toString() === (siteId || ''))
    || (worker.siteAssignments || []).length > 0;

  const complianceSummary = () => {
    // Check if this is a CompanyWorker with enhanced compliance status
    const companyWorker = worker as CompanyWorker;
    if (companyWorker.complianceStatus) {
      switch (companyWorker.complianceStatus) {
        case 'compliant':
          return { label: 'Compliant', color: 'text-green-700 bg-green-50 border-green-200' };
        case 'pending_training':
          return { label: 'Pending Training', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' };
        case 'non_compliant':
          return { label: 'Non-Compliant', color: 'text-red-700 bg-red-50 border-red-200' };
        case 'expired':
          return { label: 'Expired', color: 'text-red-700 bg-red-50 border-red-200' };
        default:
          return { label: 'Unknown', color: 'text-gray-700 bg-gray-50 border-gray-200' };
      }
    }

    // Fallback to legacy compliance calculation
    const totalTrainings = worker.trainings?.length || 0;
    const completed = worker.trainingHistory?.filter((t: any) => t.status === 'Completed').length || 0;
    const expired = worker.trainingHistory?.filter((t: any) => t.status === 'Expired').length || 0;
    if (expired > 0) return { label: 'Expired', color: 'text-red-700 bg-red-50 border-red-200' };
    if (completed < totalTrainings) return { label: 'Pending', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' };
    return { label: 'Compliant', color: 'text-green-700 bg-green-50 border-green-200' };
  };

  const renderAbout = () => {
    return (
      <div className="p-6 space-y-8">
        {/* Summary Header - Flush with page, no cards */}
        <div className="w-full">
          <div className="flex items-start gap-6">
            <PhotoUpload
              workerId={worker?.id || 0}
              currentPhotoUrl={(worker as any)?.profilePictureUrl}
              onPhotoUpload={onPhotoUpload}
              onPhotoDelete={onPhotoDelete}
              mode="overlay"
            />
            <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-1">
                <div className="text-xl font-semibold text-gray-900">{worker?.name}</div>
                <div className="text-sm text-gray-600">Employee No: {(worker as CompanyWorker).employeeNumber || worker.nationalId || 'N/A'}</div>
                <div className="text-xs text-gray-500">Company: {worker.company || 'N/A'}</div>
              </div>
              <div className="text-sm text-gray-700 space-y-1">
                <div className="flex items-center"><PhoneIcon className="h-4 w-4 text-gray-400 mr-2" /> {worker.phoneNumber || 'N/A'}</div>
                {worker.gender && <div>Gender: {worker.gender}</div>}
                {worker.dateOfBirth && <div>DOB: {new Date(worker.dateOfBirth).toLocaleDateString()}</div>}
              </div>
              <div className="text-sm text-gray-700 space-y-1">
                <div>Date Added: {worker.createdAt ? new Date(worker.createdAt).toLocaleDateString() : 'N/A'}</div>
                <div>Primary Trade: {(worker.trades && worker.trades[0]?.name) || 'N/A'}</div>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-0.5 text-xs rounded border ${isAssignedToSite ? 'text-blue-700 bg-blue-50 border-blue-200' : 'text-gray-700 bg-gray-50 border-gray-200'}`}>
                    {isAssignedToSite ? 'Assigned to site' : 'Available'}
                  </span>
                  <span className={`px-2 py-0.5 text-xs rounded border ${complianceSummary().color}`}>
                    {complianceSummary().label}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Flush Form Fields - No cards, just clean form layout */}
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Phone</label>
              <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.phoneNumber || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Email</label>
              <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.email || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Gender</label>
              <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.gender || 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Date of Birth</label>
              <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.dateOfBirth ? new Date(worker.dateOfBirth).toLocaleDateString() : 'N/A'}</div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Trades</label>
              <div className="flex flex-wrap gap-2 border-b border-gray-200 pb-1">
                {(worker.trades || []).map((trade: any) => (
                  <span key={trade.id} className="px-2 py-0.5 rounded bg-blue-100 text-blue-700 text-xs">{trade.name}</span>
                ))}
                {(worker.trades || []).length === 0 && <span className="text-sm text-gray-500">No trades</span>}
              </div>
            </div>
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Skills</label>
              <div className="flex flex-wrap gap-2 border-b border-gray-200 pb-1">
                {(worker.skills || []).map((skill: any) => (
                  <span key={skill.id} className="px-2 py-0.5 rounded bg-green-100 text-green-700 text-xs">{skill.name}</span>
                ))}
                {(worker.skills || []).length === 0 && <span className="text-sm text-gray-500">No skills</span>}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="space-y-2">
              <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Man Hours</label>
              <div className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-1">{worker.manHours?.toLocaleString() || '0'}</div>
            </div>
            {worker.inductionDate && (
              <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Induction Date</label>
                <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{new Date(worker.inductionDate).toLocaleDateString()}</div>
              </div>
            )}
            {worker.medicalCheckDate && (
              <div className="space-y-2">
                <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Medical Check</label>
                <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{new Date(worker.medicalCheckDate).toLocaleDateString()}</div>
              </div>
            )}
          </div>
        </div>

        {/* Embedded Tab Navigation for Worker Functions */}
        <div className="pt-4">
          <TabContainer
            tabs={[
              { 
                id: 'overview', 
                label: 'Overview', 
                icon: <LayoutDashboard className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Current Status</label>
                        <div className="text-sm text-gray-900">{isAssignedToSite ? 'Active on Site' : 'Available for Assignment'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Compliance Status</label>
                        <div className="text-sm text-gray-900">{complianceSummary().label}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Last Active</label>
                        <div className="text-sm text-gray-900">{worker.updatedAt ? new Date(worker.updatedAt).toLocaleDateString() : 'N/A'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Site Assignments</label>
                        <div className="text-sm text-gray-900">{(worker.siteAssignments || []).length} sites</div>
                      </div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'trainings', 
                label: 'Trainings', 
                icon: <GraduationCap className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Trainings</label>
                        <div className="text-lg font-semibold text-gray-900">{worker.trainings?.length || 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Completed</label>
                        <div className="text-lg font-semibold text-green-600">{worker.trainingHistory?.filter((t: any) => t.status === 'Completed').length || 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Expired</label>
                        <div className="text-lg font-semibold text-red-600">{worker.trainingHistory?.filter((t: any) => t.status === 'Expired').length || 0}</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Recent Training</label>
                      <div className="text-sm text-gray-900">
                        {worker.trainingHistory && worker.trainingHistory.length > 0 
                          ? worker.trainingHistory[0].training?.name || 'N/A'
                          : 'No training history'
                        }
                      </div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'certificates', 
                label: 'Certificates', 
                icon: <Award className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Certificates</label>
                        <div className="text-lg font-semibold text-gray-900">{worker.certifications?.length || 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Valid</label>
                        <div className="text-lg font-semibold text-green-600">{worker.certifications?.filter((c: any) => c.status === 'valid').length || 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Expiring Soon</label>
                        <div className="text-lg font-semibold text-yellow-600">{worker.certifications?.filter((c: any) => c.status === 'expiring').length || 0}</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Primary Certification</label>
                      <div className="text-sm text-gray-900">
                        {worker.certifications && worker.certifications.length > 0 
                          ? worker.certifications[0].name
                          : 'No certifications'
                        }
                      </div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'time', 
                label: 'Time', 
                icon: <Clock className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Hours</label>
                        <div className="text-lg font-semibold text-gray-900">{worker.manHours?.toLocaleString() || '0'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">This Month</label>
                        <div className="text-lg font-semibold text-gray-900">{timeLogs.filter(log => new Date(log.date).getMonth() === new Date().getMonth()).reduce((sum, log) => sum + (log.totalHours || 0), 0)} hrs</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Average Daily</label>
                        <div className="text-lg font-semibold text-gray-900">{timeLogs.length > 0 ? (timeLogs.reduce((sum, log) => sum + (log.totalHours || 0), 0) / timeLogs.length).toFixed(1) : '0'} hrs</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Last Clock In</label>
                      <div className="text-sm text-gray-900">
                        {timeLogs.length > 0 
                          ? `${timeLogs[0].date} at ${timeLogs[0].clockIn}`
                          : 'No time records'
                        }
                      </div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'incidents', 
                label: 'Incidents', 
                icon: <AlertIcon className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Incidents</label>
                        <div className="text-lg font-semibold text-gray-900">{Array.isArray((worker as any).incidents) ? (worker as any).incidents.length : 0}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">This Year</label>
                        <div className="text-lg font-semibold text-gray-900">0</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Last Incident</label>
                        <div className="text-lg font-semibold text-gray-900">None</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Safety Record</label>
                      <div className="text-sm text-gray-900">Clean safety record</div>
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'tools', 
                label: 'Tools', 
                icon: <Wrench className="h-4 w-4" />, 
                content: (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Assigned Tools</label>
                        <div className="text-lg font-semibold text-gray-900">0</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Tool Certifications</label>
                        <div className="text-lg font-semibold text-gray-900">0</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Tool Access Level</label>
                      <div className="text-sm text-gray-900">Standard access</div>
                    </div>
                  </div>
                ) 
              },
            ] as Tab[]}
            activeTab={activeAboutTab}
            onTabChange={setActiveAboutTab}
          />
        </div>

        {/* Audit Trail at bottom */}
        <div className="pt-4 border-t border-gray-200">
          <AuditTrail
            entity={{
              createdAt: (worker as any)?.createdAt,
              createdBy: (worker as any)?.createdBy,
              updatedAt: (worker as any)?.updatedAt,
              updatedBy: (worker as any)?.updatedBy,
            }}
            entityType="Worker"
            entityId={(worker?.id as any) || 0}
          />
        </div>
      </div>
    );
  };

  const renderTrainingRecords = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Trainings</h3>
          <button className="text-green-500 hover:text-green-600 text-sm font-medium">+ Record Training</button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Training</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(worker.trainingHistory || []).map((t: any) => (
                <tr key={t.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{t.training?.name || 'Training'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(t.completionDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{t.expiryDate ? new Date(t.expiryDate).toLocaleDateString() : 'No expiry'}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      t.status === 'Completed' ? 'bg-green-100 text-green-800' :
                      t.status === 'Expired' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {t.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-green-500 hover:text-green-600">View</button>
                    <span className="px-2 text-gray-300">|</span>
                    <button className="text-blue-500 hover:text-blue-600">Renew</button>
                  </td>
                </tr>
              ))}
              {(!worker.trainingHistory || worker.trainingHistory.length === 0) && (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <FileText className="h-8 w-8 text-gray-400 mb-2" />
                      <p>No training history recorded yet</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderCertificates = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Certifications</h3>
          <button className="text-green-500 hover:text-green-600 text-sm font-medium">+ Add Certification</button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Certification</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(worker.certifications || []).map((c: any) => (
                <tr key={c.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{c.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(c.issueDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(c.expiryDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      c.status === 'valid' ? 'bg-green-100 text-green-800' :
                      c.status === 'expiring' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {c.status === 'valid' ? 'Valid' : c.status === 'expiring' ? 'Expiring Soon' : 'Expired'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-green-500 hover:text-green-600">View</button>
                    <span className="px-2 text-gray-300">|</span>
                    <button className="text-blue-500 hover:text-blue-600">Renew</button>
                  </td>
                </tr>
              ))}
              {(!worker.certifications || worker.certifications.length === 0) && (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <FileText className="h-8 w-8 text-gray-400 mb-2" />
                      <p>No certifications recorded yet</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderTimeLogs = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Time Logs</h3>
          <div className="flex items-center space-x-3">
            <select className="border border-gray-300 rounded-md text-sm p-2">
              <option value="current-month">Current Month</option>
              <option value="last-month">Last Month</option>
              <option value="custom">Custom Range</option>
            </select>
            <button className="text-green-500 hover:text-green-600 text-sm font-medium">Request Manual Entry</button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock In</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock Out</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Break</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Hours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OT Hours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toolbox Talk</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {timeLogs.map((log) => (
                <tr key={log.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.clockIn}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.clockOut || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.breakDuration} min</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.totalHours} hrs</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.overtime || 0} hrs</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      log.status === 'on-site' ? 'bg-green-100 text-green-800' :
                      log.status === 'late' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {log.status === 'on-site' ? 'On Time' : log.status === 'late' ? 'Late' : 'Absent'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.toolboxTalkAttended ? (
                    <span className="text-green-500">Attended</span>
                  ) : (
                    <span className="text-red-500">Missed</span>
                  )}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderIncidents = () => {
    const hasIncidents = Array.isArray((worker as any).incidents) && (worker as any).incidents.length > 0;
    if (!hasIncidents) {
      return (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6 m-6">
          <div className="text-center py-10">
            <AlertTriangle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Incidents Reported</h3>
            <p className="text-gray-500 max-w-md mx-auto mb-6">This worker has no recorded incidents on this site.</p>
            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-500 hover:bg-green-600">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Report New Incident
            </button>
          </div>
        </div>
      );
    }
    return (
      <div className="p-6">{/* Placeholder for incidents list */}</div>
    );
  };

  const renderPayroll = () => {
    return (
      <div className="p-2">
        <PayrollTab workerId={(worker.id || 0).toString()} workerName={worker.name} />
      </div>
    );
  };

  const renderMedical = () => {
    return (
      <div className="p-6">
        <div className="text-gray-500">Medical records not implemented in mock.</div>
      </div>
    );
  };

  switch (tab.data?.type || tab.type) {
      case 'about':
        return renderAbout();
      case 'training-records':
      case 'training-history':
        return renderTrainingRecords();
      case 'certificates-overview':
      case 'certificate-detail':
        return renderCertificates();
      case 'time-logs':
      case 'time-overview':
      case 'attendance-records':
        return renderTimeLogs();
      case 'incidents-overview':
      case 'incident-detail':
        return renderIncidents();
      case 'payroll-overview':
      case 'pay-history':
      case 'benefits':
        return renderPayroll();
      case 'medical-overview':
      case 'medical-records':
      case 'health-checks':
        return renderMedical();
      default:
        return (
          <div className="p-6">
            <div className="text-center text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">Content Not Available</h3>
              <p>The selected content type is not yet implemented.</p>
            </div>
          </div>
        );
  }
};

// Factory that returns a pure renderer to be used by VSCodeInterface
const WorkerTabs = (props: WorkerTabsProps) => {
  const renderTabContent = (tab: VSCodeTab) => {
    return <WorkerTabContent {...props} tab={tab} />;
  };
  return { renderTabContent };
};

export default WorkerTabs;


