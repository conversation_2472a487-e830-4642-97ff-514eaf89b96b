import React, { useState } from 'react';
import { FileText, Alert<PERSON>riangle, Plus, Eye, Edit, Trash2, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { VSCodeTab } from '../shared/VSCodeInterface';
import { Worker, TimeLog } from '../../types';
import { CompanyWorker } from '../../data/workers';
import PayrollTab from '../worker/PayrollTab';
// import { FILE_BASE_URL } from '../../utils/constants';
import { PhotoUpload } from '../workers/PhotoUpload';
// import { AuditTrail } from '../common/AuditTrail';
import TabContainer, { Tab } from '../data/shared/TabContainer';

interface WorkerTabsProps {
  worker: Worker | CompanyWorker;
  timeLogs?: TimeLog[];
  siteId?: string;
  onPhotoUpload?: (file: File) => Promise<string>;
  onPhotoDelete?: () => Promise<boolean>;
}

// Reusable table components
const DataTable: React.FC<{
  title: string;
  addButtonText: string;
  onAdd: () => void;
  children: React.ReactNode;
  loading?: boolean;
}> = ({ title, addButtonText, onAdd, children, loading = false }) => (
  <div className="space-y-4">
    <div className="flex justify-between items-center">
      <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      <button
        onClick={onAdd}
        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
      >
        <Plus className="h-4 w-4 mr-1" />
        {addButtonText}
      </button>
    </div>
    
    {loading ? (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      </div>
    ) : (
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        {children}
      </div>
    )}
  </div>
);

const EmptyState: React.FC<{ message: string; icon?: React.ReactNode }> = ({ message, icon = <FileText className="h-8 w-8 text-gray-400" /> }) => (
  <div className="text-center py-12">
    <div className="flex justify-center mb-4">
      {icon}
    </div>
    <p className="text-gray-500">{message}</p>
  </div>
);

const StatusBadge: React.FC<{ status: string; variant?: 'success' | 'warning' | 'error' | 'info' }> = ({ status, variant = 'info' }) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getVariantClasses()}`}>
      {status}
    </span>
  );
};

const ActionButton: React.FC<{
  onClick: () => void;
  variant: 'view' | 'edit' | 'delete';
  disabled?: boolean;
}> = ({ onClick, variant, disabled = false }) => {
  const getButtonProps = () => {
    switch (variant) {
      case 'view':
        return {
          icon: <Eye className="h-4 w-4" />,
          className: 'text-blue-600 hover:text-blue-800',
          title: 'View'
        };
      case 'edit':
        return {
          icon: <Edit className="h-4 w-4" />,
          className: 'text-green-600 hover:text-green-800',
          title: 'Edit'
        };
      case 'delete':
        return {
          icon: <Trash2 className="h-4 w-4" />,
          className: 'text-red-600 hover:text-red-800',
          title: 'Delete'
        };
    }
  };

  const props = getButtonProps();

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`p-1 rounded hover:bg-gray-100 transition-colors ${props.className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      title={props.title}
    >
      {props.icon}
    </button>
  );
};

// Child component that can safely use hooks
export const WorkerTabContent: React.FC<WorkerTabsProps & { tab: VSCodeTab }> = ({ worker, timeLogs = [], siteId, onPhotoUpload, onPhotoDelete, tab }) => {
  const [activeAboutTab, setActiveAboutTab] = useState('overview');

  const isAssignedToSite = (worker.siteAssignments || []).some(sa => (sa as any).siteId?.toString() === (siteId || ''))
    || (worker.siteAssignments || []).length > 0;

  const complianceSummary = () => {
    // Check if this is a CompanyWorker with enhanced compliance status
    const companyWorker = worker as CompanyWorker;
    if (companyWorker.complianceStatus) {
      switch (companyWorker.complianceStatus) {
        case 'compliant':
          return { label: 'Compliant', color: 'text-green-700 bg-green-50 border-green-200' };
        case 'pending_training':
          return { label: 'Pending Training', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' };
        case 'non_compliant':
          return { label: 'Non-Compliant', color: 'text-red-700 bg-red-50 border-red-200' };
        case 'expired':
          return { label: 'Expired', color: 'text-red-700 bg-red-50 border-red-200' };
        default:
          return { label: 'Unknown', color: 'text-gray-700 bg-gray-50 border-gray-200' };
      }
    }

    // Fallback to legacy compliance calculation
    const totalTrainings = worker.trainings?.length || 0;
    const completed = worker.trainingHistory?.filter((t: any) => t.status === 'Completed').length || 0;
    const expired = worker.trainingHistory?.filter((t: any) => t.status === 'Expired').length || 0;
    if (expired > 0) return { label: 'Expired', color: 'text-red-700 bg-red-50 border-red-200' };
    if (completed < totalTrainings) return { label: 'Pending', color: 'text-yellow-700 bg-yellow-50 border-yellow-200' };
    return { label: 'Compliant', color: 'text-green-700 bg-green-50 border-green-200' };
  };

  // Handler functions for table actions
  const handleAddTraining = () => console.log('Add training');
  const handleAddCertificate = () => console.log('Add certificate');
  const handleAddAttendance = () => console.log('Add attendance record');
  const handleReportIncident = () => console.log('Report incident');
  const handleAssignTool = () => console.log('Assign tool');

  const getTrainingStatus = (training: any) => {
    if (!training.expiryDate) return { status: 'Valid', variant: 'success' as const };
    const expiryDate = new Date(training.expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 3600 * 24));
    
    if (daysUntilExpiry < 0) return { status: 'Expired', variant: 'error' as const };
    if (daysUntilExpiry <= 30) return { status: 'Expiring', variant: 'warning' as const };
    return { status: 'Valid', variant: 'success' as const };
  };

  const getAttendanceStatus = (log: TimeLog) => {
    switch (log.status) {
      case 'on-site':
        return { status: 'On-time', variant: 'success' as const };
      case 'late':
        return { status: 'Late', variant: 'warning' as const };
      default:
        return { status: 'Absent', variant: 'error' as const };
    }
  };

  const renderAbout = () => {
    return (
      <div className="p-6 space-y-8">
        {/* Summary Header - Flush with page, no cards */}
        <div className="w-full">
          <div className="flex items-start gap-6">
            <PhotoUpload
              workerId={worker?.id || 0}
              currentPhotoUrl={(worker as any)?.photoUrl}
              onPhotoUpload={onPhotoUpload}
              onPhotoDelete={onPhotoDelete}
              mode="overlay"
            />
            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-1">
                <div className="text-xl font-semibold text-gray-900">{worker?.name}</div>
                <div className="text-sm font-semibold text-green-600">{(worker.trades && worker.trades[0]?.name) || 'N/A'}</div>
                <div className="text-xs text-gray-500">{worker.company || 'N/A'}</div>
              </div>
              <div className="text-sm text-gray-700 space-y-1">
                <div>Employee ID: {(worker as CompanyWorker).employeeNumber || worker.nationalId || 'N/A'}</div>
                <div>Phone: {worker.phoneNumber || 'N/A'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Embedded Tab Navigation for Worker Functions - moved directly under summary */}
        <div className="pt-4">
          <TabContainer
            tabs={[
              { 
                id: 'overview', 
                label: 'Overview', 
                content: (
                  <div className="space-y-8">
                    {/* Overview quick stats */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Current Status</label>
                        <div className="text-sm text-gray-900">{isAssignedToSite ? 'Active on Site' : 'Available for Assignment'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Compliance Status</label>
                        <div className="text-sm text-gray-900">{complianceSummary().label}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Last Active</label>
                        <div className="text-sm text-gray-900">{worker.updatedAt ? new Date(worker.updatedAt).toLocaleDateString() : 'N/A'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Site Assignments</label>
                        <div className="text-sm text-gray-900">{(worker.siteAssignments || []).length} sites</div>
                      </div>
                    </div>

                    {/* Core profile fields */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Phone</label>
                        <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.phoneNumber || 'N/A'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Email</label>
                        <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{worker.email || 'N/A'}</div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Trades</label>
                        <div className="flex flex-wrap gap-2 border-b border-gray-200 pb-1">
                          {(worker.trades || []).map((trade: any) => (
                            <span key={trade.id} className="px-2 py-0.5 rounded bg-blue-100 text-blue-700 text-xs">{trade.name}</span>
                          ))}
                          {(worker.trades || []).length === 0 && <span className="text-sm text-gray-500">No trades</span>}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Skills</label>
                        <div className="flex flex-wrap gap-2 border-b border-gray-200 pb-1">
                          {(worker.skills || []).map((skill: any) => (
                            <span key={skill.id} className="px-2 py-0.5 rounded bg-green-100 text-green-700 text-xs">{skill.name}</span>
                          ))}
                          {(worker.skills || []).length === 0 && <span className="text-sm text-gray-500">No skills</span>}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Total Man Hours</label>
                        <div className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-1">{worker.manHours?.toLocaleString() || '0'}</div>
                      </div>
                      {worker.inductionDate && (
                        <div className="space-y-2">
                          <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Induction Date</label>
                          <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{new Date(worker.inductionDate).toLocaleDateString()}</div>
                        </div>
                      )}
                      {worker.medicalCheckDate && (
                        <div className="space-y-2">
                          <label className="block text-xs font-medium text-gray-500 uppercase tracking-wide">Medical Check</label>
                          <div className="text-sm text-gray-900 border-b border-gray-200 pb-1">{new Date(worker.medicalCheckDate).toLocaleDateString()}</div>
                        </div>
                      )}
                    </div>
                  </div>
                ) 
              },
              { 
                id: 'trainings', 
                label: 'Trainings', 
                content: (
                  <DataTable
                    title="Training Records"
                    addButtonText="Add Training"
                    onAdd={handleAddTraining}
                  >
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {(worker.trainingHistory || []).map((training: any) => {
                            const statusInfo = getTrainingStatus(training);
                            return (
                              <tr key={training.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {training.training?.name || 'Training'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {training.completionDate ? new Date(training.completionDate).toLocaleDateString() : 'N/A'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {training.expiryDate ? new Date(training.expiryDate).toLocaleDateString() : 'No expiry'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <StatusBadge status={statusInfo.status} variant={statusInfo.variant} />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                  <ActionButton variant="view" onClick={() => console.log('View training', training.id)} />
                                  <ActionButton variant="edit" onClick={() => console.log('Edit training', training.id)} />
                                  <ActionButton variant="delete" onClick={() => console.log('Delete training', training.id)} />
                                </td>
                              </tr>
                            );
                          })}
                          {(!worker.trainingHistory || worker.trainingHistory.length === 0) && (
                            <tr>
                              <td colSpan={5} className="px-6 py-8">
                                <EmptyState message="No training records found" icon={<FileText className="h-8 w-8 text-gray-400" />} />
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </DataTable>
                ) 
              },
              { 
                id: 'certificates', 
                label: 'Certificates', 
                content: (
                  <DataTable
                    title="Certificates"
                    addButtonText="Add Certificate"
                    onAdd={handleAddCertificate}
                  >
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {(worker.certifications || []).map((cert: any) => {
                            const statusInfo = getTrainingStatus(cert);
                            return (
                              <tr key={cert.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                  {cert.name}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {cert.issueDate ? new Date(cert.issueDate).toLocaleDateString() : 'N/A'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {cert.expiryDate ? new Date(cert.expiryDate).toLocaleDateString() : 'No expiry'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <StatusBadge status={statusInfo.status} variant={statusInfo.variant} />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                  <ActionButton variant="view" onClick={() => console.log('View certificate', cert.id)} />
                                  <ActionButton variant="edit" onClick={() => console.log('Edit certificate', cert.id)} />
                                  <ActionButton variant="delete" onClick={() => console.log('Delete certificate', cert.id)} />
                                </td>
                              </tr>
                            );
                          })}
                          {(!worker.certifications || worker.certifications.length === 0) && (
                            <tr>
                              <td colSpan={5} className="px-6 py-8">
                                <EmptyState message="No certificates found" icon={<FileText className="h-8 w-8 text-gray-400" />} />
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </DataTable>
                ) 
              },
              { 
                id: 'time', 
                label: 'Attendance', 
                content: (
                  <DataTable
                    title="Attendance Records"
                    addButtonText="Add Record"
                    onAdd={handleAddAttendance}
                  >
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock In</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock Out</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Hours</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OT Hours</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toolbox</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {timeLogs.map((log) => {
                            const statusInfo = getAttendanceStatus(log);
                            return (
                              <tr key={log.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.date}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.clockIn}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.clockOut || '-'}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.totalHours} hrs</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.overtime || 0} hrs</td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <StatusBadge status={statusInfo.status} variant={statusInfo.variant} />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {log.toolboxTalkAttended ? (
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                  ) : (
                                    <XCircle className="h-4 w-4 text-red-500" />
                                  )}
                                </td>
                              </tr>
                            );
                          })}
                          {timeLogs.length === 0 && (
                            <tr>
                              <td colSpan={7} className="px-6 py-8">
                                <EmptyState message="No attendance records found" icon={<Clock className="h-8 w-8 text-gray-400" />} />
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </DataTable>
                ) 
              },
              { 
                id: 'incidents', 
                label: 'Incidents', 
                content: (
                  <DataTable
                    title="Safety Incidents"
                    addButtonText="Report Incident"
                    onAdd={handleReportIncident}
                  >
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Severity</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {Array.isArray((worker as any).incidents) && (worker as any).incidents.length > 0 ? (
                            (worker as any).incidents.map((incident: any) => (
                              <tr key={incident.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {incident.date ? new Date(incident.date).toLocaleDateString() : 'N/A'}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{incident.type || 'N/A'}</td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <StatusBadge 
                                    status={incident.severity || 'Unknown'} 
                                    variant={incident.severity === 'High' ? 'error' : incident.severity === 'Medium' ? 'warning' : 'info'} 
                                  />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <StatusBadge 
                                    status={incident.status || 'Open'} 
                                    variant={incident.status === 'Closed' ? 'success' : 'warning'} 
                                  />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                  <ActionButton variant="view" onClick={() => console.log('View incident', incident.id)} />
                                  <ActionButton variant="edit" onClick={() => console.log('Edit incident', incident.id)} />
                                  <ActionButton variant="delete" onClick={() => console.log('Delete incident', incident.id)} />
                                </td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan={5} className="px-6 py-8">
                                <EmptyState message="No safety incidents reported" icon={<AlertTriangle className="h-8 w-8 text-gray-400" />} />
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </DataTable>
                ) 
              },
              { 
                id: 'tools', 
                label: 'Tools', 
                content: (
                  <DataTable
                    title="Assigned Tools & Equipment"
                    addButtonText="Assign Tool"
                    onAdd={handleAssignTool}
                  >
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial #</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned Date</th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {/* Mock data for tools - replace with actual data when available */}
                          {[
                            { id: 1, name: 'Safety Helmet', type: 'PPE', serialNumber: 'HELM-001', status: 'Active', assignedDate: '2024-12-01' },
                            { id: 2, name: 'Safety Boots', type: 'PPE', serialNumber: 'BOOT-002', status: 'Active', assignedDate: '2024-12-01' },
                            { id: 3, name: 'Drill Machine', type: 'Equipment', serialNumber: 'DRILL-003', status: 'Active', assignedDate: '2024-12-05' }
                          ].map((tool) => (
                            <tr key={tool.id} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{tool.name}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{tool.type}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{tool.serialNumber}</td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <StatusBadge 
                                  status={tool.status} 
                                  variant={tool.status === 'Active' ? 'success' : tool.status === 'Returned' ? 'info' : 'error'} 
                                />
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(tool.assignedDate).toLocaleDateString()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <ActionButton variant="view" onClick={() => console.log('View tool', tool.id)} />
                                <ActionButton variant="edit" onClick={() => console.log('Edit tool', tool.id)} />
                                <ActionButton variant="delete" onClick={() => console.log('Delete tool', tool.id)} />
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </DataTable>
                ) 
              },
            ] as Tab[]}
            activeTab={activeAboutTab}
            onTabChange={setActiveAboutTab}
          />
        </div>

        {/* Audit info removed for cleaner form-focused UI */}
      </div>
    );
  };

  const renderTrainingRecords = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Trainings</h3>
          <button className="text-green-500 hover:text-green-600 text-sm font-medium">+ Record Training</button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Training</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(worker.trainingHistory || []).map((t: any) => (
                <tr key={t.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{t.training?.name || 'Training'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(t.completionDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{t.expiryDate ? new Date(t.expiryDate).toLocaleDateString() : 'No expiry'}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      t.status === 'Completed' ? 'bg-green-100 text-green-800' :
                      t.status === 'Expired' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {t.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-green-500 hover:text-green-600">View</button>
                    <span className="px-2 text-gray-300">|</span>
                    <button className="text-blue-500 hover:text-blue-600">Renew</button>
                  </td>
                </tr>
              ))}
              {(!worker.trainingHistory || worker.trainingHistory.length === 0) && (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <FileText className="h-8 w-8 text-gray-400 mb-2" />
                      <p>No training history recorded yet</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderCertificates = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Certifications</h3>
          <button className="text-green-500 hover:text-green-600 text-sm font-medium">+ Add Certification</button>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Certification</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(worker.certifications || []).map((c: any) => (
                <tr key={c.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{c.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(c.issueDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{new Date(c.expiryDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      c.status === 'valid' ? 'bg-green-100 text-green-800' :
                      c.status === 'expiring' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {c.status === 'valid' ? 'Valid' : c.status === 'expiring' ? 'Expiring Soon' : 'Expired'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button className="text-green-500 hover:text-green-600">View</button>
                    <span className="px-2 text-gray-300">|</span>
                    <button className="text-blue-500 hover:text-blue-600">Renew</button>
                  </td>
                </tr>
              ))}
              {(!worker.certifications || worker.certifications.length === 0) && (
                <tr>
                  <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <FileText className="h-8 w-8 text-gray-400 mb-2" />
                      <p>No certifications recorded yet</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderTimeLogs = () => {
    return (
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden m-6">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">Time Logs</h3>
          <div className="flex items-center space-x-3">
            <select className="border border-gray-300 rounded-md text-sm p-2">
              <option value="current-month">Current Month</option>
              <option value="last-month">Last Month</option>
              <option value="custom">Custom Range</option>
            </select>
            <button className="text-green-500 hover:text-green-600 text-sm font-medium">Request Manual Entry</button>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock In</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock Out</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Break</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Hours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OT Hours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toolbox Talk</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {timeLogs.map((log) => (
                <tr key={log.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{log.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.clockIn}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.clockOut || '-'}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.breakDuration} min</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.totalHours} hrs</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.overtime || 0} hrs</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      log.status === 'on-site' ? 'bg-green-100 text-green-800' :
                      log.status === 'late' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {log.status === 'on-site' ? 'On Time' : log.status === 'late' ? 'Late' : 'Absent'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{log.toolboxTalkAttended ? (
                    <span className="text-green-500">Attended</span>
                  ) : (
                    <span className="text-red-500">Missed</span>
                  )}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderIncidents = () => {
    const hasIncidents = Array.isArray((worker as any).incidents) && (worker as any).incidents.length > 0;
    if (!hasIncidents) {
      return (
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6 m-6">
          <div className="text-center py-10">
            <AlertTriangle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Incidents Reported</h3>
            <p className="text-gray-500 max-w-md mx-auto mb-6">This worker has no recorded incidents on this site.</p>
            <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-500 hover:bg-green-600">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Report New Incident
            </button>
          </div>
        </div>
      );
    }
    return (
      <div className="p-6">{/* Placeholder for incidents list */}</div>
    );
  };

  const renderPayroll = () => {
    return (
      <div className="p-2">
        <PayrollTab workerId={(worker.id || 0).toString()} workerName={worker.name} />
      </div>
    );
  };

  const renderMedical = () => {
    return (
      <div className="p-6">
        <div className="text-gray-500">Medical records not implemented in mock.</div>
      </div>
    );
  };

  switch (tab.data?.type || tab.type) {
      case 'about':
        return renderAbout();
      case 'training-records':
      case 'training-history':
        return renderTrainingRecords();
      case 'certificates-overview':
      case 'certificate-detail':
        return renderCertificates();
      case 'time-logs':
      case 'time-overview':
      case 'attendance-records':
        return renderTimeLogs();
      case 'incidents-overview':
      case 'incident-detail':
        return renderIncidents();
      case 'payroll-overview':
      case 'pay-history':
      case 'benefits':
        return renderPayroll();
      case 'medical-overview':
      case 'medical-records':
      case 'health-checks':
        return renderMedical();
      default:
        return (
          <div className="p-6">
            <div className="text-center text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">Content Not Available</h3>
              <p>The selected content type is not yet implemented.</p>
            </div>
          </div>
        );
  }
};

// Factory that returns a pure renderer to be used by VSCodeInterface
const WorkerTabs = (props: WorkerTabsProps) => {
  const renderTabContent = (tab: VSCodeTab) => {
    return <WorkerTabContent {...props} tab={tab} />;
  };
  return { renderTabContent };
};

export default WorkerTabs;


