import React, { useState } from 'react';
import { Plus } from 'lucide-react';
import CreateWorkerForm from './CreateWorkerForm';
import { toast } from 'react-toastify';

const CreateWorkerDemo: React.FC = () => {
  const [showForm, setShowForm] = useState(false);

  const handleSuccess = () => {
    toast.success('Worker created successfully');
    setShowForm(false);
  }

  const handleCancel = () => {
    setShowForm(false);
  };

  if (showForm) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <CreateWorkerForm
            onCancel={handleCancel}
            useDummyData={true} // Using dummy data as requested
            onSuccess={handleSuccess}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Worker Management System
          </h1>
          <p className="text-gray-600 mb-8">
            Create and manage workers with comprehensive validation and photo upload capabilities.
          </p>
          
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 text-lg font-medium"
          >
            <Plus className="h-5 w-5 mr-2" />
            Create New Worker
          </button>

          <div className="mt-12 text-left">
            <h2 className="text-xl font-semibold mb-4">Form Features:</h2>
            <ul className="space-y-2 text-gray-600">
              <li>• Comprehensive form validation for all required fields</li>
              <li>• Photo upload with file type and size validation</li>
              <li>• Take photo button (placeholder functionality)</li>
              <li>• Multi-select dropdowns for Skills, Training, and Trades</li>
              <li>• Real-time validation feedback</li>
              <li>• GraphQL integration with fallback to dummy data</li>
              <li>• Responsive design with Tailwind CSS</li>
              <li>• Loading states and error handling</li>
            </ul>
          </div>

          <div className="mt-8 text-left">
            <h2 className="text-xl font-semibold mb-4">Validation Rules:</h2>
            <ul className="space-y-2 text-gray-600">
              <li>• Name, Company, National ID, Gender, and Phone Number are required</li>
              <li>• National ID must be 8-12 digits</li>
              <li>• Phone number format validation</li>
              <li>• Email format validation (when provided)</li>
              <li>• Age must be between 16 and 80 years</li>
              <li>• Induction and Medical Check dates cannot be in the future</li>
              <li>• Photo files must be images under 10MB</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateWorkerDemo;
