import React from 'react';
import { Settings } from 'lucide-react';
import PlaceholderStep from './PlaceholderStep';

interface IntegrationStepProps {
  data: any;
  onComplete: (data: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}







const IntegrationStep: React.FC<IntegrationStepProps> = (props) => {
  return (
    <PlaceholderStep
      {...props}
      title="Technology Integration Setup"
      description="Configure Hikvision devices, test connectivity, set attendance rules, and connect external systems"
      icon={Settings}
    />
  );
};

export default IntegrationStep;
