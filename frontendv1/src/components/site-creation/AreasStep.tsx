import React, { useState, useEffect } from 'react';
import { Grid3X3, Plus, X, Edit2, Check } from 'lucide-react';

interface AreasData {
  areaNames: string[];
}

interface AreasStepProps {
  data: AreasData;
  onComplete: (data: AreasData) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  onCreateSite?: () => void;
  isCreating?: boolean;
}

const SUGGESTED_AREAS = [
  'Main Building',
  'Parking Area',
  'Storage Zone',
  'Office Block',
  'Workshop',
  'Security Gate',
  'Loading Bay',
  'Equipment Yard',
  'Waste Management Area',
  'Emergency Assembly Point'
];

const AreasStep: React.FC<AreasStepProps> = ({
  data,
  onComplete
}) => {
  const [formData, setFormData] = useState<AreasData>(data);
  const [newAreaName, setNewAreaName] = useState('');
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingName, setEditingName] = useState('');

  const handleAddArea = () => {
    if (newAreaName.trim() && !formData.areaNames.includes(newAreaName.trim())) {
      const updatedData = {
        ...formData,
        areaNames: [...formData.areaNames, newAreaName.trim()]
      };
      setFormData(updatedData);
      setNewAreaName('');
    }
  };

  const handleRemoveArea = (index: number) => {
    const updatedData = {
      ...formData,
      areaNames: formData.areaNames.filter((_, i) => i !== index)
    };
    setFormData(updatedData);
  };

  const handleEditArea = (index: number) => {
    setEditingIndex(index);
    setEditingName(formData.areaNames[index]);
  };

  const handleSaveEdit = () => {
    if (editingIndex !== null && editingName.trim()) {
      const updatedData = {
        ...formData,
        areaNames: formData.areaNames.map((name, i) => 
          i === editingIndex ? editingName.trim() : name
        )
      };
      setFormData(updatedData);
      setEditingIndex(null);
      setEditingName('');
    }
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditingName('');
  };

  const handleAddSuggestedArea = (areaName: string) => {
    if (!formData.areaNames.includes(areaName)) {
      const updatedData = {
        ...formData,
        areaNames: [...formData.areaNames, areaName]
      };
      setFormData(updatedData);
    }
  };

  // Auto-save on data change
  useEffect(() => {
    const timer = setTimeout(() => {
      onComplete(formData);
    }, 1000);

    return () => clearTimeout(timer);
  }, [formData, onComplete]);

  const availableSuggestions = SUGGESTED_AREAS.filter(
    area => !formData.areaNames.includes(area)
  );

  return (
    <div className="p-6 md:p-8">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Grid3X3 className="h-6 w-6 text-blue-600 mr-3" />
          <h2 className="text-2xl font-bold text-gray-900">Site Areas</h2>
        </div>
        <p className="text-gray-600">
          Define the different areas within your site. Keep it simple - just area names 
          are needed. You can always add more areas later.
        </p>
      </div>

      <div className="space-y-8">
        {/* Add New Area */}
        <div className="rounded-lg p-6 border border-[#4CAF50]">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Area</h3>
          <div className="flex space-x-2">
            <input
              type="text"
              value={newAreaName}
              onChange={(e) => setNewAreaName(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Main Building, Parking Area"
              onKeyPress={(e) => e.key === 'Enter' && handleAddArea()}
            />
            <button
              onClick={handleAddArea}
              disabled={!newAreaName.trim() || formData.areaNames.includes(newAreaName.trim())}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Area
            </button>
          </div>
        </div>

        {/* Current Areas */}
        {formData.areaNames.length > 0 && (
          <div className="bg-white border border-gray-200 rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Site Areas ({formData.areaNames.length})
              </h3>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                {formData.areaNames.map((areaName, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 rounded-md border border-[#4CAF50]"
                  >
                    {editingIndex === index ? (
                      <div className="flex items-center space-x-2 flex-1">
                        <input
                          type="text"
                          value={editingName}
                          onChange={(e) => setEditingName(e.target.value)}
                          className="flex-1 px-3 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit()}
                        />
                        <button
                          onClick={handleSaveEdit}
                          className="p-1 text-green-600 hover:text-green-700"
                        >
                          <Check className="h-4 w-4" />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="p-1 text-gray-400 hover:text-gray-600"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      <>
                        <span className="text-gray-900 font-medium">{areaName}</span>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleEditArea(index)}
                            className="p-1 text-gray-400 hover:text-gray-600"
                          >
                            <Edit2 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleRemoveArea(index)}
                            className="p-1 text-red-400 hover:text-red-600"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Suggested Areas */}
        {availableSuggestions.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">Suggested Areas</h3>
            <p className="text-sm text-blue-700 mb-4">
              Click on any suggestion to add it to your site areas:
            </p>
            <div className="flex flex-wrap gap-2">
              {availableSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleAddSuggestedArea(suggestion)}
                  className="px-3 py-1 bg-white border border-blue-300 text-blue-700 rounded-md hover:bg-blue-100 text-sm"
                >
                  + {suggestion}
                </button>
              ))}
            </div>
          </div>
        )}



        {/* Empty State */}
        {formData.areaNames.length === 0 && (
          <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
            <Grid3X3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No areas defined yet</h4>
            <p className="text-gray-600 mb-4">
              Start by adding your first site area using the form above or choose from suggestions.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AreasStep;
