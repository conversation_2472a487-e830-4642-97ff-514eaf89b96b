import React, { useState, useEffect } from 'react';
import { MapPin, Search, Map, Navigation, AlertCircle } from 'lucide-react';
import DrawingMap, { PolygonMetrics } from '../map/DrawingMap';
import { DrawnPolygon, MapCenter } from '../map/InteractiveMap';
import locationService, { OSMSearchResult } from '../../services/locationService';
import { sanitizeSiteBoundaryData } from '../../utils/siteBoundaryValidation';

interface SiteBoundaryData {
  // Location search and display
  searchQuery: string;
  displayName: string;
  addressStreet: string;
  addressCity: string;
  addressCounty: string;
  addressPostalCode: string;
  addressCountry: string;
  latitude: number;
  longitude: number;
  accuracy: string;
  osmPlaceId: string;
  osmType: string;
  osmId: string;

  // Site boundary geometry (matches schema)
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  } | null;
  drawingComplete: boolean;
  lastModified: string;

  // Calculated metrics
  calculatedArea?: number;
  calculatedPerimeter?: number;
}

interface SiteBoundaryStepProps {
  data: SiteBoundaryData;
  onComplete: (data: SiteBoundaryData) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  onCreateSite?: () => void;
  isCreating?: boolean;
}

const SiteBoundaryStep: React.FC<SiteBoundaryStepProps> = ({
  data,
  onComplete
}) => {
  const [formData, setFormData] = useState<SiteBoundaryData>(data);
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<OSMSearchResult[]>([]);
  const [searchError, setSearchError] = useState<string | null>(null);

  const [mapCenter, setMapCenter] = useState<MapCenter>({ latitude: -1.2921, longitude: 36.8219 });
  const [currentPolygon, setCurrentPolygon] = useState<DrawnPolygon | null>(null);

  const handleInputChange = (field: keyof SiteBoundaryData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLocationSearch = async () => {
    if (!formData.searchQuery.trim()) return;

    setIsSearching(true);
    setSearchError(null);

    try {
      const results = await locationService.searchLocations(formData.searchQuery, 'ke', 5);

      if (results && results.length > 0) {
        setSearchResults(results);
      } else {
        setSearchError('No locations found. Please try a different search term.');
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Location search failed:', error);
      setSearchError('Error searching for locations. Please try again.');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const selectLocation = (result: OSMSearchResult) => {
    const updatedData = {
      ...formData,
      displayName: result.display_name,
      latitude: result.lat,
      longitude: result.lon,
      addressStreet: result.address.road || '',
      addressCity: result.address.city || '',
      addressCounty: result.address.county || '',
      addressPostalCode: result.address.postcode || '',
      addressCountry: result.address.country || 'Kenya',
      osmPlaceId: result.osm_id,
      osmType: result.osm_type,
      osmId: result.osm_id,
      accuracy: 'osm_search',
      lastModified: new Date().toISOString()
    };

    setFormData(updatedData);
    setMapCenter({ latitude: result.lat, longitude: result.lon });
    setSearchResults([]);
  };

  // Handle polygon drawing completion
  const handlePolygonComplete = (polygon: DrawnPolygon, metrics: PolygonMetrics) => {
    const updatedData = {
      ...formData,
      geometry: polygon.geometry,
      drawingComplete: true,
      calculatedArea: metrics.area,
      calculatedPerimeter: metrics.perimeter,
      lastModified: new Date().toISOString()
    };

    setFormData(updatedData);
    setCurrentPolygon(polygon);
  };

  // Handle polygon changes (during editing)
  const handlePolygonChange = (polygon: DrawnPolygon | null, metrics: PolygonMetrics | null) => {
    if (polygon && metrics) {
      const updatedData = {
        ...formData,
        geometry: polygon.geometry,
        drawingComplete: metrics.isValid,
        calculatedArea: metrics.area,
        calculatedPerimeter: metrics.perimeter,
        lastModified: new Date().toISOString()
      };

      setFormData(updatedData);
      setCurrentPolygon(polygon);
    } else {
      // Polygon was deleted
      const updatedData = {
        ...formData,
        geometry: null,
        drawingComplete: false,
        calculatedArea: undefined,
        calculatedPerimeter: undefined,
        lastModified: new Date().toISOString()
      };

      setFormData(updatedData);
      setCurrentPolygon(null);
    }
  };

  // Auto-save on data change
  useEffect(() => {
    const timer = setTimeout(() => {
      const sanitizedData = sanitizeSiteBoundaryData(formData);
      onComplete(sanitizedData);
    }, 1000);

    return () => clearTimeout(timer);
  }, [formData, onComplete]);

  return (
    <div className="p-6 md:p-8">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <MapPin className="h-6 w-6 text-blue-600 mr-3" />
          <h2 className="text-2xl font-bold text-gray-900">Site Boundary</h2>
        </div>
        <p className="text-gray-600">
          Search for your site location and draw the boundary. This helps us understand 
          the exact area and calculate important metrics.
        </p>
      </div>



      <div className="space-y-8">
        {/* Location Search */}
        <div className="rounded-lg p-6 border border-[#4CAF50]">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Location Search
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search for location *
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={formData.searchQuery}
                  onChange={(e) => handleInputChange('searchQuery', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Westlands, Nairobi"
                  onKeyDown={(e) => e.key === 'Enter' && handleLocationSearch()}
                />
                <button
                  onClick={handleLocationSearch}
                  disabled={isSearching || !formData.searchQuery.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSearching ? 'Searching...' : 'Search'}
                </button>
              </div>
            </div>

            {/* Search Error */}
            {searchError && (
              <div className="flex items-start p-3 bg-red-50 border border-red-200 rounded-md">
                <AlertCircle className="h-5 w-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-red-700">{searchError}</span>
              </div>
            )}

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="border border-gray-200 rounded-md">
                <div className="p-3 bg-gray-50 border-b border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900">Search Results</h4>
                </div>
                <div className="max-h-48 overflow-y-auto">
                  {searchResults.map((result, index) => (
                    <button
                      key={`${result.osm_type}-${result.osm_id}-${index}`}
                      onClick={() => selectLocation(result)}
                      className="w-full text-left p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                    >
                      <div className="text-sm font-medium text-gray-900">{result.name}</div>
                      <div className="text-xs text-gray-600 mt-1">{result.display_name}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {result.lat.toFixed(6)}, {result.lon.toFixed(6)} • {result.type}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Selected Location */}
            {formData.displayName && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex items-start">
                  <Navigation className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-green-900">Selected Location</h4>
                    <p className="text-sm text-green-700 mt-1">{formData.displayName}</p>
                    <div className="text-xs text-green-600 mt-2">
                      Coordinates: {formData.latitude.toFixed(6)}, {formData.longitude.toFixed(6)}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Address Details */}
        {formData.displayName && (
          <div className="rounded-lg p-6 border border-[#4CAF50]">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Address Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Street Address
                </label>
                <input
                  type="text"
                  value={formData.addressStreet}
                  onChange={(e) => handleInputChange('addressStreet', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Street address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City
                </label>
                <input
                  type="text"
                  value={formData.addressCity}
                  onChange={(e) => handleInputChange('addressCity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="City"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  County
                </label>
                <input
                  type="text"
                  value={formData.addressCounty}
                  onChange={(e) => handleInputChange('addressCounty', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="County"
                />
              </div>
            </div>
          </div>
        )}

        {/* Boundary Drawing */}
        {formData.displayName && (
          <div className="rounded-lg p-6 border border-[#4CAF50]">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Map className="h-5 w-5 mr-2" />
              Site Boundary Drawing
            </h3>

            <DrawingMap
              center={mapCenter}
              zoom={15}
              height="500px"
              onPolygonComplete={handlePolygonComplete}
              onPolygonChange={handlePolygonChange}
              initialPolygon={currentPolygon || undefined}
              showLocationMarker={true}
              locationMarkerText={formData.displayName}
              showMetrics={true}
              showValidation={false}
            />
          </div>
        )}


      </div>
    </div>
  );
};

export default SiteBoundaryStep;
