import React from 'react';
import { Check, AlertTriangle } from 'lucide-react';

interface StepData {
  id: number;
  title: string;
  description: string;
  isCompleted: boolean;
  isActive: boolean;
  hasErrors: boolean;
  errorCount: number;
}

interface StepSidebarProps {
  steps: StepData[];
  currentStep: number;
  onStepClick: (stepId: number) => void;
  progressPercentage: number;
}

const StepSidebar: React.FC<StepSidebarProps> = ({
  steps,
  currentStep,
  onStepClick
}) => {
  return (
    <div className="step-sidebar">
      <div className="step-list">
        {steps.map((step) => {
          const isClickable = step.isCompleted || step.id === currentStep || step.id === currentStep + 1;

          return (
            <div
              key={step.id}
              className={`step-item ${
                isClickable
                  ? 'cursor-pointer hover:bg-gray-50 rounded-lg p-3 -m-3 transition-colors'
                  : 'cursor-not-allowed'
              }`}
              onClick={() => isClickable && onStepClick(step.id)}
            >
              <div className="step-content">
                <h3 className={`step-title ${
                  step.isActive
                    ? 'text-blue-600'
                    : step.isCompleted
                    ? 'text-[#4CAF50]'
                    : step.hasErrors
                    ? 'text-red-600'
                    : 'text-gray-500'
                }`}>
                  {step.title}
                </h3>
                <p className={`step-description ${
                  step.isActive
                    ? 'text-blue-500'
                    : step.isCompleted
                    ? 'text-green-600'
                    : step.hasErrors
                    ? 'text-red-500'
                    : 'text-gray-500'
                }`}>
                  {step.description}
                  {step.hasErrors && step.errorCount && (
                    <span className="ml-1 text-red-600">
                      ({step.errorCount} error{step.errorCount > 1 ? 's' : ''})
                    </span>
                  )}
                </p>
              </div>

              <div className={`step-circle ${
                step.isCompleted
                  ? 'completed'
                  : step.isActive
                  ? 'current'
                  : step.hasErrors
                  ? 'error'
                  : 'pending'
              }`}>
                {step.isCompleted ? (
                  <Check className="w-5 h-5" />
                ) : step.hasErrors ? (
                  <AlertTriangle className="w-5 h-5" />
                ) : (
                  <span>{step.id}</span>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StepSidebar;
