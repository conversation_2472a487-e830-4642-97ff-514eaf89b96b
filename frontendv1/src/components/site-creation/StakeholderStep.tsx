import React from 'react';
import { Users } from 'lucide-react';
import PlaceholderStep from './PlaceholderStep';

interface StakeholderStepProps {
  data: any;
  onComplete: (data: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}







const StakeholderStep: React.FC<StakeholderStepProps> = (props) => {

  return (
    <PlaceholderStep
      {...props}
      title="Stakeholder Management"
      description="Define client, contractors, and subcontractors; assign project management; validate licenses and set communication protocols"
      icon={Users}
    />
  );


};

export default StakeholderStep;
