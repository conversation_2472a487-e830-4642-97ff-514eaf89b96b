import React from 'react';
import { LucideIcon } from 'lucide-react';

interface PlaceholderStepProps {
  title: string;
  description: string;
  icon: LucideIcon;
  data: any;
  onComplete: (data: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  onCreateSite?: () => void;
  isCreating?: boolean;
}

const PlaceholderStep: React.FC<PlaceholderStepProps> = ({
  title,
  description,
  icon: Icon,
  onComplete
}) => {
  // Auto-complete with mock data for dev mode (only once)
  React.useEffect(() => {
    onComplete({ placeholder: true, timestamp: Date.now() });
  }, []); // Empty dependency array - run only once

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <p className="mt-1 text-sm text-gray-500">{description}</p>
      </div>

      {/* Placeholder Content */}
      <div className="bg-white shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="text-center py-12">
            <Icon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Coming Soon</h3>
            <p className="mt-1 text-sm text-gray-500">
              This step is under development. In dev mode, you can continue to the next step.
            </p>
            <div className="mt-6">
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Development Mode Active
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>
                        This step will be automatically completed with placeholder data so you can 
                        continue testing the workflow.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default PlaceholderStep;
