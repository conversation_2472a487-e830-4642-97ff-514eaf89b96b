import React, { useState, useEffect } from 'react';
import { Building, Calendar, User, FileText } from 'lucide-react';

interface BasicInfoData {
  siteName: string;
  projectType: string;
  constructionType: string;
  plannedStartDate: string;
  plannedEndDate: string;
  estimatedBudget: number;
  currency: string;
  clientName: string;
  projectManagerName: string;
  description?: string;
}

interface BasicInfoStepProps {
  data: BasicInfoData;
  onComplete: (data: BasicInfoData) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  onCreateSite?: () => void;
  isCreating?: boolean;
}

const PROJECT_TYPES = [
  'Residential',
  'Commercial',
  'Industrial',
  'Infrastructure',
  'Technology',
  'Healthcare',
  'Fit-Out',
  'Hospitality',
  'Institution',
  'MixedUse'
];

const CONSTRUCTION_TYPES = [
  'New',
  'Renovation',
  'Extension',
  'Demolition',
  'Maintenance'
];

const CURRENCIES = [
  { code: 'KES', name: 'Kenyan Shilling' },
  { code: 'USD', name: 'US Dollar' },
  { code: 'EUR', name: 'Euro' },
  { code: 'GBP', name: 'British Pound' }
];

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  data,
  onComplete
}) => {
  const [formData, setFormData] = useState<BasicInfoData>(data);

  const handleInputChange = (field: keyof BasicInfoData, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Auto-save on data change
  useEffect(() => {
    const timer = setTimeout(() => {
      onComplete(formData);
    }, 1000);

    return () => clearTimeout(timer);
  }, [formData, onComplete]);

  return (
    <div className="p-6 md:p-8">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Building className="h-6 w-6 text-blue-600 mr-3" />
          <h2 className="text-2xl font-bold text-gray-900">Basic Information</h2>
        </div>
        <p className="text-gray-600">
          Let's start with the essential details about your construction project. 
          We'll auto-generate codes and calculate timelines for you.
        </p>
      </div>

      <div className="space-y-8">
        {/* Project Identity */}
        <div className="rounded-lg p-6 border border-[#4CAF50]">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Project Identity
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site Name *
              </label>
              <input
                type="text"
                value={formData.siteName}
                onChange={(e) => handleInputChange('siteName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Westlands Office Complex"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site Code *
              </label>
              <input
                type="text"
                value={`${formData.projectType.substring(0, 3).toUpperCase()}${formData.siteName.replace(/\s+/g, '').substring(0, 4).toUpperCase()}${new Date().getFullYear().toString().slice(-2)}${Math.floor(Math.random() * 999).toString().padStart(3, '0')}`}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-600"
                placeholder="Auto-generated"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project Type *
              </label>
              <select
                value={formData.projectType}
                onChange={(e) => handleInputChange('projectType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select project type</option>
                {PROJECT_TYPES.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Construction Type *
              </label>
              <select
                value={formData.constructionType}
                onChange={(e) => handleInputChange('constructionType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select construction type</option>
                {CONSTRUCTION_TYPES.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Description - Full Width */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Brief description of the project..."
            />
          </div>
        </div>

        {/* Timeline & Budget */}
        <div className="rounded-lg p-6 border border-[#4CAF50]">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Timeline & Budget
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Planned Start Date *
              </label>
              <input
                type="date"
                value={formData.plannedStartDate}
                onChange={(e) => handleInputChange('plannedStartDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Planned End Date *
              </label>
              <input
                type="date"
                value={formData.plannedEndDate}
                onChange={(e) => handleInputChange('plannedEndDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Estimated Budget *
              </label>
              <div className="flex">
                <select
                  value={formData.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {CURRENCIES.map(curr => (
                    <option key={curr.code} value={curr.code}>{curr.code}</option>
                  ))}
                </select>
                <input
                  type="number"
                  value={formData.estimatedBudget}
                  onChange={(e) => handleInputChange('estimatedBudget', parseFloat(e.target.value) || 0)}
                  className="flex-1 px-3 py-2 border border-l-0 border-gray-300 rounded-r-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Key Contacts */}
        <div className="rounded-lg p-6 border border-[#4CAF50]">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Key Contacts
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Client Name *
              </label>
              <input
                type="text"
                value={formData.clientName}
                onChange={(e) => handleInputChange('clientName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., ABC Construction Ltd"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project Manager *
              </label>
              <input
                type="text"
                value={formData.projectManagerName}
                onChange={(e) => handleInputChange('projectManagerName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., John Doe"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoStep;
