import React from 'react';
import { FileText, User, Image, Shield, CheckCircle, History } from 'lucide-react';
import { Permit } from '../../types/permits';
import { VSCodeTab } from '../shared/VSCodeInterface';
import PermitDetailsTab from './tabs/PermitDetailsTab';
import WorkerTab from './tabs/WorkerTab';
import DocumentTab from './tabs/DocumentTab';
import PhotoTab from './tabs/PhotoTab';
import RiskAssessmentTab from './tabs/RiskAssessmentTab';
import ApprovalTab from './tabs/ApprovalTab';
import AuditTrailTab from './tabs/AuditTrailTab';
import PermitFormTab from './tabs/PermitFormTab';

interface PermitTabsProps {
  permit: Permit;
}

const PermitTabs = ({ permit }: PermitTabsProps) => {
  const renderTabContent = (tab: VSCodeTab) => {
    switch (tab.type) {
      case 'details':
        return <PermitDetailsTab permit={permit} />;
      case 'permit-form':
        return <PermitFormTab permit={permit} />;
      case 'worker':
        return <WorkerTab worker={tab.data?.worker} />;
      case 'document':
        return <DocumentTab document={tab.data?.document} />;
      case 'photo':
        return <PhotoTab photo={tab.data?.photo} />;
      case 'risk-assessment':
        return <RiskAssessmentTab riskAssessment={tab.data?.riskAssessment} />;
      case 'daily-risk-assessment':
        return <RiskAssessmentTab riskAssessment={tab.data?.dailyRiskAssessments} isDailyAssessment={true} />;
      case 'approval':
        return <ApprovalTab approval={tab.data?.approval} />;
      case 'audit-trail':
        return <AuditTrailTab permit={permit} />;
      default:
        return (
          <div className="p-6">
            <div className="text-center text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">Content Not Available</h3>
              <p>The selected content type is not yet implemented.</p>
            </div>
          </div>
        );
    }
  };

  return { renderTabContent };
};

export default PermitTabs;
