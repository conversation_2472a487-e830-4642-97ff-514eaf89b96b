import React from 'react';
import {
  Flame,
  Shield,
  HardHat,
  Shovel,
  Zap,
  FileText,
  Bookmark
} from 'lucide-react';

export interface PermitTypeCardData {
  id: string;
  name: string;
  description: string;
  permitType: 'hot-work' | 'confined-space' | 'work-at-height' | 'excavation' | 'electrical' | 'general';
  isBookmarked?: boolean;
  onClick?: () => void;
}

interface PermitTypeCardProps {
  data: PermitTypeCardData;
}

// Configuration for different permit types
const permitTypeConfig = {
  'hot-work': {
    name: 'Hot Work',
    icon: Flame,
    color: '#DC2626'
  },
  'confined-space': {
    name: 'Confined Space',
    icon: Shield,
    color: '#7C3AED'
  },
  'work-at-height': {
    name: 'Work at Height',
    icon: HardHat,
    color: '#2563EB'
  },
  'excavation': {
    name: 'Excavation',
    icon: Shovel,
    color: '#D97706'
  },
  'electrical': {
    name: 'Electrical',
    icon: Zap,
    color: '#059669'
  },
  'general': {
    name: 'General Work',
    icon: FileText,
    color: '#6B7280'
  }
};

export const PermitTypeCard: React.FC<PermitTypeCardProps> = ({ data }) => {
  const config = permitTypeConfig[data.permitType];
  const IconComponent = config.icon;

  return (
    <div
      className="relative bg-white border-2 border-gray-200 hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden h-full"
      style={{ borderRadius: '5px' }}
      onClick={data.onClick}
    >
      {/* Main content container */}
      <div className="p-5 h-full flex flex-col">
        {/* Header with badge, permit type pill, and bookmark */}
        <div className="flex items-start justify-between mb-5">
          {/* Circular badge */}
          <div
            className="w-12 h-12 rounded-full flex items-center justify-center shadow-sm"
            style={{ backgroundColor: config.color }}
          >
            <IconComponent className="w-6 h-6 text-white" />
          </div>

          <div className="flex items-center space-x-2">
            {/* Permit type pill */}
            <div
              className="px-3 py-1 text-xs font-medium text-white"
              style={{
                backgroundColor: config.color,
                borderRadius: '5px'
              }}
            >
              {config.name}
            </div>

            {/* Bookmark icon */}
            {data.isBookmarked && (
              <Bookmark className="w-5 h-5 text-gray-400 fill-current" />
            )}
          </div>
        </div>

        {/* Title */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2 leading-tight">
            {data.name}
          </h3>
        </div>

        {/* Description */}
        <div className="flex-1 mb-5">
          <p className="text-gray-600 text-sm leading-relaxed line-clamp-4">
            {data.description}
          </p>
        </div>

        {/* Action button */}
        <div className="mt-auto">
          <button 
            className="w-full px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              data.onClick?.();
            }}
          >
            View
          </button>
        </div>
      </div>
    </div>
  );
};

export default PermitTypeCard;
