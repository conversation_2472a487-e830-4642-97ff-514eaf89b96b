import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermitTypeCard, { PermitTypeCardData } from "./PermitTypeCard";

interface PermitTypesProps {
  siteId: string;
}

// Educational permit type data for learning purposes
const permitTypesData: PermitTypeCardData[] = [
  {
    id: "hot-work",
    name: "Hot Work Permit",
    permitType: "hot-work",
    description: "Required for welding, cutting, grinding, and other hot work activities that could ignite flammable materials. This permit ensures proper fire prevention measures are in place."
  },
  {
    id: "confined-space",
    name: "Confined Space Entry Permit",
    permitType: "confined-space",
    description: "Required for entry into confined spaces such as tanks, vessels, manholes, or other enclosed areas. Critical for preventing atmospheric hazards and ensuring safe entry/exit procedures."
  },
  {
    id: "work-at-height",
    name: "Work at Height Permit",
    permitType: "work-at-height",
    description: "Required for work performed at heights above 2 meters or where fall hazards exist. Ensures proper fall protection systems and safety measures are implemented."
  },
  {
    id: "excavation",
    name: "Excavation Permit",
    permitType: "excavation",
    description: "Required for excavation work, trenching, and ground disturbance activities. Protects against cave-ins, utility strikes, and other underground hazards."
  },
  {
    id: "electrical",
    name: "Electrical Work Permit",
    permitType: "electrical",
    description: "Required for electrical installation, maintenance, and repair work. Ensures proper lockout/tagout procedures and protection against electrical hazards."
  },
  {
    id: "general",
    name: "General Work Permit",
    permitType: "general",
    description: "Required for general construction and maintenance activities with standard safety requirements. Covers routine work activities that still require safety oversight."
  }
];

const PermitTypes: React.FC<PermitTypesProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const { siteId: paramSiteId } = useParams();
  const currentSiteId = siteId || paramSiteId;

  const handlePermitTypeClick = (permitType: PermitTypeCardData) => {
    // For educational purposes, we could navigate to a detailed learning page
    // For now, we'll navigate to the permit form page for demonstration
    const routeMap: { [key: string]: string } = {
      'hot-work': 'hot-work',
      'confined-space': 'confined-space',
      'work-at-height': 'work-at-height',
      'excavation': 'excavation',
      'electrical': 'electrical',
      'general': 'general-work'
    };

    const route = routeMap[permitType.permitType] || 'general-work';
    navigate(`/sites/${currentSiteId}/${route}/form`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900">Permit Types</h2>
        <p className="text-sm text-gray-600">Learn about different permit types and their safety requirements</p>
      </div>

      {/* Permit Types Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-fr">
        {permitTypesData.map((permitType) => (
          <PermitTypeCard
            key={permitType.id}
            data={{
              ...permitType,
              onClick: () => handlePermitTypeClick(permitType)
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default PermitTypes;
