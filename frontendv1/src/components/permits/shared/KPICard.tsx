import React from "react";

interface KPICardProps {
  title: string;
  value: number | string;
  change?: number;
  icon?: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  change,
  icon,
  onClick,
  className = ''
}) => {
  return (
    <div
      className={`bg-white p-4 rounded-lg border border-gray-200 shadow-sm ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''} ${className}`}
      onClick={onClick}
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-sm font-medium text-gray-500">{title}</h3>
          <p className="text-2xl font-semibold mt-1">{value}</p>
          {change !== undefined && (
            <div className="flex items-center mt-1">
              <span
                className={`text-xs font-medium ${
                  change > 0 ? 'text-green-600' : change < 0 ? 'text-red-600' : 'text-gray-500'
                }`}
              >
                {change > 0 ? '+' : ''}{change}%
              </span>
            </div>
          )}
        </div>
        {icon && <div className="p-2 rounded-full bg-gray-50">{icon}</div>}
      </div>
    </div>
  );
};

export default KPICard;
