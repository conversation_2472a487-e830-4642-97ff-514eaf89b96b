import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FileText,
  Search,
  Filter,
  ChevronRight,
  Flame,
  HardHat,
  Zap,
  Mountain
} from 'lucide-react';

interface RequestedPermitsProps {
  siteId: string;
}

interface PermitItem {
  id: string;
  type: 'hot-work' | 'confined-space' | 'work-at-height' | 'excavation' | 'electrical' | 'general-work';
  title: string;
  description: string;
  serialNumber: string;
  status: 'pending-approval' | 'disapproved';
  createdDate: string;
  expiryDate: string;
  location: string;
  requestedBy: string;
  icon: React.ReactNode;
  color: string;
  route: string;
}

const RequestedPermits: React.FC<RequestedPermitsProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock permit data - only pending approval and disapproved permits
  const permits: PermitItem[] = [
    {
      id: '1',
      type: 'hot-work',
      title: 'Hot Work Permit',
      description: 'Welding and cutting operations',
      serialNumber: 'HWP-2024-001',
      status: 'pending-approval',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'Building A - Level 2',
      requestedBy: 'John Mwangi',
      icon: <Flame className="h-5 w-5" />,
      color: 'text-red-600',
      route: '/hot-work/form'
    },
    {
      id: '2',
      type: 'confined-space',
      title: 'Confined Space Entry Permit',
      description: 'Tank inspection and maintenance',
      serialNumber: 'CSE-2024-002',
      status: 'disapproved',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'Storage Tank Area',
      requestedBy: 'Sarah Njeri',
      icon: <Mountain className="h-5 w-5" />,
      color: 'text-blue-600',
      route: '/confined-space/form'
    },
    {
      id: '3',
      type: 'work-at-height',
      title: 'Work at Height Permit',
      description: 'Roof maintenance and repairs',
      serialNumber: 'WAH-2024-003',
      status: 'pending-approval',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'Main Building Roof',
      requestedBy: 'Peter Kamau',
      icon: <HardHat className="h-5 w-5" />,
      color: 'text-orange-600',
      route: '/work-at-height/form'
    },
    {
      id: '4',
      type: 'electrical',
      title: 'Electrical Work Permit',
      description: 'Power line installation',
      serialNumber: 'EWP-2024-005',
      status: 'disapproved',
      createdDate: '2024-01-15',
      expiryDate: '2024-01-16',
      location: 'Electrical Room',
      requestedBy: 'David Ochieng',
      icon: <Zap className="h-5 w-5" />,
      color: 'text-purple-600',
      route: '/electrical/form'
    }
  ];

  const permitTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'hot-work', label: 'Hot Work' },
    { value: 'confined-space', label: 'Confined Space' },
    { value: 'work-at-height', label: 'Work at Height' },
    { value: 'excavation', label: 'Excavation' },
    { value: 'electrical', label: 'Electrical' },
    { value: 'general-work', label: 'General Work' }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'pending-approval', label: 'Pending Approval' },
    { value: 'disapproved', label: 'Disapproved' }
  ];

  const handlePermitClick = (permit: PermitItem) => {
    navigate(`/sites/${siteId}${permit.route}`);
  };

  // Filter permits based on search term, type, and status
  const filteredPermits = permits.filter(permit => {
    const matchesSearch = permit.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permit.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permit.serialNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'all' || permit.type === filterType;
    const matchesStatus = filterStatus === 'all' || permit.status === filterStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Requested Permits</h2>
          <p className="text-gray-600">Permits pending approval or disapproved</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search permits..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>

          {/* Type Filter */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent appearance-none"
            >
              {permitTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>

          {/* Status Filter */}
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Permits List */}
      {filteredPermits.length > 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="divide-y divide-gray-200">
            {filteredPermits.map((permit) => (
              <div
                key={permit.id}
                className="p-6 hover:bg-gray-50 cursor-pointer transition-colors group"
                onClick={() => handlePermitClick(permit)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 flex-1">
                    {/* Icon */}
                    <div className={`flex-shrink-0 ${permit.color}`}>
                      {permit.icon}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                          {permit.title}
                        </h3>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          permit.status === 'pending-approval' 
                            ? 'bg-yellow-100 text-yellow-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {permit.status === 'pending-approval' ? 'Pending Approval' : 'Disapproved'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{permit.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>Serial: {permit.serialNumber}</span>
                        <span>Location: {permit.location}</span>
                        <span>Requested by: {permit.requestedBy}</span>
                      </div>
                    </div>

                    {/* Arrow */}
                    <div className="flex-shrink-0">
                      <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No requested permits found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || filterType !== 'all' || filterStatus !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'No permits are currently pending approval or disapproved'}
          </p>
        </div>
      )}
    </div>
  );
};

export default RequestedPermits;
