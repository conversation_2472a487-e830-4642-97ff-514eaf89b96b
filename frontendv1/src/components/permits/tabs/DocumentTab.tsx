import React from 'react';
import { FileText, Download, Eye, Calendar, User } from 'lucide-react';

interface DocumentTabProps {
  document: {
    id: string;
    name: string;
    type: string;
    size?: string;
    uploadedBy?: string;
    uploadedAt?: Date;
  };
}

const DocumentTab: React.FC<DocumentTabProps> = ({ document }) => {
  if (!document) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium mb-2">Document Not Found</h3>
          <p>The requested document could not be loaded.</p>
        </div>
      </div>
    );
  }

  const getFileIcon = () => {
    switch (document.type) {
      case 'pdf':
        return <FileText className="h-8 w-8 text-red-500" />;
      default:
        return <FileText className="h-8 w-8 text-gray-500" />;
    }
  };

  return (
    <div className="p-6 max-w-4xl">
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm" style={{ borderRadius: '5px' }}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50" style={{ borderRadius: '5px 5px 0 0' }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {getFileIcon()}
              <div>
                <h1 className="text-xl font-bold text-gray-900">{document.name}</h1>
                <p className="text-sm text-gray-600">{document.type.toUpperCase()} Document</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="flex items-center px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors">
                <Eye className="h-4 w-4 mr-1" />
                View
              </button>
              <button className="flex items-center px-3 py-2 text-sm border border-green-300 text-green-700 rounded hover:bg-green-50 transition-colors">
                <Download className="h-4 w-4 mr-1" />
                Download
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Document Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Details</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-500">Name:</div>
                  <div className="text-sm text-gray-900">{document.name}</div>
                </div>
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-500">Type:</div>
                  <div className="text-sm text-gray-900">{document.type.toUpperCase()}</div>
                </div>
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-500">Size:</div>
                  <div className="text-sm text-gray-900">{document.size || '2.4 MB'}</div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Upload Information</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Uploaded by:</div>
                  <div className="text-sm text-gray-900">{document.uploadedBy || 'System'}</div>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Date:</div>
                  <div className="text-sm text-gray-900">
                    {document.uploadedAt?.toLocaleDateString() || new Date().toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Document Preview */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Preview</h3>
            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <FileText className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">Document Preview</h4>
              <p className="text-gray-600 mb-4">
                Preview functionality is not available for this document type.
              </p>
              <div className="flex justify-center space-x-4">
                <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                  <Eye className="h-4 w-4 mr-2" />
                  Open in New Tab
                </button>
                <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors">
                  <Download className="h-4 w-4 mr-2" />
                  Download File
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentTab;
