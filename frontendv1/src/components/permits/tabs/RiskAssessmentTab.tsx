import React from 'react';
import { Shield, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface RiskAssessmentTabProps {
  riskAssessment: any;
  isDailyAssessment?: boolean;
}

const RiskAssessmentTab: React.FC<RiskAssessmentTabProps> = ({ riskAssessment, isDailyAssessment = false }) => {
  const mockHazards = [
    { id: 1, description: 'Hot surfaces and sparks', riskLevel: 'High', controlMeasures: 'Fire watch, fire extinguisher on site' },
    { id: 2, description: 'Toxic fumes', riskLevel: 'Medium', controlMeasures: 'Adequate ventilation, respiratory protection' },
    { id: 3, description: 'Burns from welding equipment', riskLevel: 'High', controlMeasures: 'PPE, proper training' }
  ];

  const mockControlMeasures = [
    { id: 1, measure: 'Fire watch personnel assigned', status: 'implemented' },
    { id: 2, measure: 'Fire extinguisher positioned within 10m', status: 'implemented' },
    { id: 3, measure: 'Adequate ventilation confirmed', status: 'implemented' },
    { id: 4, measure: 'All personnel wearing appropriate PPE', status: 'implemented' }
  ];

  const getRiskColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'high':
        return 'text-red-600 bg-red-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'implemented':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'not-implemented':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-6 max-w-4xl">
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm" style={{ borderRadius: '5px' }}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50" style={{ borderRadius: '5px 5px 0 0' }}>
          <div className="flex items-center space-x-4">
            <Shield className="h-8 w-8 text-orange-500" />
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                {isDailyAssessment ? 'Daily Risk Assessment' : 'Risk Assessment & Method Statement (RAMS)'}
              </h1>
              <p className="text-sm text-gray-600">
                {isDailyAssessment ? 'Daily safety review and hazard assessment' : 'Comprehensive risk analysis and control measures'}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Identified Hazards */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Identified Hazards</h3>
            <div className="space-y-4">
              {mockHazards.map((hazard) => (
                <div key={hazard.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-gray-900">{hazard.description}</h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskColor(hazard.riskLevel)}`}>
                      {hazard.riskLevel} Risk
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    <strong>Control Measures:</strong> {hazard.controlMeasures}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Control Measures */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Control Measures Implementation</h3>
            <div className="space-y-3">
              {mockControlMeasures.map((measure) => (
                <div key={measure.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  {getStatusIcon(measure.status)}
                  <span className="flex-1 text-sm text-gray-900">{measure.measure}</span>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    measure.status === 'implemented' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {measure.status.charAt(0).toUpperCase() + measure.status.slice(1)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Risk Matrix */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Assessment Matrix</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-4 py-2 border border-gray-300 text-left text-sm font-medium text-gray-900">Hazard</th>
                    <th className="px-4 py-2 border border-gray-300 text-left text-sm font-medium text-gray-900">Likelihood</th>
                    <th className="px-4 py-2 border border-gray-300 text-left text-sm font-medium text-gray-900">Severity</th>
                    <th className="px-4 py-2 border border-gray-300 text-left text-sm font-medium text-gray-900">Risk Level</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Hot surfaces and sparks</td>
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Likely</td>
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Major</td>
                    <td className="px-4 py-2 border border-gray-300">
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">High</span>
                    </td>
                  </tr>
                  <tr className="bg-gray-50">
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Toxic fumes</td>
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Possible</td>
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Moderate</td>
                    <td className="px-4 py-2 border border-gray-300">
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Medium</span>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Equipment burns</td>
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Likely</td>
                    <td className="px-4 py-2 border border-gray-300 text-sm text-gray-900">Major</td>
                    <td className="px-4 py-2 border border-gray-300">
                      <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">High</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RiskAssessmentTab;
