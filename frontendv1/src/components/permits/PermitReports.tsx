import React, { useState, useEffect } from "react";
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	TrendingUp,
	Download,
	Printer,
} from "lucide-react";
import { PermitReportData } from "../../types/permits";
import {
	<PERSON><PERSON><PERSON>,
	Bar,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	ResponsiveContaine<PERSON>,
	<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Cell,
} from "recharts";

interface PermitReportsProps {
	siteId: string;
}

// Mock report data
const mockPermitsByType = [
	{ name: "Hot Work", count: 45, percentage: 35 },
	{ name: "Work at Height", count: 32, percentage: 25 },
	{ name: "Confined Space", count: 25, percentage: 19 },
	{ name: "General Work", count: 27, percentage: 21 },
];

const mockPermitsByStatus = [
	{ name: "Completed", count: 89, percentage: 69 },
	{ name: "Active", count: 8, percentage: 6 },
	{ name: "Cancelled", count: 15, percentage: 12 },
	{ name: "Expired", count: 17, percentage: 13 },
];

const mockMonthlyTrends = [
	{ month: "Oct", permits: 28, violations: 2 },
	{ month: "Nov", permits: 35, violations: 1 },
	{ month: "Dec", permits: 42, violations: 3 },
	{ month: "Jan", permits: 38, violations: 1 },
];

const COLORS = ["#10B981", "#F59E0B", "#EF4444", "#6B7280"];

const PermitReports: React.FC<PermitReportsProps> = ({ siteId }) => {
	const [reportType, setReportType] = useState("overview");
	const [dateRange, setDateRange] = useState("last-30");
	const [reportData, _setReportData] = useState<PermitReportData | null>(null);

	useEffect(() => {
		// Fetch report data based on selected parameters
		console.log(`Fetching permit reports for site ${siteId}`);
	}, [siteId, reportType, dateRange]);

	const generateReport = () => {
		// Generate custom report based on selected parameters
		console.log("Generating custom report...");
	};

	const exportReport = (format: "pdf" | "excel") => {
		console.log(`Exporting report as ${format}`);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold text-gray-900">Permit Reports</h2>
				<div className="flex space-x-2">
					<button
						onClick={() => exportReport("pdf")}
						className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
					>
						<Printer className="h-4 w-4 mr-1" />
						Print
					</button>
					<button
						onClick={() => exportReport("excel")}
						className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
					>
						<Download className="h-4 w-4 mr-1" />
						Export
					</button>
				</div>
			</div>

			{/* Report Templates */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Quick Reports</h3>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
						<div className="flex items-center mb-2">
							<PieChart className="h-5 w-5 text-green-600 mr-2" />
							<h4 className="font-medium text-gray-900">Permit Distribution</h4>
						</div>
						<p className="text-sm text-gray-500">
							Breakdown of permits by type and status
						</p>
					</button>

					<button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
						<div className="flex items-center mb-2">
							<TrendingUp className="h-5 w-5 text-green-600 mr-2" />
							<h4 className="font-medium text-gray-900">Compliance Trends</h4>
						</div>
						<p className="text-sm text-gray-500">
							Monthly permit compliance and violation trends
						</p>
					</button>

					<button className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 text-left transition-colors">
						<div className="flex items-center mb-2">
							<BarChart3 className="h-5 w-5 text-green-600 mr-2" />
							<h4 className="font-medium text-gray-900">Performance Metrics</h4>
						</div>
						<p className="text-sm text-gray-500">
							Approval times and efficiency metrics
						</p>
					</button>
				</div>
			</div>

			{/* Custom Report Builder */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Custom Report</h3>
				<div className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Report Type
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
								value={reportType}
								onChange={(e) => setReportType(e.target.value)}
							>
								<option value="overview">Overview Report</option>
								<option value="compliance">Compliance Report</option>
								<option value="violations">Violations Report</option>
								<option value="efficiency">Efficiency Report</option>
							</select>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Date Range
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
								value={dateRange}
								onChange={(e) => setDateRange(e.target.value)}
							>
								<option value="last-7">Last 7 Days</option>
								<option value="last-30">Last 30 Days</option>
								<option value="last-90">Last 90 Days</option>
								<option value="year-to-date">Year to Date</option>
								<option value="custom">Custom Range</option>
							</select>
						</div>
						<div className="flex items-end">
							<button
								onClick={generateReport}
								className="w-full px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
							>
								Generate Report
							</button>
						</div>
					</div>
				</div>
			</div>

			{/* Report Visualizations */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Permits by Type */}
				<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
					<h3 className="text-lg font-medium mb-4">Permits by Type</h3>
					<div className="h-64">
						<ResponsiveContainer width="100%" height="100%">
							<RechartsPieChart>
								<RechartsPieChart
									data={mockPermitsByType}
									cx="50%"
									cy="50%"
									outerRadius={80}
									dataKey="count"
								>
									{mockPermitsByType.map((_entry, index) => (
										<Cell
											key={`cell-${index}`}
											fill={COLORS[index % COLORS.length]}
										/>
									))}
								</RechartsPieChart>
								<Tooltip />
							</RechartsPieChart>
						</ResponsiveContainer>
					</div>
					<div className="mt-4 grid grid-cols-2 gap-2">
						{mockPermitsByType.map((item, index) => (
							<div key={item.name} className="flex items-center text-sm">
								<div
									className="w-3 h-3 rounded-full mr-2"
									style={{ backgroundColor: COLORS[index % COLORS.length] }}
								></div>
								<span className="text-gray-600">
									{item.name}: {item.count}
								</span>
							</div>
						))}
					</div>
				</div>

				{/* Permits by Status */}
				<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
					<h3 className="text-lg font-medium mb-4">Permits by Status</h3>
					<div className="h-64">
						<ResponsiveContainer width="100%" height="100%">
							<BarChart data={mockPermitsByStatus}>
								<CartesianGrid strokeDasharray="3 3" />
								<XAxis dataKey="name" />
								<YAxis />
								<Tooltip />
								<Bar dataKey="count" fill="#10B981" />
							</BarChart>
						</ResponsiveContainer>
					</div>
				</div>
			</div>

			{/* Monthly Trends */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Monthly Permit Trends</h3>
				<div className="h-64">
					<ResponsiveContainer width="100%" height="100%">
						<BarChart data={mockMonthlyTrends}>
							<CartesianGrid strokeDasharray="3 3" />
							<XAxis dataKey="month" />
							<YAxis />
							<Tooltip />
							<Bar dataKey="permits" fill="#10B981" name="Permits" />
							<Bar dataKey="violations" fill="#EF4444" name="Violations" />
						</BarChart>
					</ResponsiveContainer>
				</div>
			</div>

			{/* Key Metrics Summary */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Key Performance Indicators</h3>
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<div className="text-center p-4 bg-green-50 rounded-lg">
						<div className="text-2xl font-bold text-green-600">98.5%</div>
						<div className="text-sm text-gray-600">Compliance Rate</div>
					</div>
					<div className="text-center p-4 bg-blue-50 rounded-lg">
						<div className="text-2xl font-bold text-blue-600">2.5h</div>
						<div className="text-sm text-gray-600">Avg. Approval Time</div>
					</div>
					<div className="text-center p-4 bg-yellow-50 rounded-lg">
						<div className="text-2xl font-bold text-yellow-600">129</div>
						<div className="text-sm text-gray-600">Total Permits (30d)</div>
					</div>
					<div className="text-center p-4 bg-red-50 rounded-lg">
						<div className="text-2xl font-bold text-red-600">7</div>
						<div className="text-sm text-gray-600">Violations (30d)</div>
					</div>
				</div>
			</div>

			{/* Report Data Table */}
			{reportData && (
				<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
					<div className="flex justify-between items-center mb-4">
						<h3 className="text-lg font-medium">{reportData.title}</h3>
						<div className="flex space-x-2">
							<button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
								<Printer className="h-4 w-4 inline mr-1" />
								Print
							</button>
							<button className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
								<Download className="h-4 w-4 inline mr-1" />
								Export
							</button>
						</div>
					</div>

					<div className="overflow-x-auto">
						<table className="min-w-full divide-y divide-gray-200">
							<thead className="bg-gray-50">
								<tr>
									{reportData.columns.map((column) => (
										<th
											key={column.key}
											className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
										>
											{column.label}
										</th>
									))}
								</tr>
							</thead>
							<tbody className="bg-white divide-y divide-gray-200">
								{reportData.tableData.map((row, index) => (
									<tr key={index}>
										{reportData.columns.map((column) => (
											<td
												key={column.key}
												className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
											>
												{row[column.key]}
											</td>
										))}
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>
			)}
		</div>
	);
};

export default PermitReports;
