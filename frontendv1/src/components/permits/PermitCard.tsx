import React from 'react';
import {
  Flame,
  Shield,
  HardHat,
  Shovel,
  Zap,
  FileText,
  Bookmark
} from 'lucide-react';

export interface PermitCardData {
  id: string;
  title: string;
  description: string;
  permitType: 'hot-work' | 'confined-space' | 'work-at-height' | 'excavation' | 'electrical' | 'general';
  location: string; // Specific area within site (e.g., "Storage Area", "Main Gate", "Building A")
  engineerName: string;
  engineerAvatar?: string;
  isBookmarked?: boolean;
  onClick?: () => void;
  onGeneratePermit?: () => void;
}

// Permit type configuration with colors matching the specification
const permitTypeConfig = {
  'hot-work': {
    color: '#FF6B3D',
    icon: Flame,
    name: 'Hot Work'
  },
  'confined-space': {
    color: '#1F8FB6',
    icon: Shield,
    name: 'Confined Space'
  },
  'work-at-height': {
    color: '#7B61FF',
    icon: HardHat,
    name: 'Working at Height'
  },
  'electrical': {
    color: '#FFCB2B',
    icon: Zap,
    name: 'Electrical'
  },
  'excavation': {
    color: '#D9903D',
    icon: Shovel,
    name: 'Excavation'
  },
  'general': {
    color: '#6C6F72',
    icon: FileText,
    name: 'General'
  }
};

interface PermitCardProps {
  data: PermitCardData;
}

export const PermitCard: React.FC<PermitCardProps> = ({ data }) => {
  const config = permitTypeConfig[data.permitType];
  const IconComponent = config.icon;

  return (
    <div
      className="relative bg-white border-2 border-gray-200 hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden w-full min-w-0 h-80 sm:h-96 lg:h-80 xl:h-96"
      style={{ borderRadius: '5px' }}
      onClick={data.onClick}
    >
      {/* Main content container */}
      <div className="p-4 sm:p-5 h-full flex flex-col">
        {/* Header with badge, permit type pill, and bookmark */}
        <div className="flex items-start justify-between mb-3 sm:mb-4">
          {/* Circular badge */}
          <div
            className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center shadow-sm"
            style={{ backgroundColor: config.color }}
          >
            <IconComponent className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>

          <div className="flex items-center space-x-2">
            {/* Permit type pill */}
            <div
              className="px-2 sm:px-3 py-1 text-xs font-medium text-white"
              style={{
                backgroundColor: config.color,
                borderRadius: '5px'
              }}
            >
              {config.name}
            </div>

            {/* Bookmark icon */}
            {data.isBookmarked && (
              <Bookmark className="w-5 h-5 text-gray-400 fill-current" />
            )}
          </div>
        </div>

        {/* Title and description */}
        <div className="flex-1 mb-3 sm:mb-4">
          <h3
            className="text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2 overflow-hidden"
            style={{
              fontFamily: 'brandfont, system-ui, sans-serif',
              display: '-webkit-box',
              WebkitLineClamp: 1,
              WebkitBoxOrient: 'vertical'
            }}
          >
            {data.title}
          </h3>
          <p
            className="text-sm text-gray-600 leading-relaxed overflow-hidden"
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}
          >
            {data.description}
          </p>
        </div>

        {/* Footer metadata - Engineer and Location as vertical list */}
        <div className="space-y-1 sm:space-y-2 mb-3 sm:mb-4">
          {/* Engineer */}
          <div>
            <span className="text-sm text-gray-700 font-medium">
              {data.engineerName}
            </span>
          </div>

          {/* Location */}
          <div>
            <span className="text-sm text-gray-600">
              {data.location}
            </span>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex space-x-2 sm:space-x-3">
          <button
            className="flex-1 px-3 sm:px-4 py-2 border border-gray-300 text-gray-700 text-xs sm:text-sm font-medium rounded-lg hover:bg-gray-50 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              data.onClick?.();
            }}
          >
            Task
          </button>
          <button
            className="flex-1 px-3 sm:px-4 py-2 bg-gray-900 text-white text-xs sm:text-sm font-medium rounded-lg hover:bg-gray-800 group-hover:bg-gray-800 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              data.onGeneratePermit?.();
            }}
          >
            Generate
          </button>
        </div>
      </div>
    </div>
  );
};

export default PermitCard;
