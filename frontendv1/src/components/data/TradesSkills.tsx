import { useState } from "react";
import { Plus, Search, Wrench } from "lucide-react";

const TradesSkills = () => {
	const [searchTerm, setSearchTerm] = useState("");

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">Trades & Skills</h2>
				<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
					<Plus className="h-4 w-4 mr-2" />
					Add Trade/Skill
				</button>
			</div>

			{/* Search */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="relative">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
					<input
						type="text"
						placeholder="Search trades and skills..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					/>
				</div>
			</div>

			{/* Placeholder Content */}
			<div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
				<Wrench className="h-12 w-12 text-gray-400 mx-auto mb-4" />
				<h3 className="text-lg font-medium text-gray-900 mb-2">
					Trades & Skills
				</h3>
				<p className="text-gray-500 mb-6">
					Manage trade classifications and skill requirements for workforce
					planning.
				</p>
				<button className="flex items-center mx-auto px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
					<Plus className="h-4 w-4 mr-2" />
					Create Your First Trade/Skill
				</button>
			</div>
		</div>
	);
};

export default TradesSkills;
