import { ReactNode } from "react";

export interface Tab {
	id: string;
	label: string;
	icon?: ReactNode;
	content: ReactNode;
}

interface TabContainerProps {
	tabs: Tab[];
	activeTab: string;
	onTabChange: (tabId: string) => void;
	variant?: "default" | "centered";
}

const TabContainer = ({ tabs, activeTab, onTabChange, variant = "default" }: TabContainerProps) => {
	const handleTabChange = (tabId: string) => {
		onTabChange(tabId);
	};

	const activeTabContent = tabs.find((tab) => tab.id === activeTab)?.content;

	const navClasses = variant === "centered"
		? "-mb-px flex space-x-8 justify-center"
		: "-mb-px flex space-x-8";

	return (
		<div className="w-full">
			{/* Tab Navigation */}
			<div className="border-b border-gray-200 mb-6">
				<nav className={navClasses}>
					{tabs.map((tab) => (
						<button
							key={tab.id}
							onClick={() => handleTabChange(tab.id)}
							className={`
                flex items-center py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                ${
									activeTab === tab.id
										? "border-green-500 text-green-600"
										: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
								}
              `}
						>
							{tab.icon && <span className="mr-2">{tab.icon}</span>}
							{tab.label}
						</button>
					))}
				</nav>
			</div>

			{/* Tab Content */}
			<div className="w-full">{activeTabContent}</div>
		</div>
	);
};

export default TabContainer;
