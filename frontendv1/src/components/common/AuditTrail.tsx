import React from 'react';
import { Clock, User } from 'lucide-react';

interface AuditTrailProps {
  entity: {
    createdAt: string;
    createdBy: string;
    updatedAt?: string;
    updatedBy?: string;
  };
  entityType: string;
  entityId: number;
}

export const AuditTrail: React.FC<AuditTrailProps> = ({
  entity,
  entityType,
  entityId,
}) => {
  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h4 className="text-sm font-medium text-gray-700 mb-3">Audit Information</h4>

      <div className="space-y-3">
        {/* Created */}
        <div className="flex items-start space-x-3">
          <Clock className="h-4 w-4 text-gray-400 mt-0.5" />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-900">Created</span>
              <span className="text-sm text-gray-500">{formatDateTime(entity.createdAt)}</span>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <User className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-600">by {entity.createdBy}</span>
            </div>
          </div>
        </div>

        {/* Updated */}
        {entity.updatedAt && (
          <div className="flex items-start space-x-3">
            <Clock className="h-4 w-4 text-gray-400 mt-0.5" />
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">Last Updated</span>
                <span className="text-sm text-gray-500">{formatDateTime(entity.updatedAt)}</span>
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <User className="h-3 w-3 text-gray-400" />
                <span className="text-xs text-gray-600">by {entity.updatedBy}</span>
              </div>
            </div>
          </div>
        )}

        {/* Entity Info */}
        <div className="pt-2 border-t border-gray-200">
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <span>{entityType} ID: {entityId}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
