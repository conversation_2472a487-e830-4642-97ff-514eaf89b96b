import React, { useState } from 'react';
import { Clock, Edit, Plus, Trash2, Eye } from 'lucide-react';

export interface AuditLogEntry {
  id: number;
  entityType: string;
  entityId: number;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW';
  fieldChanges?: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  performedBy: string;
  performedAt: string;
  ipAddress?: string;
  userAgent?: string;
  notes?: string;
}

interface AuditHistoryProps {
  entityType: string;
  entityId: number;
  auditLogs: AuditLogEntry[];
  showFilters?: boolean;
}

export const AuditHistory: React.FC<AuditHistoryProps> = ({
  entityType,
  entityId,
  auditLogs,
  showFilters = true,
}) => {
  const [filterAction, setFilterAction] = useState<string>('');
  const [filterUser, setFilterUser] = useState<string>('');
  const [filterDateFrom, setFilterDateFrom] = useState<string>('');
  const [filterDateTo, setFilterDateTo] = useState<string>('');

  const filteredLogs = auditLogs.filter(log => {
    if (filterAction && log.action !== filterAction) return false;
    if (filterUser && !log.performedBy.toLowerCase().includes(filterUser.toLowerCase())) return false;
    if (filterDateFrom && new Date(log.performedAt) < new Date(filterDateFrom)) return false;
    if (filterDateTo && new Date(log.performedAt) > new Date(filterDateTo)) return false;
    return true;
  });

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'CREATE':
        return <Plus className="h-4 w-4 text-green-600" />;
      case 'UPDATE':
        return <Edit className="h-4 w-4 text-blue-600" />;
      case 'DELETE':
        return <Trash2 className="h-4 w-4 text-red-600" />;
      case 'VIEW':
        return <Eye className="h-4 w-4 text-gray-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'CREATE':
        return 'bg-green-100 text-green-800';
      case 'UPDATE':
        return 'bg-blue-100 text-blue-800';
      case 'DELETE':
        return 'bg-red-100 text-red-800';
      case 'VIEW':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return 'null';
    if (typeof value === 'boolean') return value ? 'true' : 'false';
    if (typeof value === 'object') return JSON.stringify(value);
    return String(value);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">Audit History</h3>
          <span className="text-sm text-gray-500">
            {entityType} ID: {entityId}
          </span>
        </div>
      </div>

      {showFilters && (
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Action
              </label>
              <select
                value={filterAction}
                onChange={(e) => setFilterAction(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">All Actions</option>
                <option value="CREATE">Create</option>
                <option value="UPDATE">Update</option>
                <option value="DELETE">Delete</option>
                <option value="VIEW">View</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                User
              </label>
              <input
                type="text"
                value={filterUser}
                onChange={(e) => setFilterUser(e.target.value)}
                placeholder="Search by user..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                value={filterDateFrom}
                onChange={(e) => setFilterDateFrom(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                value={filterDateTo}
                onChange={(e) => setFilterDateTo(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      )}

      <div className="max-h-96 overflow-y-auto">
        {filteredLogs.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p>No audit history found</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredLogs.map((log) => (
              <div key={log.id} className="p-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {getActionIcon(log.action)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getActionColor(log.action)}`}>
                          {log.action}
                        </span>
                        <span className="text-sm font-medium text-gray-900">
                          {log.performedBy}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {new Date(log.performedAt).toLocaleString()}
                      </span>
                    </div>

                    {log.fieldChanges && log.fieldChanges.length > 0 && (
                      <div className="mt-3">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Field Changes:</h4>
                        <div className="space-y-2">
                          {log.fieldChanges.map((change, index) => (
                            <div key={index} className="bg-gray-50 rounded-md p-3">
                              <div className="text-sm font-medium text-gray-900 mb-1">
                                {change.field}
                              </div>
                              <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-500">From:</span>
                                  <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded text-red-800 font-mono text-xs">
                                    {formatValue(change.oldValue)}
                                  </div>
                                </div>
                                <div>
                                  <span className="text-gray-500">To:</span>
                                  <div className="mt-1 p-2 bg-green-50 border border-green-200 rounded text-green-800 font-mono text-xs">
                                    {formatValue(change.newValue)}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {log.notes && (
                      <div className="mt-3">
                        <h4 className="text-sm font-medium text-gray-700 mb-1">Notes:</h4>
                        <p className="text-sm text-gray-600">{log.notes}</p>
                      </div>
                    )}

                    {(log.ipAddress || log.userAgent) && (
                      <div className="mt-3 text-xs text-gray-500">
                        {log.ipAddress && <div>IP: {log.ipAddress}</div>}
                        {log.userAgent && <div>User Agent: {log.userAgent}</div>}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
