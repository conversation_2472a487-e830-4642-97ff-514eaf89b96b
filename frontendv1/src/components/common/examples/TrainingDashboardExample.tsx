import React, { useState, useMemo } from 'react';
import { Filter, Calendar, BookOpen, Users } from 'lucide-react';
import UniversalFilterModal, { FilterValues } from '../UniversalFilterModal';

// Example training data structure
interface TrainingRecord {
  id: string;
  workerName: string;
  trainingName: string;
  trainingType: 'safety' | 'technical' | 'compliance' | 'certification';
  status: 'completed' | 'in_progress' | 'scheduled' | 'overdue';
  completionDate?: string;
  expiryDate?: string;
  score?: number;
}

// Mock training data
const mockTrainingData: TrainingRecord[] = [
  {
    id: '1',
    workerName: '<PERSON>',
    trainingName: 'Safety Induction',
    trainingType: 'safety',
    status: 'completed',
    completionDate: '2024-01-15',
    expiryDate: '2025-01-15',
    score: 95
  },
  {
    id: '2',
    workerName: '<PERSON>',
    trainingName: 'Electrical Safety',
    trainingType: 'technical',
    status: 'in_progress',
    completionDate: undefined,
    expiryDate: undefined
  },
  {
    id: '3',
    workerName: '<PERSON>',
    trainingName: 'OSHA Compliance',
    trainingType: 'compliance',
    status: 'overdue',
    completionDate: '2023-06-10',
    expiryDate: '2024-06-10'
  }
];

const TrainingDashboardExample: React.FC = () => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [searchTerm, setSearchTerm] = useState('');

  // Dynamic filter configuration based on training data
  const filterConfig = useMemo(() => {
    const trainingTypes = Array.from(new Set(mockTrainingData.map(t => t.trainingType)));
    const statuses = Array.from(new Set(mockTrainingData.map(t => t.status)));

    return [
      {
        id: 'trainingType',
        label: 'Training Type',
        type: 'multiselect' as const,
        options: trainingTypes.map(type => ({
          value: type,
          label: type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' '),
          count: mockTrainingData.filter(t => t.trainingType === type).length
        }))
      },
      {
        id: 'status',
        label: 'Training Status',
        type: 'dropdown' as const,
        placeholder: 'Select status',
        options: statuses.map(status => ({
          value: status,
          label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),
          count: mockTrainingData.filter(t => t.status === status).length
        }))
      },
      {
        id: 'completionDate',
        label: 'Completion Date Range',
        type: 'daterange' as const
      },
      {
        id: 'expiryDate',
        label: 'Expiry Date Range',
        type: 'daterange' as const
      },
      {
        id: 'minScore',
        label: 'Minimum Score',
        type: 'number' as const,
        placeholder: 'Enter minimum score',
        min: 0,
        max: 100
      }
    ];
  }, []);

  // Filter training data based on active filters and search
  const filteredTrainingData = useMemo(() => {
    return mockTrainingData.filter(training => {
      // Search filter
      const matchesSearch = training.workerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           training.trainingName.toLowerCase().includes(searchTerm.toLowerCase());

      // Training type filter (multiselect)
      const matchesType = !activeFilters.trainingType || 
                         !Array.isArray(activeFilters.trainingType) ||
                         activeFilters.trainingType.length === 0 ||
                         activeFilters.trainingType.includes(training.trainingType);

      // Status filter
      const matchesStatus = !activeFilters.status || training.status === activeFilters.status;

      // Completion date range filter
      const matchesCompletionDate = !activeFilters.completionDate || 
                                   !activeFilters.completionDate.start ||
                                   !training.completionDate ||
                                   (new Date(training.completionDate) >= new Date(activeFilters.completionDate.start) &&
                                    (!activeFilters.completionDate.end || new Date(training.completionDate) <= new Date(activeFilters.completionDate.end)));

      // Expiry date range filter
      const matchesExpiryDate = !activeFilters.expiryDate || 
                               !activeFilters.expiryDate.start ||
                               !training.expiryDate ||
                               (new Date(training.expiryDate) >= new Date(activeFilters.expiryDate.start) &&
                                (!activeFilters.expiryDate.end || new Date(training.expiryDate) <= new Date(activeFilters.expiryDate.end)));

      // Minimum score filter
      const matchesScore = !activeFilters.minScore || 
                          !training.score ||
                          training.score >= parseInt(activeFilters.minScore);

      return matchesSearch && matchesType && matchesStatus && matchesCompletionDate && matchesExpiryDate && matchesScore;
    });
  }, [mockTrainingData, searchTerm, activeFilters]);

  // Filter handlers
  const handleApplyFilters = (values: FilterValues) => {
    setActiveFilters(values);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
  };

  // Count active filters
  const activeFilterCount = Object.values(activeFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== '' && v !== null);
    }
    return value !== '' && value !== null && value !== false;
  }).length;

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Training Dashboard Example</h1>
        <p className="text-gray-600">Example implementation of Universal Filter Modal for training management</p>
      </div>

      {/* Filter Controls */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center space-x-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <input
              type="text"
              placeholder="Search training records..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
            <BookOpen className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          </div>

          {/* Filter Button */}
          <button
            onClick={() => setIsFilterOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {activeFilterCount > 0 && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {activeFilterCount}
              </span>
            )}
          </button>
        </div>

        <div className="text-sm text-gray-500">
          Showing {filteredTrainingData.length} of {mockTrainingData.length} records
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="mb-6 flex flex-wrap gap-2">
          {Object.entries(activeFilters).map(([key, value]) => {
            if (!value || (Array.isArray(value) && value.length === 0)) return null;
            
            const filter = filterConfig.find(f => f.id === key);
            if (!filter) return null;

            let displayValue = '';
            if (Array.isArray(value)) {
              displayValue = value.map(v => {
                const option = filter.options?.find(opt => opt.value === v);
                return option ? option.label : v;
              }).join(', ');
            } else if (typeof value === 'object' && value.start) {
              displayValue = `${value.start} - ${value.end || 'Present'}`;
            } else {
              const option = filter.options?.find(opt => opt.value === value);
              displayValue = option ? option.label : value.toString();
            }

            return (
              <span
                key={key}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800"
              >
                {filter.label}: {displayValue}
                <button
                  onClick={() => {
                    const newFilters = { ...activeFilters };
                    delete newFilters[key];
                    setActiveFilters(newFilters);
                  }}
                  className="ml-2 text-green-600 hover:text-green-800"
                >
                  ×
                </button>
              </span>
            );
          })}
        </div>
      )}

      {/* Training Records Table */}
      <div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Worker
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Training
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Dates
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Score
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredTrainingData.map((training) => (
              <tr key={training.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-gray-400 mr-3" />
                    <div className="text-sm font-medium text-gray-900">
                      {training.workerName}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{training.trainingName}</div>
                  <div className="text-sm text-gray-500 capitalize">
                    {training.trainingType.replace('_', ' ')}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(training.status)}`}>
                    {training.status.replace('_', ' ')}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    <div>
                      {training.completionDate && (
                        <div>Completed: {training.completionDate}</div>
                      )}
                      {training.expiryDate && (
                        <div>Expires: {training.expiryDate}</div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {training.score ? `${training.score}%` : '-'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {filteredTrainingData.length === 0 && (
          <div className="px-6 py-10 text-center">
            <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No training records found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No training records match your search criteria.
            </p>
          </div>
        )}
      </div>

      {/* Universal Filter Modal */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Training Records"
        subtitle="Refine your search to find specific training records"
        filters={filterConfig}
        initialValues={activeFilters}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        size="lg"
      />
    </div>
  );
};

export default TrainingDashboardExample;
