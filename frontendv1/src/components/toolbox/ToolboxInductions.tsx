import React from 'react';
import { mockInductions } from '../../mock/siteData';

interface ToolboxInductionsProps {
  siteId: string;
  onSelectInduction: (id: string) => void;
}

const ToolboxInductions: React.FC<ToolboxInductionsProps> = ({ onSelectInduction }) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Inductions</h2>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          Create New Induction
        </button>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-medium">All Inductions</h3>
        </div>
        <ul className="divide-y divide-gray-200">
          {mockInductions.map(induction => (
            <li 
              key={induction.id} 
              className="p-4 hover:bg-gray-50 cursor-pointer"
              onClick={() => onSelectInduction(induction.id)}
            >
              <div className="flex justify-between items-center">
                <span className="font-medium">{induction.title}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${induction.completed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                  {induction.completed ? 'Completed' : 'Pending'}
                </span>
              </div>
              {induction.date && (
                <p className="text-xs text-gray-500 mt-1">
                  Completed on: {induction.date.toLocaleDateString()}
                </p>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default ToolboxInductions;