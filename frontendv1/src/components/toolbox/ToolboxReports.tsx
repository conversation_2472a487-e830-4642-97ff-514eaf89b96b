import React, { useState } from 'react';
import { BarChart3, Download, Printer, Calendar, Users, CheckCircle } from 'lucide-react';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';

interface ToolboxReportsProps {
  siteId: string;
}

// Mock data for reports
const mockAttendanceData = [
  { month: 'Jan', attendance: 85 },
  { month: 'Feb', attendance: 88 },
  { month: 'Mar', attendance: 82 },
  { month: 'Apr', attendance: 91 },
  { month: 'May', attendance: 89 },
  { month: 'Jun', attendance: 93 },
];

const mockInductionsByType = [
  { name: 'Safety', count: 12, percentage: 40 },
  { name: 'Equipment', count: 8, percentage: 27 },
  { name: 'Procedures', count: 6, percentage: 20 },
  { name: 'Compliance', count: 4, percentage: 13 },
];

const mockCompletionRates = [
  { name: 'Completed', count: 24, percentage: 80 },
  { name: 'Pending', count: 6, percentage: 20 },
];

const COLORS = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6'];

const ToolboxReports: React.FC<ToolboxReportsProps> = ({  }) => {
  const [reportType, setReportType] = useState('attendance');
  const [dateRange, setDateRange] = useState('last-30');

  const generateReport = () => {
    console.log(`Generating ${reportType} report for ${dateRange}`);
    // Implementation would fetch actual report data
  };

  const exportReport = (format: 'pdf' | 'excel') => {
    console.log(`Exporting ${reportType} report as ${format}`);
    // Implementation would handle actual export
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Toolbox Talks Reports</h2>
        <div className="flex space-x-2">
          <button 
            onClick={() => exportReport('pdf')}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <Printer className="h-4 w-4 mr-1" />
            Print
          </button>
          <button 
            onClick={() => exportReport('excel')}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </button>
        </div>
      </div>

      {/* Report Builder */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <h3 className="text-lg font-medium mb-4">Custom Report</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Report Type
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
              >
                <option value="attendance">Attendance Report</option>
                <option value="inductions">Inductions Report</option>
                <option value="completion">Completion Rates</option>
                <option value="performance">Worker Performance</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date Range
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
              >
                <option value="last-7">Last 7 Days</option>
                <option value="last-30">Last 30 Days</option>
                <option value="last-90">Last 90 Days</option>
                <option value="year-to-date">Year to Date</option>
                <option value="custom">Custom Range</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={generateReport}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
              >
                <BarChart3 className="h-4 w-4 mr-2 inline-block" />
                Generate Report
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Report Visualizations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Attendance Trends */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Attendance Trends</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={mockAttendanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[0, 100]} />
                <Tooltip formatter={(value) => [`${value}%`, 'Attendance']} />
                <Line 
                  type="monotone" 
                  dataKey="attendance" 
                  stroke="#10B981" 
                  strokeWidth={2} 
                  dot={{ r: 4 }} 
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Inductions by Type */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Inductions by Type</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={mockInductionsByType}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ name, percentage }) => `${name}: ${percentage}%`}
                >
                  {mockInductionsByType.map((_entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Completion Rates */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Completion Rates</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={mockCompletionRates}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[0, 30]} />
                <Tooltip formatter={(value) => [value, 'Count']} />
                <Bar dataKey="count" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-2">
            {mockCompletionRates.map((item, index) => (
              <div key={item.name} className="flex items-center text-sm">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: index === 0 ? '#3B82F6' : '#EF4444' }}
                ></div>
                <span className="text-gray-600">{item.name}: {item.percentage}%</span>
              </div>
            ))}
          </div>
        </div>

        {/* Summary Stats */}
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-medium mb-4">Summary Statistics</h3>
          <div className="grid grid-cols-1 gap-4">
            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-blue-100 rounded-full mr-3">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Toolbox Talks</p>
                <p className="text-lg font-semibold">32</p>
              </div>
            </div>
            
            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-green-100 rounded-full mr-3">
                <Users className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Average Attendance</p>
                <p className="text-lg font-semibold">87%</p>
              </div>
            </div>
            
            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-yellow-100 rounded-full mr-3">
                <CheckCircle className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Completion Rate</p>
                <p className="text-lg font-semibold">80%</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolboxReports;