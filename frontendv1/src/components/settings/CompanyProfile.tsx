import { useState } from "react";
import {
	Building,
	MapPin,
	Phone,
	Mail,
	Globe,
	Save,
	Upload,
} from "lucide-react";

interface CompanyInfo {
	name: string;
	registrationNumber: string;
	taxId: string;
	address: {
		street: string;
		city: string;
		state: string;
		postalCode: string;
		country: string;
	};
	contact: {
		phone: string;
		email: string;
		website?: string;
	};
	logo?: string;
	description?: string;
}

const CompanyProfile = () => {
	const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
		name: "Kimaru Construction Ltd",
		registrationNumber: "C.123456/2020",
		taxId: "P051234567M",
		address: {
			street: "123 Waiyaki Way",
			city: "Nairobi",
			state: "Nairobi County",
			postalCode: "00100",
			country: "Kenya",
		},
		contact: {
			phone: "+254 20 123 4567",
			email: "<EMAIL>",
			website: "https://kimaruconstruction.co.ke",
		},
		description:
			"Leading construction company in Kenya specializing in commercial and residential projects.",
	});

	const [isEditing, setIsEditing] = useState(false);
	const [hasChanges, setHasChanges] = useState(false);

	const handleInputChange = (field: string, value: string) => {
		setCompanyInfo((prev) => {
			const keys = field.split(".");
			if (keys.length === 1) {
				return { ...prev, [field]: value };
			} else if (keys.length === 2) {
				return {
					...prev,
					[keys[0]]: {
						...(prev[keys[0] as keyof CompanyInfo] as object),
						[keys[1]]: value,
					},
				};
			}
			return prev;
		});
		setHasChanges(true);
	};

	const handleSave = () => {
		// TODO: Implement save functionality
		console.log("Saving company info:", companyInfo);
		setIsEditing(false);
		setHasChanges(false);
	};

	const handleCancel = () => {
		// TODO: Reset to original values
		setIsEditing(false);
		setHasChanges(false);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h2 className="text-2xl font-bold text-gray-900">Company Profile</h2>
					<p className="text-sm text-gray-600">
						Manage your company information and branding
					</p>
				</div>
				<div className="flex gap-2">
					{isEditing ? (
						<>
							<button
								onClick={handleCancel}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button
								onClick={handleSave}
								disabled={!hasChanges}
								className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-green-600 transition-colors disabled:opacity-50"
							>
								<Save className="h-4 w-4 mr-2" />
								Save Changes
							</button>
						</>
					) : (
						<button
							onClick={() => setIsEditing(true)}
							className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-600 transition-colors"
						>
							Edit Profile
						</button>
					)}
				</div>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Company Logo */}
				<div className="lg:col-span-1">
					<div className="bg-white rounded-lg border border-gray-200 p-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">
							Company Logo
						</h3>
						<div className="text-center">
							<div className="mx-auto h-32 w-32 rounded-lg bg-gray-100 flex items-center justify-center mb-4">
								{companyInfo.logo ? (
									<img
										src={companyInfo.logo}
										alt="Company Logo"
										className="h-full w-full object-contain rounded-lg"
									/>
								) : (
									<Building className="h-16 w-16 text-gray-400" />
								)}
							</div>
							{isEditing && (
								<button className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
									<Upload className="h-4 w-4 mr-2" />
									Upload Logo
								</button>
							)}
						</div>
					</div>
				</div>

				{/* Company Information */}
				<div className="lg:col-span-2 space-y-6">
					{/* Basic Information */}
					<div className="bg-white rounded-lg border border-gray-200 p-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">
							Basic Information
						</h3>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Company Name
								</label>
								{isEditing ? (
									<input
										type="text"
										value={companyInfo.name}
										onChange={(e) => handleInputChange("name", e.target.value)}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2">
										{companyInfo.name}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Registration Number
								</label>
								{isEditing ? (
									<input
										type="text"
										value={companyInfo.registrationNumber}
										onChange={(e) =>
											handleInputChange("registrationNumber", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2">
										{companyInfo.registrationNumber}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Tax ID (PIN)
								</label>
								{isEditing ? (
									<input
										type="text"
										value={companyInfo.taxId}
										onChange={(e) => handleInputChange("taxId", e.target.value)}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2">
										{companyInfo.taxId}
									</p>
								)}
							</div>
						</div>
						<div className="mt-4">
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Description
							</label>
							{isEditing ? (
								<textarea
									value={companyInfo.description || ""}
									onChange={(e) =>
										handleInputChange("description", e.target.value)
									}
									rows={3}
									className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									placeholder="Brief description of your company..."
								/>
							) : (
								<p className="text-sm text-gray-900 py-2">
									{companyInfo.description || "No description provided"}
								</p>
							)}
						</div>
					</div>

					{/* Contact Information */}
					<div className="bg-white rounded-lg border border-gray-200 p-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
							<Phone className="h-5 w-5 mr-2" />
							Contact Information
						</h3>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Phone
								</label>
								{isEditing ? (
									<input
										type="tel"
										value={companyInfo.contact.phone}
										onChange={(e) =>
											handleInputChange("contact.phone", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2 flex items-center">
										<Phone className="h-4 w-4 mr-2 text-gray-400" />
										{companyInfo.contact.phone}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Email
								</label>
								{isEditing ? (
									<input
										type="email"
										value={companyInfo.contact.email}
										onChange={(e) =>
											handleInputChange("contact.email", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2 flex items-center">
										<Mail className="h-4 w-4 mr-2 text-gray-400" />
										{companyInfo.contact.email}
									</p>
								)}
							</div>
							<div className="md:col-span-2">
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Website
								</label>
								{isEditing ? (
									<input
										type="url"
										value={companyInfo.contact.website || ""}
										onChange={(e) =>
											handleInputChange("contact.website", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
										placeholder="https://yourcompany.com"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2 flex items-center">
										<Globe className="h-4 w-4 mr-2 text-gray-400" />
										{companyInfo.contact.website || "No website provided"}
									</p>
								)}
							</div>
						</div>
					</div>

					{/* Address Information */}
					<div className="bg-white rounded-lg border border-gray-200 p-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
							<MapPin className="h-5 w-5 mr-2" />
							Address
						</h3>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="md:col-span-2">
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Street Address
								</label>
								{isEditing ? (
									<input
										type="text"
										value={companyInfo.address.street}
										onChange={(e) =>
											handleInputChange("address.street", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2">
										{companyInfo.address.street}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									City
								</label>
								{isEditing ? (
									<input
										type="text"
										value={companyInfo.address.city}
										onChange={(e) =>
											handleInputChange("address.city", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2">
										{companyInfo.address.city}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									State/County
								</label>
								{isEditing ? (
									<input
										type="text"
										value={companyInfo.address.state}
										onChange={(e) =>
											handleInputChange("address.state", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2">
										{companyInfo.address.state}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Postal Code
								</label>
								{isEditing ? (
									<input
										type="text"
										value={companyInfo.address.postalCode}
										onChange={(e) =>
											handleInputChange("address.postalCode", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								) : (
									<p className="text-sm text-gray-900 py-2">
										{companyInfo.address.postalCode}
									</p>
								)}
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Country
								</label>
								{isEditing ? (
									<select
										value={companyInfo.address.country}
										onChange={(e) =>
											handleInputChange("address.country", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									>
										<option value="Kenya">Kenya</option>
										<option value="Uganda">Uganda</option>
										<option value="Tanzania">Tanzania</option>
										<option value="Rwanda">Rwanda</option>
									</select>
								) : (
									<p className="text-sm text-gray-900 py-2">
										{companyInfo.address.country}
									</p>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default CompanyProfile;
