import { useState } from "react";
import {
	Save,
	Clock,
	Globe,
	Database,
	Shield,
	AlertTriangle,
} from "lucide-react";

interface SystemSettings {
	timezone: string;
	dateFormat: string;
	timeFormat: string;
	currency: string;
	language: string;
	workingHours: {
		start: string;
		end: string;
		workingDays: string[];
	};
	backup: {
		enabled: boolean;
		frequency: string;
		retentionDays: number;
	};
	security: {
		sessionTimeout: number;
		passwordPolicy: {
			minLength: number;
			requireUppercase: boolean;
			requireNumbers: boolean;
			requireSpecialChars: boolean;
		};
		twoFactorAuth: boolean;
	};
}

const SystemConfig = () => {
	const [settings, setSettings] = useState<SystemSettings>({
		timezone: "Africa/Nairobi",
		dateFormat: "DD/MM/YYYY",
		timeFormat: "24h",
		currency: "KES",
		language: "en",
		workingHours: {
			start: "08:00",
			end: "17:00",
			workingDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
		},
		backup: {
			enabled: true,
			frequency: "daily",
			retentionDays: 30,
		},
		security: {
			sessionTimeout: 480, // 8 hours in minutes
			passwordPolicy: {
				minLength: 8,
				requireUppercase: true,
				requireNumbers: true,
				requireSpecialChars: true,
			},
			twoFactorAuth: false,
		},
	});

	const [hasChanges, setHasChanges] = useState(false);

	const handleInputChange = (field: string, value: any) => {
		setSettings((prev) => {
			const keys = field.split(".");
			if (keys.length === 1) {
				return { ...prev, [field]: value };
			} else if (keys.length === 2) {
				return {
					...prev,
					[keys[0]]: {
						...((prev[keys[0] as keyof SystemSettings]) as object),
						[keys[1]]: value,
					},
				};
			} else if (keys.length === 3) {
				return {
					...prev,
					[keys[0]]: {
						...((prev[keys[0] as keyof SystemSettings]) as object),
						[keys[1]]: {
							...(prev[keys[0] as keyof SystemSettings] as any)[keys[1]],
							[keys[2]]: value,
						},
					},
				};
			}
			return prev;
		});
		setHasChanges(true);
	};

	const handleSave = () => {
		// TODO: Implement save functionality
		console.log("Saving system settings:", settings);
		setHasChanges(false);
	};

	const workingDayOptions = [
		{ value: "monday", label: "Monday" },
		{ value: "tuesday", label: "Tuesday" },
		{ value: "wednesday", label: "Wednesday" },
		{ value: "thursday", label: "Thursday" },
		{ value: "friday", label: "Friday" },
		{ value: "saturday", label: "Saturday" },
		{ value: "sunday", label: "Sunday" },
	];

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h2 className="text-2xl font-bold text-gray-900">
						System Configuration
					</h2>
					<p className="text-sm text-gray-600">
						Configure system-wide settings and preferences
					</p>
				</div>
				<button
					onClick={handleSave}
					disabled={!hasChanges}
					className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-green-600 transition-colors disabled:opacity-50"
				>
					<Save className="h-4 w-4 mr-2" />
					Save Changes
				</button>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* General Settings */}
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
						<Globe className="h-5 w-5 mr-2" />
						General Settings
					</h3>
					<div className="space-y-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Timezone
							</label>
							<select
								value={settings.timezone}
								onChange={(e) => handleInputChange("timezone", e.target.value)}
								className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
							>
								<option value="Africa/Nairobi">Africa/Nairobi (EAT)</option>
								<option value="Africa/Kampala">Africa/Kampala (EAT)</option>
								<option value="Africa/Dar_es_Salaam">
									Africa/Dar_es_Salaam (EAT)
								</option>
							</select>
						</div>
						<div className="grid grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Date Format
								</label>
								<select
									value={settings.dateFormat}
									onChange={(e) =>
										handleInputChange("dateFormat", e.target.value)
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
								>
									<option value="DD/MM/YYYY">DD/MM/YYYY</option>
									<option value="MM/DD/YYYY">MM/DD/YYYY</option>
									<option value="YYYY-MM-DD">YYYY-MM-DD</option>
								</select>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Time Format
								</label>
								<select
									value={settings.timeFormat}
									onChange={(e) =>
										handleInputChange("timeFormat", e.target.value)
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
								>
									<option value="24h">24 Hour</option>
									<option value="12h">12 Hour (AM/PM)</option>
								</select>
							</div>
						</div>
						<div className="grid grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Currency
								</label>
								<select
									value={settings.currency}
									onChange={(e) =>
										handleInputChange("currency", e.target.value)
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
								>
									<option value="KES">KES (Kenyan Shilling)</option>
									<option value="UGX">UGX (Ugandan Shilling)</option>
									<option value="TZS">TZS (Tanzanian Shilling)</option>
									<option value="USD">USD (US Dollar)</option>
								</select>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Language
								</label>
								<select
									value={settings.language}
									onChange={(e) =>
										handleInputChange("language", e.target.value)
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
								>
									<option value="en">English</option>
									<option value="sw">Swahili</option>
								</select>
							</div>
						</div>
					</div>
				</div>

				{/* Working Hours */}
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
						<Clock className="h-5 w-5 mr-2" />
						Working Hours
					</h3>
					<div className="space-y-4">
						<div className="grid grid-cols-2 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Start Time
								</label>
								<input
									type="time"
									value={settings.workingHours.start}
									onChange={(e) =>
										handleInputChange("workingHours.start", e.target.value)
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
								/>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									End Time
								</label>
								<input
									type="time"
									value={settings.workingHours.end}
									onChange={(e) =>
										handleInputChange("workingHours.end", e.target.value)
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
								/>
							</div>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Working Days
							</label>
							<div className="space-y-2">
								{workingDayOptions.map((day) => (
									<label key={day.value} className="flex items-center">
										<input
											type="checkbox"
											checked={settings.workingHours.workingDays.includes(
												day.value,
											)}
											onChange={(e) => {
												const newDays = e.target.checked
													? [...settings.workingHours.workingDays, day.value]
													: settings.workingHours.workingDays.filter(
															(d) => d !== day.value,
														);
												handleInputChange("workingHours.workingDays", newDays);
											}}
											className="rounded border-gray-300 text-green-600 focus:ring-green-500"
										/>
										<span className="ml-2 text-sm text-gray-700">
											{day.label}
										</span>
									</label>
								))}
							</div>
						</div>
					</div>
				</div>

				{/* Backup Settings */}
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
						<Database className="h-5 w-5 mr-2" />
						Backup Settings
					</h3>
					<div className="space-y-4">
						<div>
							<label className="flex items-center">
								<input
									type="checkbox"
									checked={settings.backup.enabled}
									onChange={(e) =>
										handleInputChange("backup.enabled", e.target.checked)
									}
									className="rounded border-gray-300 text-green-600 focus:ring-green-500"
								/>
								<span className="ml-2 text-sm font-medium text-gray-700">
									Enable Automatic Backups
								</span>
							</label>
						</div>
						{settings.backup.enabled && (
							<>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Backup Frequency
									</label>
									<select
										value={settings.backup.frequency}
										onChange={(e) =>
											handleInputChange("backup.frequency", e.target.value)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									>
										<option value="hourly">Hourly</option>
										<option value="daily">Daily</option>
										<option value="weekly">Weekly</option>
									</select>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Retention Period (Days)
									</label>
									<input
										type="number"
										min="1"
										max="365"
										value={settings.backup.retentionDays}
										onChange={(e) =>
											handleInputChange(
												"backup.retentionDays",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
							</>
						)}
					</div>
				</div>

				{/* Security Settings */}
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
						<Shield className="h-5 w-5 mr-2" />
						Security Settings
					</h3>
					<div className="space-y-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Session Timeout (minutes)
							</label>
							<input
								type="number"
								min="30"
								max="1440"
								value={settings.security.sessionTimeout}
								onChange={(e) =>
									handleInputChange(
										"security.sessionTimeout",
										parseInt(e.target.value),
									)
								}
								className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
							/>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Password Policy
							</label>
							<div className="space-y-2">
								<div>
									<label className="block text-sm text-gray-600 mb-1">
										Minimum Length
									</label>
									<input
										type="number"
										min="6"
										max="32"
										value={settings.security.passwordPolicy.minLength}
										onChange={(e) =>
											handleInputChange(
												"security.passwordPolicy.minLength",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
								<label className="flex items-center">
									<input
										type="checkbox"
										checked={settings.security.passwordPolicy.requireUppercase}
										onChange={(e) =>
											handleInputChange(
												"security.passwordPolicy.requireUppercase",
												e.target.checked,
											)
										}
										className="rounded border-gray-300 text-green-600 focus:ring-green-500"
									/>
									<span className="ml-2 text-sm text-gray-700">
										Require uppercase letters
									</span>
								</label>
								<label className="flex items-center">
									<input
										type="checkbox"
										checked={settings.security.passwordPolicy.requireNumbers}
										onChange={(e) =>
											handleInputChange(
												"security.passwordPolicy.requireNumbers",
												e.target.checked,
											)
										}
										className="rounded border-gray-300 text-green-600 focus:ring-green-500"
									/>
									<span className="ml-2 text-sm text-gray-700">
										Require numbers
									</span>
								</label>
								<label className="flex items-center">
									<input
										type="checkbox"
										checked={
											settings.security.passwordPolicy.requireSpecialChars
										}
										onChange={(e) =>
											handleInputChange(
												"security.passwordPolicy.requireSpecialChars",
												e.target.checked,
											)
										}
										className="rounded border-gray-300 text-green-600 focus:ring-green-500"
									/>
									<span className="ml-2 text-sm text-gray-700">
										Require special characters
									</span>
								</label>
							</div>
						</div>
						<div>
							<label className="flex items-center">
								<input
									type="checkbox"
									checked={settings.security.twoFactorAuth}
									onChange={(e) =>
										handleInputChange(
											"security.twoFactorAuth",
											e.target.checked,
										)
									}
									className="rounded border-gray-300 text-green-600 focus:ring-green-500"
								/>
								<span className="ml-2 text-sm font-medium text-gray-700">
									Enable Two-Factor Authentication
								</span>
							</label>
						</div>
					</div>
				</div>
			</div>

			{/* Warning Notice */}
			<div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
				<div className="flex">
					<div className="flex-shrink-0">
						<AlertTriangle className="h-5 w-5 text-amber-400" />
					</div>
					<div className="ml-3">
						<h3 className="text-sm font-medium text-amber-800">
							Important Notice
						</h3>
						<div className="mt-2 text-sm text-amber-700">
							<p>
								Changes to system configuration will affect all users. Some
								changes may require users to log in again. Please ensure you
								test changes in a development environment first.
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SystemConfig;
