import { ReactNode } from "react";
import { useUser } from "../../hooks/userContext";
import { type UserType } from "../../types/auth";

interface RoleProtectedComponentProps {
	allowedRoles: UserType["role"][];
	children: ReactNode;
	fallback?: ReactNode;
}

export function RoleProtectedComponent({
	allowedRoles,
	children,
	fallback = null,
}: RoleProtectedComponentProps) {
	const userData = useUser();
	if (!userData || !userData.user) return <>{fallback}</>;
	if (!allowedRoles.includes(userData.user.role)) return <>{fallback}</>;

	return <>{children}</>;
}
