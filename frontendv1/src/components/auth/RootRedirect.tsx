import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuthContext';
import { LoadingSpinner } from '../common/LoadingSpinner';

/**
 * RootRedirect component handles the root route (/) redirection logic
 * - If user is not authenticated: redirect to /landing
 * - If user is authenticated: redirect to /dashboard (main app)
 * - Shows loading spinner while checking authentication status
 */
const RootRedirect: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#f3f2ee] flex items-center justify-center">
        <LoadingSpinner size="lg" text="Loading..." />
      </div>
    );
  }

  // Redirect based on authentication status
  if (isAuthenticated) {
    // User is signed in, redirect to main dashboard
    return <Navigate to="/dashboard" replace />;
  } else {
    // User is not signed in, redirect to landing page
    return <Navigate to="/landing" replace />;
  }
};

export default RootRedirect;
