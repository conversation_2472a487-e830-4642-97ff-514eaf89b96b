import { useState, useEffect } from "react";
import {
	Package,
	AlertTriangle,
	TrendingUp,
	Calendar,
	Users,
	ShoppingCart,
	HardHat,
	Shield,
} from "lucide-react";

interface PPEDashboardProps {
	siteId: string;
	onNavigateToTab?: (tabId: string) => void;
}

const PPEDashboard = ({ siteId, onNavigateToTab }: PPEDashboardProps) => {
	const [dashboardData, setDashboardData] = useState({
		totalPPEItems: 0,
		availableItems: 0,
		lowStockItems: 0,
		reservedItems: 0,
		assignedToday: 0,
		expiringItems: 0,
		totalValue: 0,
		categories: [] as any[],
	});
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		// Mock data - in real app, this would come from API
		const mockData = {
			totalPPEItems: 142,
			availableItems: 115,
			lowStockItems: 3,
			reservedItems: 24,
			assignedToday: 8,
			expiringItems: 2,
			totalValue: 3580,
			categories: [
				{ name: "Head Protection", count: 45, value: 1125 },
				{ name: "Body Protection", count: 38, value: 570 },
				{ name: "Hand Protection", count: 35, value: 280 },
				{ name: "Foot Protection", count: 24, value: 1605 },
			],
		};

		setTimeout(() => {
			setDashboardData(mockData);
			setIsLoading(false);
		}, 500);
	}, [siteId]);

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
						{[...Array(4)].map((_, i) => (
							<div key={i} className="h-24 bg-gray-200 rounded"></div>
						))}
					</div>
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="h-64 bg-gray-200 rounded"></div>
						<div className="h-64 bg-gray-200 rounded"></div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">PPE Dashboard</h2>
				<div className="flex gap-2">
					<button
						onClick={() => onNavigateToTab?.("inventory")}
						className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
					>
						<Package className="h-4 w-4 mr-2" />
						View Inventory
					</button>
					<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
						<ShoppingCart className="h-4 w-4 mr-2" />
						Restock PPE
					</button>
				</div>
			</div>

			{/* Key Metrics */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total PPE Items</p>
							<p className="text-2xl font-bold">{dashboardData.totalPPEItems}</p>
						</div>
						<Package className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Available</p>
							<p className="text-2xl font-bold text-green-600">
								{dashboardData.availableItems}
							</p>
						</div>
						<TrendingUp className="h-8 w-8 text-green-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Low Stock</p>
							<p className="text-2xl font-bold text-red-600">
								{dashboardData.lowStockItems}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-red-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Reserved</p>
							<p className="text-2xl font-bold text-yellow-600">
								{dashboardData.reservedItems}
							</p>
						</div>
						<Calendar className="h-8 w-8 text-yellow-500" />
					</div>
				</div>
			</div>

			{/* Secondary Metrics */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Assigned Today</p>
							<p className="text-2xl font-bold text-blue-600">
								{dashboardData.assignedToday}
							</p>
						</div>
						<Users className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Expiring Soon</p>
							<p className="text-2xl font-bold text-orange-600">
								{dashboardData.expiringItems}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-orange-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Value</p>
							<p className="text-2xl font-bold text-purple-600">
								${dashboardData.totalValue.toLocaleString()}
							</p>
						</div>
						<Package className="h-8 w-8 text-purple-500" />
					</div>
				</div>
			</div>

			{/* PPE Categories Overview */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold mb-4">PPE Categories</h3>
					<div className="space-y-4">
						{dashboardData.categories.map((category, index) => (
							<div key={index} className="flex items-center justify-between">
								<div className="flex items-center space-x-3">
									<div className="p-2 bg-green-100 rounded-lg">
										<HardHat className="h-5 w-5 text-green-600" />
									</div>
									<div>
										<p className="font-medium">{category.name}</p>
										<p className="text-sm text-gray-500">{category.count} items</p>
									</div>
								</div>
								<div className="text-right">
									<p className="font-medium">${category.value}</p>
									<p className="text-sm text-gray-500">value</p>
								</div>
							</div>
						))}
					</div>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
					<div className="space-y-3">
						<button
							onClick={() => onNavigateToTab?.("inventory")}
							className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
						>
							<div className="flex items-center space-x-3">
								<Package className="h-5 w-5 text-blue-500" />
								<span>Manage Inventory</span>
							</div>
							<span className="text-gray-400">→</span>
						</button>
						<button
							onClick={() => onNavigateToTab?.("maintenance")}
							className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
						>
							<div className="flex items-center space-x-3">
								<Shield className="h-5 w-5 text-green-500" />
								<span>PPE Maintenance</span>
							</div>
							<span className="text-gray-400">→</span>
						</button>
						<button className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
							<div className="flex items-center space-x-3">
								<Users className="h-5 w-5 text-purple-500" />
								<span>Assign PPE</span>
							</div>
							<span className="text-gray-400">→</span>
						</button>
						<button className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
							<div className="flex items-center space-x-3">
								<ShoppingCart className="h-5 w-5 text-orange-500" />
								<span>Restock Items</span>
							</div>
							<span className="text-gray-400">→</span>
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default PPEDashboard;
