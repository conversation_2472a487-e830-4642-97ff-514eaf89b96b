import { useState, useEffect } from "react";
import {
	Search,
	Filter,
	Calendar,
	CheckCircle,
	AlertTriangle,
	Clock,
	User,
	Plus,
	Eye,
} from "lucide-react";

interface PPEMaintenanceRecord {
	id: string;
	ppeItemId: string;
	ppeItemName: string;
	ppeItemSku: string;
	maintenanceType: "inspection" | "cleaning" | "repair" | "replacement";
	status: "scheduled" | "in-progress" | "completed" | "overdue";
	scheduledDate: Date;
	completedDate?: Date;
	assignedTo?: string;
	notes?: string;
	nextMaintenanceDate?: Date;
}

interface PPEMaintenanceProps {
	siteId: string;
}

const PPEMaintenance = ({ siteId }: PPEMaintenanceProps) => {
	const [maintenanceRecords, setMaintenanceRecords] = useState<PPEMaintenanceRecord[]>([]);
	const [filteredRecords, setFilteredRecords] = useState<PPEMaintenanceRecord[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState("");
	const [typeFilter, setTypeFilter] = useState("");
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		// Mock data
		const mockRecords: PPEMaintenanceRecord[] = [
			{
				id: "maint-001",
				ppeItemId: "ppe-001",
				ppeItemName: "Safety Helmet - Hard Hat",
				ppeItemSku: "PPE-HH-001",
				maintenanceType: "inspection",
				status: "scheduled",
				scheduledDate: new Date("2024-12-20"),
				assignedTo: "John Mwangi",
				nextMaintenanceDate: new Date("2025-01-20"),
			},
			{
				id: "maint-002",
				ppeItemId: "ppe-002",
				ppeItemName: "Safety Vest - High Visibility",
				ppeItemSku: "PPE-SV-001",
				maintenanceType: "cleaning",
				status: "overdue",
				scheduledDate: new Date("2024-12-10"),
				assignedTo: "Sarah Ochieng",
			},
			{
				id: "maint-003",
				ppeItemId: "ppe-003",
				ppeItemName: "Safety Gloves - Cut Resistant",
				ppeItemSku: "PPE-GL-001",
				maintenanceType: "inspection",
				status: "completed",
				scheduledDate: new Date("2024-12-15"),
				completedDate: new Date("2024-12-15"),
				assignedTo: "David Kimani",
				notes: "All items inspected and in good condition",
				nextMaintenanceDate: new Date("2025-01-15"),
			},
		];

		setTimeout(() => {
			setMaintenanceRecords(mockRecords);
			setFilteredRecords(mockRecords);
			setIsLoading(false);
		}, 500);
	}, [siteId]);

	// Filter logic
	useEffect(() => {
		let filtered = maintenanceRecords.filter((record) => {
			const matchesSearch =
				record.ppeItemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				record.ppeItemSku.toLowerCase().includes(searchTerm.toLowerCase()) ||
				record.assignedTo?.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesStatus = !statusFilter || record.status === statusFilter;
			const matchesType = !typeFilter || record.maintenanceType === typeFilter;

			return matchesSearch && matchesStatus && matchesType;
		});

		setFilteredRecords(filtered);
	}, [maintenanceRecords, searchTerm, statusFilter, typeFilter]);

	const getStatusBadge = (status: string) => {
		const badges = {
			scheduled: "bg-blue-100 text-blue-800",
			"in-progress": "bg-yellow-100 text-yellow-800",
			completed: "bg-green-100 text-green-800",
			overdue: "bg-red-100 text-red-800",
		};
		return badges[status as keyof typeof badges] || "bg-gray-100 text-gray-800";
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "completed":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "overdue":
				return <AlertTriangle className="h-4 w-4 text-red-500" />;
			case "in-progress":
				return <Clock className="h-4 w-4 text-yellow-500" />;
			default:
				return <Calendar className="h-4 w-4 text-blue-500" />;
		}
	};

	const getTypeLabel = (type: string) => {
		const labels = {
			inspection: "Inspection",
			cleaning: "Cleaning",
			repair: "Repair",
			replacement: "Replacement",
		};
		return labels[type as keyof typeof labels] || type;
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="h-32 bg-gray-200 rounded mb-4"></div>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-16 bg-gray-200 rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">PPE Maintenance</h2>
				<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
					<Plus className="h-4 w-4 mr-2" />
					Schedule Maintenance
				</button>
			</div>

			{/* Summary Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Records</p>
							<p className="text-2xl font-bold">{maintenanceRecords.length}</p>
						</div>
						<Calendar className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Completed</p>
							<p className="text-2xl font-bold text-green-600">
								{maintenanceRecords.filter((r) => r.status === "completed").length}
							</p>
						</div>
						<CheckCircle className="h-8 w-8 text-green-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Overdue</p>
							<p className="text-2xl font-bold text-red-600">
								{maintenanceRecords.filter((r) => r.status === "overdue").length}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-red-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Scheduled</p>
							<p className="text-2xl font-bold text-blue-600">
								{maintenanceRecords.filter((r) => r.status === "scheduled").length}
							</p>
						</div>
						<Clock className="h-8 w-8 text-blue-500" />
					</div>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4 items-center">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search by PPE item, SKU, or assigned person..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-gray-400" />
						<select
							value={statusFilter}
							onChange={(e) => setStatusFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Status</option>
							<option value="scheduled">Scheduled</option>
							<option value="in-progress">In Progress</option>
							<option value="completed">Completed</option>
							<option value="overdue">Overdue</option>
						</select>
						<select
							value={typeFilter}
							onChange={(e) => setTypeFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Types</option>
							<option value="inspection">Inspection</option>
							<option value="cleaning">Cleaning</option>
							<option value="repair">Repair</option>
							<option value="replacement">Replacement</option>
						</select>
					</div>
				</div>
			</div>

			{/* Maintenance Records Table */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									PPE Item
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Maintenance Type
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Scheduled Date
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Assigned To
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredRecords.map((record) => (
								<tr key={record.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{record.ppeItemName}
											</div>
											<div className="text-sm text-gray-500">
												SKU: {record.ppeItemSku}
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span className="text-sm text-gray-900">
											{getTypeLabel(record.maintenanceType)}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center space-x-2">
											{getStatusIcon(record.status)}
											<span
												className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadge(
													record.status,
												)}`}
											>
												{record.status.charAt(0).toUpperCase() +
													record.status.slice(1)}
											</span>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
										{record.scheduledDate.toLocaleDateString()}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center space-x-2">
											<User className="h-4 w-4 text-gray-400" />
											<span className="text-sm text-gray-900">
												{record.assignedTo || "Unassigned"}
											</span>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<button className="text-green-600 hover:text-green-900 mr-3">
											<Eye className="h-4 w-4" />
										</button>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
};

export default PPEMaintenance;
