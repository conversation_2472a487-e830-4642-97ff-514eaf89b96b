import { SiteInfo } from '../types';

// Mock site data
export const mockSite: SiteInfo = {
  id: "site-1",
  name: "Downtown Construction Site",
  location: "Nairobi, Kenya",
  projectManager: "<PERSON>",
  healthStatus: "green",
  workersOnSite: 45,
  activePermits: 8,
  openIncidents: 0,
  timeline: "Jan 2025 - Dec 2026",
  currentPhase: "Construction",
  progressPercentage: 65,
  tenantId: '',
  status: 'active',
  createdAt: new Date('2024-09-01T00:00:00Z')
};

// Mock inductions
export const mockInductions = [
  { id: 'ind-1', title: 'Site Safety Induction', completed: true, date: new Date('2024-10-01') },
  { id: 'ind-2', title: 'Fire Safety Procedures', completed: false, date: null },
  { id: 'ind-3', title: 'Working at Heights', completed: true, date: new Date('2024-10-05') },
  { id: 'ind-4', title: 'Equipment Operation', completed: false, date: null },
  { id: 'ind-5', title: 'Emergency Evacuation', completed: true, date: new Date('2024-10-10') },
  { id: 'ind-6', title: 'Manual Handling', completed: false, date: null },
  { id: 'ind-7', title: 'Hazardous Materials', completed: false, date: null },
];

// Mock tasks
export const mockTasks = [
  { id: 'task-1', title: 'Foundation Excavation', status: 'in-progress' },
  { id: 'task-2', title: 'Concrete Pouring - Level 1', status: 'scheduled' },
  { id: 'task-3', title: 'Electrical Wiring - Ground Floor', status: 'completed' },
  { id: 'task-4', title: 'Plumbing Installation - Level 2', status: 'scheduled' },
  { id: 'task-5', title: 'Roof Tiling - Section A', status: 'blocked' },
  { id: 'task-6', title: 'Window Installation - West Wing', status: 'scheduled' },
];

// Mock workers
export const mockWorkers = [
  { id: 'worker-1', name: 'John Smith', trade: 'Carpenter', attended: false },
  { id: 'worker-2', name: 'Maria Garcia', trade: 'Electrician', attended: true },
  { id: 'worker-3', name: 'David Johnson', trade: 'Plumber', attended: false },
  { id: 'worker-4', name: 'Sarah Williams', trade: 'Painter', attended: true },
  { id: 'worker-5', name: 'Michael Brown', trade: 'Bricklayer', attended: false },
  { id: 'worker-6', name: 'Emma Wilson', trade: 'Roofer', attended: false },
  { id: 'worker-7', name: 'James Taylor', trade: 'Laborer', attended: true },
  { id: 'worker-8', name: 'Olivia Martinez', trade: 'Welder', attended: false },
];