// Enhanced Worker Management GraphQL Queries
// Centralized Company-Level Architecture Implementation

import { gql } from '@apollo/client';

// Company Worker Management Queries

export const GET_COMPANY_WORKERS = gql`
  query GetCompanyWorkers($tenantId: String!) {
    companyWorkers(tenantId: $tenantId) {
      id
      tenantId
      employeeNumber
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      manHours
      photoUrl
      inductionDate
      medicalCheckDate
      rating
      status
      age
      trainingsCompleted
      createdAt
      createdBy
      updatedAt
      updatedBy
      
      # Company-level specific fields
      complianceStatus
      currentSiteId
      totalSiteAssignments
      lastAssignmentDate
      hireDate
      terminationDate
      rehireEligible
      performanceRating
      notes
      
      # Related data
      trades {
        id
        name
        description
        category
        requiredCertifications
        averageHourlyRate
        demandLevel
        safetyRisk
      }
      
      skills {
        id
        name
        description
        category
        proficiencyLevel
        yearsOfExperience
        certificationRequired
      }
      
      trainings {
        id
        name
        description
        duration
        validityPeriod
        category
        isRequired
        isMandatory
      }
      
      certifications {
        id
        name
        issuingBody
        issueDate
        expiryDate
        certificateNumber
        status
        documentUrl
      }
      
      # Enhanced site assignments
      siteAssignments {
        id
        workerId
        siteId
        role
        startDate
        endDate
        status
        assignmentType
        assignmentReason
        assignedBy
        actualEndDate
        transferredToSite
        transferredFromSite
        performanceNotes
        hourlyRate
        overtimeRate
        totalHoursWorked
        averageHoursPerDay
        attendanceRate
        safetyIncidents
        qualityRating
        createdAt
        createdBy
      }
      
      # Training compliance
      trainingCompliance {
        id
        workerId
        trainingId
        trainingName
        tradeId
        tradeName
        complianceStatus
        isRequired
        isMandatory
        completionDate
        expiryDate
        renewalDueDate
        certificateUrl
        lastAssessmentDate
        nextAssessmentDue
        blockingSiteAssignment
        notes
        createdAt
        updatedAt
      }
    }
  }
`;

export const GET_COMPANY_WORKER_BY_ID = gql`
  query GetCompanyWorkerById($id: Int!, $tenantId: String!) {
    companyWorker(id: $id, tenantId: $tenantId) {
      id
      tenantId
      employeeNumber
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      manHours
      photoUrl
      inductionDate
      medicalCheckDate
      rating
      status
      age
      trainingsCompleted
      createdAt
      createdBy
      updatedAt
      updatedBy
      
      # Company-level specific fields
      complianceStatus
      currentSiteId
      totalSiteAssignments
      lastAssignmentDate
      hireDate
      terminationDate
      rehireEligible
      performanceRating
      notes
      
      # Related data with full details
      trades {
        id
        name
        description
        category
        requiredCertifications
        averageHourlyRate
        demandLevel
        safetyRisk
      }
      
      skills {
        id
        name
        description
        category
        proficiencyLevel
        yearsOfExperience
        certificationRequired
      }
      
      trainings {
        id
        name
        description
        duration
        validityPeriod
        category
        isRequired
        isMandatory
      }
      
      certifications {
        id
        name
        issuingBody
        issueDate
        expiryDate
        certificateNumber
        status
        documentUrl
      }
      
      siteAssignments {
        id
        workerId
        siteId
        role
        startDate
        endDate
        status
        assignmentType
        assignmentReason
        assignedBy
        actualEndDate
        transferredToSite
        transferredFromSite
        performanceNotes
        hourlyRate
        overtimeRate
        totalHoursWorked
        averageHoursPerDay
        attendanceRate
        safetyIncidents
        qualityRating
        createdAt
        createdBy
      }
      
      trainingCompliance {
        id
        workerId
        trainingId
        trainingName
        tradeId
        tradeName
        complianceStatus
        isRequired
        isMandatory
        completionDate
        expiryDate
        renewalDueDate
        certificateUrl
        lastAssessmentDate
        nextAssessmentDue
        blockingSiteAssignment
        notes
        createdAt
        updatedAt
      }
    }
  }
`;

export const GET_SITE_WORKERS = gql`
  query GetSiteWorkers($siteId: String!, $tenantId: String!) {
    siteWorkers(siteId: $siteId, tenantId: $tenantId) {
      workerId
      workerName
      employeeNumber
      currentRole
      assignmentDate
      assignmentStatus
      primaryTrade
      complianceStatus
      isOnSite
      lastCheckIn
      hoursWorkedToday
      hoursWorkedThisWeek
      attendanceRate
      safetyScore
      photoUrl
      
      # Worker details from company database
      worker {
        id
        name
        phoneNumber
        email
        photoUrl
        status
        rating
        manHours
        
        trades {
          id
          name
          category
        }
        
        skills {
          id
          name
          proficiencyLevel
        }
        
        trainingCompliance {
          id
          trainingName
          complianceStatus
          blockingSiteAssignment
          expiryDate
        }
      }
    }
  }
`;

export const GET_AVAILABLE_WORKERS = gql`
  query GetAvailableWorkers($tenantId: String!) {
    availableWorkers(tenantId: $tenantId) {
      id
      employeeNumber
      name
      phoneNumber
      email
      photoUrl
      status
      complianceStatus
      currentSiteId
      totalSiteAssignments
      lastAssignmentDate
      performanceRating
      
      trades {
        id
        name
        category
      }
      
      skills {
        id
        name
        proficiencyLevel
      }
      
      trainingCompliance {
        id
        trainingName
        complianceStatus
        blockingSiteAssignment
        expiryDate
        renewalDueDate
      }
    }
  }
`;

export const GET_COMPANY_WORKER_STATS = gql`
  query GetCompanyWorkerStats($tenantId: String!) {
    companyWorkerStats(tenantId: $tenantId) {
      totalWorkers
      activeWorkers
      workersOnSites
      availableWorkers
      compliantWorkers
      nonCompliantWorkers
      workersNeedingTraining
      workersByTrade {
        tradeName
        count
      }
      workersBySite {
        siteId
        siteName
        count
      }
      averageExperience
      averagePerformanceRating
      totalManHours
      averageHourlyRate
    }
  }
`;

export const GET_WORKER_TRAINING_COMPLIANCE = gql`
  query GetWorkerTrainingCompliance($workerId: Int!, $tenantId: String!) {
    workerTrainingCompliance(workerId: $workerId, tenantId: $tenantId) {
      id
      workerId
      trainingId
      trainingName
      tradeId
      tradeName
      complianceStatus
      isRequired
      isMandatory
      completionDate
      expiryDate
      renewalDueDate
      certificateUrl
      lastAssessmentDate
      nextAssessmentDue
      blockingSiteAssignment
      notes
      createdAt
      updatedAt
    }
  }
`;

// Worker Management Mutations

export const CREATE_COMPANY_WORKER = gql`
  mutation CreateCompanyWorker($input: CreateCompanyWorkerInput!) {
    createCompanyWorker(input: $input) {
      id
      employeeNumber
      name
      complianceStatus
      currentSiteId
      message
      success
    }
  }
`;

export const UPDATE_COMPANY_WORKER = gql`
  mutation UpdateCompanyWorker($id: Int!, $input: UpdateCompanyWorkerInput!) {
    updateCompanyWorker(id: $id, input: $input) {
      id
      employeeNumber
      name
      complianceStatus
      currentSiteId
      message
      success
    }
  }
`;

export const ASSIGN_WORKER_TO_SITE = gql`
  mutation AssignWorkerToSite($input: AssignWorkerToSiteInput!) {
    assignWorkerToSite(input: $input) {
      id
      workerId
      siteId
      role
      assignmentStatus
      message
      success
    }
  }
`;

export const TRANSFER_WORKER_BETWEEN_SITES = gql`
  mutation TransferWorkerBetweenSites($input: TransferWorkerInput!) {
    transferWorkerBetweenSites(input: $input) {
      id
      workerId
      fromSiteId
      toSiteId
      transferDate
      message
      success
    }
  }
`;

export const UPDATE_WORKER_TRAINING_COMPLIANCE = gql`
  mutation UpdateWorkerTrainingCompliance($input: UpdateTrainingComplianceInput!) {
    updateWorkerTrainingCompliance(input: $input) {
      id
      workerId
      trainingId
      complianceStatus
      completionDate
      expiryDate
      message
      success
    }
  }
`;

export const BULK_IMPORT_WORKERS_TO_SITE = gql`
  mutation BulkImportWorkersToSite($input: BulkImportWorkersInput!) {
    bulkImportWorkersToSite(input: $input) {
      successCount
      failureCount
      failures {
        workerId
        workerName
        reason
      }
      message
      success
    }
  }
`;
