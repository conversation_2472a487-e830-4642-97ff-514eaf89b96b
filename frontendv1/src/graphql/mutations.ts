import { gql } from '@apollo/client';

// Enhanced Worker Mutations for Centralized Company-Level Management

export const CREATE_COMPANY_WORKER = gql`
  mutation CreateCompanyWorker($input: CreateCompanyWorkerInput!) {
    createCompanyWorker(input: $input) {
      id
      tenantId
      name
      employeeNumber
      complianceStatus
      currentSiteId
      hireDate
      performanceRating

      # Basic info
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      profilePictureUrl

      # Related data
      trades {
        id
        name
      }
      skills {
        id
        name
      }

      # Site assignments
      siteAssignments {
        id
        siteId
        role
        startDate
        status
        assignmentType
      }

      # Training compliance
      trainingCompliance {
        id
        trainingName
        complianceStatus
        blockingSiteAssignment
      }

      createdAt
      createdBy
    }
  }
`;

export const ASSIGN_WORKER_TO_SITE = gql`
  mutation AssignWorkerToSite($input: AssignWorkerToSiteInput!) {
    assignWorkerToSite(input: $input) {
      id
      workerId
      siteId
      role
      startDate
      status
      assignmentType
      assignmentReason
      assignedBy
      hourlyRate
      overtimeRate

      worker {
        id
        name
        employeeNumber
        complianceStatus
      }
    }
  }
`;

export const UPDATE_WORKER_SITE_ASSIGNMENT = gql`
  mutation UpdateWorkerSiteAssignment($id: Int!, $input: UpdateSiteAssignmentInput!) {
    updateWorkerSiteAssignment(id: $id, input: $input) {
      id
      status
      endDate
      actualEndDate
      performanceRating
      performanceNotes
      transferredToSite
      transferredFromSite
    }
  }
`;

export const BULK_IMPORT_WORKERS_TO_SITE = gql`
  mutation BulkImportWorkersToSite($input: BulkImportWorkersInput!) {
    bulkImportWorkersToSite(input: $input) {
      successCount
      failureCount
      errors
      assignments {
        id
        workerId
        workerName
        siteId
        role
        status
      }
    }
  }
`;

export const UPDATE_WORKER_TRAINING_COMPLIANCE = gql`
  mutation UpdateWorkerTrainingCompliance($input: UpdateTrainingComplianceInput!) {
    updateWorkerTrainingCompliance(input: $input) {
      id
      workerId
      trainingId
      complianceStatus
      completionDate
      expiryDate
      certificateUrl
      blockingSiteAssignment
    }
  }
`;

export const CREATE_WORKER_WITH_TRAINING = gql`
mutation CreateWorkerWithTraining($input: CreateWorkerWithTrainingInput!) {
  createWorkerWithTraining(input: $input) {
    id
    name
  }
}
`

export const CREATE_WORKER = gql`
  mutation CreateWorker(
    $name: String!
    $company: String!
    $nationalId: String!
    $gender: String!
    $phoneNumber: String!
    $dateOfBirth: Date
    $trainingIds: [Int!]
    $tradeIds: [Int!]
    $skillIds: [Int!]
    $mpesaNumber: String
    $email: String
    $inductionDate: DateTime
    $medicalCheckDate: DateTime
  ) {
    createWorker(
      name: $name
      company: $company
      nationalId: $nationalId
      gender: $gender
      phoneNumber: $phoneNumber
      dateOfBirth: $dateOfBirth
      trainingIds: $trainingIds
      tradeIds: $tradeIds
      skillIds: $skillIds
      mpesaNumber: $mpesaNumber
      email: $email
      inductionDate: $inductionDate
      medicalCheckDate: $medicalCheckDate
    ) {
      id
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      mpesaNumber
      inductionDate
      medicalCheckDate
      photoUrl
      age
      trainingsCompleted
      manHours
      rating
      trades {
        id
        name
      }
      skills {
        id
        name
      }
      trainings {
        id
        name
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

export const UPDATE_WORKER = gql`
  mutation UpdateWorker(
    $id: Int!
    $name: String
    $company: String
    $dateOfBirth: Date
    $trainingIds: [Int!]
    $tradeIds: [Int!]
    $skillIds: [Int!]
    $manHours: Int
    $rating: Float
    $gender: String
    $phoneNumber: String
    $mpesaNumber: String
    $email: String
    $inductionDate: DateTime
    $medicalCheckDate: DateTime
  ) {
    updateWorker(
      id: $id
      name: $name
      company: $company
      dateOfBirth: $dateOfBirth
      trainingIds: $trainingIds
      tradeIds: $tradeIds
      skillIds: $skillIds
      manHours: $manHours
      rating: $rating
      gender: $gender
      phoneNumber: $phoneNumber
      mpesaNumber: $mpesaNumber
      email: $email
      inductionDate: $inductionDate
      medicalCheckDate: $medicalCheckDate
    ) {
      id
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      mpesaNumber
      inductionDate
      medicalCheckDate
      age
      trainingsCompleted
      manHours
      rating
      trades {
        id
        name
      }
      skills {
        id
        name
      }
      trainings {
        id
        name
      }
      updatedAt
      updatedBy
    }
  }
`;
export const UPDATE_WORKER_WITH_TRAINING = gql`mutation UpdateWorkerWithTraining($input: UpdateWorkerWithTrainingInput!) {
  updateWorkerWithTraining(input: $input) {
    id
    name
  }
}`

export const DELETE_WORKER = gql`
  mutation DeleteWorker($id: Int!) {
    deleteWorker(id: $id)
  }
`;

export const UPLOAD_WORKER_PHOTO = gql`
  mutation UploadWorkerPhoto($workerId: Int!, $photo: Upload!) {
    uploadWorkerPhoto(workerId: $workerId, photo: $photo) {
      photoUrl
      hikvisionRegistered
      message
    }
  }
`;

export const DELETE_WORKER_PHOTO = gql`
  mutation DeleteWorkerPhoto($workerId: Int!) {
    deleteWorkerPhoto(workerId: $workerId) {
      success
      message
    }
  }
`;

// Training Mutations
export const CREATE_TRAINING = gql`
  mutation CreateTraining($input: CreateTrainingInput!) {
    createTraining(input: $input) {
      id
      name
      description
      startDate
      endDate
      duration
      validityPeriodMonths
      trainingType
      trainer
      frequency
      status
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_TRAINING = gql`
  mutation UpdateTraining($id: Int!, $input: UpdateTrainingInput!) {
    updateTraining(id: $id, input: $input) {
      id
      name
      description
      startDate
      endDate
      duration
      validityPeriodMonths
      trainingType
      trainer
      frequency
      status
      updatedAt
      updatedBy
    }
  }
`;

export const RECORD_TRAINING_COMPLETION = gql`
  mutation RecordTrainingCompletion($input: TrainingCompletionInput!) {
    recordTrainingCompletion(input: $input) {
      id
      workerId
      trainingId
      completionDate
      expiryDate
      score
      notes
      status
      createdAt
      createdBy
    }
  }
`;

export const BULK_ASSIGN_TRAINING = gql`
  mutation BulkAssignTraining($trainingId: Int!, $workerIds: [Int!]!, $scheduledDate: String) {
    bulkAssignTraining(trainingId: $trainingId, workerIds: $workerIds, scheduledDate: $scheduledDate) {
      success
      assignedCount
      message
    }
  }
`;

export const RENEW_TRAINING = gql`
  mutation RenewTraining($workerId: Int!, $trainingHistoryId: Int!, $input: TrainingRenewalInput!) {
    renewTraining(workerId: $workerId, trainingHistoryId: $trainingHistoryId, input: $input) {
      id
      workerId
      trainingId
      completionDate
      expiryDate
      score
      notes
      status
      createdAt
      createdBy
    }
  }
`;

// Toolbox Session Mutations
export const CREATE_TOOLBOX_SESSION = gql`
  mutation CreateToolboxSession($input: CreateToolboxSessionInput!) {
    createToolboxSession(input: $input) {
      id
      sessionTime
      topic
      conductor
      photoUrl
      notes
      attendances {
        id
        workerId
        wasPresent
        notes
      }
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_TOOLBOX_SESSION = gql`
  mutation UpdateToolboxSession($id: Int!, $input: UpdateToolboxSessionInput!) {
    updateToolboxSession(id: $id, input: $input) {
      id
      sessionTime
      topic
      conductor
      photoUrl
      notes
      attendances {
        id
        workerId
        wasPresent
        notes
      }
      updatedAt
      updatedBy
    }
  }
`;

export const DELETE_TOOLBOX_SESSION = gql`
  mutation DeleteToolboxSession($id: Int!) {
    deleteToolboxSession(id: $id) {
      success
      message
    }
  }
`;

export const UPDATE_TOOLBOX_ATTENDANCE = gql`
  mutation UpdateToolboxAttendance($sessionId: Int!, $attendances: [ToolboxAttendanceInput!]!) {
    updateToolboxAttendance(sessionId: $sessionId, attendances: $attendances) {
      success
      updatedCount
      message
    }
  }
`;

// Worker Attendance Mutations
export const RECORD_WORKER_ATTENDANCE = gql`
  mutation RecordWorkerAttendance($input: WorkerAttendanceInput!) {
    recordWorkerAttendance(input: $input) {
      id
      workerId
      checkInTime
      checkOutTime
      status
      notes
      isVerifiedByHikvision
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_WORKER_ATTENDANCE = gql`
  mutation UpdateWorkerAttendance($id: Int!, $input: UpdateWorkerAttendanceInput!) {
    updateWorkerAttendance(id: $id, input: $input) {
      id
      workerId
      checkInTime
      checkOutTime
      status
      notes
      isVerifiedByHikvision
      updatedAt
      updatedBy
    }
  }
`;

// Trade and Skill Mutations
export const CREATE_TRADE = gql`
  mutation CreateTrade($input: CreateTradeInput!) {
    createTrade(input: $input) {
      id
      name
      description
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_TRADE = gql`
  mutation UpdateTrade($id: Int!, $input: UpdateTradeInput!) {
    updateTrade(id: $id, input: $input) {
      id
      name
      description
      updatedAt
      updatedBy
    }
  }
`;

export const CREATE_SKILL = gql`
  mutation CreateSkill($input: CreateSkillInput!) {
    createSkill(input: $input) {
      id
      name
      description
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_SKILL = gql`
  mutation UpdateSkill($id: Int!, $input: UpdateSkillInput!) {
    updateSkill(id: $id, input: $input) {
      id
      name
      description
      updatedAt
      updatedBy
    }
  }
`;

// Hikvision Integration Mutations
export const SYNC_HIKVISION_ATTENDANCE = gql`
  mutation SyncHikvisionAttendance($siteId: String!, $date: String!) {
    syncHikvisionAttendance(siteId: $siteId, date: $date) {
      success
      syncedCount
      message
      attendanceRecords {
        workerId
        checkInTime
        checkOutTime
        isVerified
      }
    }
  }
`;

export const REGISTER_WORKER_FACE = gql`
  mutation RegisterWorkerFace($workerId: Int!, $photo: Upload!) {
    registerWorkerFace(workerId: $workerId, photo: $photo) {
      success
      hikvisionPersonId
      message
    }
  }
`;

export const UNREGISTER_WORKER_FACE = gql`
  mutation UnregisterWorkerFace($workerId: Int!) {
    unregisterWorkerFace(workerId: $workerId) {
      success
      message
    }
  }
`;

// Job/Task Management Mutations
export const CREATE_JOB = gql`
  mutation CreateJob($input: CreateJobInput!) {
    createJob(input: $input) {
      id
      title
      description
      status
      requiredPermits
      timeForCompletion
      startDate
      dueDate
      calculatedDueDate
      requestedById
      requestedBy {
        id
        name
      }
      requestedDate
      chiefEngineerId
      chiefEngineer {
        id
        name
      }
      workers {
        id
        name
        company
      }
      createdAt
      createdBy
    }
  }
`;

export const BLOCK_JOB = gql`
  mutation BlockJob($input: BlockJobInput!) {
    blockJob(input: $input) {
      id
      title
      status
      blockedById
      blockedBy {
        id
        name
      }
      blockedDate
      updatedAt
      updatedBy
    }
  }
`;

export const REVIEW_JOB = gql`
  mutation ReviewJob($input: ReviewJobInput!) {
    reviewJob(input: $input) {
      id
      title
      status
      requiredPermits
      reviewedById
      reviewedBy {
        id
        name
      }
      reviewedDate
      hazards {
        id
        description
        controlMeasures {
          id
          description
          closed
        }
      }
      documents {
        id
        name
        url
      }
      updatedAt
      updatedBy
    }
  }
`;

export const APPROVE_JOB = gql`
  mutation ApproveJob($input: ApproveJobInput!) {
    approveJob(input: $input) {
      id
      title
      status
      approvedById
      approvedBy {
        id
        name
      }
      approvedDate
      updatedAt
      updatedBy
    }
  }
`;

export const DISAPPROVE_JOB = gql`
  mutation DisapproveJob($input: DisapproveJobInput!) {
    disapproveJob(input: $input) {
      id
      title
      status
      disapprovedById
      disapprovedBy {
        id
        name
      }
      disapprovedDate
      updatedAt
      updatedBy
    }
  }
`;

export const FINISH_JOB = gql`
  mutation FinishJob($input: FinishJobInput!) {
    finishJob(input: $input) {
      id
      title
      status
      finishedById
      finishedBy {
        id
        name
      }
      finishedDate
      updatedAt
      updatedBy
    }
  }
`;

// Toolbox Mutations
export const CREATE_TOOLBOX = gql`
  mutation CreateToolbox($input: CreateToolboxInput!) {
    createToolbox(input: $input) {
      id
      date
      status
      emergencyProcedures
      toolboxTrainingTopics
      conductor {
        workerId
        name
        signatureFileId
      }
      jobs {
        id
        title
        description
        hazards {
          id
          description
          controlMeasures {
            id
            description
            closed
          }
        }
      }
      createdAt
      createdBy
    }
  }
`;

export const ADD_ATTENDEES = gql`
  mutation AddAttendees($toolboxId: Int!, $workerIds: [Int!]!) {
    addAttendees(toolboxId: $toolboxId, workerIds: $workerIds)
  }
`;

export const SUMMARIZE_TOOLBOX = gql`
  mutation SummarizeToolbox($input: SummarizeToolboxInput!) {
    summarizeToolbox(input: $input)
  }
`;

export const ADD_HAZARD = gql`
  mutation AddHazard($input: AddHazardInput!) {
    addHazard(input: $input)
  }
`;

export const ADD_CONTROL_MEASURE = gql`
  mutation AddControlMeasure($input: AddControlMeasureInput!) {
    addControlMeasure(input: $input)
  }
`;

export const BLOCK_JOBS = gql`
  mutation BlockJobs($inputs: [BlockJobInput!]!) {
    blockJobs(inputs: $inputs) {
      id
      title
      status
      blockedById
      blockedBy {
        id
        name
      }
      blockedDate
      updatedAt
      updatedBy
    }
  }
`;

export const REVIEW_JOBS = gql`
  mutation ReviewJobs($inputs: [ReviewJobInput!]!) {
    reviewJobs(inputs: $inputs) {
      id
      title
      status
      requiredPermits
      reviewedById
      reviewedBy {
        id
        name
      }
      reviewedDate
      hazards {
        id
        description
        controlMeasures {
          id
          description
          closed
        }
      }
      documents {
        id
        name
        url
      }
      updatedAt
      updatedBy
    }
  }
`;

export const APPROVE_JOBS = gql`
  mutation ApproveJobs($inputs: [ApproveJobInput!]!) {
    approveJobs(inputs: $inputs) {
      id
      title
      status
      approvedById
      approvedBy {
        id
        name
      }
      approvedDate
      updatedAt
      updatedBy
    }
  }
`;

export const DISAPPROVE_JOBS = gql`
  mutation DisapproveJobs($inputs: [DisapproveJobInput!]!) {
    disapproveJobs(inputs: $inputs) {
      id
      title
      status
      approvedById
      approvedBy {
        id
        name
      }
      approvedDate
      updatedAt
      updatedBy
    }
  }
`;

export const FINISH_JOBS = gql`
  mutation FinishJobs($inputs: [FinishJobInput!]!) {
    finishJobs(inputs: $inputs) {
      id
      title
      status
      finishedById
      finishedBy {
        id
        name
      }
      finishedDate
      updatedAt
      updatedBy
    }
  }
`;


