// Task Service - GraphQL operations for task management
// Aligns with backend Task model structure

import { Task } from '../types/tasks';

// GraphQL mutation for creating a task
// This aligns with the backend Task model structure
export const CREATE_TASK_MUTATION = `
  mutation CreateTask(
    $name: String!
    $type: String!
    $description: String!
    $assignedWorkerId: Int!
    $siteId: String!
    $category: String!
    $priority: String!
    $location: String!
    $plannedStartDate: DateTime!
    $plannedEndDate: DateTime!
    $estimatedDuration: Float!
    $requiresPermit: Boolean!
    $permitTypes: [String!]
    $riskLevel: String!
    $safetyRequirements: [String!]
    $requiredPPE: [String!]
    $requiredTrainings: [String!]
    $requiredCertifications: [String!]
    $tags: [String!]
  ) {
    createTask(
      name: $name
      type: $type
      description: $description
      assignedWorkerId: $assignedWorkerId
      siteId: $siteId
      category: $category
      priority: $priority
      location: $location
      plannedStartDate: $plannedStartDate
      plannedEndDate: $plannedEndDate
      estimatedDuration: $estimatedDuration
      requiresPermit: $requiresPermit
      permitTypes: $permitTypes
      riskLevel: $riskLevel
      safetyRequirements: $safetyRequirements
      requiredPPE: $requiredPPE
      requiredTrainings: $requiredTrainings
      requiredCertifications: $requiredCertifications
      tags: $tags
    ) {
      id
      name
      type
      description
      assignedWorkerId
      isActive
      createdAt
      updatedAt
    }
  }
`;

// GraphQL query for fetching tasks
export const GET_TASKS_QUERY = `
  query GetTasks($siteId: String!) {
    getTasks(siteId: $siteId) {
      id
      name
      type
      description
      assignedWorkerId
      isActive
      createdAt
      updatedAt
    }
  }
`;

// GraphQL query for fetching a single task
export const GET_TASK_BY_ID_QUERY = `
  query GetTaskById($id: Int!) {
    getTaskById(id: $id) {
      id
      name
      type
      description
      assignedWorkerId
      isActive
      createdAt
      updatedAt
    }
  }
`;

// GraphQL mutation for updating a task
export const UPDATE_TASK_MUTATION = `
  mutation UpdateTask(
    $id: Int!
    $name: String
    $type: String
    $description: String
    $assignedWorkerId: Int
    $isActive: Boolean
  ) {
    updateTask(
      id: $id
      name: $name
      type: $type
      description: $description
      assignedWorkerId: $assignedWorkerId
      isActive: $isActive
    ) {
      id
      name
      type
      description
      assignedWorkerId
      isActive
      createdAt
      updatedAt
    }
  }
`;

// GraphQL mutation for deleting a task
export const DELETE_TASK_MUTATION = `
  mutation DeleteTask($id: Int!) {
    deleteTask(id: $id)
  }
`;

// Interface for task creation input that matches backend expectations
export interface CreateTaskInput {
  name: string;
  type: string;
  description: string;
  assignedWorkerId: number;
  siteId: string;
  category: string;
  priority: string;
  location: string;
  plannedStartDate: Date;
  plannedEndDate: Date;
  estimatedDuration: number;
  requiresPermit: boolean;
  permitTypes?: string[];
  riskLevel: string;
  safetyRequirements?: string[];
  requiredPPE?: string[];
  requiredTrainings?: string[];
  requiredCertifications?: string[];
  tags?: string[];
}

// Interface for task update input
export interface UpdateTaskInput {
  id: number;
  name?: string;
  type?: string;
  description?: string;
  assignedWorkerId?: number;
  isActive?: boolean;
}

// Task service class
export class TaskService {
  private apiEndpoint: string;

  constructor(apiEndpoint: string = '/graphql') {
    this.apiEndpoint = apiEndpoint;
  }

  // Convert frontend Task to backend CreateTaskInput
  private convertToCreateInput(task: Partial<Task>, siteId: string): CreateTaskInput {
    return {
      name: task.title || '',
      type: task.category || 'other',
      description: task.description || '',
      assignedWorkerId: parseInt(task.assignedSupervisor || '1'), // Convert to number as backend expects
      siteId,
      category: task.category || 'other',
      priority: task.priority || 'medium',
      location: task.location || '',
      plannedStartDate: task.plannedStartDate || new Date(),
      plannedEndDate: task.plannedEndDate || new Date(),
      estimatedDuration: task.estimatedDuration || 8,
      requiresPermit: task.requiresPermit || false,
      permitTypes: task.permitTypes || [],
      riskLevel: task.riskLevel || 'low',
      safetyRequirements: task.safetyRequirements || [],
      requiredPPE: task.requiredPPE || [],
      requiredTrainings: task.requiredTrainings || [],
      requiredCertifications: task.requiredCertifications || [],
      tags: task.tags || []
    };
  }

  // Create a new task
  async createTask(taskData: Partial<Task>, siteId: string): Promise<any> {
    const input = this.convertToCreateInput(taskData, siteId);
    
    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: CREATE_TASK_MUTATION,
          variables: input
        })
      });

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      return result.data.createTask;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  }

  // Get tasks for a site
  async getTasks(siteId: string): Promise<any[]> {
    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: GET_TASKS_QUERY,
          variables: { siteId }
        })
      });

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      return result.data.getTasks || [];
    } catch (error) {
      console.error('Error fetching tasks:', error);
      throw error;
    }
  }

  // Get a single task by ID
  async getTaskById(id: number): Promise<any> {
    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: GET_TASK_BY_ID_QUERY,
          variables: { id }
        })
      });

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      return result.data.getTaskById;
    } catch (error) {
      console.error('Error fetching task:', error);
      throw error;
    }
  }

  // Update a task
  async updateTask(input: UpdateTaskInput): Promise<any> {
    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: UPDATE_TASK_MUTATION,
          variables: input
        })
      });

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      return result.data.updateTask;
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  }

  // Delete a task
  async deleteTask(id: number): Promise<boolean> {
    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: DELETE_TASK_MUTATION,
          variables: { id }
        })
      });

      const result = await response.json();
      
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      return result.data.deleteTask;
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  }
}

// Export a default instance
export const taskService = new TaskService();
