import { WeatherData, WeatherCache, WeatherServiceConfig, WeatherCondition, WeatherForecast, WeatherAlert } from '../types/weather';

// Mock weather service configuration
const WEATHER_CONFIG: WeatherServiceConfig = {
  apiKey: 'mock-api-key', // In real app, this would be from environment variables
  baseUrl: 'https://api.openweathermap.org/data/2.5',
  cacheTimeout: 30, // 30 minutes
  retryAttempts: 3,
  retryDelay: 1000
};

// In-memory cache for weather data
class WeatherCacheManager {
  private cache = new Map<string, WeatherCache>();

  set(siteId: string, data: WeatherData): void {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + WEATHER_CONFIG.cacheTimeout * 60 * 1000);
    
    this.cache.set(siteId, {
      siteId,
      data,
      cachedAt: now.toISOString(),
      expiresAt: expiresAt.toISOString()
    });
  }

  get(siteId: string): WeatherData | null {
    const cached = this.cache.get(siteId);
    if (!cached) return null;

    const now = new Date();
    const expiresAt = new Date(cached.expiresAt);

    if (now > expiresAt) {
      this.cache.delete(siteId);
      return null;
    }

    return cached.data;
  }

  clear(): void {
    this.cache.clear();
  }

  getAll(): WeatherData[] {
    const now = new Date();
    const validData: WeatherData[] = [];

    for (const [siteId, cached] of this.cache.entries()) {
      const expiresAt = new Date(cached.expiresAt);
      if (now <= expiresAt) {
        validData.push(cached.data);
      } else {
        this.cache.delete(siteId);
      }
    }

    return validData;
  }
}

const weatherCache = new WeatherCacheManager();

// Mock weather data generator
const generateMockWeatherData = (siteId: string, siteName: string, location: string): WeatherData => {
  // Generate realistic coordinates for Kenya (since sites seem to be in Kenya)
  const baseCoords = { lat: -1.2921, lon: 36.8219 }; // Nairobi
  const coords = {
    lat: baseCoords.lat + (Math.random() - 0.5) * 2, // ±1 degree variation
    lon: baseCoords.lon + (Math.random() - 0.5) * 2
  };

  const weatherConditions: WeatherCondition[] = [
    { id: 800, main: 'Clear', description: 'clear sky', icon: '01d' },
    { id: 801, main: 'Clouds', description: 'few clouds', icon: '02d' },
    { id: 802, main: 'Clouds', description: 'scattered clouds', icon: '03d' },
    { id: 803, main: 'Clouds', description: 'broken clouds', icon: '04d' },
    { id: 500, main: 'Rain', description: 'light rain', icon: '10d' },
    { id: 501, main: 'Rain', description: 'moderate rain', icon: '10d' },
    { id: 200, main: 'Thunderstorm', description: 'thunderstorm with light rain', icon: '11d' }
  ];

  const currentCondition = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
  const baseTemp = 20 + Math.random() * 15; // 20-35°C typical for Kenya

  // Generate 7-day forecast
  const forecast: WeatherForecast[] = [];
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  
  for (let i = 1; i <= 7; i++) {
    const date = new Date();
    date.setDate(date.getDate() + i);
    const dayName = days[date.getDay()];
    const condition = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
    
    forecast.push({
      date: date.toISOString().split('T')[0],
      day: dayName,
      temperature: {
        min: Math.round(baseTemp - 5 + Math.random() * 3),
        max: Math.round(baseTemp + 5 + Math.random() * 5)
      },
      conditions: condition,
      humidity: Math.round(40 + Math.random() * 40),
      windSpeed: Math.round(Math.random() * 10),
      precipitationChance: condition.main === 'Rain' ? Math.round(60 + Math.random() * 40) : Math.round(Math.random() * 30)
    });
  }

  // Generate alerts for severe weather
  const alerts: WeatherAlert[] = [];
  if (Math.random() > 0.7) { // 30% chance of weather alert
    alerts.push({
      id: `alert-${siteId}-${Date.now()}`,
      type: 'warning',
      severity: 'moderate',
      title: 'Heavy Rain Warning',
      description: 'Heavy rainfall expected in the area. Construction activities may be affected.',
      startTime: new Date().toISOString(),
      endTime: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(), // 6 hours from now
      areas: [location]
    });
  }

  return {
    siteId,
    siteName,
    location,
    coordinates: coords,
    current: {
      temperature: Math.round(baseTemp),
      feelsLike: Math.round(baseTemp + (Math.random() - 0.5) * 4),
      humidity: Math.round(50 + Math.random() * 30),
      pressure: Math.round(1010 + Math.random() * 20),
      visibility: Math.round(8000 + Math.random() * 2000),
      uvIndex: Math.round(Math.random() * 11),
      windSpeed: Math.round(Math.random() * 15),
      windDirection: Math.round(Math.random() * 360),
      conditions: currentCondition,
      timestamp: new Date().toISOString()
    },
    forecast,
    alerts: alerts.length > 0 ? alerts : undefined,
    lastUpdated: new Date().toISOString()
  };
};

// Weather service class
export class WeatherService {
  private static instance: WeatherService;

  private constructor() {}

  static getInstance(): WeatherService {
    if (!WeatherService.instance) {
      WeatherService.instance = new WeatherService();
    }
    return WeatherService.instance;
  }

  async getWeatherForSite(siteId: string, siteName: string, location: string): Promise<WeatherData> {
    // Check cache first
    const cached = weatherCache.get(siteId);
    if (cached) {
      return cached;
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

    // In real implementation, this would call OpenWeather API
    // const response = await fetch(`${WEATHER_CONFIG.baseUrl}/weather?q=${location}&appid=${WEATHER_CONFIG.apiKey}&units=metric`);
    
    // Generate mock data
    const weatherData = generateMockWeatherData(siteId, siteName, location);
    
    // Cache the result
    weatherCache.set(siteId, weatherData);
    
    return weatherData;
  }

  async getWeatherForMultipleSites(sites: Array<{ id: string; name: string; location: string }>): Promise<WeatherData[]> {
    const promises = sites.map(site => 
      this.getWeatherForSite(site.id, site.name, site.location)
    );
    
    return Promise.all(promises);
  }

  getCachedWeatherData(): WeatherData[] {
    return weatherCache.getAll();
  }

  clearCache(): void {
    weatherCache.clear();
  }

  // Check if weather conditions are safe for construction work
  isWeatherSafeForWork(weather: WeatherData): { safe: boolean; warnings: string[] } {
    const warnings: string[] = [];
    const current = weather.current;

    if (current.temperature < -10) {
      warnings.push('Extremely cold conditions - consider work delays');
    } else if (current.temperature > 40) {
      warnings.push('Extremely hot conditions - ensure adequate hydration and breaks');
    }

    if (current.windSpeed > 15) {
      warnings.push('High wind speeds - crane operations may be unsafe');
    }

    if (current.visibility < 1000) {
      warnings.push('Poor visibility conditions - exercise caution');
    }

    if (current.uvIndex > 8) {
      warnings.push('High UV index - ensure workers have adequate sun protection');
    }

    if (weather.alerts && weather.alerts.length > 0) {
      warnings.push('Active weather alerts in the area');
    }

    return {
      safe: warnings.length === 0,
      warnings
    };
  }
}

export const weatherService = WeatherService.getInstance();
