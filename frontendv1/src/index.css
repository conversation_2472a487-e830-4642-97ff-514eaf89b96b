@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

:root {
	font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
		<PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body {
	@apply bg-[#f3f2ee] text-gray-800;
	margin: 0;
	padding: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: transparent;
}

::-webkit-scrollbar-thumb {
	background-color: rgba(156, 163, 175, 0.5);
	border-radius: 20px;
}

::-webkit-scrollbar-thumb:hover {
	background-color: rgba(156, 163, 175, 0.7);
}

/* Hide scrollbar utility class */
.scrollbar-hide {
	-ms-overflow-style: none; /* Internet Explorer 10+ */
	scrollbar-width: none; /* Firefox */
}

/* Responsive grid utilities for permits */
@media (max-width: 639px) {
	/* Extra small screens - ensure single column with proper spacing */
	.permits-grid {
		grid-template-columns: 1fr;
		gap: 1rem;
	}
}

@media (min-width: 640px) and (max-width: 1023px) {
	/* Small to medium screens - 2 columns */
	.permits-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 1.5rem;
	}
}

@media (min-width: 1024px) and (max-width: 1279px) {
	/* Large screens (13" laptops) - 3 columns */
	.permits-grid {
		grid-template-columns: repeat(3, 1fr);
		gap: 1.5rem;
	}
}

@media (min-width: 1280px) {
	/* Extra large screens - 4 columns */
	.permits-grid {
		grid-template-columns: repeat(4, 1fr);
		gap: 1.5rem;
	}
}

.scrollbar-hide::-webkit-scrollbar {
	display: none; /* Safari and Chrome */
}

/* Flyout menu animations and styling */
.flyout-menu {
	/* Ensure flyout appears as seamless extension */
	border-left: 1px solid rgba(229, 231, 235, 0.3);
}

/* Step Sidebar Styles - Clean design with proper alignment */
.step-sidebar {
	@apply w-80 flex-shrink-0;
	background-color: #fdfdf9;
	border-right: 1px solid #e5e7eb;
	height: 100vh; /* Full viewport height to run separator all the way through */
}

.step-list {
	@apply p-6;
}

.step-item {
	@apply flex items-center justify-between mb-8 last:mb-0;
	min-height: 3rem; /* Ensure consistent height for alignment */
}

.step-item:hover .step-title {
	color: #4CAF50;
	transition: color 0.2s ease-in-out;
}

.step-item:hover .step-description {
	color: #4CAF50;
	transition: color 0.2s ease-in-out;
}

.step-content {
	@apply flex-1 text-right pr-4;
}

.step-title {
	@apply text-sm font-medium leading-tight text-gray-900;
	transition: color 0.2s ease-in-out;
}

.step-description {
	@apply text-xs mt-1 leading-relaxed text-gray-600;
	transition: color 0.2s ease-in-out;
}

.step-circle {
	@apply w-9 h-9 rounded-full flex items-center justify-center flex-shrink-0 transition-all duration-200 font-medium text-sm;
}

.step-circle.completed {
	background-color: rgba(76, 175, 80, 0.1);
	border: 2px solid #4CAF50;
	color: #4CAF50;
}

.step-circle.current {
	@apply bg-blue-600 text-white;
	border: 2px solid #2563eb;
}

.step-circle.error {
	@apply bg-red-500 text-white;
	border: 2px solid #ef4444;
}

.step-circle.pending {
	background-color: rgba(37, 99, 235, 0.05);
	border: 2px solid rgba(37, 99, 235, 0.3);
	color: #64748b;
}

/* Main content area for site creation */
.main-content {
	@apply flex-1 p-8 overflow-y-auto;
}

.form-container {
	background-color: #fdfdf9;
	@apply rounded-lg border border-gray-200 shadow-sm;
}

/* Flyout menu item hover styling */
.flyout-menu-item:hover:not(.active) {
	background-color: #fdfdf9 !important;
	color: #22c55e !important; /* green-500 */
}

.flyout-enter {
	transform: translateX(-100%);
	opacity: 0;
}

.flyout-enter-active {
	transform: translateX(0);
	opacity: 1;
	transition: transform 300ms ease-out, opacity 300ms ease-out;
}

.flyout-exit {
	transform: translateX(0);
	opacity: 1;
}

.flyout-exit-active {
	transform: translateX(-100%);
	opacity: 0;
	transition: transform 250ms ease-in, opacity 250ms ease-in;
}
