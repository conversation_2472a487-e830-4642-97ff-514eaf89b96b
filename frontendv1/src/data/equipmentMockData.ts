// Company Equipment Mock Data for the redesigned equipment management system

// Company Equipment Types
export type EquipmentOwnershipType = "company" | "rented" | "contracted";

export interface CompanyEquipment {
  id: string;
  equipmentNumber: string;
  name: string;
  category: string;
  manufacturer: string;
  model: string;
  serialNumber?: string;
  yearOfManufacture: number;
  purchaseDate: string;
  purchasePrice: number;
  currentBookValue: number;
  ownershipType: EquipmentOwnershipType;
  overallStatus: "active" | "inactive" | "retired" | "maintenance";
  isAvailableForAssignment: boolean;
  currentSiteId?: string;
  currentSiteName?: string;
  assignedOperator?: string;
  totalHours: number;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  nextInspectionDate?: string;
  complianceStatus: "compliant" | "warning" | "critical" | "overdue";
  specifications: Record<string, string>;
  rentalInfo?: {
    rentalCompany: string;
    rentalStartDate: string;
    rentalEndDate: string;
    monthlyRate: number;
    contactPerson: string;
    contactPhone: string;
  };
  contractorInfo?: {
    contractorCompany: string;
    contractStartDate: string;
    contractEndDate: string;
    contactPerson: string;
    contactPhone: string;
    serviceType: string;
  };
  createdAt: string;
  updatedAt: string;
}

export const mockCompanyEquipment: CompanyEquipment[] = [
  {
    id: "eq-comp-001",
    equipmentNumber: "EQ-COMP-2024-001",
    name: "CAT 320 Excavator",
    category: "Excavators",
    manufacturer: "Caterpillar",
    model: "320D",
    serialNumber: "CAT320D2024001",
    yearOfManufacture: 2024,
    purchaseDate: "2024-01-15",
    purchasePrice: 150000,
    currentBookValue: 135000,
    ownershipType: "company",
    overallStatus: "active",
    isAvailableForAssignment: true,
    currentSiteId: "site-001",
    currentSiteName: "Downtown Construction",
    assignedOperator: "John Smith",
    totalHours: 2847,
    lastMaintenanceDate: "2024-07-15",
    nextMaintenanceDate: "2024-10-15",
    nextInspectionDate: "2024-09-01",
    complianceStatus: "compliant",
    specifications: {
      "Engine Power": "122 kW",
      "Operating Weight": "20,300 kg",
      "Bucket Capacity": "1.2 m³",
      "Max Digging Depth": "6.5 m"
    },
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-08-15T14:30:00Z"
  },
  {
    id: "eq-comp-002",
    equipmentNumber: "EQ-COMP-2024-002",
    name: "Komatsu WA200 Loader",
    category: "Loaders",
    manufacturer: "Komatsu",
    model: "WA200",
    serialNumber: "KOM200WA2024002",
    yearOfManufacture: 2023,
    purchaseDate: "2023-11-20",
    purchasePrice: 95000,
    currentBookValue: 85000,
    ownershipType: "company",
    overallStatus: "active",
    isAvailableForAssignment: false,
    currentSiteId: "site-002",
    currentSiteName: "Highway Expansion",
    assignedOperator: "Mike Johnson",
    totalHours: 1923,
    lastMaintenanceDate: "2024-08-01",
    nextMaintenanceDate: "2024-11-01",
    nextInspectionDate: "2024-09-10",
    complianceStatus: "warning",
    specifications: {
      "Engine Power": "110 kW",
      "Operating Weight": "8,500 kg",
      "Bucket Capacity": "2.1 m³",
      "Lift Capacity": "3,200 kg"
    },
    createdAt: "2023-11-20T09:15:00Z",
    updatedAt: "2024-08-01T11:20:00Z"
  },
  {
    id: "eq-rent-001",
    equipmentNumber: "EQ-RENT-2024-001",
    name: "Liebherr LTM 1050 Crane",
    category: "Cranes",
    manufacturer: "Liebherr",
    model: "LTM 1050-3.1",
    serialNumber: "LIE1050TM2023001",
    yearOfManufacture: 2023,
    purchaseDate: "2024-03-01",
    purchasePrice: 0,
    currentBookValue: 0,
    ownershipType: "rented",
    overallStatus: "active",
    isAvailableForAssignment: true,
    currentSiteId: "site-003",
    currentSiteName: "Bridge Construction",
    assignedOperator: "Sarah Wilson",
    totalHours: 456,
    lastMaintenanceDate: "2024-07-20",
    nextMaintenanceDate: "2024-10-20",
    nextInspectionDate: "2024-09-15",
    complianceStatus: "compliant",
    specifications: {
      "Max Lifting Capacity": "50 tonnes",
      "Boom Length": "36 m",
      "Engine Power": "270 kW",
      "Operating Weight": "36,000 kg"
    },
    rentalInfo: {
      rentalCompany: "Heavy Lift Rentals Inc.",
      rentalStartDate: "2024-03-01",
      rentalEndDate: "2024-12-31",
      monthlyRate: 8500,
      contactPerson: "David Brown",
      contactPhone: "******-0123"
    },
    createdAt: "2024-03-01T08:00:00Z",
    updatedAt: "2024-08-15T16:45:00Z"
  },
  {
    id: "eq-cont-001",
    equipmentNumber: "EQ-CONT-2024-001",
    name: "Specialized Drilling Rig",
    category: "Drilling Equipment",
    manufacturer: "Atlas Copco",
    model: "ROC D65",
    serialNumber: "ATC65ROC2022001",
    yearOfManufacture: 2022,
    purchaseDate: "2024-05-15",
    purchasePrice: 0,
    currentBookValue: 0,
    ownershipType: "contracted",
    overallStatus: "active",
    isAvailableForAssignment: false,
    currentSiteId: "site-004",
    currentSiteName: "Foundation Project",
    assignedOperator: "Contractor Team",
    totalHours: 234,
    lastMaintenanceDate: "2024-08-10",
    nextMaintenanceDate: "2024-11-10",
    nextInspectionDate: "2024-09-25",
    complianceStatus: "compliant",
    specifications: {
      "Drilling Diameter": "76-127 mm",
      "Max Drilling Depth": "32 m",
      "Engine Power": "129 kW",
      "Operating Weight": "14,500 kg"
    },
    contractorInfo: {
      contractorCompany: "Precision Drilling Solutions",
      contractStartDate: "2024-05-15",
      contractEndDate: "2024-11-15",
      contactPerson: "Robert Martinez",
      contactPhone: "******-0456",
      serviceType: "Specialized Foundation Drilling"
    },
    createdAt: "2024-05-15T12:30:00Z",
    updatedAt: "2024-08-10T09:15:00Z"
  },
  {
    id: "eq-comp-003",
    equipmentNumber: "EQ-COMP-2024-003",
    name: "Concrete Mixer CM-400",
    category: "Mixers",
    manufacturer: "CIFA",
    model: "CM-400",
    serialNumber: "CIF400CM2024003",
    yearOfManufacture: 2024,
    purchaseDate: "2024-02-10",
    purchasePrice: 45000,
    currentBookValue: 42000,
    ownershipType: "company",
    overallStatus: "active",
    isAvailableForAssignment: true,
    totalHours: 1156,
    lastMaintenanceDate: "2024-07-25",
    nextMaintenanceDate: "2024-10-25",
    nextInspectionDate: "2024-09-20",
    complianceStatus: "compliant",
    specifications: {
      "Mixing Capacity": "400 L",
      "Engine Power": "15 kW",
      "Operating Weight": "850 kg",
      "Discharge Height": "1.4 m"
    },
    createdAt: "2024-02-10T14:20:00Z",
    updatedAt: "2024-07-25T10:30:00Z"
  },
  {
    id: "eq-rent-002",
    equipmentNumber: "EQ-RENT-2024-002",
    name: "Temporary Lighting Tower",
    category: "Lighting",
    manufacturer: "Generac",
    model: "LT-500",
    serialNumber: "GEN500LT2023002",
    yearOfManufacture: 2023,
    purchaseDate: "2024-06-01",
    purchasePrice: 0,
    currentBookValue: 0,
    ownershipType: "rented",
    overallStatus: "active",
    isAvailableForAssignment: false,
    currentSiteId: "site-002",
    currentSiteName: "Highway Expansion",
    assignedOperator: "Tom Davis",
    totalHours: 89,
    lastMaintenanceDate: "2024-08-10",
    nextMaintenanceDate: "2024-11-10",
    nextInspectionDate: "2024-11-15",
    complianceStatus: "compliant",
    specifications: {
      "Light Output": "4 x 1000W LED",
      "Tower Height": "9 m",
      "Engine Power": "20 kW",
      "Fuel Capacity": "200 L"
    },
    rentalInfo: {
      rentalCompany: "Site Solutions Rental",
      rentalStartDate: "2024-06-01",
      rentalEndDate: "2024-12-01",
      monthlyRate: 1200,
      contactPerson: "Lisa Chen",
      contactPhone: "******-0789"
    },
    createdAt: "2024-06-01T16:00:00Z",
    updatedAt: "2024-08-10T13:45:00Z"
  }
];

// Equipment Categories
export const equipmentCategories = [
  "Excavators",
  "Loaders",
  "Cranes",
  "Drilling Equipment",
  "Mixers",
  "Lighting",
  "Compactors",
  "Generators",
  "Pumps",
  "Safety Equipment"
];

// Equipment Status Options
export const equipmentStatuses = [
  { id: "active", name: "Active", color: "green" },
  { id: "inactive", name: "Inactive", color: "gray" },
  { id: "maintenance", name: "In Maintenance", color: "yellow" },
  { id: "retired", name: "Retired", color: "red" }
];

// Compliance Status Options
export const complianceStatuses = [
  { id: "compliant", name: "Compliant", color: "green" },
  { id: "warning", name: "Warning", color: "yellow" },
  { id: "critical", name: "Critical", color: "orange" },
  { id: "overdue", name: "Overdue", color: "red" }
];

// Ownership Type Options
export const ownershipTypes = [
  { id: "company", name: "Company Owned", color: "blue" },
  { id: "rented", name: "Rented", color: "purple" },
  { id: "contracted", name: "Contracted", color: "orange" }
];

// Sites for equipment assignment
export const equipmentSites = [
  { id: "site-001", name: "Downtown Construction" },
  { id: "site-002", name: "Highway Expansion" },
  { id: "site-003", name: "Bridge Construction" },
  { id: "site-004", name: "Foundation Project" },
  { id: "site-005", name: "Residential Complex" }
];

// Equipment KPI calculations
export const getEquipmentKPIs = (equipment: CompanyEquipment[]) => {
  const totalEquipment = equipment.length;
  const availableEquipment = equipment.filter(eq => eq.isAvailableForAssignment).length;
  const assignedEquipment = equipment.filter(eq => !eq.isAvailableForAssignment && eq.overallStatus === "active").length;
  const maintenanceEquipment = equipment.filter(eq => eq.overallStatus === "maintenance").length;
  const companyOwned = equipment.filter(eq => eq.ownershipType === "company").length;
  const rented = equipment.filter(eq => eq.ownershipType === "rented").length;
  const contracted = equipment.filter(eq => eq.ownershipType === "contracted").length;

  const overdueInspections = equipment.filter(eq =>
    eq.nextInspectionDate && new Date(eq.nextInspectionDate) < new Date()
  ).length;

  const complianceIssues = equipment.filter(eq =>
    eq.complianceStatus === "warning" || eq.complianceStatus === "critical" || eq.complianceStatus === "overdue"
  ).length;

  const totalValue = equipment.reduce((sum, eq) => sum + eq.currentBookValue, 0);
  const monthlyRentalCost = equipment
    .filter(eq => eq.ownershipType === "rented" && eq.rentalInfo)
    .reduce((sum, eq) => sum + (eq.rentalInfo?.monthlyRate || 0), 0);

  return {
    totalEquipment,
    availableEquipment,
    assignedEquipment,
    maintenanceEquipment,
    companyOwned,
    rented,
    contracted,
    overdueInspections,
    complianceIssues,
    totalValue,
    monthlyRentalCost,
    utilizationRate: totalEquipment > 0 ? Math.round((assignedEquipment / totalEquipment) * 100) : 0
  };
};
