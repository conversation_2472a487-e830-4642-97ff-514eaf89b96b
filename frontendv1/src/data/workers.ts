// Enhanced Worker Management Mock Data - Centralized Company-Level Architecture
// This file implements the redesigned worker management system following the
// enhanced company-level architecture outlined in the redesign plan

import { Worker, WorkerSiteAssignment } from '../types';

// Training Compliance Status Types
export type TrainingComplianceStatus = 'compliant' | 'pending_training' | 'non_compliant' | 'expired';

// Site Assignment Status Types
export type SiteAssignmentStatus = 'assigned' | 'active' | 'suspended' | 'completed' | 'transferred';

// Enhanced Worker Interface for Company-Level Management
export interface CompanyWorker extends Worker {
  // Company-level specific fields
  employeeNumber: string;                    // Unique company-wide employee number
  complianceStatus: TrainingComplianceStatus; // Overall training compliance
  currentSiteId?: string;                    // Currently assigned site
  totalSiteAssignments: number;              // Number of sites worked at
  lastAssignmentDate?: string;               // Last site assignment date

  // Enhanced site assignments with full tracking
  siteAssignments: EnhancedSiteAssignment[];

  // Training compliance tracking
  trainingCompliance: WorkerTrainingCompliance[];

  // Company-level metadata
  hireDate: string;
  terminationDate?: string;
  rehireEligible: boolean;
  performanceRating: number;                 // 1-5 scale
  notes?: string;
}

// Enhanced Site Assignment with full lifecycle tracking
export interface EnhancedSiteAssignment extends WorkerSiteAssignment {
  assignmentType: 'initial' | 'transfer' | 'temporary' | 'permanent';
  assignmentReason: string;
  assignedBy: string;
  endDate?: string;
  actualEndDate?: string;
  transferredToSite?: string;
  transferredFromSite?: string;
  performanceNotes?: string;
  hourlyRate: number;
  overtimeRate?: number;
  totalHoursWorked?: number;
  averageHoursPerDay?: number;
  attendanceRate?: number;                   // Percentage
  safetyIncidents?: number;
  qualityRating?: number;                    // 1-5 scale
}

// Worker Training Compliance Tracking
export interface WorkerTrainingCompliance {
  id: string;
  workerId: number;
  trainingId: number;
  trainingName: string;
  tradeId?: number;                          // If trade-specific
  tradeName?: string;
  complianceStatus: TrainingComplianceStatus;
  isRequired: boolean;
  isMandatory: boolean;                      // Blocks site assignment if not completed
  completionDate?: string;
  expiryDate?: string;
  renewalDueDate?: string;
  certificateUrl?: string;
  lastAssessmentDate?: string;
  nextAssessmentDue?: string;
  blockingSiteAssignment: boolean;           // True if this blocks assignment
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// Site Worker Summary (for site-level views)
export interface SiteWorkerSummary {
  workerId: number;
  workerName: string;
  employeeNumber: string;
  currentRole: string;
  assignmentDate: string;
  assignmentStatus: SiteAssignmentStatus;
  primaryTrade: string;
  complianceStatus: TrainingComplianceStatus;
  isOnSite: boolean;
  lastCheckIn?: string;
  hoursWorkedToday?: number;
  hoursWorkedThisWeek?: number;
  attendanceRate: number;
  safetyScore: number;
  photoUrl?: string;
}

// Company Worker Statistics
export interface CompanyWorkerStats {
  totalWorkers: number;
  activeWorkers: number;
  workersOnSites: number;
  availableWorkers: number;
  compliantWorkers: number;
  nonCompliantWorkers: number;
  workersNeedingTraining: number;
  workersByTrade: { [tradeName: string]: number };
  workersBySite: { [siteId: string]: number };
  averageExperience: number;                 // In months
  averagePerformanceRating: number;
  totalManHours: number;
  averageHourlyRate: number;
}

// Mock Training Compliance Data
export const mockTrainingCompliance: WorkerTrainingCompliance[] = [
  {
    id: 'tc-001',
    workerId: 1,
    trainingId: 1,
    trainingName: 'Site Safety Induction',
    complianceStatus: 'compliant',
    isRequired: true,
    isMandatory: true,
    completionDate: '2025-01-15T12:00:00Z',
    expiryDate: '2025-07-15T12:00:00Z',
    renewalDueDate: '2025-06-15T12:00:00Z',
    certificateUrl: '/certificates/safety-induction-001.pdf',
    lastAssessmentDate: '2025-01-15T12:00:00Z',
    nextAssessmentDue: '2025-06-15T12:00:00Z',
    blockingSiteAssignment: false,
    notes: 'Completed with excellent score',
    createdAt: '2025-01-15T12:00:00Z',
    updatedAt: '2025-01-15T12:00:00Z'
  },
  {
    id: 'tc-002',
    workerId: 1,
    trainingId: 2,
    trainingName: 'Working at Heights',
    tradeId: 1,
    tradeName: 'Carpenter',
    complianceStatus: 'pending_training',
    isRequired: true,
    isMandatory: true,
    blockingSiteAssignment: true,
    notes: 'Required for elevated carpentry work',
    createdAt: '2025-01-15T12:00:00Z',
    updatedAt: '2025-01-15T12:00:00Z'
  },
  {
    id: 'tc-003',
    workerId: 2,
    trainingId: 1,
    trainingName: 'Site Safety Induction',
    complianceStatus: 'compliant',
    isRequired: true,
    isMandatory: true,
    completionDate: '2025-01-15T12:00:00Z',
    expiryDate: '2025-07-15T12:00:00Z',
    renewalDueDate: '2025-06-15T12:00:00Z',
    certificateUrl: '/certificates/safety-induction-002.pdf',
    lastAssessmentDate: '2025-01-15T12:00:00Z',
    nextAssessmentDue: '2025-06-15T12:00:00Z',
    blockingSiteAssignment: false,
    createdAt: '2025-01-15T12:00:00Z',
    updatedAt: '2025-01-15T12:00:00Z'
  },
  {
    id: 'tc-004',
    workerId: 2,
    trainingId: 2,
    trainingName: 'Working at Heights',
    tradeId: 2,
    tradeName: 'Electrician',
    complianceStatus: 'compliant',
    isRequired: true,
    isMandatory: true,
    completionDate: '2024-12-01T12:00:00Z',
    expiryDate: '2025-12-01T12:00:00Z',
    renewalDueDate: '2025-11-01T12:00:00Z',
    certificateUrl: '/certificates/heights-002.pdf',
    lastAssessmentDate: '2024-12-01T12:00:00Z',
    nextAssessmentDue: '2025-11-01T12:00:00Z',
    blockingSiteAssignment: false,
    createdAt: '2024-12-01T12:00:00Z',
    updatedAt: '2024-12-01T12:00:00Z'
  },
  {
    id: 'tc-005',
    workerId: 3,
    trainingId: 1,
    trainingName: 'Site Safety Induction',
    complianceStatus: 'compliant',
    isRequired: true,
    isMandatory: true,
    completionDate: '2025-01-10T12:00:00Z',
    expiryDate: '2025-07-10T12:00:00Z',
    renewalDueDate: '2025-06-10T12:00:00Z',
    certificateUrl: '/certificates/safety-induction-003.pdf',
    lastAssessmentDate: '2025-01-10T12:00:00Z',
    nextAssessmentDue: '2025-06-10T12:00:00Z',
    blockingSiteAssignment: false,
    createdAt: '2025-01-10T12:00:00Z',
    updatedAt: '2025-01-10T12:00:00Z'
  },
  {
    id: 'tc-006',
    workerId: 4,
    trainingId: 1,
    trainingName: 'Site Safety Induction',
    complianceStatus: 'expired',
    isRequired: true,
    isMandatory: true,
    completionDate: '2024-06-15T12:00:00Z',
    expiryDate: '2024-12-15T12:00:00Z',
    renewalDueDate: '2024-11-15T12:00:00Z',
    certificateUrl: '/certificates/safety-induction-004.pdf',
    lastAssessmentDate: '2024-06-15T12:00:00Z',
    nextAssessmentDue: '2025-01-15T12:00:00Z',
    blockingSiteAssignment: true,
    notes: 'Training expired, renewal required before site assignment',
    createdAt: '2024-06-15T12:00:00Z',
    updatedAt: '2024-12-15T12:00:00Z'
  }
];

// Mock Enhanced Site Assignments
export const mockEnhancedSiteAssignments: EnhancedSiteAssignment[] = [
  {
    id: 1,
    workerId: 1,
    siteId: 'site1',
    role: 'Senior Carpenter',
    startDate: '2024-12-01T00:00:00Z',
    status: 'active',
    assignmentType: 'initial',
    assignmentReason: 'New hire assignment to primary site',
    assignedBy: 'HR Manager',
    hourlyRate: 35.00,
    overtimeRate: 52.50,
    totalHoursWorked: 320,
    averageHoursPerDay: 8.5,
    attendanceRate: 95.2,
    safetyIncidents: 0,
    qualityRating: 4.5,
    performanceNotes: 'Excellent work quality, strong leadership skills',
    createdAt: '2024-12-01T00:00:00Z',
    createdBy: 'HR Manager'
  },
  {
    id: 2,
    workerId: 2,
    siteId: 'site1',
    role: 'Lead Electrician',
    startDate: '2024-11-01T00:00:00Z',
    status: 'active',
    assignmentType: 'transfer',
    assignmentReason: 'Transferred from Site 2 for specialized electrical work',
    assignedBy: 'Project Manager',
    transferredFromSite: 'site2',
    hourlyRate: 42.00,
    overtimeRate: 63.00,
    totalHoursWorked: 480,
    averageHoursPerDay: 8.0,
    attendanceRate: 98.5,
    safetyIncidents: 0,
    qualityRating: 4.8,
    performanceNotes: 'Outstanding technical skills, excellent safety record',
    createdAt: '2024-11-01T00:00:00Z',
    createdBy: 'Project Manager'
  },
  {
    id: 3,
    workerId: 3,
    siteId: 'site1',
    role: 'Plumber',
    startDate: '2025-01-05T00:00:00Z',
    status: 'active',
    assignmentType: 'initial',
    assignmentReason: 'New hire assignment for plumbing phase',
    assignedBy: 'Site Manager',
    hourlyRate: 32.00,
    overtimeRate: 48.00,
    totalHoursWorked: 80,
    averageHoursPerDay: 8.0,
    attendanceRate: 100.0,
    safetyIncidents: 0,
    qualityRating: 4.2,
    performanceNotes: 'Good technical skills, learning company procedures',
    createdAt: '2025-01-05T00:00:00Z',
    createdBy: 'Site Manager'
  },
  {
    id: 4,
    workerId: 4,
    siteId: 'site2',
    role: 'Mason',
    startDate: '2024-08-15T00:00:00Z',
    status: 'inactive',
    assignmentType: 'initial',
    assignmentReason: 'Initial assignment to masonry team',
    assignedBy: 'Site Manager',
    hourlyRate: 28.00,
    overtimeRate: 42.00,
    totalHoursWorked: 960,
    averageHoursPerDay: 7.5,
    attendanceRate: 85.0,
    safetyIncidents: 1,
    qualityRating: 3.5,
    performanceNotes: 'Suspended pending safety training renewal',
    createdAt: '2024-08-15T00:00:00Z',
    createdBy: 'Site Manager'
  },
  {
    id: 5,
    workerId: 5,
    siteId: 'site3',
    role: 'General Laborer',
    startDate: '2024-10-01T00:00:00Z',
    status: 'active',
    assignmentType: 'initial',
    assignmentReason: 'Initial assignment to general construction team',
    assignedBy: 'Site Supervisor',
    hourlyRate: 22.00,
    overtimeRate: 33.00,
    totalHoursWorked: 640,
    averageHoursPerDay: 8.0,
    attendanceRate: 92.0,
    safetyIncidents: 0,
    qualityRating: 4.0,
    performanceNotes: 'Reliable worker, good attitude',
    createdAt: '2024-10-01T00:00:00Z',
    createdBy: 'Site Supervisor'
  }
];

// Import existing mock data for reference
import { mockWorkers, mockTrades, mockSkills, mockTrainings, mockCertifications } from './mockData';

// Enhanced Company Workers - Centralized Master Database
export const mockCompanyWorkers: CompanyWorker[] = [
  {
    // Extend existing worker data with company-level enhancements
    ...(({ siteAssignments, ...rest }) => rest)(mockWorkers[0]),
    employeeNumber: 'EMP-001',
    complianceStatus: 'pending_training',
    currentSiteId: 'site1',
    totalSiteAssignments: 1,
    lastAssignmentDate: '2024-12-01T00:00:00Z',
    hireDate: '2024-12-01T00:00:00Z',
    rehireEligible: true,
    performanceRating: 4.5,
    notes: 'Excellent carpenter with strong leadership potential',

    // Override site assignments with enhanced version
    siteAssignments: [mockEnhancedSiteAssignments[0]],

    // Training compliance
    trainingCompliance: [
      mockTrainingCompliance[0], // Compliant safety induction
      mockTrainingCompliance[1]  // Pending heights training
    ]
  } as CompanyWorker,
  {
    ...(({ siteAssignments, ...rest }) => rest)(mockWorkers[1]),
    employeeNumber: 'EMP-002',
    complianceStatus: 'compliant',
    currentSiteId: 'site1',
    totalSiteAssignments: 2,
    lastAssignmentDate: '2024-11-01T00:00:00Z',
    hireDate: '2024-09-01T00:00:00Z',
    rehireEligible: true,
    performanceRating: 4.8,
    notes: 'Outstanding electrician with excellent safety record',

    // Override site assignments with enhanced version
    siteAssignments: [mockEnhancedSiteAssignments[1]],
    trainingCompliance: [
      mockTrainingCompliance[2], // Compliant safety induction
      mockTrainingCompliance[3]  // Compliant heights training
    ]
  } as CompanyWorker,
  {
    ...(({ siteAssignments, ...rest }) => rest)(mockWorkers[2]),
    employeeNumber: 'EMP-003',
    complianceStatus: 'compliant',
    currentSiteId: 'site1',
    totalSiteAssignments: 1,
    lastAssignmentDate: '2025-01-05T00:00:00Z',
    hireDate: '2025-01-05T00:00:00Z',
    rehireEligible: true,
    performanceRating: 4.2,
    notes: 'New hire with good technical skills',

    // Override site assignments with enhanced version
    siteAssignments: [mockEnhancedSiteAssignments[2]],
    trainingCompliance: [mockTrainingCompliance[4]]
  } as CompanyWorker,
  // Additional workers for testing different scenarios
  {
    id: 4,
    tenantId: 'tenant-1',
    name: 'James Omondi',
    company: 'ABC Construction',
    nationalId: '98765432',
    phoneNumber: '+*********** 890',
    email: '<EMAIL>',
    dateOfBirth: '1988-11-20',
    gender: 'Male',
    manHours: 2400,
    photoUrl: 'https://randomuser.me/api/portraits/men/4.jpg',
    inductionDate: '2024-08-15T09:00:00Z',
    medicalCheckDate: '2024-08-10T10:00:00Z',
    rating: 3.5,
    status: 'active',
    trades: [mockTrades[3]], // Mason
    skills: [mockSkills[0], mockSkills[2]], // Power Tools, Safety Protocols
    trainings: [mockTrainings[0]], // Site Safety Induction
    trainingHistory: [],
    certifications: [mockCertifications[4]], // First Aid (expired)
    age: 36,
    trainingsCompleted: 1,
    createdAt: '2024-08-15T00:00:00Z',
    createdBy: 'HR Manager',
    updatedAt: '2024-12-15T10:30:00Z',
    updatedBy: 'Admin',

    // Company-level enhancements
    employeeNumber: 'EMP-004',
    complianceStatus: 'expired',
    currentSiteId: 'site2',
    totalSiteAssignments: 1,
    lastAssignmentDate: '2024-08-15T00:00:00Z',
    hireDate: '2024-08-15T00:00:00Z',
    rehireEligible: true,
    performanceRating: 3.5,
    notes: 'Suspended pending safety training renewal',

    siteAssignments: [mockEnhancedSiteAssignments[3]],
    trainingCompliance: [mockTrainingCompliance[5]]
  } as CompanyWorker,
  {
    id: 5,
    tenantId: 'tenant-1',
    name: 'Grace Nyong\'o',
    company: 'ABC Construction',
    nationalId: '55667788',
    phoneNumber: '+*********** 456',
    email: '<EMAIL>',
    dateOfBirth: '1995-07-08',
    gender: 'Female',
    manHours: 1280,
    photoUrl: 'https://randomuser.me/api/portraits/women/5.jpg',
    inductionDate: '2024-10-01T09:00:00Z',
    medicalCheckDate: '2024-09-25T10:00:00Z',
    rating: 4.0,
    status: 'active',
    trades: [mockTrades[4]], // General Labor
    skills: [mockSkills[0], mockSkills[2]], // Power Tools, Safety Protocols
    trainings: [mockTrainings[0]], // Site Safety Induction
    trainingHistory: [],
    certifications: [mockCertifications[1]], // Safety Training
    age: 29,
    trainingsCompleted: 1,
    createdAt: '2024-10-01T00:00:00Z',
    createdBy: 'Site Supervisor',
    updatedAt: '2024-12-15T10:30:00Z',
    updatedBy: 'Admin',

    // Company-level enhancements
    employeeNumber: 'EMP-005',
    complianceStatus: 'compliant',
    currentSiteId: 'site3',
    totalSiteAssignments: 1,
    lastAssignmentDate: '2024-10-01T00:00:00Z',
    hireDate: '2024-10-01T00:00:00Z',
    rehireEligible: true,
    performanceRating: 4.0,
    notes: 'Reliable worker with good attitude',

    siteAssignments: [mockEnhancedSiteAssignments[4]],
    trainingCompliance: []
  } as CompanyWorker,
  // Available workers (not currently assigned to any site)
  {
    id: 6,
    tenantId: 'tenant-1',
    name: 'Peter Mwangi',
    company: 'ABC Construction',
    nationalId: '11223344',
    phoneNumber: '+*********** 012',
    email: '<EMAIL>',
    dateOfBirth: '1990-03-15',
    gender: 'Male',
    manHours: 1800,
    photoUrl: 'https://randomuser.me/api/portraits/men/6.jpg',
    inductionDate: '2024-06-01T09:00:00Z',
    medicalCheckDate: '2024-05-25T10:00:00Z',
    rating: 4.3,
    status: 'active',
    trades: [mockTrades[0]], // Carpenter
    skills: [mockSkills[0], mockSkills[1], mockSkills[2]], // Power Tools, Blueprint Reading, Safety Protocols
    trainings: [mockTrainings[0], mockTrainings[1]], // Site Safety Induction, Heights Training
    trainingHistory: [],
    certifications: [mockCertifications[0], mockCertifications[1]], // Safety Training, First Aid
    age: 34,
    trainingsCompleted: 2,
    createdAt: '2024-06-01T00:00:00Z',
    createdBy: 'HR Manager',
    updatedAt: '2024-12-15T10:30:00Z',
    updatedBy: 'Admin',

    // Company-level enhancements
    employeeNumber: 'EMP-006',
    complianceStatus: 'compliant',
    currentSiteId: undefined, // Not assigned to any site
    totalSiteAssignments: 0,
    lastAssignmentDate: undefined,
    hireDate: '2024-06-01T00:00:00Z',
    rehireEligible: true,
    performanceRating: 4.3,
    notes: 'Experienced carpenter available for assignment',

    siteAssignments: [], // No site assignments
    trainingCompliance: []
  } as CompanyWorker,
  {
    id: 7,
    tenantId: 'tenant-1',
    name: 'Sarah Wanjiru',
    company: 'ABC Construction',
    nationalId: '99887766',
    phoneNumber: '+*********** 789',
    email: '<EMAIL>',
    dateOfBirth: '1992-09-22',
    gender: 'Female',
    manHours: 1500,
    photoUrl: 'https://randomuser.me/api/portraits/women/7.jpg',
    inductionDate: '2024-07-15T09:00:00Z',
    medicalCheckDate: '2024-07-10T10:00:00Z',
    rating: 4.1,
    status: 'active',
    trades: [mockTrades[1]], // Electrician
    skills: [mockSkills[0], mockSkills[2]], // Power Tools, Safety Protocols
    trainings: [mockTrainings[0]], // Site Safety Induction
    trainingHistory: [],
    certifications: [mockCertifications[1]], // Safety Training
    age: 32,
    trainingsCompleted: 1,
    createdAt: '2024-07-15T00:00:00Z',
    createdBy: 'HR Manager',
    updatedAt: '2024-12-15T10:30:00Z',
    updatedBy: 'Admin',

    // Company-level enhancements
    employeeNumber: 'EMP-007',
    complianceStatus: 'pending_training',
    currentSiteId: undefined, // Not assigned to any site
    totalSiteAssignments: 0,
    lastAssignmentDate: undefined,
    hireDate: '2024-07-15T00:00:00Z',
    rehireEligible: true,
    performanceRating: 4.1,
    notes: 'Skilled electrician pending heights training certification',

    siteAssignments: [], // No site assignments
    trainingCompliance: []
  } as CompanyWorker,
  {
    id: 8,
    tenantId: 'tenant-1',
    name: 'Michael Otieno',
    company: 'ABC Construction',
    nationalId: '44556677',
    phoneNumber: '+*********** 456',
    email: '<EMAIL>',
    dateOfBirth: '1985-12-10',
    gender: 'Male',
    manHours: 2200,
    photoUrl: 'https://randomuser.me/api/portraits/men/8.jpg',
    inductionDate: '2024-05-01T09:00:00Z',
    medicalCheckDate: '2024-04-25T10:00:00Z',
    rating: 4.6,
    status: 'active',
    trades: [mockTrades[2]], // Plumber
    skills: [mockSkills[0], mockSkills[2]], // Power Tools, Safety Protocols
    trainings: [mockTrainings[0], mockTrainings[1]], // Site Safety Induction, Heights Training
    trainingHistory: [],
    certifications: [mockCertifications[0], mockCertifications[1]], // Safety Training, First Aid
    age: 39,
    trainingsCompleted: 2,
    createdAt: '2024-05-01T00:00:00Z',
    createdBy: 'HR Manager',
    updatedAt: '2024-12-15T10:30:00Z',
    updatedBy: 'Admin',

    // Company-level enhancements
    employeeNumber: 'EMP-008',
    complianceStatus: 'compliant',
    currentSiteId: undefined, // Not assigned to any site
    totalSiteAssignments: 0,
    lastAssignmentDate: undefined,
    hireDate: '2024-05-01T00:00:00Z',
    rehireEligible: true,
    performanceRating: 4.6,
    notes: 'Senior plumber with excellent technical skills, available for assignment',

    siteAssignments: [], // No site assignments
    trainingCompliance: []
  } as CompanyWorker
];

// Site Worker Summaries (for site-level views)
export const mockSiteWorkerSummaries: { [siteId: string]: SiteWorkerSummary[] } = {
  'site1': [
    {
      workerId: 1,
      workerName: 'David Kamau',
      employeeNumber: 'EMP-001',
      currentRole: 'Senior Carpenter',
      assignmentDate: '2024-12-01T00:00:00Z',
      assignmentStatus: 'assigned',
      primaryTrade: 'Carpenter',
      complianceStatus: 'pending_training',
      isOnSite: true,
      lastCheckIn: '2025-01-17T07:30:00Z',
      hoursWorkedToday: 6.5,
      hoursWorkedThisWeek: 32.5,
      attendanceRate: 95.2,
      safetyScore: 4.5,
      photoUrl: 'https://randomuser.me/api/portraits/men/1.jpg'
    },
    {
      workerId: 2,
      workerName: 'Mary Wanjiku',
      employeeNumber: 'EMP-002',
      currentRole: 'Lead Electrician',
      assignmentDate: '2024-11-01T00:00:00Z',
      assignmentStatus: 'assigned',
      primaryTrade: 'Electrician',
      complianceStatus: 'compliant',
      isOnSite: true,
      lastCheckIn: '2025-01-17T07:15:00Z',
      hoursWorkedToday: 7.0,
      hoursWorkedThisWeek: 40.0,
      attendanceRate: 98.5,
      safetyScore: 4.8,
      photoUrl: 'https://randomuser.me/api/portraits/women/2.jpg'
    },
    {
      workerId: 3,
      workerName: 'Peter Ochieng',
      employeeNumber: 'EMP-003',
      currentRole: 'Plumber',
      assignmentDate: '2025-01-05T00:00:00Z',
      assignmentStatus: 'assigned',
      primaryTrade: 'Plumber',
      complianceStatus: 'compliant',
      isOnSite: false,
      lastCheckIn: '2025-01-16T16:45:00Z',
      hoursWorkedToday: 0,
      hoursWorkedThisWeek: 32.0,
      attendanceRate: 100.0,
      safetyScore: 4.2,
      photoUrl: 'https://randomuser.me/api/portraits/men/3.jpg'
    }
  ],
  'site2': [
    {
      workerId: 4,
      workerName: 'James Omondi',
      employeeNumber: 'EMP-004',
      currentRole: 'Mason',
      assignmentDate: '2024-08-15T00:00:00Z',
      assignmentStatus: 'assigned',
      primaryTrade: 'Mason',
      complianceStatus: 'expired',
      isOnSite: false,
      attendanceRate: 85.0,
      safetyScore: 3.5,
      photoUrl: 'https://randomuser.me/api/portraits/men/4.jpg'
    }
  ],
  'site3': [
    {
      workerId: 5,
      workerName: 'Grace Nyong\'o',
      employeeNumber: 'EMP-005',
      currentRole: 'General Laborer',
      assignmentDate: '2024-10-01T00:00:00Z',
      assignmentStatus: 'assigned',
      primaryTrade: 'General Labor',
      complianceStatus: 'compliant',
      isOnSite: true,
      lastCheckIn: '2025-01-17T08:00:00Z',
      hoursWorkedToday: 5.5,
      hoursWorkedThisWeek: 38.0,
      attendanceRate: 92.0,
      safetyScore: 4.0,
      photoUrl: 'https://randomuser.me/api/portraits/women/5.jpg'
    }
  ]
};

// Company Worker Statistics
export const mockCompanyWorkerStats: CompanyWorkerStats = {
  totalWorkers: 8,
  activeWorkers: 8,
  workersOnSites: 5,
  availableWorkers: 3,
  compliantWorkers: 5,
  nonCompliantWorkers: 3,
  workersNeedingTraining: 3,
  workersByTrade: {
    'Carpenter': 2,
    'Electrician': 2,
    'Plumber': 2,
    'Mason': 1,
    'General Labor': 1
  },
  workersBySite: {
    'site1': 3,
    'site2': 1,
    'site3': 1
  },
  averageExperience: 18, // months
  averagePerformanceRating: 4.2,
  totalManHours: 9520,
  averageHourlyRate: 31.80
};

// Utility Functions for Worker Management

/**
 * Get workers assigned to a specific site
 */
export const getWorkersBySite = (siteId: string): CompanyWorker[] => {
  return mockCompanyWorkers.filter(worker =>
    worker.siteAssignments.some(assignment =>
      assignment.siteId === siteId && assignment.status === 'active'
    )
  );
};

/**
 * Get available workers (not currently assigned to any site)
 */
export const getAvailableWorkers = (): CompanyWorker[] => {
  return mockCompanyWorkers.filter(worker =>
    !worker.currentSiteId &&
    !worker.siteAssignments.some(assignment => assignment.status === 'active')
  );
};

/**
 * Get workers by compliance status
 */
export const getWorkersByComplianceStatus = (status: TrainingComplianceStatus): CompanyWorker[] => {
  return mockCompanyWorkers.filter(worker => worker.complianceStatus === status);
};

/**
 * Check if worker can be assigned to site (compliance check)
 */
export const canWorkerBeAssignedToSite = (workerId: number): boolean => {
  const worker = mockCompanyWorkers.find(w => w.id === workerId);
  if (!worker) return false;

  // Check for blocking training compliance issues
  const blockingIssues = worker.trainingCompliance.filter(tc => tc.blockingSiteAssignment);
  return blockingIssues.length === 0;
};

/**
 * Get worker training requirements by trade
 */
export const getWorkerTrainingRequirements = (workerId: number): WorkerTrainingCompliance[] => {
  const worker = mockCompanyWorkers.find(w => w.id === workerId);
  if (!worker) return [];

  return worker.trainingCompliance;
};

/**
 * Get site worker summary
 */
export const getSiteWorkerSummary = (siteId: string): SiteWorkerSummary[] => {
  return mockSiteWorkerSummaries[siteId] || [];
};