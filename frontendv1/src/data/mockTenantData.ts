import {
  Tenant,
  SiteInfo,
  Worker,
  Trade,
  Skill,
  Training,
  TrainingStatus,
  WorkerSiteAssignment,
  WorkerTrade,
  WorkerSkill
} from '../types';

// Mock Tenant Data
export const mockTenants: Tenant[] = [
  {
    id: 'tenant-1',
    name: 'ABC Construction Ltd',
    subdomain: 'abc-construction',
    subscriptionPlan: 'enterprise',
    maxSites: 50,
    status: 'active',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2025-01-15T10:30:00Z'
  },
  {
    id: 'tenant-2',
    name: 'XYZ Contractors',
    subdomain: 'xyz-contractors',
    subscriptionPlan: 'professional',
    maxSites: 20,
    status: 'active',
    createdAt: '2024-06-01T00:00:00Z',
    updatedAt: '2025-01-10T14:20:00Z'
  }
];

// Mock Sites (Projects) - belong to tenants
export const mockSites: SiteInfo[] = [
  {
    id: 'site1',
    tenantId: 'tenant-1',
    name: 'Westlands Construction Site',
    healthStatus: 'green',
    workersOnSite: 42,
    activePermits: 8,
    openIncidents: 0,
    projectManager: '<PERSON>',
    location: 'Waiyaki Way, Westlands, Nairobi',
    timeline: 'Jan 2025 - Dec 2026',
    currentPhase: 'Foundation',
    progressPercentage: 25,


    status: 'active',
    createdAt: new Date()
  },
  {
    id: 'site2',
    tenantId: 'tenant-1',
    name: 'Mombasa Road Project',
    healthStatus: 'amber',
    workersOnSite: 36,
    activePermits: 5,
    openIncidents: 2,
    projectManager: 'Sarah Ochieng',
    location: 'Enterprise Road, Industrial Area',
    timeline: 'Mar 2025 - Aug 2026',
    currentPhase: 'Structural',
    progressPercentage: 40,


    status: 'active',
    createdAt: new Date()
  },
  {
    id: 'site3',
    tenantId: 'tenant-1',
    name: 'Thika Highway Expansion',
    healthStatus: 'red',
    workersOnSite: 28,
    activePermits: 3,
    openIncidents: 4,
    projectManager: 'David Kimani',
    location: 'Thika Road, Kasarani Junction',
    timeline: 'Nov 2024 - Jul 2026',
    currentPhase: 'Excavation',
    progressPercentage: 15,


    status: 'active',
    createdAt: new Date()
  }
];

// Mock Trades - belong to tenant
export const mockTrades: Trade[] = [
  {
    id: 1,
    tenantId: 'tenant-1',
    name: 'Carpenter',
    description: 'Wood construction specialist',
    requiredCertifications: ['Basic Carpentry', 'Safety Training'],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 2,
    tenantId: 'tenant-1',
    name: 'Electrician',
    description: 'Electrical systems specialist',
    requiredCertifications: ['Electrical License', 'Safety Training'],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 3,
    tenantId: 'tenant-1',
    name: 'Plumber',
    description: 'Plumbing and water systems specialist',
    requiredCertifications: ['Plumbing License', 'Safety Training'],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  }
];

// Mock Skills - belong to tenant
export const mockSkills: Skill[] = [
  {
    id: 1,
    tenantId: 'tenant-1',
    name: 'Power Tools',
    description: 'Proficient with power tools',
    certificationRequired: true,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 2,
    tenantId: 'tenant-1',
    name: 'Heavy Machinery',
    description: 'Operation of heavy construction equipment',
    certificationRequired: true,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 3,
    tenantId: 'tenant-1',
    name: 'Safety Protocols',
    description: 'Knowledge of construction safety procedures',
    certificationRequired: false,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  }
];

// Mock Trainings - belong to tenant
export const mockTrainings: Training[] = [
  {
    id: 1,
    tenantId: 'tenant-1',
    name: 'Site Safety Induction',
    description: 'Comprehensive safety orientation for new workers',
    startDate: '2025-01-15T09:00:00Z',
    endDate: '2025-01-15T12:00:00Z',
    duration: '3h',
    validityPeriodMonths: 6,
    trainingType: 'Safety',
    trainer: 'John Smith',
    frequency: 'Every 6 months',
    status: TrainingStatus.Completed,
    workers: [],
    trainingHistory: [],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 2,
    tenantId: 'tenant-1',
    name: 'Working at Heights',
    description: 'Safety procedures for elevated work',
    startDate: '2025-01-20T09:00:00Z',
    endDate: '2025-01-20T16:00:00Z',
    duration: '7h',
    validityPeriodMonths: 12,
    trainingType: 'Safety',
    trainer: 'Mary Johnson',
    frequency: 'Annually',
    status: TrainingStatus.Scheduled,
    workers: [],
    trainingHistory: [],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  }
];

// Mock Workers - belong to tenant, assigned to sites via junction table
export const mockWorkers: Worker[] = [
  {
    id: 1,
    tenantId: 'tenant-1',
    name: 'David Kamau',
    company: 'ABC Construction',
    nationalId: '12345678',
    phoneNumber: '+*********** 678',
    email: '<EMAIL>',
    dateOfBirth: '1990-05-15',
    gender: 'Male',
    manHours: 2080,
    photoUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
    inductionDate: '2025-01-15T09:00:00Z',
    medicalCheckDate: '2025-01-10T10:00:00Z',
    rating: 4.5,
    hireDate: '2024-12-01T00:00:00Z',
    status: 'active',
    trades: [],
    skills: [],
    trainings: [],
    trainingHistory: [],
    siteAssignments: [],
    certifications: [],
    age: 34,
    trainingsCompleted: 1,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 2,
    tenantId: 'tenant-1',
    name: 'Mary Wanjiku',
    company: 'ABC Construction',
    nationalId: '87654321',
    phoneNumber: '+*********** 654',
    email: '<EMAIL>',
    dateOfBirth: '1985-08-22',
    gender: 'Female',
    manHours: 3200,
    photoUrl: 'https://randomuser.me/api/portraits/women/2.jpg',
    inductionDate: '2024-12-01T09:00:00Z',
    medicalCheckDate: '2024-11-25T10:00:00Z',
    rating: 4.8,
    hireDate: '2024-11-01T00:00:00Z',
    status: 'active',
    trades: [],
    skills: [],
    trainings: [],
    trainingHistory: [],
    siteAssignments: [],
    certifications: [],
    age: 39,
    trainingsCompleted: 2,
    createdAt: '2024-12-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-10T14:20:00Z',
    updatedBy: 'Admin'
  }
];

// Mock Worker Site Assignments - Junction table for worker-site relationships
export const mockWorkerSiteAssignments: WorkerSiteAssignment[] = [
  {
    id: 1,
    workerId: 1,
    siteId: 'site1',
    role: 'Carpenter',
    startDate: '2025-01-15T00:00:00Z',
    status: 'active',
    hourlyRate: 1500,
    notes: 'Primary carpenter for foundation work',
    createdAt: '2025-01-15T00:00:00Z',
    createdBy: 'John Mwangi',
    updatedAt: '2025-01-15T00:00:00Z',
    updatedBy: 'John Mwangi'
  },
  {
    id: 2,
    workerId: 2,
    siteId: 'site1',
    role: 'Electrician',
    startDate: '2024-12-01T00:00:00Z',
    status: 'active',
    hourlyRate: 1800,
    notes: 'Lead electrician for electrical installations',
    createdAt: '2024-12-01T00:00:00Z',
    createdBy: 'John Mwangi',
    updatedAt: '2025-01-10T14:20:00Z',
    updatedBy: 'John Mwangi'
  },
  {
    id: 3,
    workerId: 2,
    siteId: 'site2',
    role: 'Senior Electrician',
    startDate: '2025-01-10T00:00:00Z',
    status: 'active',
    hourlyRate: 2000,
    notes: 'Cross-site assignment for specialized electrical work',
    createdAt: '2025-01-10T00:00:00Z',
    createdBy: 'Sarah Ochieng',
    updatedAt: '2025-01-10T00:00:00Z',
    updatedBy: 'Sarah Ochieng'
  }
];

// Mock Worker Trade Assignments - Junction table for worker-trade relationships
export const mockWorkerTrades: WorkerTrade[] = [
  {
    id: 1,
    workerId: 1,
    tradeId: 1,
    certifiedDate: '2024-06-15T00:00:00Z',
    level: 'journeyman',
    certificationNumber: 'CARP-2024-001',
    createdAt: '2024-06-15T00:00:00Z',
    createdBy: 'HR System',
    updatedAt: '2024-06-15T00:00:00Z',
    updatedBy: 'HR System'
  },
  {
    id: 2,
    workerId: 2,
    tradeId: 2,
    certifiedDate: '2024-05-20T00:00:00Z',
    level: 'master',
    certificationNumber: 'ELEC-2024-002',
    createdAt: '2024-05-20T00:00:00Z',
    createdBy: 'HR System',
    updatedAt: '2024-05-20T00:00:00Z',
    updatedBy: 'HR System'
  }
];

// Mock Worker Skill Assignments - Junction table for worker-skill relationships
export const mockWorkerSkills: WorkerSkill[] = [
  {
    id: 1,
    workerId: 1,
    skillId: 1,
    acquiredDate: '2024-06-15T00:00:00Z',
    proficiencyLevel: 'advanced',
    createdAt: '2024-06-15T00:00:00Z',
    createdBy: 'HR System',
    updatedAt: '2024-06-15T00:00:00Z',
    updatedBy: 'HR System'
  },
  {
    id: 2,
    workerId: 1,
    skillId: 3,
    acquiredDate: '2024-06-15T00:00:00Z',
    proficiencyLevel: 'intermediate',
    createdAt: '2024-06-15T00:00:00Z',
    createdBy: 'HR System',
    updatedAt: '2024-06-15T00:00:00Z',
    updatedBy: 'HR System'
  },
  {
    id: 3,
    workerId: 2,
    skillId: 2,
    acquiredDate: '2024-05-20T00:00:00Z',
    proficiencyLevel: 'expert',
    createdAt: '2024-05-20T00:00:00Z',
    createdBy: 'HR System',
    updatedAt: '2024-05-20T00:00:00Z',
    updatedBy: 'HR System'
  },
  {
    id: 4,
    workerId: 2,
    skillId: 3,
    acquiredDate: '2024-05-20T00:00:00Z',
    proficiencyLevel: 'expert',
    createdAt: '2024-05-20T00:00:00Z',
    createdBy: 'HR System',
    updatedAt: '2024-05-20T00:00:00Z',
    updatedBy: 'HR System'
  }
];
