import { useNavigate, useLocation } from "react-router-dom";

export const useHashNavigation = () => {
	const navigate = useNavigate();
	const location = useLocation();

	const navigateToHash = (path: string) => {
		if (path.includes("#")) {
			const [pathname, hash] = path.split("#");

			// If we're already on the same pathname, just update the hash
			if (location.pathname === pathname) {
				window.location.hash = hash;
				// Manually trigger hashchange event for consistency
				window.dispatchEvent(new HashChangeEvent("hashchange"));
			} else {
				// Navigate to the new path with hash
				navigate(path);
			}
		} else {
			// Regular navigation
			navigate(path);
		}
	};

	return { navigateToHash };
};
