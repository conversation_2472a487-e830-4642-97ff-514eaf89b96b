import { useState, useCallback } from 'react';

// Types
export interface SiteCreationSession {
  id: string;
  tenantId: string;
  createdBy: string;
  templateId?: string;
  currentStep: number;
  completedSteps: number[];
  data: SiteCreationData;
  expiresAt: Date;
  lastSaved: Date;
}

export interface SiteCreationData {
  basicInfo: any;
  areas: any;
  stakeholders: any;
  regulatory: any;
  integrations: any;
}

const SESSION_STORAGE_KEY = 'site-creation-session';
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours

export const useSiteCreationSession = () => {
  const [session, setSession] = useState<SiteCreationSession | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [siteData, setSiteData] = useState<SiteCreationData>({
    basicInfo: {},
    areas: {},
    stakeholders: {},
    regulatory: {},
    integrations: {}
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Initialize or restore session
  const initializeSession = useCallback(async () => {
    setIsLoading(true);
    try {
      // Check for existing session in localStorage
      const existingSession = localStorage.getItem(SESSION_STORAGE_KEY);
      
      if (existingSession) {
        const parsedSession = JSON.parse(existingSession);
        if (new Date(parsedSession.expiresAt) > new Date()) {
          setSession(parsedSession);
          setCurrentStep(parsedSession.currentStep);
          setCompletedSteps(parsedSession.completedSteps);
          setSiteData(parsedSession.data);
          return;
        }
      }

      // Create new session
      const newSession: SiteCreationSession = {
        id: `session_${Date.now()}`,
        tenantId: 'tenant-1', // Get from context
        createdBy: 'current-user', // Get from auth context
        currentStep: 1,
        completedSteps: [],
        data: {
          basicInfo: {},
          areas: {},
          stakeholders: {},
          regulatory: {},
          integrations: {}
        },
        expiresAt: new Date(Date.now() + SESSION_DURATION),
        lastSaved: new Date()
      };

      setSession(newSession);
      localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(newSession));
    } catch (error) {
      console.error('Failed to initialize session:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Auto-save session
  const autoSave = useCallback(async () => {
    setIsSaving(true);
    try {
      setSession(prevSession => {
        if (!prevSession) return prevSession;

        const updatedSession = {
          ...prevSession,
          currentStep,
          completedSteps,
          data: siteData,
          lastSaved: new Date()
        };

        localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(updatedSession));
        return updatedSession;
      });
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsSaving(false);
    }
  }, [currentStep, completedSteps, siteData]);

  // Check for unsaved changes
  const hasUnsavedChanges = useCallback((): boolean => {
    if (!session) return false;
    return JSON.stringify(session.data) !== JSON.stringify(siteData);
  }, [session?.data, siteData]);

  // Clear session
  const clearSession = useCallback(() => {
    localStorage.removeItem(SESSION_STORAGE_KEY);
    setSession(null);
  }, []);

  // Update step data
  const updateStepData = useCallback((stepId: number, stepData: any) => {
    const dataKeyMap: Record<number, keyof SiteCreationData> = {
      1: 'basicInfo',
      2: 'areas',
      3: 'stakeholders',
      4: 'regulatory',
      5: 'integrations'
    };

    const dataKey = dataKeyMap[stepId] || 'basicInfo';

    setSiteData(prevData => ({
      ...prevData,
      [dataKey]: stepData
    }));

    // Mark step as completed
    setCompletedSteps(prevSteps => {
      if (!prevSteps.includes(stepId)) {
        return [...prevSteps, stepId];
      }
      return prevSteps;
    });
  }, []);

  return {
    session,
    currentStep,
    setCurrentStep,
    completedSteps,
    siteData,
    isLoading,
    isSaving,
    initializeSession,
    autoSave,
    hasUnsavedChanges,
    clearSession,
    updateStepData
  };
};
