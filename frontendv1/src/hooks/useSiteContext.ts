import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { NavigationContext, SiteInfo } from '../types';
import { getNavigationContext } from '../utils/routeUtils';
import { useTenantContext } from './useTenantContext.tsx';
import { mockSites } from '../data/mockTenantData';

// Helper to get sites for current tenant
const getSitesForTenant = (tenantId: string): Record<string, SiteInfo> => {
  const tenantSites = mockSites.filter(site => site.tenantId === tenantId);
  return tenantSites.reduce((acc, site) => {
    acc[site.id] = site;
    return acc;
  }, {} as Record<string, SiteInfo>);
};

/**
 * Custom hook for managing site context and navigation state with tenant awareness
 */
export const useSiteContext = () => {
  const location = useLocation();
  const { tenantId } = useTenantContext();
  const [navigationContext, setNavigationContext] = useState<NavigationContext>(() =>
    getNavigationContext(location.pathname)
  );
  const [currentSite, setCurrentSite] = useState<SiteInfo | null>(null);

	useEffect(() => {
		const context = getNavigationContext(location.pathname);

    // If we have a site ID and tenant ID, fetch the site information
    if (context.siteId && tenantId) {
      const tenantSites = getSitesForTenant(tenantId);
      const site = tenantSites[context.siteId];
      if (site) {
        context.siteName = site.name;
        setCurrentSite(site);
      } else {
        // Handle case where site is not found or doesn't belong to tenant
        setCurrentSite(null);
      }
    } else {
      setCurrentSite(null);
    }

    setNavigationContext(context);
  }, [location.pathname, tenantId]);

  return {
    navigationContext,
    currentSite,
    isCompanyLevel: navigationContext.isCompanyLevel,
    isSiteLevel: navigationContext.isSiteLevel,
    siteId: navigationContext.siteId,
    siteName: navigationContext.siteName,
    tenantId,
  };
};

/**
 * Hook to get all available sites for site switching (tenant-aware)
 */
export const useAllSites = () => {
  const { tenantId } = useTenantContext();
  return tenantId ? Object.values(getSitesForTenant(tenantId)) : [];
};
