import { useState, useEffect, useCallback } from 'react';
import { mockGraphQLClient } from '../services/mockGraphQLClient';
import { Worker, Training, Trade, Skill, ToolboxSession, WorkerTrainingHistory } from '../types';
import { CompanyWorker } from '../data/workers';

// Apollo GraphQL stub types and interfaces
interface ApolloQueryResult<T> {
	data?: T;
	loading: boolean;
	error?: Error;
	refetch: () => Promise<any>;
	fetchMore?: (options: any) => Promise<any>;
	networkStatus?: number;
}

interface ApolloMutationResult<T> {
	data?: T;
	loading: boolean;
	error?: Error;
}

interface ApolloMutationTuple<T, TVariables> {
	0: (options?: { variables?: TVariables; [key: string]: any }) => Promise<{ data?: T }>;
	1: ApolloMutationResult<T>;
}

// Apollo GraphQL stub hooks
export const useQuery = <T = any, TVariables = any>(
	_query: any,
	options?: {
		variables?: TVariables;
		skip?: boolean;
		errorPolicy?: 'none' | 'ignore' | 'all';
		fetchPolicy?: 'cache-first' | 'cache-and-network' | 'network-only' | 'cache-only' | 'no-cache' | 'standby';
		[key: string]: any;
	}
): ApolloQueryResult<T> => {
	const [data, setData] = useState<T | undefined>();
	const [loading, setLoading] = useState(!options?.skip);
	const [error, setError] = useState<Error | undefined>();

	const refetch = useCallback(async () => {
		setLoading(true);
		setError(undefined);
		try {
			// Simulate GraphQL query execution
			await new Promise(resolve => setTimeout(resolve, 500));
			// In a real implementation, this would execute the actual query
			setData({} as T);
		} catch (err) {
			setError(err as Error);
		} finally {
			setLoading(false);
		}
	}, []);

	useEffect(() => {
		if (!options?.skip) {
			refetch();
		}
	}, [options?.skip, JSON.stringify(options?.variables), refetch]);

	return {
		data,
		loading,
		error,
		refetch,
		fetchMore: async (_options: any) => ({ data: {} as T }),
		networkStatus: loading ? 1 : 7
	};
};

export const useMutation = <T = any, TVariables = any>(
	_mutation: any,
	options?: {
		refetchQueries?: any[];
		errorPolicy?: 'none' | 'ignore' | 'all';
		onCompleted?: (data: T) => void;
		onError?: (error: Error) => void;
		[key: string]: any;
	}
): ApolloMutationTuple<T, TVariables> => {
	const [data, setData] = useState<T | undefined>();
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | undefined>();

	const mutate = useCallback(async (_mutationOptions?: { variables?: TVariables; [key: string]: any }) => {
		setLoading(true);
		setError(undefined);
		try {
			// Simulate GraphQL mutation execution
			await new Promise(resolve => setTimeout(resolve, 300));
			// In a real implementation, this would execute the actual mutation
			const result = {} as T;
			setData(result);
			options?.onCompleted?.(result);
			return { data: result };
		} catch (err) {
			const error = err as Error;
			setError(error);
			options?.onError?.(error);
			throw error;
		} finally {
			setLoading(false);
		}
	}, [options]);

	return [mutate, { data, loading, error }];
};

export const useLazyQuery = <T = any, TVariables = any>(
	_query: any,
	_options?: {
		errorPolicy?: 'none' | 'ignore' | 'all';
		fetchPolicy?: 'cache-first' | 'cache-and-network' | 'network-only' | 'cache-only' | 'no-cache' | 'standby';
		[key: string]: any;
	}
): [
	(options?: { variables?: TVariables; [key: string]: any }) => Promise<any>,
	ApolloQueryResult<T>
] => {
	const [data, setData] = useState<T | undefined>();
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | undefined>();

	const executeQuery = useCallback(async (_queryOptions?: { variables?: TVariables; [key: string]: any }) => {
		setLoading(true);
		setError(undefined);
		try {
			// Simulate GraphQL query execution
			await new Promise(resolve => setTimeout(resolve, 500));
			// In a real implementation, this would execute the actual query
			const result = {} as T;
			setData(result);
			return { data: result };
		} catch (err) {
			setError(err as Error);
			throw err;
		} finally {
			setLoading(false);
		}
	}, []);

	const refetch = useCallback(async () => {
		return executeQuery();
	}, [executeQuery]);

	return [
		executeQuery,
		{
			data,
			loading,
			error,
			refetch,
			fetchMore: async (_options: any) => ({ data: {} as T }),
			networkStatus: loading ? 1 : 7
		}
	];
};

export const useSubscription = <T = any, TVariables = any>(
	_subscription: any,
	options?: {
		variables?: TVariables;
		skip?: boolean;
		onSubscriptionData?: (options: { subscriptionData: { data: T } }) => void;
		[key: string]: any;
	}
): { data?: T; loading: boolean; error?: Error } => {
	const [data, setData] = useState<T | undefined>();
	const [loading, setLoading] = useState(!options?.skip);
	const [error, _setError] = useState<Error | undefined>();

	useEffect(() => {
		if (options?.skip) return;

		// Simulate subscription setup
		setLoading(true);
		const timeout = setTimeout(() => {
			setLoading(false);
			// Simulate subscription data
			const mockData = {} as T;
			setData(mockData);
			options?.onSubscriptionData?.({ subscriptionData: { data: mockData } });
		}, 1000);

		return () => clearTimeout(timeout);
	}, [options?.skip, JSON.stringify(options?.variables)]);

	return { data, loading, error };
};

// Additional Apollo-like hooks
export const useApolloClient = () => {
	return {
		query: async (_options: { query: any; variables?: any }) => {
			// Simulate Apollo client query
			await new Promise(resolve => setTimeout(resolve, 500));
			return { data: {} };
		},
		mutate: async (_options: { mutation: any; variables?: any }) => {
			// Simulate Apollo client mutation
			await new Promise(resolve => setTimeout(resolve, 300));
			return { data: {} };
		},
		cache: {
			readQuery: (_options: { query: any; variables?: any }) => ({}),
			writeQuery: (_options: { query: any; variables?: any; data: any }) => {},
			evict: (_options: { id?: string; fieldName?: string }) => {},
			gc: () => {},
		},
		clearStore: async () => {},
		resetStore: async () => {},
	};
};

export const useReactiveVar = <T>(reactiveVar: any): T => {
	const [value, setValue] = useState<T>(reactiveVar());

	useEffect(() => {
		// Simulate reactive variable subscription
		const interval = setInterval(() => {
			setValue(reactiveVar());
		}, 1000);

		return () => clearInterval(interval);
	}, [reactiveVar]);

	return value;
};

export const useLazySubscription = <T = any, TVariables = any>(
	_subscription: any,
	_options?: {
		errorPolicy?: 'none' | 'ignore' | 'all';
		[key: string]: any;
	}
): [
	(options?: { variables?: TVariables; [key: string]: any }) => void,
	{ data?: T; loading: boolean; error?: Error; called: boolean }
] => {
	const [data, setData] = useState<T | undefined>();
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | undefined>();
	const [called, setCalled] = useState(false);

	const startSubscription = useCallback((_subscriptionOptions?: { variables?: TVariables; [key: string]: any }) => {
		setCalled(true);
		setLoading(true);
		setError(undefined);

		// Simulate subscription start
		setTimeout(() => {
			setLoading(false);
			setData({} as T);
		}, 1000);
	}, []);

	return [startSubscription, { data, loading, error, called }];
};

// GraphQL query/mutation constants (stubs)
export const GET_WORKER_ATTENDANCE = 'GET_WORKER_ATTENDANCE';
export const RECORD_WORKER_ATTENDANCE = 'RECORD_WORKER_ATTENDANCE';
export const UPDATE_WORKER_ATTENDANCE = 'UPDATE_WORKER_ATTENDANCE';
export const GET_WORKERS = 'GET_WORKERS';
export const CREATE_WORKER = 'CREATE_WORKER';
export const UPDATE_WORKER = 'UPDATE_WORKER';
export const DELETE_WORKER = 'DELETE_WORKER';

// Mock GraphQL hook types with tenant context
interface MockQueryResult<T> {
  data: T | undefined;
  loading: boolean;
  error: Error | undefined;
  refetch: () => Promise<void>;
}

interface TenantQueryOptions {
  tenantId: string;
  siteId?: string;
}

interface MockMutationResult<T> {
  mutate: (variables?: any) => Promise<{ data: T }>;
  loading: boolean;
  error: Error | undefined;
}

// Enhanced Worker Hooks with tenant support and future GraphQL readiness
export const useWorkers = (options: TenantQueryOptions): MockQueryResult<{ workers: Worker[] }> => {
  const [data, setData] = useState<{ workers: Worker[] } | undefined>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | undefined>();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.getWorkers(options.tenantId, options.siteId);
      setData(result.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [options.tenantId, options.siteId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};

export const useWorker = (id: number, tenantId: string): MockQueryResult<{ worker: CompanyWorker | null }> => {
  const [data, setData] = useState<{ worker: CompanyWorker | null } | undefined>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | undefined>();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.getWorkerById(id, tenantId);
      setData(result.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [id, tenantId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};

export const useCreateWorker = (): MockMutationResult<{ createWorker: Worker }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (variables: { input: Partial<Worker> }) => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.createWorker(variables.input);
      return { data: result.data };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

export const useUpdateWorker = (): MockMutationResult<{ updateWorker: Worker }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (variables: { id: number; input: Partial<Worker> }) => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.updateWorker(variables.id, variables.input);
      return { data: result.data };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

export const useUploadWorkerPhoto = (): MockMutationResult<{ uploadWorkerPhoto: { photoUrl: string; hikvisionRegistered: boolean; message: string } }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (variables: { workerId: number; photo: File }) => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.uploadWorkerPhoto(variables.workerId, variables.photo);
      return { data: result.data };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

export const useDeleteWorkerPhoto = (): MockMutationResult<{ deleteWorkerPhoto: { success: boolean; message: string } }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (variables: { workerId: number }) => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.deleteWorkerPhoto(variables.workerId);
      return { data: result.data };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

// Training Hooks - removed old version, using tenant-aware version below

export const useTrainingExpiryTracker = (siteId?: string): MockQueryResult<{ trainingExpiryTracker: any[] }> => {
  const [data, setData] = useState<{ trainingExpiryTracker: any[] } | undefined>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | undefined>();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.getTrainingExpiryTracker(siteId);
      setData(result.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [siteId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};

export const useRecordTrainingCompletion = (): MockMutationResult<{ recordTrainingCompletion: WorkerTrainingHistory }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (variables: {
    input: {
      workerId: number;
      trainingId: number;
      completionDate: string;
      expiryDate?: string;
      score?: number;
      notes?: string;
    }
  }) => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.recordTrainingCompletion(variables.input);
      return { data: result.data };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

export const useBulkAssignTraining = (): MockMutationResult<{ bulkAssignTraining: { success: boolean; assignedCount: number; message: string } }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (variables: { trainingId: number; workerIds: number[]; scheduledDate?: string }) => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.bulkAssignTraining(variables.trainingId, variables.workerIds, variables.scheduledDate);
      return { data: result.data };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

// Toolbox Session Hooks
export const useToolboxSessions = (siteId?: string, dateFrom?: string, dateTo?: string): MockQueryResult<{ toolboxSessions: ToolboxSession[] }> => {
  const [data, setData] = useState<{ toolboxSessions: ToolboxSession[] } | undefined>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | undefined>();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.getToolboxSessions(siteId, dateFrom, dateTo);
      setData(result.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [siteId, dateFrom, dateTo]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};

export const useCreateToolboxSession = (): MockMutationResult<{ createToolboxSession: ToolboxSession }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (variables: {
    input: {
      sessionTime: string;
      topic: string;
      conductor: string;
      notes?: string;
      photoFile?: File;
      attendeeIds: number[];
    }
  }) => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.createToolboxSession(variables.input);
      return { data: result.data };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

// Worker Attendance Hooks
export const useWorkerAttendance = (workerId: number, dateFrom?: string, dateTo?: string): MockQueryResult<{ workerAttendance: any[] }> => {
	const [data, setData] = useState<{ workerAttendance: any[] } | undefined>();
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<Error | undefined>();

	const fetchData = useCallback(async () => {
		try {
			setLoading(true);
			setError(undefined);
			// Simulate attendance data fetch
			await new Promise(resolve => setTimeout(resolve, 500));
			const mockAttendance = {
				workerAttendance: [
					{
						id: '1',
						workerId,
						date: dateFrom || new Date().toISOString().split('T')[0],
						checkIn: '08:00:00',
						checkOut: '17:00:00',
						hoursWorked: 8,
						status: 'present'
					}
				]
			};
			setData(mockAttendance);
		} catch (err) {
			setError(err as Error);
		} finally {
			setLoading(false);
		}
	}, [workerId, dateFrom, dateTo]);

	useEffect(() => {
		fetchData();
	}, [fetchData]);

	return {
		data,
		loading,
		error,
		refetch: fetchData,
	};
};

export const useRecordWorkerAttendance = (): MockMutationResult<{ recordWorkerAttendance: any }> => {
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | undefined>();

	const mutate = async (variables: {
		workerId: number;
		date: string;
		checkIn?: string;
		checkOut?: string;
		status: string
	}) => {
		try {
			setLoading(true);
			setError(undefined);
			// Simulate attendance recording
			await new Promise(resolve => setTimeout(resolve, 300));
			const result = {
				data: {
					recordWorkerAttendance: {
						id: Date.now().toString(),
						...variables,
						createdAt: new Date().toISOString()
					}
				}
			};
			return result;
		} catch (err) {
			setError(err as Error);
			throw err;
		} finally {
			setLoading(false);
		}
	};

	return { mutate, loading, error };
};

export const useUpdateWorkerAttendance = (): MockMutationResult<{ updateWorkerAttendance: any }> => {
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<Error | undefined>();

	const mutate = async (variables: {
		id: string;
		checkIn?: string;
		checkOut?: string;
		status?: string
	}) => {
		try {
			setLoading(true);
			setError(undefined);
			// Simulate attendance update
			await new Promise(resolve => setTimeout(resolve, 300));
			const result = {
				data: {
					updateWorkerAttendance: {
						...variables,
						updatedAt: new Date().toISOString()
					}
				}
			};
			return result;
		} catch (err) {
			setError(err as Error);
			throw err;
		} finally {
			setLoading(false);
		}
	};

	return { mutate, loading, error };
};

// Trade and Skill Hooks with tenant support
export const useTrades = (tenantId: string): MockQueryResult<{ trades: Trade[] }> => {
  const [data, setData] = useState<{ trades: Trade[] } | undefined>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | undefined>();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.getTrades(tenantId);
      setData(result.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [tenantId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};

export const useSkills = (tenantId: string): MockQueryResult<{ skills: Skill[] }> => {
  const [data, setData] = useState<{ skills: Skill[] } | undefined>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | undefined>();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.getSkills(tenantId);
      setData(result.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [tenantId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};

// Hikvision Integration Hooks
export const useRegisterWorkerFace = (): MockMutationResult<{ registerWorkerFace: { success: boolean; hikvisionPersonId: string; message: string } }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (variables: { workerId: number; photo: File }) => {
    try {
      setLoading(true);
      setError(undefined);
      // Simulate face registration
      await new Promise(resolve => setTimeout(resolve, 1000));

      const result = {
        data: {
          registerWorkerFace: {
            success: true,
            hikvisionPersonId: `hik_person_${variables.workerId}_${Date.now()}`,
            message: 'Worker face registered successfully with Hikvision system'
          }
        }
      };

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

export const useSyncHikvisionAttendance = (): MockMutationResult<{ syncHikvisionAttendance: { success: boolean; syncedCount: number; message: string; attendanceRecords: any[] } }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (_variables: { siteId: string; date: string }) => {
    try {
      setLoading(true);
      setError(undefined);
      // Simulate sync operation
      await new Promise(resolve => setTimeout(resolve, 3000));

      const result = {
        data: {
          syncHikvisionAttendance: {
            success: true,
            syncedCount: 5,
            message: 'Successfully synced attendance data from Hikvision terminals',
            attendanceRecords: [
              {
                workerId: 'W001',
                checkInTime: '07:55:00',
                checkOutTime: '17:05:00',
                isVerified: true,
                terminalId: 'term1'
              },
              {
                workerId: 'W002',
                checkInTime: '08:15:00',
                isVerified: true,
                terminalId: 'term2'
              }
            ]
          }
        }
      };

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

export const useUnregisterWorkerFace = (): MockMutationResult<{ unregisterWorkerFace: { success: boolean; message: string } }> => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const mutate = async (_variables: { workerId: number }) => {
    try {
      setLoading(true);
      setError(undefined);
      // Simulate face unregistration
      await new Promise(resolve => setTimeout(resolve, 1000));

      const result = {
        data: {
          unregisterWorkerFace: {
            success: true,
            message: 'Worker face unregistered successfully from Hikvision system'
          }
        }
      };

      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
};

// Training Hooks with tenant support
export const useTrainings = (tenantId: string): MockQueryResult<{ trainings: Training[] }> => {
  const [data, setData] = useState<{ trainings: Training[] } | undefined>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | undefined>();

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(undefined);
      const result = await mockGraphQLClient.getTrainings(tenantId);
      setData(result.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [tenantId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};

// Enhanced Training Assignment Hook with future GraphQL readiness
export const useTrainingAssignment = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const assignTraining = useCallback(async (assignment: {
    trainingId: number;
    workerIds: number[];
    targetCompletionDate?: string;
    notes?: string;
    assignmentType?: 'manual' | 'auto' | 'bulk';
  }) => {
    try {
      setLoading(true);
      setError(undefined);

      // Enhanced mock implementation with realistic behavior

      // Simulate network delay based on assignment type
      const delay = assignment.assignmentType === 'bulk' ? 2000 : 1000;
      await new Promise(resolve => setTimeout(resolve, delay));

      // Simulate potential failures for testing
      if (Math.random() > 0.95) {
        throw new Error('Assignment failed due to network error');
      }

      return {
        success: true,
        assignedCount: assignment.workerIds.length,
        message: `Training assigned to ${assignment.workerIds.length} worker(s)`
      };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const autoAssignByTrade = useCallback(async (_input: {
    trainingId: number;
    tradeIds: number[];
    siteId?: string;
    tenantId: string;
  }) => {
    try {
      setLoading(true);
      setError(undefined);

      // Enhanced mock implementation
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Simulate realistic assignment counts
      const assignedCount = Math.floor(Math.random() * 10) + 1;

      return {
        success: true,
        assignedCount,
        message: `Training auto-assigned to ${assignedCount} workers`,
        assignedWorkers: []
      };
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    assignTraining,
    autoAssignByTrade,
    loading,
    error,
  };
};

// Enhanced Training Completion Hook with file upload
export const useTrainingCompletion = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | undefined>();

  const uploadCertificate = async (file: File): Promise<string> => {
    // Simulate cloud storage upload
    const formData = new FormData();
    formData.append('certificate', file);

    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Return mock URL - in real implementation this would be the actual uploaded file URL
    return `https://storage.example.com/certificates/${Date.now()}-${file.name}`;
  };

  const recordCompletion = useCallback(async (completion: {
    workerId: number;
    trainingId: number;
    completionDate: string;
    expiryDate?: string;
    score?: number;
    notes?: string;
    certificateFile?: File;
    trainerName?: string;
    location?: string;
  }) => {
    try {
      setLoading(true);
      setError(undefined);

      let certificateUrl: string | undefined;

      // Upload certificate file if provided
      if (completion.certificateFile) {
        try {
          certificateUrl = await uploadCertificate(completion.certificateFile);
        } catch (uploadError) {
          console.error('Certificate upload failed:', uploadError);
          // Continue without certificate URL
        }
      }

      // Enhanced mock implementation
      const mockCompletion = {
        ...completion,
        certificateUrl
      };



      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate potential failures
      if (Math.random() > 0.98) {
        throw new Error('Failed to record completion');
      }

      const result = await mockGraphQLClient.recordTrainingCompletion(mockCompletion);
      return result.data;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    recordCompletion,
    loading,
    error,
  };
};

// Utility hook for cache management (mock implementation)
export const useGraphQLCache = () => {
  const clearCache = () => {
    // Reset mock data
    mockGraphQLClient.resetData();
  };

  const refetchQueries = (_queries: string[]) => {
    // In a real implementation, this would refetch specific queries
  };

  const updateCache = (_query: any, _variables: any, _updateFn: (data: any) => any) => {
    // In a real implementation, this would update the Apollo cache
  };

  return {
    clearCache,
    refetchQueries,
    updateCache,
  };
};
