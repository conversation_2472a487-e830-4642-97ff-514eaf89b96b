import React, { useState } from 'react';
import Dashboard from './pages/Dashboard';
import MyTeam from './pages/MyTeam';
import TaskPlanning from './pages/TaskPlanning';
import PermitRequests from './pages/PermitRequests';
import Reports from './pages/Reports';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  const renderCurrentPage = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard activeTab={activeTab} onTabChange={handleTabChange} />;
      case 'team':
        return <MyTeam activeTab={activeTab} onTabChange={handleTabChange} />;
      case 'tasks':
        return <TaskPlanning activeTab={activeTab} onTabChange={handleTabChange} />;
      case 'permits':
        return <PermitRequests activeTab={activeTab} onTabChange={handleTabChange} />;
      case 'reports':
        return <Reports activeTab={activeTab} onTabChange={handleTabChange} />;
      default:
        return <Dashboard activeTab={activeTab} onTabChange={handleTabChange} />;
    }
  };

  return (
    <div className="App">
      {renderCurrentPage()}
    </div>
  );
}

export default App;
