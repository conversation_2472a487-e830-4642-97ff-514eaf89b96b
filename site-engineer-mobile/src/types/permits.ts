// Permit-related types for Site Engineer Mobile Application

// Status Types
export type PermitStatus = 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected' | 'active' | 'closed';
export type Priority = 'low' | 'medium' | 'high' | 'critical';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

export interface EngineerPermitRequest {
  id: string;
  permitNumber?: string;
  workActivity: string;
  description: string;
  location: string;
  siteId: string;
  requestedBy: string; // Engineer ID
  assignedWorkers: string[];
  plannedStartDate: Date;
  plannedEndDate: Date;
  estimatedDuration: number; // in hours
  riskLevel: RiskLevel;
  priority: Priority;
  status: PermitStatus;
  submittedAt?: Date;
  approvedAt?: Date;
  reviewedBy?: string;
  reviewNotes?: string;
  rejectionReason?: string;
  attachments: string[];
  requiredPPE: string[];
  safetyRequirements: string[];
  hazards: string[];
  controlMeasures: string[];
  emergencyProcedures: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface PermitApprovalStep {
  id: string;
  stepName: string;
  approverRole: string;
  approverName?: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedAt?: Date;
  notes?: string;
  order: number;
}

export interface PermitStatusTracking {
  permitId: string;
  currentStep: string;
  approvalSteps: PermitApprovalStep[];
  estimatedApprovalTime: number; // in hours
  canProceed: boolean;
  nextAction: string;
}

export interface WorkActivityTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  riskLevel: RiskLevel;
  estimatedDuration: number;
  requiredPermitTypes: string[];
  requiredPPE: string[];
  standardHazards: string[];
  standardControlMeasures: string[];
  safetyRequirements: string[];
  isActive: boolean;
}

// Mock data for development
export const mockPermitRequests: EngineerPermitRequest[] = [
  {
    id: 'permit-1',
    permitNumber: 'HWP-2024-001',
    workActivity: 'Hot Work - Welding',
    description: 'Structural steel welding for second floor framework',
    location: 'Block A Second Floor',
    siteId: 'site-1',
    requestedBy: 'engineer-1',
    assignedWorkers: ['worker-1'],
    plannedStartDate: new Date(),
    plannedEndDate: new Date(Date.now() + 4 * 60 * 60 * 1000),
    estimatedDuration: 4,
    riskLevel: 'high',
    priority: 'critical',
    status: 'approved',
    submittedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    approvedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    reviewedBy: 'safety-officer-1',
    reviewNotes: 'All safety requirements met. Fire watch assigned.',
    attachments: ['risk-assessment.pdf', 'method-statement.pdf'],
    requiredPPE: ['Welding helmet', 'Fire-resistant clothing', 'Safety boots', 'Gloves'],
    safetyRequirements: ['Fire watch', 'Fire extinguisher nearby', 'Clear combustibles'],
    hazards: ['Fire risk', 'Hot surfaces', 'Fumes', 'Arc flash'],
    controlMeasures: ['Fire watch present', 'Ventilation provided', 'PPE worn', 'Fire extinguisher ready'],
    emergencyProcedures: ['Stop work immediately', 'Alert fire watch', 'Use fire extinguisher if safe'],
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'permit-2',
    permitNumber: 'EWP-2024-002',
    workActivity: 'Electrical Installation',
    description: 'Installation of main electrical distribution panel',
    location: 'Block B Electrical Room',
    siteId: 'site-1',
    requestedBy: 'engineer-1',
    assignedWorkers: ['worker-2'],
    plannedStartDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
    plannedEndDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000),
    estimatedDuration: 6,
    riskLevel: 'high',
    priority: 'medium',
    status: 'under-review',
    submittedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    attachments: ['electrical-drawings.pdf'],
    requiredPPE: ['Insulated gloves', 'Arc flash suit', 'Safety boots', 'Hard hat'],
    safetyRequirements: ['Lockout/Tagout', 'Voltage testing', 'Isolation verification'],
    hazards: ['Electrical shock', 'Arc flash', 'Burns', 'Falls'],
    controlMeasures: ['LOTO procedures', 'Voltage testing', 'Insulated tools', 'Proper PPE'],
    emergencyProcedures: ['De-energize immediately', 'Call emergency services', 'Provide first aid'],
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'permit-3',
    workActivity: 'Working at Height',
    description: 'Installation of roof trusses on Block C',
    location: 'Block C Roof Level',
    siteId: 'site-1',
    requestedBy: 'engineer-1',
    assignedWorkers: ['worker-3', 'worker-4'],
    plannedStartDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    plannedEndDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000),
    estimatedDuration: 8,
    riskLevel: 'high',
    priority: 'medium',
    status: 'draft',
    attachments: [],
    requiredPPE: ['Safety harness', 'Hard hat', 'Safety boots', 'High-vis vest'],
    safetyRequirements: ['Fall protection system', 'Secure anchor points', 'Safety barriers'],
    hazards: ['Falls from height', 'Falling objects', 'Weather conditions', 'Structural collapse'],
    controlMeasures: ['Safety harnesses', 'Guardrails', 'Safety nets', 'Weather monitoring'],
    emergencyProcedures: ['Rescue procedures', 'Emergency descent', 'First aid', 'Emergency services'],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'permit-4',
    permitNumber: 'CSP-2024-003',
    workActivity: 'Confined Space Entry',
    description: 'Inspection and maintenance of underground water tank',
    location: 'Underground Water Tank - Block A',
    siteId: 'site-1',
    requestedBy: 'engineer-1',
    assignedWorkers: ['worker-1', 'worker-2'],
    plannedStartDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
    plannedEndDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000),
    estimatedDuration: 4,
    riskLevel: 'critical',
    priority: 'high',
    status: 'rejected',
    submittedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    reviewedBy: 'safety-officer-1',
    rejectionReason: 'Atmospheric testing equipment not available. Resubmit when equipment is ready.',
    attachments: ['confined-space-plan.pdf'],
    requiredPPE: ['Full body harness', 'Gas monitor', 'Emergency breathing apparatus'],
    safetyRequirements: ['Atmospheric testing', 'Continuous monitoring', 'Standby person'],
    hazards: ['Toxic gases', 'Oxygen deficiency', 'Engulfment', 'Falls'],
    controlMeasures: ['Gas monitoring', 'Ventilation', 'Standby rescue', 'Communication system'],
    emergencyProcedures: ['Emergency extraction', 'Gas monitoring', 'Emergency services', 'First aid'],
    createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
  }
];

export const mockWorkActivityTemplates: WorkActivityTemplate[] = [
  {
    id: 'activity-1',
    name: 'Hot Work - Welding',
    description: 'Welding operations requiring hot work permit',
    category: 'Hot Work',
    riskLevel: 'high',
    estimatedDuration: 4,
    requiredPermitTypes: ['Hot Work Permit'],
    requiredPPE: ['Welding helmet', 'Fire-resistant clothing', 'Safety boots', 'Gloves'],
    standardHazards: ['Fire risk', 'Hot surfaces', 'Fumes', 'Arc flash'],
    standardControlMeasures: ['Fire watch', 'Ventilation', 'PPE', 'Fire extinguisher'],
    safetyRequirements: ['Fire watch', 'Fire extinguisher nearby', 'Clear combustibles'],
    isActive: true
  },
  {
    id: 'activity-2',
    name: 'Electrical Installation',
    description: 'Electrical wiring and equipment installation',
    category: 'Electrical',
    riskLevel: 'high',
    estimatedDuration: 6,
    requiredPermitTypes: ['Electrical Work Permit'],
    requiredPPE: ['Insulated gloves', 'Arc flash suit', 'Safety boots', 'Hard hat'],
    standardHazards: ['Electrical shock', 'Arc flash', 'Burns', 'Falls'],
    standardControlMeasures: ['LOTO procedures', 'Voltage testing', 'Insulated tools', 'PPE'],
    safetyRequirements: ['Lockout/Tagout', 'Voltage testing', 'Isolation verification'],
    isActive: true
  },
  {
    id: 'activity-3',
    name: 'Working at Height',
    description: 'Work activities above 2 meters requiring height permits',
    category: 'Height Work',
    riskLevel: 'high',
    estimatedDuration: 8,
    requiredPermitTypes: ['Working at Height Permit'],
    requiredPPE: ['Safety harness', 'Hard hat', 'Safety boots', 'High-vis vest'],
    standardHazards: ['Falls from height', 'Falling objects', 'Weather conditions'],
    standardControlMeasures: ['Safety harnesses', 'Guardrails', 'Safety nets', 'Weather monitoring'],
    safetyRequirements: ['Fall protection system', 'Secure anchor points', 'Safety barriers'],
    isActive: true
  },
  {
    id: 'activity-4',
    name: 'Confined Space Entry',
    description: 'Entry into confined spaces requiring special permits',
    category: 'Confined Space',
    riskLevel: 'critical',
    estimatedDuration: 6,
    requiredPermitTypes: ['Confined Space Entry Permit'],
    requiredPPE: ['Full body harness', 'Gas monitor', 'Emergency breathing apparatus'],
    standardHazards: ['Toxic gases', 'Oxygen deficiency', 'Engulfment', 'Falls'],
    standardControlMeasures: ['Gas monitoring', 'Ventilation', 'Standby rescue', 'Communication'],
    safetyRequirements: ['Atmospheric testing', 'Continuous monitoring', 'Standby person'],
    isActive: true
  }
];
