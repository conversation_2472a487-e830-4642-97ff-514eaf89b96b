import React, { useState } from 'react';
import { Plus, Calendar, Clock, Users, MapPin, CheckSquare, AlertTriangle } from 'lucide-react';
import MobileLayout from '../components/layout/MobileLayout';
import Card from '../components/common/Card';
import Button from '../components/common/Button';
import StatusBadge from '../components/common/StatusBadge';

// Local type definitions to avoid module resolution issues
type TaskStatus = 'draft' | 'submitted' | 'approved' | 'in-progress' | 'completed' | 'blocked';
type Priority = 'low' | 'medium' | 'high' | 'critical';
type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

interface EngineerTask {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  siteId: string;
  createdBy: string;
  assignedWorkers: string[];
  plannedDate: Date;
  estimatedHours: number;
  priority: Priority;
  status: TaskStatus;
  requiresPermit: boolean;
  permitId?: string;
  progressPercentage: number;
  actualStartTime?: Date;
  actualEndTime?: Date;
  actualHours?: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  estimatedHours: number;
  requiredWorkers: number;
  requiredSkills: string[];
  requiresPermit: boolean;
  riskLevel: RiskLevel;
  safetyRequirements: string[];
  isActive: boolean;
}

interface Worker {
  id: string;
  name: string;
  trade: string;
}

// Mock data
const mockTasks: EngineerTask[] = [
  {
    id: 'task-1',
    title: 'Concrete Foundation Pour - Block A',
    description: 'Pour concrete for foundation of Block A residential building',
    category: 'Construction',
    location: 'Block A Foundation',
    siteId: 'site-1',
    createdBy: 'engineer-1',
    assignedWorkers: ['worker-1', 'worker-3', 'worker-4'],
    plannedDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
    estimatedHours: 8,
    priority: 'high',
    status: 'approved',
    requiresPermit: false,
    progressPercentage: 0,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
  },
  {
    id: 'task-2',
    title: 'Electrical Panel Installation',
    description: 'Install main electrical distribution panel for Block B',
    category: 'Electrical',
    location: 'Block B Electrical Room',
    siteId: 'site-1',
    createdBy: 'engineer-1',
    assignedWorkers: ['worker-2'],
    plannedDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
    estimatedHours: 6,
    priority: 'medium',
    status: 'submitted',
    requiresPermit: true,
    progressPercentage: 0,
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'task-3',
    title: 'Structural Steel Welding',
    description: 'Weld structural steel beams for second floor framework',
    category: 'Welding',
    location: 'Block A Second Floor',
    siteId: 'site-1',
    createdBy: 'engineer-1',
    assignedWorkers: ['worker-1'],
    plannedDate: new Date(),
    estimatedHours: 4,
    priority: 'critical',
    status: 'in-progress',
    requiresPermit: true,
    permitId: 'permit-1',
    progressPercentage: 65,
    actualStartTime: new Date(Date.now() - 3 * 60 * 60 * 1000),
    notes: 'Weather conditions good, proceeding as planned',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 30 * 60 * 1000)
  }
];

const mockTaskTemplates: TaskTemplate[] = [
  {
    id: 'template-1',
    name: 'Concrete Pour',
    description: 'Standard concrete pouring operation',
    category: 'Construction',
    estimatedHours: 8,
    requiredWorkers: 4,
    requiredSkills: ['Concrete work', 'Heavy machinery operation'],
    requiresPermit: false,
    riskLevel: 'medium',
    safetyRequirements: ['Hard hat', 'Safety boots', 'High-vis vest'],
    isActive: true
  },
  {
    id: 'template-2',
    name: 'Electrical Installation',
    description: 'Electrical wiring and equipment installation',
    category: 'Electrical',
    estimatedHours: 6,
    requiredWorkers: 2,
    requiredSkills: ['Electrical certification', 'LOTO procedures'],
    requiresPermit: true,
    riskLevel: 'high',
    safetyRequirements: ['Insulated gloves', 'Arc flash suit', 'Voltage tester'],
    isActive: true
  }
];

const mockWorkers: Worker[] = [
  { id: 'worker-1', name: 'David Kamau', trade: 'Welder' },
  { id: 'worker-2', name: 'Mary Wanjiku', trade: 'Electrician' },
  { id: 'worker-3', name: 'Peter Ochieng', trade: 'Carpenter' },
  { id: 'worker-4', name: 'Grace Muthoni', trade: 'Mason' }
];

interface TaskPlanningProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const TaskPlanning: React.FC<TaskPlanningProps> = ({ activeTab, onTabChange }) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<TaskTemplate | null>(null);
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [taskLocation, setTaskLocation] = useState('');
  const [plannedDate, setPlannedDate] = useState(new Date().toISOString().split('T')[0]);
  const [estimatedHours, setEstimatedHours] = useState('');
  const [selectedWorkers, setSelectedWorkers] = useState<string[]>([]);
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'critical'>('medium');

  const todaysTasks = mockTasks.filter(task => {
    const today = new Date();
    const taskDate = new Date(task.plannedDate);
    return taskDate.toDateString() === today.toDateString();
  });

  const tomorrowsTasks = mockTasks.filter(task => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const taskDate = new Date(task.plannedDate);
    return taskDate.toDateString() === tomorrow.toDateString();
  });

  const handleTemplateSelect = (template: TaskTemplate) => {
    setSelectedTemplate(template);
    setTaskTitle(template.name);
    setTaskDescription(template.description);
    setEstimatedHours(template.estimatedHours.toString());
  };

  const handleWorkerToggle = (workerId: string) => {
    setSelectedWorkers(prev =>
      prev.includes(workerId)
        ? prev.filter(id => id !== workerId)
        : [...prev, workerId]
    );
  };

  const handleCreateTask = () => {
    if (!taskTitle || !taskLocation || !estimatedHours) return;

    const newTask: Partial<EngineerTask> = {
      title: taskTitle,
      description: taskDescription,
      location: taskLocation,
      plannedDate: new Date(plannedDate),
      estimatedHours: parseFloat(estimatedHours),
      assignedWorkers: selectedWorkers,
      priority,
      status: 'draft',
      category: selectedTemplate?.category || 'Other',
      requiresPermit: selectedTemplate?.requiresPermit || false
    };

    console.log('Creating task:', newTask);

    // Reset form
    setShowCreateModal(false);
    setSelectedTemplate(null);
    setTaskTitle('');
    setTaskDescription('');
    setTaskLocation('');
    setEstimatedHours('');
    setSelectedWorkers([]);
    setPriority('medium');
  };

  const updateTaskProgress = (taskId: string, newStatus: string) => {
    console.log('Updating task progress:', { taskId, newStatus });
  };

  return (
    <MobileLayout
      title="Task Planning"
      activeTab={activeTab}
      onTabChange={onTabChange}
      headerActions={
        <Button
          variant="primary"
          size="sm"
          onClick={() => setShowCreateModal(true)}
        >
          <Plus className="h-4 w-4" />
        </Button>
      }
    >
      <div className="p-4 space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4">
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-blue-600">{todaysTasks.length}</p>
              <p className="text-sm text-gray-500">Today</p>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-green-600">{tomorrowsTasks.length}</p>
              <p className="text-sm text-gray-500">Tomorrow</p>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-yellow-600">
                {mockTasks.filter(t => t.status === 'submitted').length}
              </p>
              <p className="text-sm text-gray-500">Pending</p>
            </div>
          </Card>
        </div>

        {/* Today's Tasks */}
        <Card title="Today's Tasks" padding="md">
          {todaysTasks.length > 0 ? (
            <div className="space-y-3">
              {todaysTasks.map((task) => (
                <div key={task.id} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{task.title}</h4>
                      <p className="text-sm text-gray-500">{task.location}</p>
                    </div>
                    <StatusBadge status={task.status} size="sm" type="task" />
                  </div>
                  
                  <div className="flex items-center text-xs text-gray-500 space-x-4">
                    <div className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>{task.estimatedHours}h</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      <span>{task.assignedWorkers.length} workers</span>
                    </div>
                    {task.requiresPermit && (
                      <div className="flex items-center text-yellow-600">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        <span>Permit required</span>
                      </div>
                    )}
                  </div>

                  {task.status === 'in-progress' && (
                    <div className="mt-3 flex space-x-2">
                      <Button
                        variant="success"
                        size="sm"
                        onClick={() => updateTaskProgress(task.id, 'completed')}
                      >
                        Mark Complete
                      </Button>
                      <Button
                        variant="warning"
                        size="sm"
                        onClick={() => updateTaskProgress(task.id, 'blocked')}
                      >
                        Report Issue
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No tasks scheduled for today</p>
            </div>
          )}
        </Card>

        {/* Tomorrow's Tasks */}
        <Card title="Tomorrow's Tasks" padding="md">
          {tomorrowsTasks.length > 0 ? (
            <div className="space-y-3">
              {tomorrowsTasks.map((task) => (
                <div key={task.id} className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{task.title}</h4>
                      <p className="text-sm text-gray-500">{task.location}</p>
                    </div>
                    <StatusBadge status={task.status} size="sm" type="task" />
                  </div>
                  
                  <div className="flex items-center text-xs text-gray-500 space-x-4">
                    <div className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>{task.estimatedHours}h</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      <span>{task.assignedWorkers.length} workers</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No tasks planned for tomorrow</p>
              <Button
                variant="primary"
                size="sm"
                className="mt-3"
                onClick={() => setShowCreateModal(true)}
              >
                Plan Tomorrow's Work
              </Button>
            </div>
          )}
        </Card>

        {/* Quick Actions */}
        <Card title="Quick Actions" padding="md">
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="primary"
              fullWidth
              onClick={() => setShowCreateModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Task
            </Button>
            <Button
              variant="secondary"
              fullWidth
              onClick={() => onTabChange('permits')}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Request Permit
            </Button>
          </div>
        </Card>
      </div>

      {/* Create Task Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
          <div className="bg-white rounded-t-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Create New Task</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-4 space-y-4">
              {/* Template Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Task Template (Optional)
                </label>
                <select
                  value={selectedTemplate?.id || ''}
                  onChange={(e) => {
                    const template = mockTaskTemplates.find(t => t.id === e.target.value);
                    if (template) handleTemplateSelect(template);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Choose a template...</option>
                  {mockTaskTemplates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Task Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Task Title *
                </label>
                <input
                  type="text"
                  value={taskTitle}
                  onChange={(e) => setTaskTitle(e.target.value)}
                  placeholder="Enter task title..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={taskDescription}
                  onChange={(e) => setTaskDescription(e.target.value)}
                  placeholder="Describe the task..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location *
                </label>
                <input
                  type="text"
                  value={taskLocation}
                  onChange={(e) => setTaskLocation(e.target.value)}
                  placeholder="Work location..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Date and Hours */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Planned Date *
                  </label>
                  <input
                    type="date"
                    value={plannedDate}
                    onChange={(e) => setPlannedDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Est. Hours *
                  </label>
                  <input
                    type="number"
                    step="0.5"
                    value={estimatedHours}
                    onChange={(e) => setEstimatedHours(e.target.value)}
                    placeholder="8"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Priority */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority
                </label>
                <select
                  value={priority}
                  onChange={(e) => setPriority(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>

              {/* Worker Assignment */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assign Workers
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2">
                  {mockWorkers.map((worker) => (
                    <label key={worker.id} className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedWorkers.includes(worker.id)}
                        onChange={() => handleWorkerToggle(worker.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="text-sm text-gray-900">{worker.name} - {worker.trade}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-3 pt-4">
                <Button
                  variant="secondary"
                  fullWidth
                  onClick={() => setShowCreateModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  fullWidth
                  onClick={handleCreateTask}
                  disabled={!taskTitle || !taskLocation || !estimatedHours}
                >
                  Create Task
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </MobileLayout>
  );
};

export default TaskPlanning;
