import React, { useState } from 'react';
import { Users, Clock, Phone, Plus, Search, Filter } from 'lucide-react';
import MobileLayout from '../components/layout/MobileLayout';
import Card from '../components/common/Card';
import Button from '../components/common/Button';
import StatusBadge from '../components/common/StatusBadge';

// Local type definitions to avoid module resolution issues
interface Certification {
  id: string;
  name: string;
  issuedBy: string;
  issuedDate: Date;
  expiryDate: Date;
  certificateNumber: string;
  isValid: boolean;
}

interface Training {
  id: string;
  name: string;
  completedDate: Date;
  expiryDate?: Date;
  provider: string;
  isValid: boolean;
}

interface Worker {
  id: string;
  name: string;
  employeeId: string;
  trade: string;
  photo?: string;
  phone: string;
  email?: string;
  siteId: string;
  supervisorId: string;
  isActive: boolean;
  isOnSite: boolean;
  lastCheckIn?: Date;
  certifications: Certification[];
  trainings: Training[];
  hoursWorked: number;
  overtimeHours: number;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

interface OvertimeRequest {
  id: string;
  workerId: string;
  workerName: string;
  date: Date;
  hours: number;
  reason: string;
  description?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  submittedBy: string;
  submittedAt: Date;
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
}

// Mock data
const mockWorkers: Worker[] = [
  {
    id: 'worker-1',
    name: 'David Kamau',
    employeeId: 'EMP001',
    trade: 'Welder',
    photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    phone: '+254712345678',
    email: '<EMAIL>',
    siteId: 'site-1',
    supervisorId: 'engineer-1',
    isActive: true,
    isOnSite: true,
    lastCheckIn: new Date(Date.now() - 2 * 60 * 60 * 1000),
    certifications: [],
    trainings: [],
    hoursWorked: 8,
    overtimeHours: 2,
    emergencyContact: {
      name: 'Mary Kamau',
      phone: '+254712345679',
      relationship: 'Spouse'
    }
  },
  {
    id: 'worker-2',
    name: 'Mary Wanjiku',
    employeeId: 'EMP002',
    trade: 'Electrician',
    photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    phone: '+254723456789',
    email: '<EMAIL>',
    siteId: 'site-1',
    supervisorId: 'engineer-1',
    isActive: true,
    isOnSite: true,
    lastCheckIn: new Date(Date.now() - 1 * 60 * 60 * 1000),
    certifications: [],
    trainings: [],
    hoursWorked: 8,
    overtimeHours: 0,
    emergencyContact: {
      name: 'John Wanjiku',
      phone: '+254723456790',
      relationship: 'Brother'
    }
  },
  {
    id: 'worker-3',
    name: 'Peter Ochieng',
    employeeId: 'EMP003',
    trade: 'Carpenter',
    photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    phone: '+254734567890',
    siteId: 'site-1',
    supervisorId: 'engineer-1',
    isActive: true,
    isOnSite: false,
    lastCheckIn: new Date(Date.now() - 18 * 60 * 60 * 1000),
    certifications: [],
    trainings: [],
    hoursWorked: 7.5,
    overtimeHours: 1,
    emergencyContact: {
      name: 'Grace Ochieng',
      phone: '+254734567891',
      relationship: 'Wife'
    }
  },
  {
    id: 'worker-4',
    name: 'Grace Muthoni',
    employeeId: 'EMP004',
    trade: 'Mason',
    photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    phone: '+254745678901',
    siteId: 'site-1',
    supervisorId: 'engineer-1',
    isActive: true,
    isOnSite: true,
    lastCheckIn: new Date(Date.now() - 30 * 60 * 1000),
    certifications: [],
    trainings: [],
    hoursWorked: 8,
    overtimeHours: 0,
    emergencyContact: {
      name: 'James Muthoni',
      phone: '+254745678902',
      relationship: 'Husband'
    }
  }
];

const mockOvertimeRequests: OvertimeRequest[] = [
  {
    id: 'ot-1',
    workerId: 'worker-1',
    workerName: 'David Kamau',
    date: new Date(),
    hours: 2,
    reason: 'Critical welding work completion',
    description: 'Need to complete structural welding before concrete pour tomorrow',
    approvalStatus: 'pending',
    submittedBy: 'engineer-1',
    submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 'ot-2',
    workerId: 'worker-3',
    workerName: 'Peter Ochieng',
    date: new Date(Date.now() - 24 * 60 * 60 * 1000),
    hours: 1,
    reason: 'Formwork completion',
    approvalStatus: 'approved',
    submittedBy: 'engineer-1',
    submittedAt: new Date(Date.now() - 26 * 60 * 60 * 1000),
    reviewedBy: 'manager-1',
    reviewedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    reviewNotes: 'Approved for critical path activity'
  }
];

interface MyTeamProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const MyTeam: React.FC<MyTeamProps> = ({ activeTab, onTabChange }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showOvertimeModal, setShowOvertimeModal] = useState(false);
  const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);
  const [overtimeHours, setOvertimeHours] = useState('');
  const [overtimeReason, setOvertimeReason] = useState('');

  const filteredWorkers = mockWorkers.filter(worker =>
    worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.trade.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const workersOnSite = mockWorkers.filter(w => w.isOnSite).length;
  const totalHoursToday = mockWorkers.reduce((sum, w) => sum + w.hoursWorked, 0);
  const pendingOvertimeRequests = mockOvertimeRequests.filter(r => r.approvalStatus === 'pending').length;

  const handleOvertimeSubmit = () => {
    if (!selectedWorker || !overtimeHours || !overtimeReason) return;

    // In a real app, this would call the API
    console.log('Submitting overtime request:', {
      workerId: selectedWorker.id,
      hours: parseFloat(overtimeHours),
      reason: overtimeReason,
      date: new Date()
    });

    // Reset form and close modal
    setShowOvertimeModal(false);
    setSelectedWorker(null);
    setOvertimeHours('');
    setOvertimeReason('');
  };

  const getWorkerStatusColor = (worker: Worker) => {
    if (!worker.isOnSite) return 'text-gray-500';
    const hoursAgo = (Date.now() - (worker.lastCheckIn?.getTime() || 0)) / (1000 * 60 * 60);
    if (hoursAgo < 1) return 'text-green-600';
    if (hoursAgo < 4) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <MobileLayout
      title="My Team"
      activeTab={activeTab}
      onTabChange={onTabChange}
      notificationCount={pendingOvertimeRequests}
    >
      <div className="p-4 space-y-6">
        {/* Team Summary */}
        <div className="grid grid-cols-3 gap-4">
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-green-600">{workersOnSite}</p>
              <p className="text-sm text-gray-500">On Site</p>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-blue-600">{totalHoursToday}</p>
              <p className="text-sm text-gray-500">Hours Today</p>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-yellow-600">{pendingOvertimeRequests}</p>
              <p className="text-sm text-gray-500">Pending OT</p>
            </div>
          </Card>
        </div>

        {/* Search and Actions */}
        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search workers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <Button
            variant="primary"
            size="md"
            onClick={() => setShowOvertimeModal(true)}
            className="px-4"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Workers List */}
        <div className="space-y-3">
          {filteredWorkers.map((worker) => (
            <Card key={worker.id} padding="md" className="relative">
              <div className="flex items-center space-x-4">
                {/* Worker Photo */}
                <div className="relative">
                  {worker.photo ? (
                    <img
                      src={worker.photo}
                      alt={worker.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
                      <Users className="h-6 w-6 text-gray-600" />
                    </div>
                  )}
                  {/* Status indicator */}
                  <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                    worker.isOnSite ? 'bg-green-500' : 'bg-gray-400'
                  }`}></div>
                </div>

                {/* Worker Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900 truncate">{worker.name}</h3>
                    <StatusBadge 
                      status={worker.isOnSite ? 'on-site' : 'off-site'} 
                      size="sm" 
                      type="worker" 
                    />
                  </div>
                  <p className="text-sm text-gray-500">{worker.trade} • {worker.employeeId}</p>
                  
                  {/* Work Hours */}
                  <div className="flex items-center mt-2 space-x-4">
                    <div className="flex items-center text-xs text-gray-500">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>{worker.hoursWorked}h worked</span>
                    </div>
                    {worker.overtimeHours > 0 && (
                      <div className="flex items-center text-xs text-yellow-600">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{worker.overtimeHours}h OT</span>
                      </div>
                    )}
                  </div>

                  {/* Last Seen */}
                  {worker.lastCheckIn && (
                    <p className={`text-xs mt-1 ${getWorkerStatusColor(worker)}`}>
                      Last seen: {worker.lastCheckIn.toLocaleTimeString()}
                    </p>
                  )}
                </div>

                {/* Actions */}
                <div className="flex flex-col space-y-2">
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                    <Phone className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => {
                      setSelectedWorker(worker);
                      setShowOvertimeModal(true);
                    }}
                    className="p-2 text-blue-400 hover:text-blue-600 rounded-lg hover:bg-blue-50"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Recent Overtime Requests */}
        <Card title="Recent Overtime Requests" padding="md">
          <div className="space-y-3">
            {mockOvertimeRequests.slice(0, 3).map((request) => (
              <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-gray-900">{request.workerName}</p>
                  <p className="text-sm text-gray-500">{request.hours}h • {request.reason}</p>
                  <p className="text-xs text-gray-400">
                    {request.submittedAt.toLocaleDateString()}
                  </p>
                </div>
                <StatusBadge status={request.approvalStatus} size="sm" />
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Overtime Request Modal */}
      {showOvertimeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
          <div className="bg-white rounded-t-lg w-full max-w-md p-6 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Submit Overtime Request</h3>
              <button
                onClick={() => setShowOvertimeModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            {/* Worker Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Worker
              </label>
              <select
                value={selectedWorker?.id || ''}
                onChange={(e) => {
                  const worker = mockWorkers.find(w => w.id === e.target.value);
                  setSelectedWorker(worker || null);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Choose a worker...</option>
                {mockWorkers.map((worker) => (
                  <option key={worker.id} value={worker.id}>
                    {worker.name} - {worker.trade}
                  </option>
                ))}
              </select>
            </div>

            {/* Hours */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Overtime Hours
              </label>
              <input
                type="number"
                step="0.5"
                min="0.5"
                max="8"
                value={overtimeHours}
                onChange={(e) => setOvertimeHours(e.target.value)}
                placeholder="e.g., 2.5"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for Overtime
              </label>
              <textarea
                value={overtimeReason}
                onChange={(e) => setOvertimeReason(e.target.value)}
                placeholder="Describe why overtime is needed..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Actions */}
            <div className="flex space-x-3 pt-4">
              <Button
                variant="secondary"
                fullWidth
                onClick={() => setShowOvertimeModal(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                fullWidth
                onClick={handleOvertimeSubmit}
                disabled={!selectedWorker || !overtimeHours || !overtimeReason}
              >
                Submit Request
              </Button>
            </div>
          </div>
        </div>
      )}
    </MobileLayout>
  );
};

export default MyTeam;
