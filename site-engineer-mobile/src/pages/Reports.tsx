import React, { useState } from 'react';
import { FileText, Plus, Camera, AlertTriangle, CheckCircle, Clock, TrendingUp } from 'lucide-react';
import MobileLayout from '../components/layout/MobileLayout';
import Card from '../components/common/Card';
import Button from '../components/common/Button';
import StatusBadge from '../components/common/StatusBadge';

interface ReportsProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

interface DailyReport {
  id: string;
  date: Date;
  completedTasks: string[];
  issuesEncountered: string[];
  tomorrowsPlans: string;
  weatherConditions: string;
  workersPresent: number;
  hoursWorked: number;
  photos: string[];
  submittedAt: Date;
}

interface IssueReport {
  id: string;
  type: 'delay' | 'safety' | 'quality' | 'equipment' | 'weather' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  location: string;
  reportedAt: Date;
  status: 'open' | 'investigating' | 'resolved';
  photos: string[];
}

const mockDailyReports: DailyReport[] = [
  {
    id: 'report-1',
    date: new Date(),
    completedTasks: ['Concrete Foundation Pour - Block A', 'Formwork Installation'],
    issuesEncountered: ['Weather delay in morning'],
    tomorrowsPlans: 'Continue with structural steel work and electrical installation',
    weatherConditions: 'Partly cloudy, 24°C',
    workersPresent: 8,
    hoursWorked: 64,
    photos: [],
    submittedAt: new Date()
  },
  {
    id: 'report-2',
    date: new Date(Date.now() - 24 * 60 * 60 * 1000),
    completedTasks: ['Structural Steel Welding', 'Safety Inspection'],
    issuesEncountered: [],
    tomorrowsPlans: 'Concrete pour for Block A foundation',
    weatherConditions: 'Clear, 26°C',
    workersPresent: 6,
    hoursWorked: 48,
    photos: [],
    submittedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
  }
];

const mockIssueReports: IssueReport[] = [
  {
    id: 'issue-1',
    type: 'equipment',
    severity: 'high',
    title: 'Crane Malfunction',
    description: 'Main tower crane experiencing hydraulic issues, affecting lifting operations',
    location: 'Block A Construction Site',
    reportedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'investigating',
    photos: []
  },
  {
    id: 'issue-2',
    type: 'safety',
    severity: 'medium',
    title: 'Scaffolding Inspection Required',
    description: 'Scaffolding on Block B requires immediate inspection due to loose connections',
    location: 'Block B - Level 3',
    reportedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
    status: 'resolved',
    photos: []
  }
];

const Reports: React.FC<ReportsProps> = ({ activeTab, onTabChange }) => {
  const [showDailyReportModal, setShowDailyReportModal] = useState(false);
  const [showIssueReportModal, setShowIssueReportModal] = useState(false);
  const [reportType, setReportType] = useState<'daily' | 'issue'>('daily');

  // Daily Report Form State
  const [completedTasks, setCompletedTasks] = useState('');
  const [issuesEncountered, setIssuesEncountered] = useState('');
  const [tomorrowsPlans, setTomorrowsPlans] = useState('');
  const [weatherConditions, setWeatherConditions] = useState('');
  const [workersPresent, setWorkersPresent] = useState('');
  const [hoursWorked, setHoursWorked] = useState('');

  // Issue Report Form State
  const [issueType, setIssueType] = useState<'delay' | 'safety' | 'quality' | 'equipment' | 'weather' | 'other'>('safety');
  const [issueSeverity, setIssueSeverity] = useState<'low' | 'medium' | 'high' | 'critical'>('medium');
  const [issueTitle, setIssueTitle] = useState('');
  const [issueDescription, setIssueDescription] = useState('');
  const [issueLocation, setIssueLocation] = useState('');

  const handleSubmitDailyReport = () => {
    const newReport: Partial<DailyReport> = {
      date: new Date(),
      completedTasks: completedTasks.split('\n').filter(task => task.trim()),
      issuesEncountered: issuesEncountered.split('\n').filter(issue => issue.trim()),
      tomorrowsPlans,
      weatherConditions,
      workersPresent: parseInt(workersPresent),
      hoursWorked: parseInt(hoursWorked),
      photos: [],
      submittedAt: new Date()
    };

    console.log('Submitting daily report:', newReport);

    // Reset form
    setShowDailyReportModal(false);
    setCompletedTasks('');
    setIssuesEncountered('');
    setTomorrowsPlans('');
    setWeatherConditions('');
    setWorkersPresent('');
    setHoursWorked('');
  };

  const handleSubmitIssueReport = () => {
    const newIssue: Partial<IssueReport> = {
      type: issueType,
      severity: issueSeverity,
      title: issueTitle,
      description: issueDescription,
      location: issueLocation,
      reportedAt: new Date(),
      status: 'open',
      photos: []
    };

    console.log('Submitting issue report:', newIssue);

    // Reset form
    setShowIssueReportModal(false);
    setIssueTitle('');
    setIssueDescription('');
    setIssueLocation('');
  };

  const getIssueTypeIcon = (type: string) => {
    switch (type) {
      case 'safety': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'equipment': return <TrendingUp className="h-4 w-4 text-blue-600" />;
      case 'delay': return <Clock className="h-4 w-4 text-yellow-600" />;
      default: return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <MobileLayout
      title="Reports"
      activeTab={activeTab}
      onTabChange={onTabChange}
      headerActions={
        <Button
          variant="primary"
          size="sm"
          onClick={() => setShowDailyReportModal(true)}
        >
          <Plus className="h-4 w-4" />
        </Button>
      }
    >
      <div className="p-4 space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4">
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-blue-600">{mockDailyReports.length}</p>
              <p className="text-sm text-gray-500">Daily Reports</p>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-red-600">
                {mockIssueReports.filter(i => i.status === 'open').length}
              </p>
              <p className="text-sm text-gray-500">Open Issues</p>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-green-600">
                {mockIssueReports.filter(i => i.status === 'resolved').length}
              </p>
              <p className="text-sm text-gray-500">Resolved</p>
            </div>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card title="Quick Actions" padding="md">
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="primary"
              fullWidth
              onClick={() => setShowDailyReportModal(true)}
            >
              <FileText className="h-4 w-4 mr-2" />
              Daily Report
            </Button>
            <Button
              variant="warning"
              fullWidth
              onClick={() => setShowIssueReportModal(true)}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Report Issue
            </Button>
          </div>
        </Card>

        {/* Recent Daily Reports */}
        <Card title="Recent Daily Reports" padding="md">
          <div className="space-y-3">
            {mockDailyReports.map((report) => (
              <div key={report.id} className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">
                      Daily Report - {report.date.toLocaleDateString()}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {report.workersPresent} workers • {report.hoursWorked} total hours
                    </p>
                  </div>
                  <StatusBadge status="completed" size="sm" />
                </div>
                
                <div className="text-xs text-gray-500">
                  <p className="mb-1">
                    <strong>Completed:</strong> {report.completedTasks.length} tasks
                  </p>
                  <p className="mb-1">
                    <strong>Weather:</strong> {report.weatherConditions}
                  </p>
                  <p>
                    <strong>Submitted:</strong> {report.submittedAt.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Recent Issues */}
        <Card title="Recent Issues" padding="md">
          <div className="space-y-3">
            {mockIssueReports.map((issue) => (
              <div key={issue.id} className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      {getIssueTypeIcon(issue.type)}
                      <h4 className="font-medium text-gray-900">{issue.title}</h4>
                    </div>
                    <p className="text-sm text-gray-600">{issue.location}</p>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    <StatusBadge status={issue.status} size="sm" />
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(issue.severity)}`}>
                      {issue.severity.toUpperCase()}
                    </span>
                  </div>
                </div>
                
                <p className="text-xs text-gray-500 mb-2">{issue.description}</p>
                <p className="text-xs text-gray-400">
                  Reported {issue.reportedAt.toLocaleString()}
                </p>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Daily Report Modal */}
      {showDailyReportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
          <div className="bg-white rounded-t-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Daily Progress Report</h3>
                <button
                  onClick={() => setShowDailyReportModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-4 space-y-4">
              {/* Completed Tasks */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Completed Tasks *
                </label>
                <textarea
                  value={completedTasks}
                  onChange={(e) => setCompletedTasks(e.target.value)}
                  placeholder="List completed tasks (one per line)..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Issues Encountered */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Issues Encountered
                </label>
                <textarea
                  value={issuesEncountered}
                  onChange={(e) => setIssuesEncountered(e.target.value)}
                  placeholder="Any issues or delays (one per line)..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Tomorrow's Plans */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tomorrow's Plans *
                </label>
                <textarea
                  value={tomorrowsPlans}
                  onChange={(e) => setTomorrowsPlans(e.target.value)}
                  placeholder="Planned activities for tomorrow..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Weather Conditions */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Weather Conditions
                </label>
                <input
                  type="text"
                  value={weatherConditions}
                  onChange={(e) => setWeatherConditions(e.target.value)}
                  placeholder="e.g., Clear, 25°C"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Workers and Hours */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Workers Present *
                  </label>
                  <input
                    type="number"
                    value={workersPresent}
                    onChange={(e) => setWorkersPresent(e.target.value)}
                    placeholder="8"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Total Hours *
                  </label>
                  <input
                    type="number"
                    value={hoursWorked}
                    onChange={(e) => setHoursWorked(e.target.value)}
                    placeholder="64"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Photo Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Progress Photos
                </label>
                <button className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-gray-400 hover:text-gray-600">
                  <Camera className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-sm">Tap to add photos</span>
                </button>
              </div>

              {/* Actions */}
              <div className="flex space-x-3 pt-4">
                <Button
                  variant="secondary"
                  fullWidth
                  onClick={() => setShowDailyReportModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  fullWidth
                  onClick={handleSubmitDailyReport}
                  disabled={!completedTasks || !tomorrowsPlans || !workersPresent || !hoursWorked}
                >
                  Submit Report
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Issue Report Modal */}
      {showIssueReportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
          <div className="bg-white rounded-t-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Report Issue</h3>
                <button
                  onClick={() => setShowIssueReportModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-4 space-y-4">
              {/* Issue Type and Severity */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Issue Type *
                  </label>
                  <select
                    value={issueType}
                    onChange={(e) => setIssueType(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="safety">Safety</option>
                    <option value="equipment">Equipment</option>
                    <option value="delay">Delay</option>
                    <option value="quality">Quality</option>
                    <option value="weather">Weather</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Severity *
                  </label>
                  <select
                    value={issueSeverity}
                    onChange={(e) => setIssueSeverity(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
              </div>

              {/* Issue Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Issue Title *
                </label>
                <input
                  type="text"
                  value={issueTitle}
                  onChange={(e) => setIssueTitle(e.target.value)}
                  placeholder="Brief description of the issue..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Issue Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Detailed Description *
                </label>
                <textarea
                  value={issueDescription}
                  onChange={(e) => setIssueDescription(e.target.value)}
                  placeholder="Provide detailed information about the issue..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location *
                </label>
                <input
                  type="text"
                  value={issueLocation}
                  onChange={(e) => setIssueLocation(e.target.value)}
                  placeholder="Specific location of the issue..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Photo Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Issue Photos
                </label>
                <button className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-gray-400 hover:text-gray-600">
                  <Camera className="h-6 w-6 mx-auto mb-2" />
                  <span className="text-sm">Tap to add photos</span>
                </button>
              </div>

              {/* Actions */}
              <div className="flex space-x-3 pt-4">
                <Button
                  variant="secondary"
                  fullWidth
                  onClick={() => setShowIssueReportModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="warning"
                  fullWidth
                  onClick={handleSubmitIssueReport}
                  disabled={!issueTitle || !issueDescription || !issueLocation}
                >
                  Report Issue
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </MobileLayout>
  );
};

export default Reports;
