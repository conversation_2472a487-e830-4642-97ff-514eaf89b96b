import React from 'react';

interface CardProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
  clickable?: boolean;
  onClick?: () => void;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

const Card: React.FC<CardProps> = ({
  title,
  subtitle,
  children,
  actions,
  className = '',
  clickable = false,
  onClick,
  padding = 'md'
}) => {
  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  };

  const baseClasses = `bg-white rounded-lg border border-gray-200 shadow-sm ${paddingClasses[padding]}`;
  const clickableClasses = clickable ? 'cursor-pointer hover:shadow-md transition-shadow' : '';
  const classes = `${baseClasses} ${clickableClasses} ${className}`;

  const CardContent = () => (
    <>
      {(title || subtitle || actions) && (
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            {title && (
              <h3 className="text-lg font-medium text-gray-900 truncate">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-500 mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {actions && (
            <div className="flex-shrink-0 ml-4">
              {actions}
            </div>
          )}
        </div>
      )}
      {children}
    </>
  );

  if (clickable && onClick) {
    return (
      <button
        className={`${classes} text-left w-full`}
        onClick={onClick}
      >
        <CardContent />
      </button>
    );
  }

  return (
    <div className={classes}>
      <CardContent />
    </div>
  );
};

export default Card;
