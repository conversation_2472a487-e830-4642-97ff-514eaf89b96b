import React from 'react';
import { CheckCircle, Clock, AlertTriangle, XCircle, FileText, Shield } from 'lucide-react';

interface StatusBadgeProps {
  status: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  type?: 'task' | 'permit' | 'worker' | 'general';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  size = 'md',
  showIcon = true,
  type = 'general'
}) => {
  const getStatusConfig = (status: string, type: string) => {
    const configs: Record<string, { color: string; icon: React.ReactNode; label: string }> = {
      // Task statuses
      'draft': { color: 'bg-gray-100 text-gray-800', icon: <FileText className="h-3 w-3" />, label: 'Draft' },
      'submitted': { color: 'bg-blue-100 text-blue-800', icon: <Clock className="h-3 w-3" />, label: 'Submitted' },
      'approved': { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-3 w-3" />, label: 'Approved' },
      'in-progress': { color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="h-3 w-3" />, label: 'In Progress' },
      'completed': { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-3 w-3" />, label: 'Completed' },
      'blocked': { color: 'bg-red-100 text-red-800', icon: <XCircle className="h-3 w-3" />, label: 'Blocked' },
      
      // Permit statuses
      'under-review': { color: 'bg-blue-100 text-blue-800', icon: <Clock className="h-3 w-3" />, label: 'Under Review' },
      'rejected': { color: 'bg-red-100 text-red-800', icon: <XCircle className="h-3 w-3" />, label: 'Rejected' },
      'active': { color: 'bg-green-100 text-green-800', icon: <Shield className="h-3 w-3" />, label: 'Active' },
      'closed': { color: 'bg-gray-100 text-gray-800', icon: <CheckCircle className="h-3 w-3" />, label: 'Closed' },
      
      // Worker statuses
      'on-site': { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-3 w-3" />, label: 'On Site' },
      'off-site': { color: 'bg-gray-100 text-gray-800', icon: <XCircle className="h-3 w-3" />, label: 'Off Site' },
      'on-break': { color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="h-3 w-3" />, label: 'On Break' },
      'unavailable': { color: 'bg-red-100 text-red-800', icon: <XCircle className="h-3 w-3" />, label: 'Unavailable' },
      
      // Priority levels
      'low': { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="h-3 w-3" />, label: 'Low' },
      'medium': { color: 'bg-yellow-100 text-yellow-800', icon: <AlertTriangle className="h-3 w-3" />, label: 'Medium' },
      'high': { color: 'bg-orange-100 text-orange-800', icon: <AlertTriangle className="h-3 w-3" />, label: 'High' },
      'critical': { color: 'bg-red-100 text-red-800', icon: <AlertTriangle className="h-3 w-3" />, label: 'Critical' },
      
      // Overtime statuses
      'pending': { color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="h-3 w-3" />, label: 'Pending' }
    };

    return configs[status.toLowerCase()] || { 
      color: 'bg-gray-100 text-gray-800', 
      icon: <FileText className="h-3 w-3" />, 
      label: status 
    };
  };

  const config = getStatusConfig(status, type);
  
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };

  return (
    <span className={`inline-flex items-center font-medium rounded-full ${config.color} ${sizeClasses[size]}`}>
      {showIcon && (
        <span className="mr-1">
          {config.icon}
        </span>
      )}
      {config.label}
    </span>
  );
};

export default StatusBadge;
