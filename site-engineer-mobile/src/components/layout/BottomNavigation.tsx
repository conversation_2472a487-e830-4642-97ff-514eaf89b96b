import React from 'react';
import { Home, Users, CheckSquare, Shield, FileText } from 'lucide-react';

interface TabItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  badge?: number;
  path: string;
}

interface BottomNavigationProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  activeTab,
  onTabChange
}) => {
  const tabs: TabItem[] = [
    {
      id: 'dashboard',
      label: 'Home',
      icon: <Home className="h-5 w-5" />,
      path: '/dashboard'
    },
    {
      id: 'team',
      label: 'My Team',
      icon: <Users className="h-5 w-5" />,
      path: '/team'
    },
    {
      id: 'tasks',
      label: 'Tasks',
      icon: <CheckSquare className="h-5 w-5" />,
      path: '/tasks'
    },
    {
      id: 'permits',
      label: 'Permits',
      icon: <Shield className="h-5 w-5" />,
      path: '/permits'
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: <FileText className="h-5 w-5" />,
      path: '/reports'
    }
  ];

  return (
    <nav className="bg-white border-t border-gray-200 px-2 py-1 fixed bottom-0 left-0 right-0 z-50">
      <div className="flex items-center justify-around">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex flex-col items-center justify-center py-2 px-3 rounded-lg transition-colors min-w-0 flex-1 ${
              activeTab === tab.id
                ? 'text-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
            aria-label={tab.label}
          >
            <div className="mb-1">
              {tab.icon}
            </div>
            <span className="text-xs font-medium truncate">
              {tab.label}
            </span>
            {tab.badge && tab.badge > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {tab.badge > 9 ? '9+' : tab.badge}
              </span>
            )}
          </button>
        ))}
      </div>
    </nav>
  );
};

export default BottomNavigation;
